import * as React from "react";

const IconHelp = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={28}
    height={28}
    viewBox="0 0 28 28"
    {...props}
  >
    <g id="Help_icon" data-name="Help icon" transform="translate(-1181 -16)">
      <g
        id="icon_bg"
        data-name="icon bg"
        transform="translate(1181 16)"
        fill="#fff"
        stroke="#e6e6e6"
        strokeWidth={1}
      >
        <circle cx={14} cy={14} r={14} stroke="none" />
        <circle cx={14} cy={14} r={13.5} fill="none" />
      </g>
      <path
        id="Icon_ionic-ios-help"
        data-name="Icon ionic-ios-help"
        d="M18.33,11.25c2.8,0,4.725,1.471,4.725,3.587a3.449,3.449,0,0,1-2.1,3.141c-1.3.714-1.739,1.238-1.739,2.143v.559h-2.59l-.022-.608a2.914,2.914,0,0,1,1.762-3.113c1.262-.714,1.792-1.167,1.792-2.044s-.9-1.521-2.008-1.521a1.851,1.851,0,0,0-2,1.74H13.5C13.552,12.856,15.329,11.25,18.33,11.25ZM16.366,23.375a1.608,1.608,0,1,1,1.6,1.457A1.527,1.527,0,0,1,16.366,23.375Z"
        transform="translate(1176.5 11.75)"
        fill="#4061c7"
      />
    </g>
  </svg>
);
export default React.memo(IconHelp);
