import { render, screen } from "@testing-library/react";
import Button from "./button";

describe("Button", () => {
  it("renders with primary intent and medium size by default", () => {
    render(<Button />);

    const buttonElement = screen.getByRole("button");

    expect(buttonElement).toHaveClass("py-1.5");
    expect(buttonElement).toHaveClass("bg-primary-78");
    expect(buttonElement).toHaveClass("border-none");
    expect(buttonElement).toHaveClass("text-white");
    expect(buttonElement).toHaveClass("hover:bg-primary-90");
    expect(buttonElement).toHaveClass("focus:border-primary-78");
    expect(buttonElement).toHaveClass("active:bg-primary-100");
    expect(buttonElement).toHaveClass("disabled:bg-primary-60");
    expect(buttonElement).toHaveClass("px-4");
  });

  it("renders with secondary intent and medium size", () => {
    render(<Button intent="secondary" />);

    const buttonElement = screen.getByRole("button");

    expect(buttonElement).toHaveClass("py-[0.3335rem]");
    expect(buttonElement).toHaveClass("bg-white");
    expect(buttonElement).toHaveClass("border");
    expect(buttonElement).toHaveClass("text-primary-78");
    expect(buttonElement).toHaveClass("border-primary-78");
    expect(buttonElement).toHaveClass("hover:bg-primary-40");
    expect(buttonElement).toHaveClass("focus:border-primary-78");
    expect(buttonElement).toHaveClass("active:border-primary-90");
    expect(buttonElement).toHaveClass("active:bg-primary-50");
    expect(buttonElement).toHaveClass("active:text-primary-90");
    expect(buttonElement).toHaveClass("disabled:border-primary-60");
    expect(buttonElement).toHaveClass("disabled:text-primary-60");
    expect(buttonElement).toHaveClass("disabled:bg-white");
    expect(buttonElement).toHaveClass("px-4");
  });

  it("renders with teritory intent and medium size", () => {
    render(<Button intent="teritory" />);

    const buttonElement = screen.getByRole("button");

    expect(buttonElement).toHaveClass("py-1.5");
    expect(buttonElement).toHaveClass("bg-white");
    expect(buttonElement).toHaveClass("text-primary-78");
    expect(buttonElement).toHaveClass("hover:bg-primary-40");
    expect(buttonElement).toHaveClass("focus:border-primary-78");
    expect(buttonElement).toHaveClass("active:bg-primary-50");
    expect(buttonElement).toHaveClass("active:text-primary-90");
    expect(buttonElement).toHaveClass("disabled:bg-primary-60");
    expect(buttonElement).toHaveClass("disabled:text-primary-60");
    expect(buttonElement).toHaveClass("disabled:bg-white");
    expect(buttonElement).toHaveClass("px-4");
  });
});
