import React from 'react';
import PropTypes from 'prop-types';
import CurrencyDropdown from '../evidence-panel/currency-dropdown';
import CurrencyUnitDropdown from '../evidence-panel/currency-unit-dropdown';

/**
 * FundCard Component
 * 
 * A card component that displays fund information with initials, fund name and company count
 * It uses a 48x48 avatar with 2 letters, followed by the fund name and count
 */
const FundCard = ({ 
  fundName, 
  companyCount, 
  backgroundColor = "#FFFFFF",
  borderColor = "#F2F2F2",
  fundNameColor = "#2B2B33",
  countColor = "#666666",
  avatarColor = "#F5F9FF",
  currency = "USD",
  currencyUnit = "Absolute",
  currencyList,
  onCurrencyChange = () => {},
  onCurrencyUnitChange = () => {}
}) => {
  // Extract the first 2 letters from the fund name for the avatar
  const getInitials = (name) => {
    if (!name) return "FD";
    
    const words = name.trim().split(' ');
    
    if (words.length === 1) {
      // If single word, take first two letters
      return name.substring(0, 2).toUpperCase();
    } else {
      // Otherwise take first letter of first two words
      return (words[0][0] + words[1][0]).toUpperCase();
    }
  };

  const initials = getInitials(fundName);

  return (
    <div 
      className="flex items-center  rounded-lg" 
    >
      <div 
        className="flex-shrink-0 body-r text-body-r flex items-center justify-center w-12 h-12 rounded mr-3 border text-white border-neutral-5"
        style={{ background: "linear-gradient(180deg, #021155 0%, #9C27B0 100%)" }}
      >
        {initials}
      </div>      <div className="flex-1">
        <h3 
          className="heading-2-m text-[#2B2B33]"
          style={{ color: fundNameColor }}
        >
          {fundName}
        </h3>
        <p 
          className="font-caption-i text-caption-i text-neutral-60"
        >
          {companyCount} {companyCount === 1 ? 'Company is added' : 'Companies are added'}
        </p>
      </div>
        <div className="flex items-center gap-2 ml-4 pl-3 custom-unit-currency">
        <CurrencyDropdown 
          currency={currency}
          currencyList={currencyList}
          onCurrencyChange={onCurrencyChange}
        />
        <CurrencyUnitDropdown 
          currencyUnit={currencyUnit}
          onCurrencyUnitChange={onCurrencyUnitChange}
        />
      </div>
    </div>
  );
};

FundCard.propTypes = {
  fundName: PropTypes.string.isRequired,
  companyCount: PropTypes.number.isRequired,
  backgroundColor: PropTypes.string,
  borderColor: PropTypes.string,
  fundNameColor: PropTypes.string,
  countColor: PropTypes.string,
  avatarColor: PropTypes.string
};

export default FundCard;
