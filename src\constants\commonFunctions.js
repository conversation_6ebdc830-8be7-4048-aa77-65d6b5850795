/**
  * Formats a KPI value based on its type and cleans numeric values
  * @param kpiInfo The type of KPI value (text, $, %, x, etc.)
  * @param value The raw value to format
  * @returns The formatted value as a string
  */
export const formatKpiValue = (kpiInfo, value) => {
  if (!value || value === null && value === undefined && value === "" && value === "no value found" && value === "-") return "NA";

  // If kpiInfo is "text", return the value as is
  if (kpiInfo === "text") return value;

  // For specific numeric indicators ($, %, x, #), clean and format the value
  if (kpiInfo === "$" || kpiInfo === "%" || kpiInfo === "x" || kpiInfo === "#") {
    // Convert value to string if it's not already
    const valueStr = value.toString();

    // Handle parentheses for negative numbers e.g., (1,974) -> -1974
    if (valueStr.startsWith("(") && valueStr.endsWith(")")) {
      const innerValue = valueStr.substring(1, valueStr.length - 1);
      // Process the inner value and make it negative
      const processedValue = formatKpiValue(kpiInfo, innerValue);
      // If the result is a valid number, make it negative
      if (processedValue !== "NA" && !isNaN(parseFloat(processedValue))) {
        return (-parseFloat(processedValue)).toString();
      }
      return "NA";
    }

    try {
      // Remove commas and any special currency/percentage symbols
      let cleanedValue = valueStr
        .replace(/[$%,]/g, '') // Remove $, %, and commas
        .replace(/[^\d.\-]/g, ''); // Remove any non-numeric characters except dots and minus

      // If the cleaned string is empty or not a valid number, return NA
      if (!cleanedValue || cleanedValue === '-' || cleanedValue === '.' || isNaN(parseFloat(cleanedValue))) {
        return "NA";
      }

      // Convert to float and then back to string to ensure proper formatting
      return parseFloat(cleanedValue).toString();
    } catch (error) {
      return "NA";
    }
  }

  // For any other type of kpiInfo
  return "NA";
};

/**
 * Validates whether the given period string matches the expected format.
 *
 * The expected format is:
 *   - Starts with parentheses containing any characters except ')', followed by whitespace.
 *   - Then either:
 *     - A month abbreviation (Jan, Feb, ..., Dec) or a quarter (Q1-Q4), followed by whitespace and a 4-digit year, or
 *     - A 4-digit year.
 *
 * @param {string|null|undefined} period - The period string to validate.
 * @returns {boolean} Returns false if the period is null, undefined, or an empty string.
 *                    Otherwise, returns true if the period does NOT match the expected format, false if it does.
 */
export const validatePeriod = (period) => {
  if (period === null || period === undefined || period === "") {
    return false;
  }
  const validFormat = /^\([^)]+\)\s+((?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec|Q[1-4])?\s+\d{4}|\d{4})$/;
  return !validFormat.test(period);
}


/**
 * Extracts period information (value type, month, quarter, year) from a column info object.
 *
 * Handles various period date formats, such as:
 * - "(Actual) Q1 2020"
 * - "(Actual YTD) Q1 2020"
 * - "(Actual) 2023"
 * - "actual jan 2020"
 * - "actual q1 2020"
 *
 * Attempts to find the corresponding valueTypeId from the provided kpiConfig.
 *
 * @param {Object} columnInfo - The column information object containing the periodDate string.
 * @param {string|number} moduleId - The module ID to match in the kpiConfig.
 * @param {Array} kpiConfig - Array of KPI configuration objects, each with moduleId and subSectionFields.
 * @returns {Object|null} An object with extracted period info:
 *   {
 *     valueType: {string},     // e.g., "Actual", "Budget"
 *     valueTypeId: {number},   // ID for the value type (default 4 if not found)
 *     month: {number|null},    // Month number (1-12) or null if not applicable
 *     quarter: {string|number|null}, // Quarter string (e.g., "Q1") or number, or null if not applicable
 *     year: {number}           // Year (e.g., 2020)
 *   }
 *   Returns null if periodDate is missing or cannot be parsed.
 */
export const extractPeriodInfo = (columnInfo, moduleId, kpiConfig) => {
  if (!columnInfo || !columnInfo.label) {
    return null;
  }

  // Handle formats like "(Actual) Q1 2020" or "(Actual YTD) Q1 2020"
  const parenPeriodPattern = /\(([^)]+)\)\s*(Q[1-4])?\s*(\d{4})/i;
  const match = columnInfo.label.match(parenPeriodPattern);

  if (match) {
    // We have a pattern like "(Actual) Q1 2020" or "(Actual YTD) Q1 2020" or "(Actual) 2023"
    const valueType = match[1].trim(); // "Actual" or "Actual YTD"
    const quarterStr = match[2] || ""; // "Q1" or ""
    const year = parseInt(match[3], 10) || 0; // 2023 or 2020

    // Find valueTypeId from kpiConfig
    const valueTypeConfig = kpiConfig?.find(config =>
      config.moduleId === moduleId
    )?.subSectionFields.find(field =>
      field.aliasName.toLowerCase() === valueType.toLowerCase()
    );

    const valueTypeId = valueTypeConfig?.valueTypId || 4; // Default to 4 if not found

    // Extract quarter if present
    let quarter = '';
    if (quarterStr && quarterStr.toLowerCase().startsWith('q')) {
      quarter = quarterStr;
    }

    return {
      valueType: valueType.charAt(0).toUpperCase() + valueType.slice(1),
      valueTypeId,
      month: null, // No month specified in this format
      quarter, // Will be 0 if no quarter was specified
      year,
    };
  }

  // Handle the standard format (e.g. "actual jan 2020" or "actual q1 2020")
  // First check if it has parentheses that might need special handling
  if (columnInfo.label.includes('(') && columnInfo.label.includes(')')) {
    // There are parentheses but they didn't match our first pattern
    // Extract the part in parentheses as valueType, and the rest for further processing
    const parenMatch = columnInfo.label.match(/\(([^)]+)\)\s*(.*)/);
    if (parenMatch) {
      const valueType = parenMatch[1].trim();
      const remainingText = parenMatch[2].trim();
      const remainingParts = remainingText.split(' ');

      // Find valueTypeId from kpiConfig
      const valueTypeConfig = kpiConfig?.find(config =>
        config.moduleId === moduleId
      )?.subSectionFields.find(field =>
        field.aliasName.toLowerCase() === valueType.toLowerCase()
      );

      const valueTypeId = valueTypeConfig?.valueTypId || 4;

      let month = 0;
      let quarter = null;
      let year = 0;

      // Extract quarter/month and year from remaining parts
      if (remainingParts.length >= 1) {
        // Try to get the year from the last part
        year = parseInt(remainingParts[remainingParts.length - 1], 10) || 0;

        // If we have at least two parts, check for quarter/month
        if (remainingParts.length >= 2) {
          const periodPart = remainingParts[0].toLowerCase();
          if (periodPart.startsWith('q')) {
            quarter = remainingParts[0] || "";
          } else {
            // Check if it's a month
            const monthNames = ["jan", "feb", "mar", "apr", "may", "jun", "jul", "aug", "sep", "oct", "nov", "dec"];
            month = monthNames.findIndex(m => periodPart.startsWith(m)) + 1;
          }
        }
      }

      return {
        valueType: valueType.charAt(0).toUpperCase() + valueType.slice(1),
        valueTypeId,
        month,
        quarter,
        year
      };
    }
  }

  // Regular split-based approach for simple formats
  const parts = columnInfo.label.split(' ');
  if (parts.length < 3) {
    return null;
  }

  const valueType = parts[0]; // e.g., "actual", "budget"
  let month = 0;
  let quarter = 0;
  let year = parseInt(parts[2], 10) || 0;

  // Handle different period formats
  if (parts[1].startsWith('q')) {
    quarter = parseInt(parts[1].substring(1), 10) || 0;
  } else {
    // Convert month name to number
    const monthNames = ["jan", "feb", "mar", "apr", "may", "jun", "jul", "aug", "sep", "oct", "nov", "dec"];
    month = monthNames.findIndex(m => parts[1].toLowerCase().startsWith(m)) + 1;
  }

  // Find valueTypeId from kpiConfig
  const valueTypeConfig = kpiConfig?.find(config =>
    config.moduleId === moduleId
  )?.subSectionFields.find(field =>
    field.aliasName.toLowerCase() === valueType
  );

  const valueTypeId = valueTypeConfig?.valueTypId || 4; // Default to 4 if not found

  return {
    valueType: valueType.charAt(0).toUpperCase() + valueType.slice(1),
    valueTypeId,
    month,
    quarter,
    year
  };
};