import { React } from "react";
import PropTypes from "prop-types";
import { useAuth } from "react-oidc-context";
import { DELETE_TOKENS_API } from "../../../../infra/api/token-manager-service";
import PopUp from "../../../molecules/pop-up/pop-up";

const HeaderLogOutPopUp = ({ showNewUserPopUp, setShowNewUserPopUp }) => {
  const deleteTokenAPI = DELETE_TOKENS_API();

  const auth = useAuth();
  const userLogOut = async () => {
    await deleteTokenAPI.mutateAsync();
    auth.signoutRedirect();
  };
  return (
    <div>
      <PopUp
        colGrid={"grid-cols-12"}
        header={"Log out"}
        showPopUp={showNewUserPopUp}
        setShowPopUp={setShowNewUserPopUp}
        cancelButtonText={"Cancel"}
        submitButtonText={"Confirm"}
        onSubmit={userLogOut}
        cols={"col-span-4 col-start-5"}
      >
        <div className="p-6 py-4">
          <div className="my-3">Do you confirm to end this session?</div>
        </div>
      </PopUp>
    </div>
  );
};

export default HeaderLogOutPopUp;

HeaderLogOutPopUp.propTypes = {
  showNewUserPopUp: PropTypes.bool,
  setShowNewUserPopUp: PropTypes.func
};
