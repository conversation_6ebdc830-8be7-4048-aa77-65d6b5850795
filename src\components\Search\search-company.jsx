import React, { useState, useRef, useEffect } from "react";
import PropTypes from "prop-types";
import { ComboBox } from "@progress/kendo-react-dropdowns";
import CustomItem from "./search-item";
import Chip from "../chips/Chip";
import NoResults from "../../resources/images/NoResults.svg";
import SpinnerCircle from "../../partials/atoms/loader/spinner-circle";
import { ENTITY_API } from "../../infra/api/company/get-company-service";
import EntityLogo from "../../partials/molecules/entity-logo/entity-logo";
import { PopUp } from "../../partials/molecules/pop-up";
import { FiPlus } from "react-icons/fi";
const DROP_DOWN_STATUS = {
  LOADING: "LOADING",
  NO_RESULTS: "NO_RESULTS",
  RESULTS: "RESULTS",
  NO_ACTION: "NO_ACTION"
};
const SearchCompany = ({ onCompanySelect }) => {
  const [isCompanySelected, setisCompanySelected] = useState(false);
  const [selectedValue, setSelectedValue] = useState(null);
  const [dropdownStatus, setDropdownStatus] = useState(
    DROP_DOWN_STATUS.NO_ACTION
  );
  const [data, setData] = useState([]);
  const [search, setSearch] = useState("");
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [previousValue, setPreviousValue] = useState(null);
  const [isFocus, setIsFocus] = useState(false);
  const search_api = ENTITY_API(search, ["name", "security_ticker"]);
  const comboBoxRef = useRef(null);
  useEffect(() => {
    setDropdownStatus(DROP_DOWN_STATUS.LOADING);
    setData(search_api.data ? search_api.data.data : []);
    setDropdownStatus(
      search_api.data && search_api.data.data.length > 0
        ? DROP_DOWN_STATUS.RESULTS
        : DROP_DOWN_STATUS.NO_RESULTS
    );
  }, [search_api?.data]);
  const blurFlag = useRef(false);
  const getInitials = (company) => {
    let initials = "";
    let words = company.split(" ");
    if (words.length > 1) {
      initials = words[0].charAt(0) + words[1].charAt(0);
      return initials.toUpperCase();
    }
  };
  const itemRender = (li, itemProps) => {
    const itemChildren = (
      <div className="flex justify-between gap-3 rounded w-full hover:bg-primary-43 py-2.5 px-4 pcursor-pointer">
        <div className="font-body-r text-body-r flex items-center cursor-pointer">
          <div>
            <EntityLogo
              acuityID={itemProps.dataItem.acuity_id}
              companyName={itemProps.dataItem.name}
              logoSize="medium"
            />
          </div>
          {itemProps.dataItem.is_public ? (
            <>
              <label className="s-r pl-2 cursor-pointer ">
                {itemProps.dataItem["security_ticker"]}
              </label>
              <span className="s-r cursor-pointer pl-1 pr-1">|</span>
            </>
          ) : (
            <></>
          )}
          <label
            className={
              itemProps.dataItem.is_public
                ? "s-r cursor-pointer"
                : "s-r cursor-pointer pl-2"
            }
          >
            {itemProps.dataItem.name}
          </label>
        </div>
        <div className="flex gap-2">
          {itemProps.dataItem.headquarter?.country[0]?.value && (
            <Chip>{itemProps.dataItem.headquarter?.country[0]?.value}</Chip>
          )}
          {itemProps.dataItem?.sector && (
            <Chip>{itemProps.dataItem.sector[0]?.value}</Chip>
          )}
          {itemProps.dataItem.is_public && <Chip>Public</Chip>}
          {!itemProps.dataItem.is_public && <Chip>Private</Chip>}
          {itemProps.dataItem.is_public && (
            <Chip>{itemProps.dataItem.primary_exchange}</Chip>
          )}
        </div>
      </div>
    );
    return React.cloneElement(
      li,
      { ...li.props, className: "list-item focus:shadow-none pl-2" },
      itemChildren
    );
  };
  const handleEdit = () => {
    setisCompanySelected(false);
    setSelectedValue(null);
    setIsFocus(true);
    setTimeout(() => {
      if (comboBoxRef.current) {
        comboBoxRef.current.focus();
      }
    }, 0);
  };
  const toggleDialog = (event) => {
    if (!event.value.acuity_id) {
      return;
    }
    if (previousValue !== null && event.value.name !== previousValue?.name) {
      setSelectedValue(event.value);
      setIsModalOpen(true);
    } else if (
      (event.value && previousValue === null) ||
      (previousValue !== null && event.value.name === previousValue?.name)
    ) {
      setPreviousValue(event.value);
      setSelectedValue(event.value);
      onCompanySelect(event.value);
      setisCompanySelected(true);
    }
  };
  const handleBlur = () => {
    blurFlag.current = true;
    if (
      !selectedValue ||
      selectedValue === null ||
      selectedValue === undefined ||
      selectedValue === ""
    ) {
      setSelectedValue(previousValue);
      setisCompanySelected(true);
    }
  };
  const handleComponentRender = (selectedCompany) => {
    setSelectedValue(selectedCompany);
    setPreviousValue(selectedCompany);
    onCompanySelect(selectedCompany);
    setisCompanySelected(true);
    setIsModalOpen(false);
  };
  const modalClose = (previousValue) => {
    setSelectedValue(previousValue);
    setPreviousValue(previousValue);
    onCompanySelect(previousValue);
    setisCompanySelected(true);
    setIsModalOpen(false);
  };
  const filterChange = (event) => {
    setSearch(event.filter.value);
    if (event.filter.value.length >= 2) {
      setDropdownStatus(DROP_DOWN_STATUS.LOADING);
      const search_apii = ENTITY_API(event.filter.value, [
        "name",
        "security_ticker"
      ]);
      setData(search_apii.data ? search_apii.data.data : []);
    } else {
      setData([]);
      setDropdownStatus(DROP_DOWN_STATUS.NO_RESULTS);
    }
  };
  const searchFocus = {
    border: "1px solid #021155"
  };
  return (
    <div className="mt-4">
      {isCompanySelected && selectedValue ? (
        <CustomItem
          id={selectedValue.acuity_id}
          companyName={selectedValue.name}
          ticker={selectedValue.primary_ticker}
          logo={selectedValue.logo_url ? selectedValue.logo_url[0].value : ""}
          Region={selectedValue.headquarter?.country[0]?.value}
          initials={getInitials(selectedValue.name)}
          sector={selectedValue.sector ? selectedValue.sector[0]?.value : ""}
          type={selectedValue.is_public ? "Public" : "Private"}
          isExchange={
            selectedValue.primary_exchange ? selectedValue.primary_exchange : ""
          }
          onEdit={handleEdit}
        />
      ) : (
        <div className="flex flex-col gap-1 border border-gray p-5 rounded-md w-full">
          <div>
            <label
              htmlFor="company-name"
              className="text-caption-m font-caption-m text-neutral-60"
            >
              Company name
            </label>
            <span className="text-caption-m font-caption-m text-negative-100">
              *
            </span>
          </div>
          <ComboBox
            ref={comboBoxRef}
            data={data}
            textField="name"
            itemRender={itemRender}
            filterable={true}
            onFilterChange={filterChange}
            id="company-name"
            placeholder="Search by company name or ticker"
            className="text-body-r font-body-r custom-combobox focus-within:border-blue-500"
            style={isFocus ? searchFocus : {}}
            allowCustom={true}
            aria-busy={dropdownStatus === DROP_DOWN_STATUS.LOADING}
            onBlur={handleBlur}
            onChange={toggleDialog.bind(this)}
            footer={
              <span className="flex items-center justify-center s-r text-primary-78 pb-2">
                <div className="mr-0 relative w-4 h-4 flex-none">
                  <FiPlus className="text-[#93B0ED] absolute" />
                </div>
                Add Company
              </span>
            }
            listNoDataRender={() => (
              <div className="flex flex-col items-center justify-center position-relative mt-4 pb-4">
                {dropdownStatus === DROP_DOWN_STATUS.LOADING ? (
                  <div className="body-r flex h-full flex-col items-center justify-center">
                    <SpinnerCircle size={6} />
                    <div className="text-gray-600">
                      Please wait, we are getting the results ready
                    </div>
                  </div>
                ) : dropdownStatus === DROP_DOWN_STATUS.NO_RESULTS ? (
                  <>
                    <img src={NoResults} alt="No Data" />
                    <div className="text-gray-600">No Result Found!</div>
                  </>
                ) : null}
              </div>
            )}
          />
        </div>
      )}
      <div data-testid="watch-lists-add-pop-up">
        {isModalOpen && (
          <PopUp
            colGrid={"grid-cols-10"}
            header={"Change Company"}
            showPopUp={true}
            setShowPopUp={() => modalClose(previousValue)}
            cancelButtonText={"Cancel"}
            submitButtonText={"Yes, Sure"}
            onSubmit={() => {
              handleComponentRender(selectedValue);
            }}
            cols={"col-span-4 col-start-4"}
          >
            <div className="m-6 grid grid-cols-1 gap-4">
              <div className="col-span-1">
                <div className="block">
                  <label
                    htmlFor="change-name"
                    className="body-r text-neutral-80"
                  >
                    Are you sure you want to change the company?
                  </label>
                  <br />
                  <label
                    htmlFor="company-note"
                    className="caption-i text-neutral-80"
                  >
                    Note: Selected document & template will be erased.
                  </label>
                </div>
              </div>
            </div>
          </PopUp>
        )}
      </div>
    </div>
  );
};
SearchCompany.propTypes = {
  onCompanySelect: PropTypes.func.isRequired
};
export default SearchCompany;
