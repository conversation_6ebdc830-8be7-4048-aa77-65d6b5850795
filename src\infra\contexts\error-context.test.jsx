import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ErrorContextProvider } from './error-context';

// Create components for testing
const NormalComponent = () => <div data-testid="normal">Normal Component</div>;
const ErrorComponent = () => {
  throw new Error('Test Error');
  return <div>This should not render</div>;
};

describe('ErrorContextProvider', () => {
  beforeEach(() => {
    // Mock console.log to avoid error messages in test output
    jest.spyOn(console, 'log').mockImplementation(() => {});
    
    // Mock window.location.reload
    Object.defineProperty(window, 'location', {
      configurable: true,
      value: { reload: jest.fn() }
    });
  });
  
  afterEach(() => {
    console.log.mockRestore();
  });
  
  test('renders children when no error occurs', () => {
    render(
      <ErrorContextProvider>
        <NormalComponent />
      </ErrorContextProvider>
    );
    
    expect(screen.getByTestId('normal')).toBeInTheDocument();
  });
  
  test('renders ErrorPage when an error occurs', () => {
    render(
      <ErrorContextProvider>
        <ErrorComponent />
      </ErrorContextProvider>
    );
    
    // Normal component should not be rendered
    expect(screen.queryByTestId('normal')).not.toBeInTheDocument();
    
    // Error page should be rendered
    expect(screen.getByTestId('error-context-page')).toBeInTheDocument();
    expect(screen.getByText('500 - Server error')).toBeInTheDocument();
    expect(screen.getByText('Oops something went wrong. Try to refresh this page')).toBeInTheDocument();
  });
  
  test('reloads page when "Try Again" button is clicked', () => {
    render(
      <ErrorContextProvider>
        <ErrorComponent />
      </ErrorContextProvider>
    );
    
    // Find and click the reload button
    const reloadButton = screen.getByTestId('error-context-reload');
    fireEvent.click(reloadButton);
    
    // Check that window.location.reload was called
    expect(window.location.reload).toHaveBeenCalledTimes(1);
  });
  
  test('logs error when an error occurs', () => {
    render(
      <ErrorContextProvider>
        <ErrorComponent />
      </ErrorContextProvider>
    );
    
    // Check that error was logged
    expect(console.log).toHaveBeenCalledWith(
      'ErrorBoundary onError ===> ',
      expect.any(Error)
    );
  });
}); 