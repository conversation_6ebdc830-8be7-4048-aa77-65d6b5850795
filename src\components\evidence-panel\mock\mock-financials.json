{"isError": false, "body": {"job_id": "job123", "company_id": "comp123", "version": "0.1.0", "company_name": "American Association of Profession", "ticker": "ADNT", "table_groups": [{"label": "Income Statement", "tables": [{"id": "table1", "name": "Income Statement", "title": "Income Statement", "columns": [{"title": "Document Line Item", "key": "label"}], "sections": [{"type": "subSection", "rows": [{"label": {"defined_name": "r_revenue", "text": "Revenues", "style": "header"}, "cells": []}, {"label": {"defined_name": "r_admission", "text": "Admissions", "style": "lineitem"}, "cells": [{"value": {"type": "dollar", "value": 530.5, "format": "american", "decimalPlaces": 1}, "column_key": "Q1-2024|null|3/31/2024|3-months", "bbox": {"x1": 349, "y1": 150, "x2": 370, "y2": 180, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": null, "format": "american", "decimalPlaces": 1}, "column_key": "Q4-2023|null|12/31/2023|3-months", "bbox": {"x1": 380, "y1": 150, "x2": 401, "y2": 180, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": 528.9, "format": "american", "decimalPlaces": 1}, "column_key": "Q3-2023|null|9/30/2023|3-months", "bbox": {"x1": 411, "y1": 150, "x2": 432, "y2": 180, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": null, "format": "american", "decimalPlaces": 1}, "column_key": "Q2-2023|null|6/30/2023|3-months", "bbox": {"x1": 442, "y1": 150, "x2": 463, "y2": 180, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": 534.1, "format": "american", "decimalPlaces": 1}, "column_key": "Q1-2023|null|3/31/2023|3-months", "bbox": {"x1": 473, "y1": 150, "x2": 494, "y2": 180, "width": 992, "height": 1403, "pageNumber": 1}}]}, {"label": {"defined_name": "r_concessions", "text": "Concessions", "style": "lineitem"}, "cells": [{"value": {"type": "dollar", "value": 245.3, "format": "american", "decimalPlaces": 1}, "column_key": "Q1-2024|null|3/31/2024|3-months", "bbox": {"x1": 349, "y1": 200, "x2": 370, "y2": 230, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": 242.1, "format": "american", "decimalPlaces": 1}, "column_key": "Q4-2023|null|12/31/2023|3-months", "bbox": {"x1": 380, "y1": 200, "x2": 401, "y2": 230, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": 248.5, "format": "american", "decimalPlaces": 1}, "column_key": "Q3-2023|null|9/30/2023|3-months", "bbox": {"x1": 411, "y1": 200, "x2": 432, "y2": 230, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": 244.7, "format": "american", "decimalPlaces": 1}, "column_key": "Q2-2023|null|6/30/2023|3-months", "bbox": {"x1": 442, "y1": 200, "x2": 463, "y2": 230, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": 241.2, "format": "american", "decimalPlaces": 1}, "column_key": "Q1-2023|null|3/31/2023|3-months", "bbox": {"x1": 473, "y1": 200, "x2": 494, "y2": 230, "width": 992, "height": 1403, "pageNumber": 1}}]}, {"label": {"defined_name": "r_other_revenue", "text": "Other Revenue", "style": "lineitem"}, "cells": [{"value": {"type": "dollar", "value": 85.4, "format": "american", "decimalPlaces": 1}, "column_key": "Q1-2024|null|3/31/2024|3-months", "bbox": {"x1": 349, "y1": 250, "x2": 370, "y2": 280, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": 83.2, "format": "american", "decimalPlaces": 1}, "column_key": "Q4-2023|null|12/31/2023|3-months", "bbox": {"x1": 380, "y1": 250, "x2": 401, "y2": 280, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": 84.7, "format": "american", "decimalPlaces": 1}, "column_key": "Q3-2023|null|9/30/2023|3-months", "bbox": {"x1": 411, "y1": 250, "x2": 432, "y2": 280, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": 82.9, "format": "american", "decimalPlaces": 1}, "column_key": "Q2-2023|null|6/30/2023|3-months", "bbox": {"x1": 442, "y1": 250, "x2": 463, "y2": 280, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": 81.5, "format": "american", "decimalPlaces": 1}, "column_key": "Q1-2023|null|3/31/2023|3-months", "bbox": {"x1": 473, "y1": 250, "x2": 494, "y2": 280, "width": 992, "height": 1403, "pageNumber": 1}}]}, {"label": {"defined_name": "r_total_revenue", "text": "Total Revenue", "style": "total"}, "cells": [{"value": {"type": "dollar", "value": 861.2, "format": "american", "decimalPlaces": 1}, "column_key": "Q1-2024|null|3/31/2024|3-months", "bbox": {"x1": 349, "y1": 300, "x2": 370, "y2": 330, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": 850.5, "format": "american", "decimalPlaces": 1}, "column_key": "Q4-2023|null|12/31/2023|3-months", "bbox": {"x1": 380, "y1": 300, "x2": 401, "y2": 330, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": 862.1, "format": "american", "decimalPlaces": 1}, "column_key": "Q3-2023|null|9/30/2023|3-months", "bbox": {"x1": 411, "y1": 300, "x2": 432, "y2": 330, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": 859.4, "format": "american", "decimalPlaces": 1}, "column_key": "Q2-2023|null|6/30/2023|3-months", "bbox": {"x1": 442, "y1": 300, "x2": 463, "y2": 330, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": 856.8, "format": "american", "decimalPlaces": 1}, "column_key": "Q1-2023|null|3/31/2023|3-months", "bbox": {"x1": 473, "y1": 300, "x2": 494, "y2": 330, "width": 992, "height": 1403, "pageNumber": 1}}]}, {"label": {"defined_name": "r_expenses", "text": "Operating Expenses", "style": "header"}, "cells": []}, {"label": {"defined_name": "r_salaries", "text": "Salaries and Wages", "style": "lineitem"}, "cells": [{"value": {"type": "dollar", "value": 245.8, "format": "american", "decimalPlaces": 1}, "column_key": "Q1-2024|null|3/31/2024|3-months", "bbox": {"x1": 349, "y1": 350, "x2": 370, "y2": 380, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": 242.3, "format": "american", "decimalPlaces": 1}, "column_key": "Q4-2023|null|12/31/2023|3-months", "bbox": {"x1": 380, "y1": 350, "x2": 401, "y2": 380, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": 240.1, "format": "american", "decimalPlaces": 1}, "column_key": "Q3-2023|null|9/30/2023|3-months", "bbox": {"x1": 411, "y1": 350, "x2": 432, "y2": 380, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": 239.4, "format": "american", "decimalPlaces": 1}, "column_key": "Q2-2023|null|6/30/2023|3-months", "bbox": {"x1": 442, "y1": 350, "x2": 463, "y2": 380, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": 238.6, "format": "american", "decimalPlaces": 1}, "column_key": "Q1-2023|null|3/31/2023|3-months", "bbox": {"x1": 473, "y1": 350, "x2": 494, "y2": 380, "width": 992, "height": 1403, "pageNumber": 1}}]}, {"label": {"defined_name": "r_rent", "text": "Rent and Occupancy", "style": "lineitem"}, "cells": [{"value": {"type": "dollar", "value": 125.4, "format": "american", "decimalPlaces": 1}, "column_key": "Q1-2024|null|3/31/2024|3-months", "bbox": {"x1": 349, "y1": 400, "x2": 370, "y2": 430, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": 124.8, "format": "american", "decimalPlaces": 1}, "column_key": "Q4-2023|null|12/31/2023|3-months", "bbox": {"x1": 380, "y1": 400, "x2": 401, "y2": 430, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": 124.2, "format": "american", "decimalPlaces": 1}, "column_key": "Q3-2023|null|9/30/2023|3-months", "bbox": {"x1": 411, "y1": 400, "x2": 432, "y2": 430, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": 123.9, "format": "american", "decimalPlaces": 1}, "column_key": "Q2-2023|null|6/30/2023|3-months", "bbox": {"x1": 442, "y1": 400, "x2": 463, "y2": 430, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": 123.5, "format": "american", "decimalPlaces": 1}, "column_key": "Q1-2023|null|3/31/2023|3-months", "bbox": {"x1": 473, "y1": 400, "x2": 494, "y2": 430, "width": 992, "height": 1403, "pageNumber": 1}}]}, {"label": {"defined_name": "r_utilities", "text": "Utilities", "style": "lineitem"}, "cells": [{"value": {"type": "dollar", "value": 45.2, "format": "american", "decimalPlaces": 1}, "column_key": "Q1-2024|null|3/31/2024|3-months", "bbox": {"x1": 349, "y1": 450, "x2": 370, "y2": 480, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": 44.8, "format": "american", "decimalPlaces": 1}, "column_key": "Q4-2023|null|12/31/2023|3-months", "bbox": {"x1": 380, "y1": 450, "x2": 401, "y2": 480, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": 46.5, "format": "american", "decimalPlaces": 1}, "column_key": "Q3-2023|null|9/30/2023|3-months", "bbox": {"x1": 411, "y1": 450, "x2": 432, "y2": 480, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": 45.7, "format": "american", "decimalPlaces": 1}, "column_key": "Q2-2023|null|6/30/2023|3-months", "bbox": {"x1": 442, "y1": 450, "x2": 463, "y2": 480, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": 44.9, "format": "american", "decimalPlaces": 1}, "column_key": "Q1-2023|null|3/31/2023|3-months", "bbox": {"x1": 473, "y1": 450, "x2": 494, "y2": 480, "width": 992, "height": 1403, "pageNumber": 1}}]}, {"label": {"defined_name": "r_maintenance", "text": "Maintenance and Repairs", "style": "lineitem"}, "cells": [{"value": {"type": "dollar", "value": 35.6, "format": "american", "decimalPlaces": 1}, "column_key": "Q1-2024|null|3/31/2024|3-months", "bbox": {"x1": 349, "y1": 500, "x2": 370, "y2": 530, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": 34.9, "format": "american", "decimalPlaces": 1}, "column_key": "Q4-2023|null|12/31/2023|3-months", "bbox": {"x1": 380, "y1": 500, "x2": 401, "y2": 530, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": 35.2, "format": "american", "decimalPlaces": 1}, "column_key": "Q3-2023|null|9/30/2023|3-months", "bbox": {"x1": 411, "y1": 500, "x2": 432, "y2": 530, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": 34.8, "format": "american", "decimalPlaces": 1}, "column_key": "Q2-2023|null|6/30/2023|3-months", "bbox": {"x1": 442, "y1": 500, "x2": 463, "y2": 530, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": 34.5, "format": "american", "decimalPlaces": 1}, "column_key": "Q1-2023|null|3/31/2023|3-months", "bbox": {"x1": 473, "y1": 500, "x2": 494, "y2": 530, "width": 992, "height": 1403, "pageNumber": 1}}]}, {"label": {"defined_name": "r_total_expenses", "text": "Total Operating Expenses", "style": "total"}, "cells": [{"value": {"type": "dollar", "value": 452.0, "format": "american", "decimalPlaces": 1}, "column_key": "Q1-2024|null|3/31/2024|3-months", "bbox": {"x1": 349, "y1": 550, "x2": 370, "y2": 580, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": 446.8, "format": "american", "decimalPlaces": 1}, "column_key": "Q4-2023|null|12/31/2023|3-months", "bbox": {"x1": 380, "y1": 550, "x2": 401, "y2": 580, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": 446.0, "format": "american", "decimalPlaces": 1}, "column_key": "Q3-2023|null|9/30/2023|3-months", "bbox": {"x1": 411, "y1": 550, "x2": 432, "y2": 580, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": 443.8, "format": "american", "decimalPlaces": 1}, "column_key": "Q2-2023|null|6/30/2023|3-months", "bbox": {"x1": 442, "y1": 550, "x2": 463, "y2": 580, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": 441.5, "format": "american", "decimalPlaces": 1}, "column_key": "Q1-2023|null|3/31/2023|3-months", "bbox": {"x1": 473, "y1": 550, "x2": 494, "y2": 580, "width": 992, "height": 1403, "pageNumber": 1}}]}]}]}]}, {"label": "Balance Sheet", "tables": [{"id": "table2", "name": "Balance Sheet", "title": "Balance Sheet", "columns": [{"title": "Document Line Item", "key": "label"}], "sections": [{"type": "subSection", "rows": [{"label": {"defined_name": "bs_assets", "text": "Assets", "style": "header"}, "cells": []}, {"label": {"defined_name": "bs_cash", "text": "Cash and Cash Equivalents", "style": "lineitem"}, "cells": [{"value": {"type": "dollar", "value": 1250.0, "format": "american", "decimalPlaces": 1}, "column_key": "Q1-2024|null|3/31/2024|3-months", "bbox": {"x1": 349, "y1": 600, "x2": 370, "y2": 630, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": 1180.5, "format": "american", "decimalPlaces": 1}, "column_key": "Q1-2023|null|3/31/2023|3-months", "bbox": {"x1": 380, "y1": 600, "x2": 401, "y2": 630, "width": 992, "height": 1403, "pageNumber": 1}}]}]}]}]}, {"label": "Cash Flow", "tables": [{"id": "table3", "name": "Cash Flow", "title": "Cash Flow Statement", "columns": [{"title": "Document Line Item", "key": "label"}], "sections": [{"type": "subSection", "rows": [{"label": {"defined_name": "cf_operations", "text": "Operating Activities", "style": "header"}, "cells": []}, {"label": {"defined_name": "cf_net_income", "text": "Net Income", "style": "lineitem"}, "cells": [{"value": {"type": "dollar", "value": 284.7, "format": "american", "decimalPlaces": 1}, "column_key": "Q1-2024|null|3/31/2024|3-months", "bbox": {"x1": 349, "y1": 650, "x2": 370, "y2": 680, "width": 992, "height": 1403, "pageNumber": 1}}, {"value": {"type": "dollar", "value": 295.5, "format": "american", "decimalPlaces": 1}, "column_key": "Q1-2023|null|3/31/2023|3-months", "bbox": {"x1": 380, "y1": 650, "x2": 401, "y2": 680, "width": 992, "height": 1403, "pageNumber": 1}}]}]}]}]}]}}