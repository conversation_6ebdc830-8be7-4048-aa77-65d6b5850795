import { getPdfUrl } from './PdfService';

describe('PdfService', () => {
  describe('getPdfUrl', () => {
    test('should return correct URL for default sample.pdf', () => {
      const result = getPdfUrl();
      expect(result).toBe('./../ingestion/pdfs/sample.pdf');
    });

    test('should return correct URL for custom PDF name', () => {
      const customName = 'document123.pdf';
      const result = getPdfUrl(customName);
      expect(result).toBe('./../ingestion/pdfs/document123.pdf');
    });

    test('should handle PDF names with spaces', () => {
      const nameWithSpaces = 'my document file.pdf';
      const result = getPdfUrl(nameWithSpaces);
      expect(result).toBe('./../ingestion/pdfs/my document file.pdf');
    });

    test('should handle PDF names with special characters', () => {
      const nameWithSpecialChars = 'report-2024_Q1.pdf';
      const result = getPdfUrl(nameWithSpecialChars);
      expect(result).toBe('./../ingestion/pdfs/report-2024_Q1.pdf');
    });

    test('should handle empty string parameter', () => {
      const result = getPdfUrl('');
      expect(result).toBe('./../ingestion/pdfs/');
    });

    test('should handle null parameter', () => {
      const result = getPdfUrl(null);
      expect(result).toBe('./../ingestion/pdfs/null');
    });

    test('should handle undefined parameter as default', () => {
      const result = getPdfUrl(undefined);
      expect(result).toBe('./../ingestion/pdfs/sample.pdf');
    });
  });
});