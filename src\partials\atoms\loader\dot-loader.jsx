import React from "react";

const DotLoader = () => {
  return (
    <div className="flex items-center justify-center space-x-0.5">
      <div className="h-1.5 w-1.5 animate-[pulse_1s_infinite_100ms] rounded-full bg-neutral-60"></div>
      <div className="h-1.5 w-1.5 animate-[pulse_1s_infinite_200ms] rounded-full bg-neutral-60"></div>
      <div className="h-1.5 w-1.5 animate-[pulse_1s_infinite_300ms] rounded-full bg-neutral-60"></div>
    </div>
  );
};

export default DotLoader;
