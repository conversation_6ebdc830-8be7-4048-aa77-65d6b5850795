import React, { Fragment } from "react";
import PropTypes from "prop-types";
import { Switch } from "@headlessui/react";
import { cva } from "class-variance-authority";

const toggle_round = cva(
  "inline-block h-3.5 w-3.5 transform rounded-full transition duration-300",
  {
    variants: {
      checked: {
        true: ["translate-x-[1.38rem]"],
        false: ["translate-x-1"]
      },
      disabled: {
        true: [],
        false: []
      }
    },
    compoundVariants: [
      {
        checked: [true],
        disabled: [false],
        class: "bg-white"
      },
      {
        checked: [false],
        disabled: [false],
        class: "bg-primary-78"
      },
      {
        checked: [true, false],
        disabled: [true],
        class: "bg-neutral-20"
      }
    ]
  }
);

const Toggle = ({ checked, onChange, disabled = false }) => {
  return (
    <Switch
      checked={checked}
      onChange={onChange}
      as={Fragment}
      disabled={disabled ? true : ""}
    >
      {({ checked }) => (
        <button
          className={`${
            checked
              ? "bg-primary-78 hover:bg-primary-90"
              : "border border-primary-78 bg-white hover:bg-primary-40 disabled:bg-white"
          } 
          inline-flex h-5 w-10 items-center rounded-full transition duration-300 disabled:cursor-not-allowed disabled:border-neutral-20 disabled:bg-neutral-10`}
        >
          <span className="sr-only">Toggle Button</span>
          <span className={toggle_round({ checked, disabled })} />
        </button>
      )}
    </Switch>
  );
};

Toggle.propTypes = {
  checked: PropTypes.bool,
  onChange: PropTypes.func,
  disabled: PropTypes.bool
};

export default Toggle;
