import React from "react";
import { RangeSlider as Ken<PERSON> } from "@progress/kendo-react-inputs";
import "./kendo-range-slider.css";
import PropTypes from "prop-types";

const RangeSlider = ({ defaultValue, min, max, step, onChange, value }) => {
  return (
    <Kendo
      className={"w-full"}
      defaultValue={defaultValue}
      value={value}
      step={step}
      min={min}
      max={max}
      onChange={onChange}
    ></Kendo>
  );
};
RangeSlider.propTypes = {
  defaultValue: PropTypes.number,
  min: PropTypes.number,
  max: PropTypes.number,
  step: PropTypes.number,
  onChange: PropTypes.func,
  value: PropTypes.number
};

export default RangeSlider;
