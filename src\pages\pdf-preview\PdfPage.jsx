import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import PdfContainer from '../../components/PDF/PdfContainer';

const PdfPage = () => {
 // Get query parameters from URL
 const location = useLocation();
 const queryParams = new URLSearchParams(location.search);
 
 // Get processId from query parameters or filename as fallback
 const processId = queryParams.get('processId');
 const [receivedData, setReceivedData] = useState(null);
 
 const handleMessage = (event) => {
  if (event.data) {
    setReceivedData(event.data);
  } else {
    console.error('Received empty data from message event');
  }
};

 useEffect(() => {
   
   // Add event listener for messages
   window.addEventListener('message', handleMessage);
   
   return () => {
     window.removeEventListener('message', handleMessage);
     console.log('Message event listener removed');
   };
 }, [processId]);
 
 return (
   <>
     <PdfContainer processId={processId } pdfModule={receivedData} />
   </>
 );
};

export default PdfPage;