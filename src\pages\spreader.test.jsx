import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import "@testing-library/jest-dom";
import Spreader from "./spreader";

jest.mock("./spreader-history/spreader-history", () => () => (
  <div>SpreaderHistory Component</div>
));
jest.mock(
  "../components/Search/search-company",
  () =>
    ({ onCompanySelect }) => (
      <div>
        <button onClick={() => onCompanySelect({ name: "Test Company" })}>
          Select Company
        </button>
      </div>
    )
);
jest.mock("../components/evidence-panel/evidence-panel", () => () => (
  <div>EvidencePanel Component</div>
));
jest.mock(
  "../components/cards/financial-document-source/financial-document",
  () => () => <div>FinancialDocument Component</div>
);

describe("Spreader Component", () => {
  test("renders header and buttons", () => {
    render(<Spreader />);
    expect(screen.getByText("Financial Spreader")).toBeInTheDocument();
    expect(screen.getByText("Extract")).toBeInTheDocument();
  });

  test("shows back button and toggles history visibility", () => {
    render(<Spreader />);

    const historyButton = screen.getByTestId("history-button");
    fireEvent.click(historyButton);
    expect(screen.getByTestId("back-button")).toBeInTheDocument();
  });
  test("shows back button and toggles history visibility", () => {
    render(<Spreader />);

    const historyButton = screen.getByTestId("history-button");
    fireEvent.click(historyButton);

    const backButton = screen.getByTestId("back-button");
    expect(backButton).toBeInTheDocument(); // Check if back button is visible

    fireEvent.click(backButton);
    expect(backButton).not.toBeInTheDocument(); // Check if back button is hidden
  });
  test("displays empty state when no company is selected", () => {
    render(<Spreader />);
    expect(screen.getByAltText("No data found")).toBeInTheDocument();
    expect(
      screen.getByText("Select Company to start the extraction process")
    ).toBeInTheDocument();
  });
});
