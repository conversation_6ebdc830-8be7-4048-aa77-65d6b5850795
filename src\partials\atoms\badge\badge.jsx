import React from "react";
import PropTypes from "prop-types";

const Badge = ({ value }) => {
  if (value) {
    return (
      <div className="flex h-5 w-auto min-w-5 items-center rounded-3xl bg-negative-100 px-1.5">
        <div
          data-testid="badge-with-value"
          className="caption-r m-auto text-white"
        >
          {value}
        </div>
      </div>
    );
  } else {
    return (
      <div
        data-testid="badge-with-out-value"
        className="h-1.5 w-1.5 items-center rounded-3xl bg-negative-100"
      />
    );
  }
};

Badge.propTypes = {
  value: PropTypes.number
};

export default Badge;
