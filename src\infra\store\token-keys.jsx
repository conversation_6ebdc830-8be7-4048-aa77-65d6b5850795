import { OidcConfig } from "../../constants/config";
export const getEnvironmentConfig = () => {
    let config = "";
    let envConfig = {};
    const configFinder = window.location.hostname.toLowerCase();
    
    // Determine environment type
    if (!config) {
      if (window.location.pathname.includes("/foliosure/feature")) {
        config = "feature";
      } else if (window.location.href.includes("/foliosure/dev")) {
        config = "dev";
      } else if (configFinder.includes("test.beatapps.net")) {
        config = "test";
      } else if (configFinder.includes("demo.beatfoliosure") ||
                 configFinder.includes("enshi.beatfoliosure") ||
                 configFinder.includes("taabo-ch.beatfoliosure") ||
                 configFinder.includes("bristol.beatfoliosure") ||
                 configFinder.includes("admont.beatfoliosure") ||
                 configFinder.includes("asmt.beatfoliosure") ||
                 configFinder.includes("monmouth.beatfoliosure") ||
                 configFinder.includes("exeter.beatfoliosure") ||
                 configFinder.includes("pizarro.beatfoliosure") ||
                 configFinder.includes("trial.beatfoliosure") ||
                 configFinder.includes("himera.beatfoliosure") ||
                 configFinder.includes("himera-staging.beatfoliosure")) {
        config = "prod";
      } else if (configFinder.includes("uat")) {
        config = "uat";
      } else if (configFinder.includes("security.beatapps.net")) {
        config = "security";
      } else if (configFinder.includes("perf-test01")) {
        config = "perf1";
      } else if (configFinder.includes("perf-test02")) {
        config = "perf2";
      } else if (window.location.href.includes("localhost")) {
        config = "localhost";
      } else {
        config = "localhost"; // Default
      }
    }
  
    // Handle specific client configurations
    if (config === "prod") {
      if (configFinder.includes("demo.beatfoliosure")) {
        const clientConfig = OidcConfig[config].filter(x => x.clientName.toLowerCase() === "demo");
        envConfig = clientConfig[0];
      } else if (configFinder.includes("trial.beatfoliosure")) {
        const clientConfig = OidcConfig[config].filter(x => x.clientName.toLowerCase() === "trial");
        envConfig = clientConfig[0];
      } else if (configFinder.includes("taabo-ch.beatfoliosure")) {
        const clientConfig = OidcConfig[config].filter(x => x.clientName.toLowerCase() === "taabo-ch");
        envConfig = clientConfig[0];
      } else if (configFinder.includes("bristol.beatfoliosure")) {
        const clientConfig = OidcConfig[config].filter(x => x.clientName.toLowerCase() === "bristol");
        envConfig = clientConfig[0];
      } else if (configFinder.includes("admont.beatfoliosure")) {
        const clientConfig = OidcConfig[config].filter(x => x.clientName.toLowerCase() === "admont");
        envConfig = clientConfig[0];
      } else if (configFinder.includes("asmt.beatfoliosure")) {
        const clientConfig = OidcConfig[config].filter(x => x.clientName.toLowerCase() === "asmt");
        envConfig = clientConfig[0];
      } else if (configFinder.includes("monmouth.beatfoliosure")) {
        const clientConfig = OidcConfig[config].filter(x => x.clientName.toLowerCase() === "monmouth");
        envConfig = clientConfig[0];
      } else if (configFinder.includes("exeter.beatfoliosure")) {
        const clientConfig = OidcConfig[config].filter(x => x.clientName.toLowerCase() === "exeter");
        envConfig = clientConfig[0];
      } else if (configFinder.includes("pizarro.beatfoliosure")) {
        const clientConfig = OidcConfig[config].filter(x => x.clientName.toLowerCase() === "pizarro");
        envConfig = clientConfig[0];
      } else if (configFinder.includes("himera.beatfoliosure") || configFinder.includes("himera-staging.beatfoliosure")) {
        const clientConfig = OidcConfig[config].filter(x => x.clientName.toLowerCase() === "himera");
        envConfig = clientConfig[0];
      }
    } else if (config === "uat") {
      const clientConfig = OidcConfig[config];
      if (window.location.pathname.includes("security")) {
        const result = clientConfig.filter(x => x.clientName.toLowerCase() === "security");
        envConfig = result[0];
      } else if (window.location.pathname.includes("pod-b")) {
        const result = clientConfig.filter(x => x.clientName.toLowerCase() === "pod-b");
        envConfig = result[0];
      } else {
        const result = clientConfig.filter(x => x.clientName.toLowerCase() === "pod-a");
        envConfig = result[0];
      }
    } else if (config === "test") {
      const clientConfig = OidcConfig[config];
      if (window.location.pathname.includes("pod-b")) {
        const result = clientConfig.filter(x => x.clientName.toLowerCase() === "pod-b");
        envConfig = result[0];
      } else {
        const result = clientConfig.filter(x => x.clientName.toLowerCase() === "pod");
        envConfig = result[0];
      }
    } else if (config === "security" && configFinder.includes("security.beatapps.net")) {
      const clientConfig = OidcConfig[config];
      const result = clientConfig.filter(x => x.clientName.toLowerCase() === "pod");
      envConfig = result[0];
    } else {
      envConfig = OidcConfig[config];
    }
  
    const environment = `${OidcConfig.applicationName}_${config}_${envConfig.clientName}`;
    
    return envConfig;
  };
