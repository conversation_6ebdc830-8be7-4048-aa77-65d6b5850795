import React from "react";

function ImageNoResults() {
  return (
    <svg
      width="206"
      height="154"
      viewBox="0 0 206 154"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M107.268 47.6738C107.268 47.6738 66.2674 28.001 59.0614 26.873C51.8554 25.745 27.8476 52.6855 27.8476 52.6855C27.8476 52.6855 13.8865 66.1238 16.9355 97.6738C19.9845 129.224 49.0195 112.123 49.0195 112.123C49.0195 112.123 69.4624 140.592 89.7284 147.285C109.994 153.978 115.007 137.907 135.281 132.086C155.555 126.265 152.128 128.886 170.822 124.014C189.516 119.142 195.475 96.2498 185.987 82.1718C176.499 68.0938 202.981 16.5289 166.487 6.75192C129.993 -3.02508 107.268 47.6738 107.268 47.6738Z"
        fill="url(#paint0_linear_5227_1268)"
      />
      <g filter="url(#filter0_d_5227_1268)">
        <path
          d="M171.68 16.1123H32.531C30.3219 16.1123 28.531 17.9032 28.531 20.1123V129.831C28.531 132.04 30.3219 133.831 32.531 133.831H171.68C173.889 133.831 175.68 132.04 175.68 129.831V20.1123C175.68 17.9032 173.889 16.1123 171.68 16.1123Z"
          fill="white"
        />
        <path
          d="M171.68 16.6123H32.531C30.598 16.6123 29.031 18.1793 29.031 20.1123V129.831C29.031 131.764 30.598 133.331 32.531 133.331H171.68C173.613 133.331 175.18 131.764 175.18 129.831V20.1123C175.18 18.1793 173.613 16.6123 171.68 16.6123Z"
          stroke="#B3B3B3"
        />
      </g>
      <path
        d="M37.7271 25.3089C38.7427 25.3089 39.5661 24.4855 39.5661 23.4699C39.5661 22.4542 38.7427 21.6309 37.7271 21.6309C36.7114 21.6309 35.8881 22.4542 35.8881 23.4699C35.8881 24.4855 36.7114 25.3089 37.7271 25.3089Z"
        fill="#4061C7"
      />
      <path
        d="M45.085 25.3089C46.1006 25.3089 46.924 24.4855 46.924 23.4699C46.924 22.4542 46.1006 21.6309 45.085 21.6309C44.0693 21.6309 43.246 22.4542 43.246 23.4699C43.246 24.4855 44.0693 25.3089 45.085 25.3089Z"
        fill="#4061C7"
      />
      <path
        d="M52.442 25.3089C53.4577 25.3089 54.281 24.4855 54.281 23.4699C54.281 22.4542 53.4577 21.6309 52.442 21.6309C51.4264 21.6309 50.603 22.4542 50.603 23.4699C50.603 24.4855 51.4264 25.3089 52.442 25.3089Z"
        fill="#4061C7"
      />
      <path
        d="M165.804 36.0186H60.479C59.3744 36.0186 58.479 36.914 58.479 38.0186V45.0546C58.479 46.1591 59.3744 47.0546 60.479 47.0546H165.804C166.909 47.0546 167.804 46.1591 167.804 45.0546V38.0186C167.804 36.914 166.909 36.0186 165.804 36.0186Z"
        fill="#ECECEC"
      />
      <path
        d="M71.675 57.252H46.085C45.5327 57.252 45.085 57.6997 45.085 58.252V72.806C45.085 73.3582 45.5327 73.806 46.085 73.806H71.675C72.2272 73.806 72.675 73.3582 72.675 72.806V58.252C72.675 57.6997 72.2272 57.252 71.675 57.252Z"
        fill="#E6E6E6"
      />
      <path
        d="M88.23 82.3291H55.282C54.7297 82.3291 54.282 82.7768 54.282 83.3291V97.8831C54.282 98.4354 54.7297 98.8831 55.282 98.8831H88.23C88.7823 98.8831 89.23 98.4354 89.23 97.8831V83.3291C89.23 82.7768 88.7823 82.3291 88.23 82.3291Z"
        fill="url(#paint1_linear_5227_1268)"
      />
      <path
        d="M109.499 104.402H75.515C74.9627 104.402 74.515 104.85 74.515 105.402V119.956C74.515 120.509 74.9627 120.956 75.515 120.956H109.499C110.051 120.956 110.499 120.509 110.499 119.956V105.402C110.499 104.85 110.051 104.402 109.499 104.402Z"
        fill="#E6E6E6"
      />
      <path
        d="M116.82 59.0918H78.194C77.6859 59.0918 77.274 59.5035 77.274 60.0113C77.274 60.5191 77.6859 60.9308 78.194 60.9308H116.82C117.328 60.9308 117.74 60.5191 117.74 60.0113C117.74 59.5035 117.328 59.0918 116.82 59.0918Z"
        fill="#E6E6E6"
      />
      <path
        d="M134.294 86.0078H95.668C95.1599 86.0078 94.748 86.4195 94.748 86.9273C94.748 87.4351 95.1599 87.8468 95.668 87.8468H134.294C134.802 87.8468 135.214 87.4351 135.214 86.9273C135.214 86.4195 134.802 86.0078 134.294 86.0078Z"
        fill="#4061C7"
      />
      <path
        d="M154.724 108.08H116.098C115.59 108.08 115.178 108.492 115.178 109C115.178 109.507 115.59 109.919 116.098 109.919H154.724C155.232 109.919 155.644 109.507 155.644 109C155.644 108.492 155.232 108.08 154.724 108.08Z"
        fill="#E6E6E6"
      />
      <path
        d="M116.82 62.7695H78.194C77.6859 62.7695 77.274 63.1812 77.274 63.689C77.274 64.1969 77.6859 64.6085 78.194 64.6085H116.82C117.328 64.6085 117.74 64.1969 117.74 63.689C117.74 63.1812 117.328 62.7695 116.82 62.7695Z"
        fill="#E6E6E6"
      />
      <path
        d="M134.294 89.6865H95.668C95.1599 89.6865 94.748 90.0982 94.748 90.606C94.748 91.1138 95.1599 91.5255 95.668 91.5255H134.294C134.802 91.5255 135.214 91.1138 135.214 90.606C135.214 90.0982 134.802 89.6865 134.294 89.6865Z"
        fill="#4061C7"
      />
      <path
        d="M154.724 111.759H116.098C115.59 111.759 115.178 112.17 115.178 112.678C115.178 113.186 115.59 113.598 116.098 113.598H154.724C155.232 113.598 155.644 113.186 155.644 112.678C155.644 112.17 155.232 111.759 154.724 111.759Z"
        fill="#E6E6E6"
      />
      <path
        d="M94.748 66.4492H78.194C77.6859 66.4492 77.274 66.8609 77.274 67.3687C77.274 67.8765 77.6859 68.2882 78.194 68.2882H94.748C95.2562 68.2882 95.668 67.8765 95.668 67.3687C95.668 66.8609 95.2562 66.4492 94.748 66.4492Z"
        fill="#E6E6E6"
      />
      <path
        d="M112.222 93.3662H95.668C95.1599 93.3662 94.748 93.7779 94.748 94.2857C94.748 94.7935 95.1599 95.2052 95.668 95.2052H112.222C112.73 95.2052 113.142 94.7935 113.142 94.2857C113.142 93.7779 112.73 93.3662 112.222 93.3662Z"
        fill="#4061C7"
      />
      <path
        d="M132.652 115.438H116.098C115.59 115.438 115.178 115.849 115.178 116.357C115.178 116.865 115.59 117.276 116.098 117.276H132.652C133.16 117.276 133.572 116.865 133.572 116.357C133.572 115.849 133.16 115.438 132.652 115.438Z"
        fill="#E6E6E6"
      />
      <path
        d="M179.527 142.862L155.898 117.433L151.398 121.705L174.934 147.249C175.545 147.716 176.293 147.965 177.064 147.958C177.836 147.951 178.589 147.689 179.211 147.211L179.48 146.963C179.875 146.349 180.09 145.635 180.099 144.907C180.107 144.179 179.909 143.469 179.527 142.862Z"
        fill="url(#paint2_linear_5227_1268)"
      />
      <path
        d="M150.549 123.817L155.283 129.017C155.341 129.08 155.41 129.131 155.488 129.167C155.565 129.202 155.649 129.222 155.734 129.224C155.819 129.226 155.905 129.211 155.985 129.18C156.065 129.149 156.139 129.102 156.202 129.042L162.098 123.446C162.161 123.386 162.212 123.314 162.249 123.235C162.285 123.155 162.306 123.069 162.31 122.982C162.314 122.895 162.301 122.809 162.272 122.727C162.243 122.646 162.198 122.572 162.141 122.508L157.406 117.309L150.549 123.817Z"
        fill="#4061C7"
      />
      <g filter="url(#filter1_d_5227_1268)">
        <path
          d="M132.631 130.601C128.828 130.504 125.195 129.628 121.832 127.999C118.584 126.425 115.723 124.217 113.329 121.438C110.935 118.658 109.128 115.446 107.96 111.892C106.75 108.213 106.28 104.325 106.564 100.336C106.847 96.3477 107.867 92.498 109.594 88.8938C111.263 85.4124 113.518 82.3051 116.298 79.6583C119.077 77.0114 122.241 74.9586 125.701 73.5568C129.282 72.1055 133.026 71.4192 136.829 71.5168C140.631 71.6144 144.265 72.49 147.627 74.1195C150.875 75.6934 153.736 77.9009 156.13 80.6805C158.524 83.4602 160.331 86.6717 161.5 90.226C162.709 93.9055 163.179 97.7935 162.896 101.782C162.612 105.77 161.593 109.62 159.865 113.224C158.197 116.706 155.941 119.813 153.162 122.46C150.382 125.107 147.218 127.16 143.759 128.561C140.177 130.013 136.433 130.699 132.631 130.601ZM136.46 76.712C133.326 76.6316 130.241 77.1973 127.288 78.3936C124.437 79.549 121.83 81.2407 119.539 83.4219C117.249 85.603 115.39 88.1638 114.015 91.0331C112.591 94.004 111.75 97.1768 111.517 100.463C111.283 103.75 111.671 106.954 112.668 109.987C113.631 112.917 115.12 115.563 117.092 117.854C119.065 120.144 121.423 121.964 124.1 123.261C126.872 124.604 129.866 125.326 133 125.406C136.133 125.487 139.219 124.921 142.171 123.725C145.023 122.569 147.63 120.877 149.92 118.696C152.211 116.515 154.07 113.954 155.445 111.085C156.869 108.114 157.709 104.941 157.943 101.655C158.176 98.3682 157.789 95.1639 156.792 92.1309C155.828 89.2016 154.34 86.5549 152.367 84.2644C150.394 81.9738 148.037 80.1546 145.359 78.8574C142.588 77.5142 139.593 76.7924 136.46 76.712Z"
          fill="white"
        />
        <path
          d="M136.793 72.0243C133.055 71.9284 129.375 72.6029 125.856 74.0291C122.456 75.4068 119.346 77.4244 116.614 80.026C113.882 82.6276 111.665 85.6815 110.025 89.1031C108.328 92.645 107.326 96.4286 107.047 100.349C106.769 104.269 107.23 108.09 108.419 111.706C109.568 115.199 111.343 118.356 113.696 121.088C116.049 123.82 118.861 125.99 122.054 127.536C125.358 129.138 128.929 129.998 132.667 130.094C136.404 130.19 140.084 129.516 143.603 128.089C147.004 126.712 150.113 124.694 152.845 122.093C155.577 119.491 157.794 116.437 159.434 113.015C161.131 109.474 162.133 105.69 162.412 101.77C162.691 97.8495 162.229 94.0283 161.04 90.4124C159.891 86.9192 158.116 83.7628 155.763 81.0307C153.41 78.2986 150.598 76.129 147.406 74.5821C144.101 72.9808 140.53 72.1203 136.793 72.0243ZM132.964 125.914C129.765 125.832 126.708 125.095 123.878 123.723C121.146 122.399 118.739 120.542 116.725 118.204C114.711 115.866 113.192 113.164 112.208 110.174C111.19 107.077 110.795 103.806 111.033 100.451C111.272 97.0963 112.13 93.8573 113.583 90.8242C114.987 87.8951 116.884 85.281 119.223 83.0546C121.561 80.8281 124.222 79.1012 127.133 77.9217C130.147 76.7004 133.297 76.1228 136.496 76.2049C139.694 76.2869 142.751 77.0239 145.581 78.3952C148.314 79.7195 150.721 81.5765 152.734 83.9146C154.748 86.2527 156.268 88.9545 157.251 91.9449C158.269 95.0414 158.665 98.3126 158.426 101.667C158.188 105.022 157.33 108.261 155.876 111.294C154.472 114.224 152.575 116.838 150.237 119.064C147.899 121.29 145.237 123.017 142.326 124.197C139.312 125.418 136.162 125.996 132.964 125.914ZM136.865 71.0097C152.688 71.4157 164.559 85.1983 163.379 101.795C162.2 118.391 148.417 131.515 132.594 131.109C116.771 130.703 104.901 116.92 106.08 100.324C107.259 83.7277 121.042 70.6036 136.865 71.0097ZM133.036 124.899C145.589 125.221 156.523 114.809 157.459 101.643C158.394 88.4764 148.977 77.5417 136.424 77.2195C123.871 76.8974 112.936 87.3097 112.001 100.476C111.065 113.642 120.483 124.577 133.036 124.899Z"
          fill="#B3B3B3"
        />
      </g>
      <path
        opacity="0.8"
        d="M134.061 124.114C146.882 124.456 157.762 114.129 158.362 101.049C158.961 87.9695 149.053 77.0897 136.232 76.7486C123.41 76.4074 112.531 86.7341 111.931 99.8138C111.332 112.894 121.24 123.773 134.061 124.114Z"
        fill="#F0F0F0"
      />
      <path
        d="M120.144 84.3055C116.627 87.6433 114.451 91.0834 115.28 91.9926C116.108 92.9017 119.628 90.9345 123.143 87.5977C126.659 84.2609 128.836 80.8198 128.009 79.9107C127.181 79.0015 123.661 80.9677 120.144 84.3055Z"
        fill="white"
      />
      <path d="M29.531 29.8271H175.219" stroke="#E5E5E5" />
      <path d="M51.531 29.8271V134.129" stroke="#E5E5E5" />
      <defs>
        <filter
          id="filter0_d_5227_1268"
          x="22.531"
          y="11.1123"
          width="159.149"
          height="129.719"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="1" />
          <feGaussianBlur stdDeviation="3" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.18 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_5227_1268"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_5227_1268"
            result="shape"
          />
        </filter>
        <filter
          id="filter1_d_5227_1268"
          x="99.9964"
          y="65.9987"
          width="69.4664"
          height="72.1226"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="1.00045" />
          <feGaussianBlur stdDeviation="3.00135" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.18 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_5227_1268"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_5227_1268"
            result="shape"
          />
        </filter>
        <linearGradient
          id="paint0_linear_5227_1268"
          x1="103.47"
          y1="5.5"
          x2="103.47"
          y2="148.887"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#FFF0FC" />
          <stop offset="1" stop-color="#CED4F9" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_5227_1268"
          x1="58.336"
          y1="94.8108"
          x2="73.0207"
          y2="67.6107"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#EDCF28" />
          <stop offset="1" stop-color="#DEA509" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_5227_1268"
          x1="158.543"
          y1="128.829"
          x2="181.002"
          y2="144.207"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#EDCF28" />
          <stop offset="1" stop-color="#DEA509" />
        </linearGradient>
      </defs>
    </svg>
  );
}

export default React.memo(ImageNoResults);
