import React from "react";
import { render, screen } from "@testing-library/react";
import SpinnerCircle from "./spinner-circle";

describe("SpinnerCircle Component", () => {
  test("renders the spinner with loading text when provided", () => {
    render(<SpinnerCircle Loading="Loading Data" />);
    
    // Check if loading text is displayed
    const textElement = screen.getByText("Loading Data");
    expect(textElement).toBeInTheDocument();
    expect(textElement.parentElement).toHaveClass("mt-2");
  });
  
  test("renders the spinner without loading text when not provided", () => {
    const { container } = render(<SpinnerCircle />);
    
    // Check if spinner is rendered
    const spinnerElement = container.querySelector(".nep-spin-ring");
    expect(spinnerElement).toBeInTheDocument();
    
    // Check that no loading text is displayed
    const loadingTextContainer = container.querySelector(".mt-2");
    expect(loadingTextContainer).not.toBeInTheDocument();
  });
  
  test("renders with proper container styling", () => {
    render(<SpinnerCircle />);
    
    // Check if container has proper styling
    const container = screen.getByTestId("spinner-circle");
    expect(container).toHaveClass("nep-modal", "nep-modal-show");
    expect(container).toHaveStyle({
      display: "block",
      background: "rgba(0, 0, 0, 0.25)"
    });
    
    // Check if mask element exists
    const mask = container.querySelector(".nep-modal-mask");
    expect(mask).toBeInTheDocument();
    
    // Check if loader body exists with proper styling
    const loaderBody = container.querySelector(".loader-body");
    expect(loaderBody).toBeInTheDocument();
    expect(loaderBody).toHaveStyle({
      display: "inline-flex"
    });
  });
});
