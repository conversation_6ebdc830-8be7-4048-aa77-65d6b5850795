import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import CheckBox from "./check-box";

describe("CheckBox Component", () => {
  const testId = "check-box";
  const onChange = jest.fn();

  it("should render <PERSON><PERSON><PERSON> with checked", () => {
    render(
      <CheckBox
        checked={true}
        onChange={onChange}
        testId={testId}
        disabled={false}
        indeterminate={false}
      />
    );
    const checkbox = screen.getByTestId(testId);
    expect(checkbox).toBeInTheDocument();
    expect(checkbox).toBeChecked();
  });

  it("should render CheckBox without checked", () => {
    render(
      <CheckBox
        checked={false}
        onChange={onChange}
        testId={testId}
        disabled={false}
        indeterminate={false}
      />
    );
    const checkbox = screen.getByTestId(testId);
    expect(checkbox).toBeInTheDocument();
    expect(checkbox).not.toBeChecked();
  });

  it("should render <PERSON><PERSON><PERSON> with disabled", () => {
    render(
      <CheckBox
        checked={false}
        onChange={onChange}
        testId={testId}
        disabled={true}
        indeterminate={false}
      />
    );
    const checkbox = screen.getByTestId(testId);
    expect(checkbox).toBeInTheDocument();
    expect(checkbox).toBeDisabled();
  });

  it("should render CheckBox with indeterminate", () => {
    render(
      <CheckBox
        checked={false}
        onChange={onChange}
        testId={testId}
        disabled={false}
        indeterminate={true}
      />
    );
    const checkbox = screen.getByTestId(testId);
    expect(checkbox).toBeInTheDocument();
    expect(checkbox.indeterminate).toBe(true);
  });

  it("should call onChange when clicked", () => {
    render(
      <CheckBox
        checked={false}
        onChange={onChange}
        testId={testId}
        disabled={false}
        indeterminate={false}
      />
    );
    const checkbox = screen.getByTestId(testId);
    fireEvent.click(checkbox);
    expect(onChange).toHaveBeenCalled();
  });
});
