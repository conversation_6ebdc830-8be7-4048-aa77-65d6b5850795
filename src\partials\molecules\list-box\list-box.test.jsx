import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import ListBox from "./list-box";

describe("ListBox Component", () => {
  const list = ["Item 1", "Item 2", "Item 3"];
  const selectedList = "Item 1";
  const setSelectedList = jest.fn();
  const setOpen = jest.fn();
  const query = "test";

  test("renders ListBox component", () => {
    render(
      <ListBox
        list={list}
        selectedList={selectedList}
        setSelectedList={setSelectedList}
        setOpen={setOpen}
        query={query}
      />
    );

    expect(screen.getByText(selectedList)).toBeInTheDocument();
  });

  test("opens ListBox options on button click", () => {
    render(
      <ListBox
        list={list}
        selectedList={selectedList}
        setSelectedList={setSelectedList}
        setOpen={setOpen}
        query={query}
      />
    );

    const button = screen.getByText(selectedList);
    fireEvent.click(button);

    expect(setOpen).toHaveBeenCalledWith(false);
  });

  test("selects an item from the ListBox options", () => {
    render(
      <ListBox
        list={list}
        selectedList={selectedList}
        setSelectedList={setSelectedList}
        setOpen={setOpen}
        query={query}
      />
    );

    const button = screen.getByText(selectedList);
    fireEvent.click(button);

    const option = screen.getByText("Item 2");
    fireEvent.click(option);

    expect(setSelectedList).toHaveBeenCalledWith("Item 2");
    expect(setOpen).toHaveBeenCalledWith(true);
  });

  test("applies correct classes when data-open is true", () => {
    render(
      <ListBox
        list={list}
        selectedList={selectedList}
        setSelectedList={setSelectedList}
        setOpen={setOpen}
        query={query}
      />
    );

    const button = screen.getByText(selectedList);
    fireEvent.click(button);

    expect(button).toHaveClass(
      " body-r group relative inline-flex h-8 w-32 items-center rounded px-3 py-2 text-primary-78 hover:bg-primary-35 data-[open]:bg-primary-50 data-[open]:text-primary-90"
    );
    expect(button).toHaveClass(
      "body-r group relative inline-flex h-8 w-32 items-center rounded px-3 py-2 text-primary-78 hover:bg-primary-35 data-[open]:bg-primary-50 data-[open]:text-primary-90"
    );
  });
});
