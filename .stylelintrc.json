{"extends": "stylelint-config-standard", "ignoreFiles": ["src/module.css"], "rules": {"block-closing-brace-newline-after": "always", "color-no-invalid-hex": true, "property-no-unknown": true, "max-empty-lines": 1, "value-keyword-case": "lower", "selector-pseudo-class-no-unknown": [true, {"ignorePseudoClasses": ["global", "parent"]}], "indentation": null, "length-zero-no-unit": null, "font-family-no-duplicate-names": true, "font-family-no-missing-generic-family-keyword": null, "at-rule-no-unknown": true, "block-no-empty": [true, {"ignore": ["comments"]}], "comment-no-empty": true, "declaration-block-no-duplicate-properties": [true, {"ignore": ["consecutive-duplicates-with-different-values"]}], "declaration-block-no-shorthand-property-overrides": true, "function-calc-no-unspaced-operator": true, "function-linear-gradient-no-nonstandard-direction": true, "keyframe-declaration-no-important": true, "media-feature-name-no-unknown": true, "no-descending-specificity": null, "no-duplicate-at-import-rules": true, "no-duplicate-selectors": true, "no-empty-source": true, "no-extra-semicolons": true, "no-invalid-double-slash-comments": true, "selector-pseudo-element-no-unknown": true, "selector-type-no-unknown": true, "string-no-newline": true, "unit-no-unknown": true, "at-rule-name-case": "lower", "at-rule-name-space-after": "always-single-line", "at-rule-semicolon-newline-after": "always", "block-closing-brace-empty-line-before": "never", "block-closing-brace-newline-before": "always-multi-line", "block-closing-brace-space-before": "always-single-line", "block-opening-brace-newline-after": "always-multi-line", "block-opening-brace-space-after": "always-single-line", "block-opening-brace-space-before": "always", "color-hex-case": "lower", "color-hex-length": "short", "comment-empty-line-before": null, "comment-whitespace-inside": "always", "custom-property-empty-line-before": ["always", {"except": ["after-custom-property", "first-nested"], "ignore": ["after-comment", "inside-single-line-block"]}], "declaration-bang-space-after": "never", "declaration-bang-space-before": "always", "declaration-block-semicolon-newline-after": "always-multi-line", "declaration-block-semicolon-space-after": "always-single-line", "declaration-block-semicolon-space-before": "never", "declaration-block-single-line-max-declarations": 1, "declaration-block-trailing-semicolon": "always", "declaration-colon-newline-after": null, "declaration-colon-space-after": "always-single-line", "declaration-colon-space-before": "never", "declaration-empty-line-before": ["always", {"except": ["after-declaration", "first-nested"], "ignore": ["after-comment", "inside-single-line-block"]}], "function-comma-newline-after": "always-multi-line", "function-comma-space-after": "always-single-line", "function-comma-space-before": "never", "function-max-empty-lines": 0, "function-name-case": "lower", "function-parentheses-newline-inside": "always-multi-line", "function-parentheses-space-inside": "never-single-line", "function-whitespace-after": "always", "media-feature-colon-space-after": "always", "media-feature-colon-space-before": "never", "media-feature-name-case": "lower", "media-feature-parentheses-space-inside": "never", "media-feature-range-operator-space-after": "always", "media-feature-range-operator-space-before": "always", "media-query-list-comma-newline-after": "always-multi-line", "media-query-list-comma-space-after": "always-single-line", "media-query-list-comma-space-before": "never", "no-eol-whitespace": null, "no-missing-end-of-source-newline": null, "number-leading-zero": "always", "number-no-trailing-zeros": true, "property-case": "lower", "rule-empty-line-before": null, "selector-attribute-brackets-space-inside": "never", "selector-attribute-operator-space-after": "never", "selector-attribute-operator-space-before": "never", "selector-combinator-space-after": "always", "selector-combinator-space-before": "always", "selector-descendant-combinator-no-non-space": null, "selector-list-comma-newline-after": "always", "selector-list-comma-space-before": "never", "selector-max-empty-lines": 0, "selector-pseudo-class-case": "lower", "selector-pseudo-class-parentheses-space-inside": "never", "selector-pseudo-element-case": "lower", "selector-pseudo-element-colon-notation": "single", "selector-type-case": "lower", "unit-case": "lower", "value-list-comma-newline-after": "always-multi-line", "value-list-comma-space-after": "always-single-line", "value-list-comma-space-before": "never", "value-list-max-empty-lines": 0, "selector-class-pattern": "^[a-zA-Z][a-zA-Z0-9-]+$|^enp-|^mz-"}}