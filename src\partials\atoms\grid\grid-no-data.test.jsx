import React from "react";
import { render } from "@testing-library/react";
import GridNoData from "./grid-no-data";

jest.mock("../../../resources/images/empty-search-image", () => () => (
  <div data-testid="empty-search-image">Mocked EmptySearchImage</div>
));

describe("GridNoData", () => {
  it("renders without crashing", () => {
    const { getByTestId } = render(<GridNoData />);

    // Check if the outer div is rendered
    expect(getByTestId("grid-no-data")).toBeInTheDocument();

    // Check if the inner div is rendered
    expect(getByTestId("grid-no-data-inner")).toBeInTheDocument();

    // Check if the EmptySearchImage component is rendered
    expect(getByTestId("empty-search-image")).toBeInTheDocument();

    // Check if the "No Data found!" text is rendered
    expect(getByTestId("no-data-text")).toHaveTextContent("No Data found!");
  });
});
