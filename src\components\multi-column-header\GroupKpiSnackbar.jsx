import React from "react";
import DeleteIcon from '../../resources/images/delete.svg';

const GroupKpiSnackbar = ({ enabled, count, onClick, onDelete }) => {
  return (
    <div
      className={
        `fixed left-1/2 bottom-8 z-50 -translate-x-1/2 flex items-center px-6 py-3 rounded-lg shadow-lg border  bg-white transition-all duration-200`
      }
    >
      <span
        className="body-m font-medium text-primary-78 cursor-pointer"
        onClick={enabled ? onClick : undefined}
        aria-disabled={!enabled}
        style={{ userSelect: 'none' }}
      >
       Change Time Period
      </span>
      <span className="mx-3 border-l h-6 border-neutral-30" />
      <button
        className="ml-2 p-1 hover:bg-neutral-10 rounded flex items-center"
        onClick={onDelete}
        aria-label="Delete"
        style={{ border: 'none', background: 'none' }}
      >
        {/* Use imported SVG image for trash bin icon */}
        <img src={DeleteIcon} alt="Delete" width={16} height={16} style={{ display: 'block' }} />
      </button>
    </div>
  );
};

export default GroupKpiSnackbar;
