import React, { useEffect, useState, useRef } from 'react';
import { loadPdfByProcessId } from '../../services/pdfLoader';
import PdfCanvasViewer from './PdfViewer';
import PdfThumbnailList from './PdfThumbnails';
import './PdfStyles.css';
import { FaCheckCircle, FaExclamationCircle } from 'react-icons/fa';
import { GET_KPI_CONFIG_SERVICE } from '../../infra/api/company/kpi-service';

const PdfContainer = ({ processId, pdfModule }) => {
  const [pdfDoc, setPdfDoc] = useState(null);
  const [totalPages, setTotalPages] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const bottomHeight = 160;
  const [activeCard, setActiveCard] = useState('income');
  const inputDebouncerRef = useRef(null); // Add debouncer ref for delayed input validation
  const [kpiConfig, setKpiConfig] = useState([]); // Add state for KPI config
  const [isLoading, setIsLoading] = useState(false); // Add loading state for API call
  const [error, setError] = useState(null); // Add error state

  // Track selected pages for each card separately
  const [selectedPagesByCard, setSelectedPagesByCard] = useState({
    income: [],
    balance: [],
    cashflow: []
  });

  // Initialize card values with empty strings
  const [cardValues, setCardValues] = useState({
    income: '',
    balance: '',
    cashflow: ''
  });
  
  // Post message to parent window with updated data
  const postMessageToParent = () => {
    // Create a data object with the current state
    const data = {
      type: 'pdf-data-response',
      modules: [
        {
          moduleId: 7,
          items: cardValues.income,
          pages: selectedPagesByCard.income
        },
        {
          moduleId: 8,
          items: cardValues.balance,
          pages: selectedPagesByCard.balance
        },
        {
          moduleId: 9,
          items: cardValues.cashflow,
          pages: selectedPagesByCard.cashflow
        }
      ]
    };
    
    // Post the message to the parent window
    if (window.parent && window.parent !== window) {
      window.parent.postMessage(data, window.top.location.origin);
    } else {
      window.postMessage(data, window.top.location.origin);
    }
  };
  
  // Update card values when pdfModule changes
  useEffect(() => {
    if (pdfModule && pdfModule.modules && Array.isArray(pdfModule.modules)) {
      const income = pdfModule.modules.find(m => m.moduleId == 7);
      const balance = pdfModule.modules.find(m => m.moduleId == 8);
      const cashflow = pdfModule.modules.find(m => m.moduleId == 9);
      
      
      // Set card values
      setCardValues({
        income: income?.items || '',
        balance: balance?.items || '',
        cashflow: cashflow?.items || '',
      });
      
      // Parse page numbers for each card type
      const incomePages = income?.items ? parsePageNumbers(income.items) : [];
      const balancePages = balance?.items ? parsePageNumbers(balance.items) : [];
      const cashflowPages = cashflow?.items ? parsePageNumbers(cashflow.items) : [];
      
      // Update the selectedPagesByCard with all values
      setSelectedPagesByCard({
        income: incomePages,
        balance: balancePages,
        cashflow: cashflowPages
      });
      
      // Update the thumbnail selection for the active card
      if (onUpdateThumbnailSelection) {
        if (activeCard === 'income') {
          onUpdateThumbnailSelection(incomePages);
        } else if (activeCard === 'balance') {
          onUpdateThumbnailSelection(balancePages);
        } else if (activeCard === 'cashflow') {
          onUpdateThumbnailSelection(cashflowPages);
        }
      }
    }
  }, [pdfModule, totalPages]);
  
  // Helper function to parse page numbers from comma-separated string
  const parsePageNumbers = (pagesText) => {
    if (!pagesText || pagesText.trim() === '') return [];
    
    const cleanedValue = pagesText.replace(/^,+|,+$/g, '');
    const pageNumbers = cleanedValue.split(',')
      .map(num => num.trim())
      .filter(num => num !== '')
      .map(num => parseInt(num, 10))
      .filter(num => !isNaN(num) && num > 0 && num <= totalPages);
      
    return [...new Set(pageNumbers)].sort((a, b) => a - b);
  };

  useEffect(() => {
    const loadDocument = async () => {
      try {
        setIsLoading(true);
        let doc;

        // Check if processId is provided
        if (processId) {
          // Load PDF by processId through API
          doc = await loadPdfByProcessId(processId);
        } else {
          throw new Error('Either fileUrl or processId must be provided');
        }

        setPdfDoc(doc);
        setTotalPages(doc.numPages);
        setIsLoading(false);
      } catch (err) {
        console.error('Error loading PDF:', err);
        setError('Failed to load PDF. Please try again.');
        setIsLoading(false);
      }
    };

    loadDocument();
  }, [processId]);

  // Fetch KPI configuration data when component mounts
  useEffect(() => {
    const fetchKpiConfig = async () => {
      try {
        setIsLoading(true);
        const config = await GET_KPI_CONFIG_SERVICE();
        setKpiConfig(config);
        setIsLoading(false);
      } catch (error) {
        console.error("Error fetching KPI config:", error);
        setIsLoading(false);
      }
    };
    
    fetchKpiConfig();
  }, []);

  // Reference to the callback function for updating thumbnail selection
  const [onUpdateThumbnailSelection, setOnUpdateThumbnailSelection] = useState(null);

  // Callback registration function to be passed to the thumbnail component
  const registerThumbnailSelectionCallback = (callback) => {
    setOnUpdateThumbnailSelection(() => callback);
  };

  // Effect to update thumbnails when active card changes
  useEffect(() => {
    if (onUpdateThumbnailSelection) {
      onUpdateThumbnailSelection(selectedPagesByCard[activeCard] || []);
      
      // If this card has values but no selected pages, parse them from the cardValues
      if (cardValues[activeCard] && !selectedPagesByCard[activeCard].length) {
        const pageNumbers = parsePageNumbers(cardValues[activeCard]);
        if (pageNumbers.length > 0) {
          updateThumbnailSelectionFromInput(pageNumbers);
        }
      }
    }
  }, [activeCard, onUpdateThumbnailSelection]);

  // This function is used by both components to keep them in sync
  const handlePageChange = (pageNumber) => {
    setCurrentPage(pageNumber);
  };

  // Handle selected pages from thumbnails
  const handlePagesSelected = (pages) => {
    // Only update the currently active card's selected pages
    setSelectedPagesByCard(prev => ({
      ...prev,
      [activeCard]: pages
    }));

    // Update the active card's value with comma-separated page numbers
    if (pages.length > 0) {
      const pagesText = pages.join(',');
      setCardValues(prev => ({
        ...prev,
        [activeCard]: pagesText
      }));
    } else {
      setCardValues(prev => ({
        ...prev,
        [activeCard]: ''
      }));
    }
  };

  // Input validation for numeric values with commas and update thumbnails
  const handleInputChange = (e, cardType) => {
    const value = e.target.value;

    // Only check for consecutive commas
    if (/,{2,}/.test(value)) {
      return;
    }

    // Allow only digits and commas - this was restricting input to only one digit 
    // due to the 0 check, let's fix that
    if (/^[0-9,]*$/.test(value)) {
      // Simply allow the input - we'll validate page numbers separately
      setCardValues(prev => ({
        ...prev,
        [cardType]: value
      }));

      // Parse the comma-separated string into an array of page numbers
      if (value.trim() === '') {
        // If the input is empty, clear the selection
        updateThumbnailSelectionFromInput([]);
      } else {
        try {
          // Clean the input by removing leading/trailing commas before processing
          const cleanedValue = value.replace(/^,+|,+$/g, '');

          // Parse the input string into an array of numbers
          const pageNumbers = cleanedValue.split(',')
            .map(num => num.trim())
            .filter(num => num !== '')
            .map(num => parseInt(num, 10))
            .filter(num => !isNaN(num) && num > 0 && num <= totalPages);

          // Remove duplicates and sort
          const uniquePageNumbers = [...new Set(pageNumbers)].sort((a, b) => a - b);

          // Update the selected pages in the thumbnails
          updateThumbnailSelectionFromInput(uniquePageNumbers);

          // Navigate to the first valid page if there are any valid pages
          if (uniquePageNumbers.length > 0) {
            // Use the most recently added page number as the target page to navigate to
            const lastCommaIndex = value.lastIndexOf(',');
            let targetPage;
            
            if (lastCommaIndex !== -1 && lastCommaIndex < value.length - 1) {
              // If there's text after the last comma, try to use that as the target page
              const lastEnteredValue = value.substring(lastCommaIndex + 1).trim();
              const parsedPage = parseInt(lastEnteredValue, 10);
              
              if (!isNaN(parsedPage) && parsedPage > 0 && parsedPage <= totalPages) {
                targetPage = parsedPage;
              } else {
                // Fall back to the first valid page in the list
                targetPage = uniquePageNumbers[0];
              }
            } else {
              // If there's no comma or it's at the end, use the first valid page
              targetPage = uniquePageNumbers[0];
            }
            
            // Update the current page to navigate to the selected page
            setCurrentPage(targetPage);
          }

          // Only update the text box with valid page numbers if there's a difference
          // and the user has finished typing (add slight delay)
          if (uniquePageNumbers.length !== cleanedValue.split(',').filter(n => n !== '').length) {
            // Use a debounced update to avoid disrupting typing
            clearTimeout(inputDebouncerRef.current);
            inputDebouncerRef.current = setTimeout(() => {
              const validPagesText = uniquePageNumbers.join(',');
              setCardValues(prev => ({
                ...prev,
                [cardType]: validPagesText
              }));
            }, 800); // Wait for user to finish typing
          }
        } catch (error) {
          console.error('Error parsing page numbers:', error);
        }
      }
    }
  };

  // Handle keyPress to prevent typing of invalid characters or patterns
  const handleKeyPress = (e) => {
    const value = e.target.value;
    const key = e.key;

    // Block non-numeric and non-comma keys
    if (!/[0-9,]/.test(key)) {
      e.preventDefault();
      return;
    }

    // If key is comma, only prevent consecutive commas
    if (key === ',') {
      const cursorPosition = e.target.selectionStart;

      // Prevent consecutive commas
      if (cursorPosition > 0 && value.charAt(cursorPosition - 1) === ',') {
        e.preventDefault();
        return;
      }

      // Also prevent comma if character after cursor is a comma
      if (cursorPosition < value.length && value.charAt(cursorPosition) === ',') {
        e.preventDefault();
        return;
      }
    }
  };

  // Helper function to clean input from multiple consecutive commas
  const cleanInputValue = (value) => {
    // Replace any sequence of multiple commas with a single comma
    return value.replace(/,{2,}/g, ',')
      // Remove leading commas
      .replace(/^,+/, '')
      // Remove trailing commas
      .replace(/,+$/, '');
  };

  // Update the selected pages in selectedPagesByCard state and thumbnails
  const updateThumbnailSelectionFromInput = (pageNumbers) => {
    // Update the current card's selected pages
    setSelectedPagesByCard(prev => ({
      ...prev,
      [activeCard]: pageNumbers
    }));

    // Update the thumbnail selection if callback is available
    if (onUpdateThumbnailSelection) {
      onUpdateThumbnailSelection(pageNumbers);
    }
  };

  const incomeStatementRef = useRef(null);

  // Focus on the first text box when component mounts
  useEffect(() => {
    if (incomeStatementRef.current) {
      incomeStatementRef.current.focus();
    }
  }, [pdfDoc]); // Re-focus when PDF document loads

  // Set active card and reset thumbnail selection
  const handleCardClick = (cardType) => {
    if (activeCard === cardType) return; // No change if clicking the same card

    setActiveCard(cardType);

    // When switching cards, update the thumbnail selection to match the new card's saved selection
    if (onUpdateThumbnailSelection) {
      onUpdateThumbnailSelection(selectedPagesByCard[cardType] || []);
    }
  };

  // Handle save button click - explicitly trigger postMessageToParent
  const handleSave = () => {
    postMessageToParent();
  };

  // Use KPI config to create cards or use default if not loaded yet
  const cards = kpiConfig.length > 0 ? [
    { type: 'income', title: 'Income Statement', name: kpiConfig.find(c => c.moduleName === "ProfitAndLoss")?.pageConfigAliasName || "ProfitAndLoss" },
    { type: 'balance', title: 'Balance Sheet', name: kpiConfig.find(c => c.moduleName === "BalanceSheet")?.pageConfigAliasName || "BalanceSheet" },
    { type: 'cashflow', title: 'Cash Flow Statement', name: kpiConfig.find(c => c.moduleName === "CashFlow")?.pageConfigAliasName || "CashFlow" }
  ] : [
    { type: 'income', title: 'Income Statement', name: "ProfitAndLoss" },
    { type: 'balance', title: 'Balance Sheet', name: "BalanceSheet" },
    { type: 'cashflow', title: 'Cash Flow Statement', name: "CashFlow" }
  ];

  // Helper function to determine which icon to display
  const getCardIcon = (cardType) => {
    const hasValues = cardValues[cardType] && cardValues[cardType].trim() !== '';

    if (hasValues) {
      return <FaCheckCircle
        className={`inline-block mr-2 ${activeCard === cardType ? 'text-positive-100' : 'text-positive-100'}`} size={16}
      />;
    } else {
      return <FaExclamationCircle
        className={`inline-block mr-2 ${activeCard === cardType ? 'text-blue-clear-100' : 'text-blue-clear-100'}`} size={16}
      />;
    }
  };

  return (
    <div className="flex flex-col h-screen w-full overflow-hidden px-[20px]">
      <div className="flex flex-1 h-[calc(100vh-115px)] overflow-hidden ">
          <div className="w-[25%] h-full overflow-y-auto">
            <PdfThumbnailList
              pdfDoc={pdfDoc}
              totalPages={totalPages}
              currentPage={currentPage}
              onSelectPage={handlePageChange}
              onPagesSelected={handlePagesSelected}
              onRegisterSelectionCallback={registerThumbnailSelectionCallback}
              initialSelectedPages={selectedPagesByCard[activeCard] || []}
              bottomHeight={bottomHeight}
            />
          </div>
            <div className="w-[75%] h-full overflow-y-auto">
              <PdfCanvasViewer
                pdfDoc={pdfDoc}
                totalPages={totalPages}
                currentPage={currentPage}
                onPageChange={handlePageChange}
                bottomHeight={bottomHeight}
                onSave={handleSave}
              />
            </div>
                </div>

                {/* Fixed bottom row with financial statement cards */}
      <div className="border-t border-neutral-10 py-3 footer-s">
        <div className="flex flex-wrap h-full gap-3">
          {cards.map((card) => (
            <div
              key={card.type}
              className={`flex-1 min-w-[120px] w-[120px] rounded-lg transition-all duration-200 p-3 overflow-hidden cursor-pointer 
                ${activeCard === card.type
                  ? 'bg-primary-40 rounded-lg transform scale-[1.02]'
                  : 'bg-zinc-50 rounded-lg hover:bg-primary-40'
                }`}
              onClick={() => handleCardClick(card.type)}
            >
              <div className="flex flex-row">
                <div className='w-full'>                
                  <div className={`s-r pb-1 text-[#2B2B33] ${activeCard === card.type ? 'text-blue-700' : 'text-gray-700'}`}>
                  {getCardIcon(card.type)}
                  {card.name}
                </div>
                <div>
                <input
                  ref={card.type === 'income' ? incomeStatementRef : null}
                  type="text"
                  value={cardValues[card.type]}
                  className={`w-full border p-2
                  ${activeCard === card.type
                      ? 'border-blue-300 focus:border-blue-500 bg-white'
                      : 'border-gray-300 bg-gray-50 focus:border-gray-400'
                    }`}
                  placeholder="Page No"
                  pattern="[0-9,]+"
                  onChange={(e) => handleInputChange(e, card.type)}
                  onKeyPress={handleKeyPress}
                />
                </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default PdfContainer;