import { ComboBox } from "@progress/kendo-react-dropdowns";
import React, { cloneElement, useEffect, useState } from "react";
import PropTypes from "prop-types";
import { filterBy } from "@progress/kendo-data-query";
import { highlightText } from "../../utils/text-utils";

const NotesDropDown = ({
  isMapped,
  showDropdown,
  setShowDropdown,
  allCoa,
  searchQuery = "",
  selectedValue = ""
}) => {
  const [data, setData] = useState(allCoa);
  const renderDropdown = () => {
    setShowDropdown(!showDropdown);
  };
  const [selected, setSelected] = useState(null);

  const handleChange = (e) => {
    setSelected(e.value);
    renderDropdown();
  };

  return (
    <ComboBox
      disabled={isMapped}
      clearButton={false}
      filterable={true}
      value={selected}
      onFilterChange={(e) => {
        setData(filterBy(allCoa.slice(), e.filter));
      }}
      placeholder={"Select the line item to map"}
      data={data}
      textField="lineitem"
      groupField="statement"
      itemRender={(li, liProps) => {
        const isSelected = liProps.selected;
        const itemChildren = liProps.dataItem ? (
          <div
            className={`box-border flex flex-row cursor-pointer items-center px-4 py-2 gap-2 w-full h-8 border-(--neutral-gray-10) flex-none self-stretch z-[1] ${
              isSelected ? "bg-[#4061c7]" : " bg-white hover:bg-[#F5F9FF]"
            }`}
          >
            <span
              className={`w-full h-4 font-normal text-[12px] leading-4 flex-none whitespace-nowrap overflow-hidden text-ellipsis text-left ${
                isSelected ? "text-white" : "hover:text-(--neutral-gray-90)"
              }`}
              title={liProps.dataItem.lineitem} // Add tooltip on hover
            >
              {liProps.dataItem.lineitem}
            </span>
          </div>
        ) : null;
        return cloneElement(li, li.props, itemChildren);
      }}
      valueRender={(element) => {
        if (!selected) return element;
        return (
          <span className="w-full">
            {searchQuery &&
            selected.lineitem.toLowerCase().includes(searchQuery.toLowerCase())
              ? highlightText(selected.lineitem, searchQuery)
              : selected.lineitem}
          </span>
        );
      }}
      className={`${
        isMapped ? "cursor-not-allowed" : "cursor-pointer"
      } NotesDropdown absolute min-w-fit font-semibold mb-2 pt-7 border border-(--neutral-gray-10)`}
      onChange={handleChange}
    />
  );
};

export default NotesDropDown;

NotesDropDown.propTypes = {
  isMapped: PropTypes.bool,
  showDropdown: PropTypes.bool,
  setShowDropdown: PropTypes.func,
  allCoa: PropTypes.array,
  searchQuery: PropTypes.string,
  selectedValue: PropTypes.string
};
