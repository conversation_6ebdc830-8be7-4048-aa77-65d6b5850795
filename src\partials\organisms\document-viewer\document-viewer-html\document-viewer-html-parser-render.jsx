import React, { useEffect, useRef, useState } from "react";
import PropTypes from "prop-types";
import <PERSON>ram<PERSON> from "react-frame-component";
import Mark from "mark.js";
import DocumentViewerPopOverHighLight from "../document-viewer-pop-over-highlight";
import DocumentViewerPopOverDeleteHighLight from "../document-viewer-pop-over-delete-highlight";
import useDocumentViewerStore from "../document-viewer.store";
import DocumentViewerPopOverTable from "../document-viewer-pop-over-table";

const markJsOptions = {
  element: "mark",
  className: "",
  exclude: [],
  separateWordSearch: false,
  accuracy: "partially",
  diacritics: true,
  synonyms: {},
  iframes: false,
  iframesTimeout: 5000,
  acrossElements: true,
  caseSensitive: false,
  ignoreJoiners: false,
  ignorePunctuation: [],
  wildcards: "disabled",
  each: () => {}
};

const COLOR = { hover: "#F5F9FF", selected: "#E3EEFF", border: "#C4D9FF" };
const FILING_CSS_CLASS = {
  table: "filing-table",
  selected: "filing-table-selected"
};

const DocumentViewerHTMLParserRender = ({
  parsedHtml,
  searchQuery,
  readOnly,
  metaData
}) => {
  const htmlRef = useRef(null);
  const [key, setKey] = useState(0);

  const {
    setHighlightedMetaData,
    setShowHighLightPopOver,
    showHighLightPopOver,
    showTablePopOver,
    setShowTablePopOver,
    fileExtension,
    setSelectedText,
    setShowHighLightDeletePopOver,
    showHighLightDeletePopOver,
    activePreviewConfig
  } = useDocumentViewerStore((state) => state);

  const { docHeight, highlightPopover, screen } = activePreviewConfig;

  useEffect(() => {
    setKey((prevKey) => prevKey + 1);
  }, [metaData]);

  useEffect(() => {
    if (!showHighLightPopOver && !showTablePopOver) clearAllStatus();
  }, [showHighLightPopOver, showTablePopOver]);

  const getIFrameDocument = () => {
    if (htmlRef.current === null) return;
    return (
      htmlRef.current.contentDocument || htmlRef.current.contentWindow.document
    );
  };

  useEffect(() => {
    let markInstance = new Mark(getIFrameDocument());
    markInstance.unmark(markJsOptions);
    if (searchQuery?.length > 0) markInstance.mark(searchQuery, markJsOptions);
    else markInstance.unmark(markJsOptions);
  }, [searchQuery]);

  // Function to handle the click event
  const handleOnClickEvent = (e) => {
    e.preventDefault();
    const element = e.target;

    //If the clicked element is anchor tag or parent is an anchor tag, prevent the default behavior and scroll to the element
    let anchorElement = getAnchorTagElementFromChild(element);
    if (anchorElement?.tagName === "A") {
      handleAnchorTagClick(anchorElement);
      return;
    }

    if (readOnly) return;

    //If the clicked element is table or parent is an table, select the table and show the popover
    let tableElement = getTableElementFromChild(element);
    if (
      tableElement?.tagName === "TABLE" &&
      tableElement.classList.contains(FILING_CSS_CLASS.table)
    ) {
      handleTableClick(tableElement);
      return;
    }
  };

  useEffect(() => {
    setShowHighLightDeletePopOver(readOnly);
  }, [readOnly]);

  const clearAllStatus = () => {
    setShowHighLightPopOver(false);
    clearHighlightedText();
    clearSelectedTable();
  };

  const clearHighlightedText = () => {
    const idoc = getIFrameDocument();
    let selection = idoc?.getSelection();
    // Clear the selection
    selection?.removeAllRanges();
  };

  // Function to clear the selected table
  const clearSelectedTable = () => {
    const selectedTable = htmlRef.current?.contentDocument.querySelector(
      `.${FILING_CSS_CLASS.selected}`
    );
    if (selectedTable) {
      selectedTable.classList.replace(
        FILING_CSS_CLASS.selected,
        FILING_CSS_CLASS.table
      );
    }
    setShowTablePopOver(false);
  };

  const handleTableClick = (element) => {
    // replace the table class with the selected class if not highlighted
    if (showHighLightPopOver) return;
    element.classList.replace(
      FILING_CSS_CLASS.table,
      FILING_CSS_CLASS.selected
    );

    setShowTablePopOver(true);

    //dont change the metaData structure without discussion.
    const metaData = {
      version: "1.0",
      type: fileExtension,
      category: "table",
      data: {
        tableId: element.id
      }
    };

    const text = element.innerText;
    setSelectedText(text);

    setHighlightedMetaData(metaData);
  };

  // Function to get the table tag element from the child element
  const getTableElementFromChild = (element, parentElementLimit = 5) => {
    if (!element) return element;
    if (element.tagName === "TABLE") return element;
    let tableElement = element;
    for (let i = 0; i <= parentElementLimit; i++) {
      tableElement = tableElement.parentElement
        ? tableElement.parentElement
        : tableElement;
      if (tableElement?.tagName === "TABLE") break;
    }
    return tableElement;
  };

  // Function to get the anchor tag element from the child element
  const getAnchorTagElementFromChild = (element, parentElementLimit = 5) => {
    if (!element) return element;
    if (element.tagName === "A") return element;
    let anchorTagElement = element;
    for (let i = 0; i <= parentElementLimit; i++) {
      anchorTagElement = anchorTagElement.parentElement
        ? anchorTagElement.parentElement
        : anchorTagElement;
      if (anchorTagElement?.tagName === "A") break;
    }
    return anchorTagElement;
  };

  // function to handle anchor tag click
  const handleAnchorTagClick = (element) => {
    let iframeDocument = htmlRef.current.contentDocument;
    let hrefLink = element.attributes.getNamedItem("href")?.value;
    // if any hrefLink has # remove it
    hrefLink =
      hrefLink.substring(0, 1) === "#" ? hrefLink.substring(1) : hrefLink;

    // if href link contains http, open the link in new tab
    if (hrefLink.includes("http")) {
      window.open(hrefLink, "_blank");
      return;
    }
    // if the element is found by Name, scroll to the element
    const targetNames = iframeDocument.getElementsByName(hrefLink);
    if (targetNames.length > 0) {
      targetNames[0].scrollIntoView({ behavior: "smooth", block: "start" });
      return;
    }
    // if the element is found by Id, scroll to the element
    const targetId = iframeDocument.getElementById(hrefLink);
    if (targetId) {
      targetId.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  };

  const handleMouseUpEvent = () => {
    // Highlight the selected text
    if (["play-ground", "my-highlights"].includes(screen)) return;

    if (readOnly) {
      clearHighlightedText();
      clearSelectedTable();
      return;
    }

    highlightSelection();
  };

  const highlightSelection = () => {
    const idoc = getIFrameDocument();

    let selection = idoc.getSelection();

    setSelectedText(selection.toString());

    if (selection.isCollapsed) {
      clearAllStatus();
      return; // No need to highlight if nothing is selected
    }
    setShowHighLightPopOver(true);

    const isReverseSelection = () => {
      const range = selection.getRangeAt(0);
      if (selection.anchorNode === selection.focusNode) {
        // If the selection is within the same node
        return selection.anchorOffset > selection.focusOffset;
      } else {
        // If the selection spans multiple nodes
        const comparisonRange = range.cloneRange();
        comparisonRange.setStart(selection.anchorNode, selection.anchorOffset);
        comparisonRange.setEnd(selection.focusNode, selection.focusOffset);
        return comparisonRange.collapsed;
      }
    };

    const isReversed = isReverseSelection();
    const nodes = getSelectedNodes(selection, isReversed);

    const allTextNodes = getAllTextNodes(idoc.body);

    let beforeAfterText = getBeforeAndAfterText(selection, isReversed);
    let beforeFirstSelText = beforeAfterText.before;
    let afterLastSelText = beforeAfterText.after;

    let allSelTextInfoObj = [];
    let j = 0;
    nodes.forEach((item, i) => {
      for (j; j < allTextNodes.length; j++) {
        let x = allTextNodes[j];
        if (x === item && x.parentNode === item.parentNode) {
          let nodeText = x.nodeValue;
          let txtToHighlight = getTextToHighlight(
            nodeText,
            beforeFirstSelText,
            afterLastSelText,
            nodes.length,
            i
          );
          if (txtToHighlight.trim() !== "")
            allSelTextInfoObj.push({
              highlightedText: txtToHighlight,
              index: j
            });
          break;
        }
      }
    });

    //dont change the metaData structure without discussion.
    const metaData = {
      version: "1.0",
      type: fileExtension,
      category: "highlight",
      data: {
        beforeText: beforeFirstSelText,
        afterText: afterLastSelText,
        metaData: allSelTextInfoObj,
        totalTextNodes: allTextNodes.length
      }
    };
    setHighlightedMetaData(metaData);

    clearSelectedTable();
  };

  const getSelectedNodes = (selection, isReversed) => {
    const idoc = getIFrameDocument();
    let nodes = [];
    if (
      //for single node
      selection.anchorNode === selection.focusNode
    ) {
      nodes.push(selection.anchorNode);
    } else {
      //for multiple nodes
      if (isReversed) {
        //from right to left
        nodes = getTextNodesBetween(
          idoc,
          selection.focusNode,
          selection.anchorNode
        );
        nodes.unshift(selection.focusNode);
        nodes.push(selection.anchorNode);
      } else {
        //from left to right
        nodes = getTextNodesBetween(
          idoc,
          selection.anchorNode,
          selection.focusNode
        );
        nodes.unshift(selection.anchorNode);
        nodes.push(selection.focusNode);
      }
    }
    nodes = nodes.filter((x) => x.nodeType === 3);
    return nodes;
  };

  const getTextNodesBetween = (rootNode, startNode, endNode) => {
    let pastStartNode = false;
    let reachedEndNode = false;
    let textNodes = [];

    const getTextNodes = (node) => {
      if (node === startNode) pastStartNode = true;
      else if (node === endNode) reachedEndNode = true;
      else if (node.nodeType === 3) {
        if (pastStartNode && !reachedEndNode && !/^\s*$/.test(node.nodeValue)) {
          textNodes.push(node);
        }
      } else {
        for (
          let i = 0, len = node.childNodes.length;
          !reachedEndNode && i < len;
          ++i
        ) {
          getTextNodes(node.childNodes[i]);
        }
      }
    };

    getTextNodes(rootNode);
    return textNodes;
  };

  const getAllTextNodesV2 = (element) => {
    const walker = document.createTreeWalker(
      element,
      NodeFilter.SHOW_TEXT,
      null,
      false
    );
    let textNodes = [];
    while (walker.nextNode()) {
      if (
        element.tagName !== "HEAD" ||
        element.tagName !== "SCRIPT" ||
        element.tagName !== "STYLE" ||
        element.tagName !== "TITLE"
      )
        textNodes.push(walker.currentNode);
    }

    return textNodes;
  };

  const getAllTextNodes = (element) => {
    if (
      element.tagName === "HEAD" ||
      element.tagName === "SCRIPT" ||
      element.tagName === "STYLE" ||
      element.tagName === "TITLE"
    )
      return [];

    const textNodes = [];

    for (const child of element.childNodes) {
      if (child.nodeType === Node.TEXT_NODE) {
        textNodes.push(child);
      } else {
        textNodes.push(...getAllTextNodes(child)); // Recursive call for child elements
      }
    }
    return textNodes;
  };

  //Returns text before and after the selected text
  const getBeforeAndAfterText = (selection, isReversed) => {
    let before = "";
    let after = "";

    if (isReversed) {
      before = selection.extentNode.textContent.substring(
        selection.extentOffset - 32,
        selection.extentOffset
      );
      after = selection.anchorNode.textContent.substring(
        selection.anchorOffset + 32,
        selection.anchorOffset
      );
    } else {
      before = selection.anchorNode.textContent.substring(
        selection.anchorOffset - 32,
        selection.anchorOffset
      );
      after = selection.extentNode.textContent.substring(
        selection.extentOffset + 32,
        selection.extentOffset
      );
    }

    before = before.trim() === "" ? "" : before;
    after = after.trim() === "" ? "" : after;
    return { before, after };
  };

  const getTextToHighlight = (nodeText, before, after, len, i) => {
    const trimBefore = before.trim();
    const trimAfter = after.trim();

    if (len === 1) {
      if (trimBefore && !trimAfter) {
        return nodeText.split(before)[1];
      }
      if (trimAfter && !trimBefore) {
        return nodeText.split(after)[0];
      }
      if (trimBefore && trimAfter) {
        return nodeText.split(before)[1].split(after)[0];
      }
    } else {
      if (i === 0 && trimBefore) {
        return nodeText.split(before)[1];
      }
      if (i === len - 1 && trimAfter) {
        return nodeText.split(after)[0];
      }
    }

    return nodeText;
  };

  const highlightTextUpdate = () => {
    const idoc = getIFrameDocument();
    const allMarks = idoc.querySelectorAll("mark");
    allMarks.forEach((mark) => {
      mark.outerHTML = mark.innerHTML;
    });

    highlightText();
  };

  const highlightDataLakeTextV2 = () => {
    const idoc = getIFrameDocument();
    const data = metaData.data;

    if (!data?.metaData) return;
    let startWords = data.beforeText;
    let endWords = data.afterText;

    if (startWords.length === 0 && endWords.length === 0) return;

    const walker = document.createTreeWalker(
      idoc.body,
      NodeFilter.SHOW_TEXT,
      null,
      false
    );
    let startNode = null;
    let endNode = null;
    let startOffset = -1;
    let endOffset = -1;

    let skipAndCheckIndex = 5;

    const getNextNode = (walker) => {
      let nextNode = walker.nextNode();
      while (nextNode && /^\s*$/.test(nextNode.nodeValue)) {
        nextNode = walker.nextNode();
      }
      return nextNode;
    };

    // Find the start node and offset
    while (getNextNode(walker)) {
      const node = walker.currentNode;
      let currentNode = node;
      let sequenceFound = true;
      let skipIndexStart = skipAndCheckIndex;

      for (let i = 0; i < startWords.length; i++) {
        let tempOffset = -1;

        let word = startWords[i];
        const index = currentNode.nodeValue
          .replace(/\u00A0/g, " ")
          .search(new RegExp(word.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")));

        if (index === -1) {
          if (skipIndexStart > 0 && i >= 2) {
            skipIndexStart--;
          } else {
            sequenceFound = false;
            break;
          }
        }

        tempOffset = index + word.length - 1;
        currentNode = getNextNode(walker);
      }

      if (sequenceFound) {
        startNode = node;
        startOffset = node.nodeValue.indexOf(startWords[0]);
        break;
      }
    }

    // Find the end node and offset
    walker.currentNode = idoc.body; // Reset walker
    while (getNextNode(walker)) {
      const node = walker.currentNode;
      let currentNode = node;
      let sequenceFound = true;
      let skipIndexStart = skipAndCheckIndex;

      for (let i = 0; i < endWords.length; i++) {
        let tempOffset = -1;
        let word = endWords[i];
        const index = currentNode.nodeValue.indexOf(word, tempOffset + 1);
        if (index === -1) {
          if (skipIndexStart > 0 && i >= 2) {
            skipIndexStart--;
          } else {
            sequenceFound = false;
            break;
          }
        }

        tempOffset = index + word.length - 1;
        currentNode = getNextNode(walker);
      }

      if (sequenceFound) {
        endNode = node;
        endOffset = node.nodeValue.indexOf(endWords[0]);
        break;
      }
    }

    if (endWords.length === 0) {
      endNode = null;
      endOffset = -1;
    } else if (
      !startNode ||
      !endNode ||
      (startNode === endNode && startOffset >= endOffset)
    ) {
      return;
    }

    // const startText = startNode.nodeValue;
    // const endText = endNode.nodeValue;

    // const startTextBefore = startText.substring(0, startOffset);
    // const startTextAfter = startText.substring(startOffset + startWords[0].length);

    // const endTextBefore = endText.substring(0, endOffset);
    // const endTextAfter = endText.substring(endOffset + endWords[0].length);

    // //add mark text to the start node to the end node
    // startNode.nodeValue = startTextBefore;
    // endNode.nodeValue = endTextAfter;

    walker.currentNode = idoc.body; // Reset walker
    const textNodes = [];
    let node;
    let startFound = false;

    while ((node = walker.nextNode())) {
      if (walker.currentNode === startNode) startFound = true;
      if (walker.currentNode === endNode) {
        textNodes.push(node);
        break;
      }
      if (startFound) {
        textNodes.push(node);
        if (endWords.length === 0) break;
      }
    }

    textNodes.forEach((textNode) => {
      const text = textNode.nodeValue;
      const mark = document.createElement("mark");
      mark.textContent = text;
      textNode.parentNode.replaceChild(mark, textNode);
    });

    navigateToHighlightedText();
  };

  const highlightText = () => {
    if (!metaData) return;
    if (metaData.category === "highlight_datalake") {
      highlightDataLakeTextV2();
      return;
    }

    const idoc = getIFrameDocument();

    const data = metaData.data;

    if (!data?.metaData) return;

    let selTextInfoObj = data.metaData;
    let beforeText = data.beforeText;
    let afterText = data.afterText;
    let totalTextNodes = data.totalTextNodes;

    let allTextNodes = getAllTextNodes(idoc.body);

    let arrElementsToHighlight = [];
    for (let i = 0; i < selTextInfoObj.length; i++) {
      let selectedText = selTextInfoObj[i].highlightedText;

      let ele = allTextNodes[selTextInfoObj[i].index];

      let str = ele?.nodeValue;

      //check str is null or undefined or newlines or tabs
      const isNullOrWhitespace = (str) => {
        return str === null || str === undefined || /^\s*$/.test(str);
      };

      if (isNullOrWhitespace(str)) return;

      //If text selected is in single html element
      if (selTextInfoObj.length === 1) {
        const splitText = `${beforeText}${selectedText}${afterText}`;
        const markedText = `${beforeText}<mark>${selectedText}</mark>${afterText}`;
        str = str.split(splitText).join(markedText);
      } //if text selected is in more than one html elements
      else {
        let splitText;
        let markedText;
        switch (i) {
          case 0:
            //First element will have before text
            splitText = `${beforeText}${selectedText}`;
            markedText = `${beforeText}<mark>${selectedText}</mark>`;
            str = str.split(splitText).join(markedText);
            break;
          case selTextInfoObj.length - 1:
            //Last element will have after text
            splitText = `${selectedText}${afterText}`;
            markedText = `<mark>${selectedText}</mark>${afterText}`;
            str = str.split(splitText).join(markedText);
            break;
          default:
            splitText = `${selectedText}`;
            markedText = `<mark>${selectedText}</mark>`;
            str = str.split(splitText).join(markedText);
        }
      }
      arrElementsToHighlight.push({ element: ele, str: str });
    }

    arrElementsToHighlight.forEach((item) => {
      let newNode = document.createElement("span");
      newNode.innerHTML = item.str;
      item.element.parentElement.replaceChild(newNode, item.element);
    });

    navigateToHighlightedText();
  };

  const navigateToHighlightedText = () => {
    const idoc = getIFrameDocument();
    const targetId = idoc.querySelector("mark");
    const elTopPosition =
      targetId?.getBoundingClientRect().top - window.innerHeight / 3;

    idoc.documentElement.scrollBy({
      top: elTopPosition
    });
  };

  const filingsStyle = (
    <style nonce="sec-gov">
      {`
        ::-moz-selection {          
          background: yellow;
        }
        ::selection {          
          background: yellow;
        }
        .filing-table {                 
          transition: box-shadow 0.3s ease-in-out;          
        }
        ${
          !readOnly &&
          `.filing-table:hover {
            background: ${COLOR.hover};
            box-shadow: 0 2px 5px rgba(0,0,0,0.3);          
            border-radius: 5px;          
            cursor: pointer;
          }`
        }        
        .filing-table-selected {
          background: ${COLOR.selected};          
          box-shadow: 0 2px 5px rgba(0,0,0,0.3);
          border: 1px solid ${COLOR.border};
          border-radius: 5px;         
        }                                                
      `}
    </style>
  );

  return (
    <div
      key={key}
      data-testid="document-viewer-html-render"
      className={`relative ${docHeight}`}
    >
      <IFrame
        ref={htmlRef}
        head={filingsStyle}
        onClick={handleOnClickEvent}
        onMouseUp={handleMouseUpEvent}
        contentDidMount={highlightText}
        contentDidUpdate={highlightTextUpdate}
        className="size-full"
        id="iframe-document-viewer-html-render"
        data-testid="iframe-document-viewer-html-render"
      >
        {parsedHtml}
      </IFrame>

      <DocumentViewerPopOverHighLight
        show={showHighLightPopOver}
        setShow={setShowHighLightPopOver}
      />

      {highlightPopover && (
        <DocumentViewerPopOverDeleteHighLight
          show={showHighLightDeletePopOver}
          setShow={setShowHighLightDeletePopOver}
        />
      )}
      <DocumentViewerPopOverTable
        show={showTablePopOver}
        setShow={setShowTablePopOver}
      />
    </div>
  );
};

export default React.memo(DocumentViewerHTMLParserRender);

DocumentViewerHTMLParserRender.propTypes = {
  parsedHtml: PropTypes.oneOfType([PropTypes.string, PropTypes.object])
    .isRequired,
  searchQuery: PropTypes.string,
  readOnly: PropTypes.bool,
  metaData: PropTypes.shape({
    data: PropTypes.object,
    category: PropTypes.string
  })
};
