import { Tab, TabGroup, Tab<PERSON>ist, Tab<PERSON>anel, Tab<PERSON>ane<PERSON> } from "@headlessui/react";
import PropTypes from "prop-types";
import { Tooltip } from "@progress/kendo-react-tooltip";
import useDocumentViewerStore from "../../organisms/document-viewer/document-viewer.store";

const TabsGroup = ({ tabs, className, activeTab, setActiveTab }) => {
  const { isPreviewOpen } = useDocumentViewerStore();

  return (
    <TabGroup className={"flex h-full w-full flex-col justify-start"}>
      <Tooltip openDelay={100} position="bottom" anchorElement="target">
        <TabList className={`flex flex-nowrap border-b pl-3 ${className}`}>
          {tabs.map((tab) => (
            <Tab
              key={tab.key}
              title={tab.name}
              onClick={() => setActiveTab(tab.key)}
              className={`body-r flex h-12 focus:outline-none ${isPreviewOpen && "flex items-center justify-center lg:w-20 xl:w-28 2xl:w-32 3xl:w-40"} items-center px-3 py-3.5 disabled:text-neutral-20 ${activeTab === tab.key ? "border-b-4 border-primary-78 text-primary-78" : "custom-tab text-neutral-60 hover:text-primary-78"}`}
            >
              <span className="inline-block items-center truncate">
                {tab.name}
              </span>
            </Tab>
          ))}
        </TabList>
      </Tooltip>
      <TabPanels className="flex h-full flex-col">
        {tabs.map((tab) => (
          <TabPanel key={tab.key} className={"h-full"}>
            {tab.component}
          </TabPanel>
        ))}
      </TabPanels>
    </TabGroup>
  );
};

TabsGroup.propTypes = {
  tabs: PropTypes.arrayOf(
    PropTypes.shape({
      name: PropTypes.string.isRequired,
      key: PropTypes.string.isRequired,
      component: PropTypes.node.isRequired
    })
  ).isRequired,
  activeTab: PropTypes.string.isRequired,
  setActiveTab: PropTypes.func.isRequired,
  className: PropTypes.string
};

export default TabsGroup;
