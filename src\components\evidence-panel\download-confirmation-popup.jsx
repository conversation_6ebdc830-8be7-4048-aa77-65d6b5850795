import React from "react";
import PropTypes from "prop-types";
import { PopUp } from "../../partials/molecules/pop-up";

const DownloadConfirmationPopup = ({
  showDownloadConfirmationPopup,
  setShowDownloadConfirmationPopup,
  handleExcelExport
}) => {
  return (
    <PopUp
      colGrid={"grid-cols-12"}
      header={"Confirm Download?"}
      showPopUp={showDownloadConfirmationPopup}
      setShowPopUp={setShowDownloadConfirmationPopup}
      cancelButtonText={"Cancel"}
      submitButtonText={"Yes, Download"}
      onSubmit={handleExcelExport}
      cols={"col-span-4 col-start-5"}
    >
      <div className="p-6 py-4">
        <div className="my-3 text-body-r font-body-r text-neutral-80">
          Are you sure you want to download the Financial Spreader without
          mapping?
        </div>
      </div>
    </PopUp>
  );
};

DownloadConfirmationPopup.propTypes = {
  showDownloadConfirmationPopup: PropTypes.bool.isRequired,
  setShowDownloadConfirmationPopup: PropTypes.func.isRequired,
  handleExcelExport: PropTypes.func.isRequired
};

export default DownloadConfirmationPopup;
