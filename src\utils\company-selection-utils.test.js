import {
  getSnackbarValues,
  updateSelectedCompanies,
  getActiveTabData,
  applyCurrencyAndUnitChanges
} from './company-selection-utils';

describe('Company Selection Utils', () => {
  // Mock data for testing
  const mockCompaniesData = [
    {
      companyId: 1,
      name: 'Company A',
      currencyCode: 'USD',
      currency: 'USD',
      unit: 'Million'
    },
    {
      companyId: 2,
      name: 'Company B',
      currencyCode: 'EUR',
      currency: 'EUR',
      unit: 'Thousand'
    },
    {
      companyId: 3,
      name: 'Company C',
      currencyCode: 'USD',
      currency: 'USD',
      unit: 'Million'
    }
  ];

  describe('getSnackbarValues', () => {
    test('should return default values when no companies are selected', () => {
      const result = getSnackbarValues([], mockCompaniesData);
      
      expect(result).toEqual({
        currency: "Select Currency",
        unit: "Select Unit"
      });
    });

    test('should return default values when companies data is empty', () => {
      const result = getSnackbarValues([1, 2], []);
      
      expect(result).toEqual({
        currency: "Select Currency",
        unit: "Select Unit"
      });
    });

    test('should return single company values when one company is selected', () => {
      const result = getSnackbarValues([1], mockCompaniesData);
      
      expect(result).toEqual({
        currency: "USD",
        unit: "Million"
      });
    });

    test('should return currency and unit when all selected companies have same values', () => {
      const result = getSnackbarValues([1, 3], mockCompaniesData);
      
      expect(result).toEqual({
        currency: "USD",
        unit: "Million"
      });
    });

    test('should return "Select Currency" when selected companies have different currencies', () => {
      const result = getSnackbarValues([1, 2], mockCompaniesData);
      
      expect(result).toEqual({
        currency: "Select Currency",
        unit: "Select Unit"
      });
    });

    test('should handle companies with missing currency or unit fields', () => {
      const companiesWithMissingFields = [
        { companyId: 1, name: 'Company A' },
        { companyId: 2, name: 'Company B', currencyCode: 'EUR' }
      ];
      
      const result = getSnackbarValues([1], companiesWithMissingFields);
      
      expect(result).toEqual({
        currency: "Select Currency",
        unit: "Select Unit"
      });
    });
  });

  describe('updateSelectedCompanies', () => {
    test('should update currency and unit for selected companies', () => {
      const selectedCompanyIds = [1, 2];
      const newCurrency = 'GBP';
      const newUnit = 'Billion';
      
      const result = updateSelectedCompanies(
        mockCompaniesData,
        selectedCompanyIds,
        newCurrency,
        newUnit
      );
      
      expect(result[0]).toEqual({
        companyId: 1,
        name: 'Company A',
        currencyCode: 'GBP',
        currency: 'GBP',
        unit: 'Billion'
      });
      
      expect(result[1]).toEqual({
        companyId: 2,
        name: 'Company B',
        currencyCode: 'GBP',
        currency: 'GBP',
        unit: 'Billion'
      });
      
      // Company 3 should remain unchanged
      expect(result[2]).toEqual(mockCompaniesData[2]);
    });

    test('should not mutate original data', () => {
      const originalData = [...mockCompaniesData];
      const selectedCompanyIds = [1];
      
      updateSelectedCompanies(
        mockCompaniesData,
        selectedCompanyIds,
        'GBP',
        'Billion'
      );
      
      // Original data should remain unchanged
      expect(mockCompaniesData).toEqual(originalData);
    });

    test('should handle companies without currencyCode property', () => {
      const companiesWithoutCurrencyCode = [
        { companyId: 1, name: 'Company A', currency: 'USD', unit: 'Million' }
      ];
      
      const result = updateSelectedCompanies(
        companiesWithoutCurrencyCode,
        [1],
        'GBP',
        'Billion'
      );
      
      expect(result[0]).toEqual({
        companyId: 1,
        name: 'Company A',
        currency: 'GBP',
        unit: 'Billion'
      });
    });
  });

  describe('getActiveTabData', () => {
    const mockTabData = {
      consolidatedData: [{ id: 1, type: 'consolidated' }],
      financialData: [{ id: 2, type: 'financial' }],
      masterData: [{ id: 3, type: 'master' }],
      investmentData: [{ id: 4, type: 'investment' }]
    };

    const mockSetters = {
      setConsolidatedData: jest.fn(),
      setFinancialData: jest.fn(),
      setMasterData: jest.fn(),
      setInvestmentData: jest.fn()
    };

    test('should return consolidated data for "consolidated" tab', () => {
      const result = getActiveTabData('consolidated', mockTabData, mockSetters);
      
      expect(result).toEqual({
        data: mockTabData.consolidatedData,
        setData: mockSetters.setConsolidatedData
      });
    });

    test('should return financial data for "financial" tab', () => {
      const result = getActiveTabData('financial', mockTabData, mockSetters);
      
      expect(result).toEqual({
        data: mockTabData.financialData,
        setData: mockSetters.setFinancialData
      });
    });

    test('should return master data for "masterData" tab', () => {
      const result = getActiveTabData('masterData', mockTabData, mockSetters);
      
      expect(result).toEqual({
        data: mockTabData.masterData,
        setData: mockSetters.setMasterData
      });
    });

    test('should return investment data for "investmentKpi" tab', () => {
      const result = getActiveTabData('investmentKpi', mockTabData, mockSetters);
      
      expect(result).toEqual({
        data: mockTabData.investmentData,
        setData: mockSetters.setInvestmentData
      });
    });

    test('should return consolidated data for unknown tab (default case)', () => {
      const result = getActiveTabData('unknown', mockTabData, mockSetters);
      
      expect(result).toEqual({
        data: mockTabData.consolidatedData,
        setData: mockSetters.setConsolidatedData
      });
    });

    test('should work without setters parameter', () => {
      const result = getActiveTabData('financial', mockTabData);
      
      expect(result).toEqual({
        data: mockTabData.financialData,
        setData: undefined
      });
    });
  });
});