import React from 'react';
import { MdExpandLess, MdExpandMore } from "react-icons/md";

export const UploadPanelHeader = ({ 
  expanded,
  handlePanelToggle,
  fileCount,
  title
}) => {
  return (
    <div
      onClick={handlePanelToggle}
      className={`rounded gap-3 justify-between flex flex-row py-3 px-4 cursor-pointer hover:bg-primary-35 ${
        expanded ? "bg-primary-43" : ""
      }`}
    >
      <div className="flex flex-row gap-3">
        <button className="flex justify-center items-center cursor-pointer">
          {expanded ? <MdExpandLess /> : <MdExpandMore />}
        </button>
        <div
          className={`text-neutral-90 ${expanded ? "heading-2-m" : "m-m"}`}
        >
          {title}
        </div>
      </div>
      {fileCount > 0 && (
        <div className="text-primary-78 text-sm font-caption-i font-normal">
          {fileCount} uploaded
        </div>
      )}
    </div>
  );
};