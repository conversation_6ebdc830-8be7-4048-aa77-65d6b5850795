import React from "react";
import PropTypes from "prop-types";
import { RolesContext } from "../../general/context";
import { getRoles } from "../store/user-store";
import { roles } from "../../constants/config";
import NoAccess from "../../partials/molecules/no-access";

const AppRoleAccessProvider = ({ children }) => {
  const userRoles = getRoles();

  const rolePolicyConfig = {
    UserManagementPolicy: [roles.adminUser],
    EditPolicy: [roles.adminUser],
    ViewPolicy: [roles.adminUser, roles.normalUser]
  };

  const checkPolicy = (policyList) => {
    return userRoles.some((el) => policyList.includes(el));
  };

  const roleConfig = {
    USER_MANAGEMENT_POLICY: checkPolicy(rolePolicyConfig.UserManagementPolicy),
    EDIT_POLICY: checkPolicy(rolePolicyConfig.EditPolicy),
    VIEW_POLICY: checkPolicy(rolePolicyConfig.ViewPolicy)
  };

  const validateRoleAccess = () => {
    const roleAccess = Object.values(roleConfig);
    return roleAccess.includes(true) ? true : false;
  };

  return validateRoleAccess() ? (
    <RolesContext.Provider value={roleConfig}>{children}</RolesContext.Provider>
  ) : (
    <NoAccess />
  );
};

AppRoleAccessProvider.propTypes = {
  children: PropTypes.element
};

export default AppRoleAccessProvider;
