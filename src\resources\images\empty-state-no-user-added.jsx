import React from "react";

function EmptyStateNoUserAdded() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="206"
      height="164"
      fill="none"
      viewBox="0 0 206 164"
    >
      <g clipPath="url(#clip0_4783_14339)">
        <path
          fill="url(#paint0_linear_4783_14339)"
          d="M93.587 55.673s35.544-17.055 41.794-18.035c6.25-.98 11.694-3.06 23.71-1.1 12.016 1.959 22.149 14.477 23.742 21.5s3.844 10.174 1.053 23.287-7.772 19.373-7.772 19.373l-27.93 41.613s-7.338 10.975-14.866 13.541c-7.528 2.566-19.056-4.158-24.154-6.254-5.098-2.096-27.08-15.368-35.355-14.845-8.275.523-42.99-2.088-42.99-2.088s-17.5-.031-21.421-7.24c-3.922-7.209-.73-19.041 7.5-31.246 8.229-12.205 24.257-20.49 26.9-21.97 2.643-1.478 49.789-16.536 49.789-16.536z"
        ></path>
        <g filter="url(#filter0_d_4783_14339)">
          <path
            fill="#fff"
            d="M95.964 10.15L24.846 29.218c-5.38 1.442-8.572 6.973-7.13 12.354l24.292 90.602c1.443 5.38 6.974 8.572 12.354 7.13l71.118-19.068c5.38-1.443 8.572-6.974 7.13-12.354L108.318 17.28c-1.443-5.38-6.974-8.572-12.354-7.13z"
          ></path>
          <path
            stroke="#B3B3B3"
            strokeWidth="1.009"
            d="M96.094 10.637L24.977 29.705a9.582 9.582 0 00-6.774 11.736l24.292 90.602c1.37 5.111 6.625 8.144 11.737 6.774l71.117-19.068a9.583 9.583 0 006.774-11.737l-24.292-90.601c-1.371-5.112-6.625-8.144-11.737-6.774z"
          ></path>
        </g>
        <path
          fill="#E6E6E6"
          d="M97.344 54.904l-37.61 10.084a1.64 1.64 0 01-.85-3.168l37.612-10.084a1.64 1.64 0 01.85 3.168h-.002zM91.957 34.812l-37.61 10.084a1.64 1.64 0 01-.85-3.168L91.11 31.644a1.64 1.64 0 01.85 3.168h-.002zM105.312 58.498L61.165 70.334a1.64 1.64 0 11-.849-3.167l44.146-11.837a1.64 1.64 0 01.85 3.168zM99.924 38.405L55.778 50.241a1.64 1.64 0 11-.85-3.167l44.147-11.837a1.64 1.64 0 01.85 3.168zM103.299 77.445L65.687 87.529a1.64 1.64 0 01-.849-3.168l37.61-10.084a1.64 1.64 0 01.851 3.168zM108.769 97.515l-37.611 10.084a1.636 1.636 0 01-1.97-1.169 1.64 1.64 0 011.12-1.998l37.611-10.084a1.64 1.64 0 01.85 3.167zM111.265 81.04L67.119 92.876a1.64 1.64 0 01-.85-3.168l44.147-11.836a1.64 1.64 0 01.849 3.168zM116.735 101.109l-44.146 11.836a1.64 1.64 0 01-.85-3.167l44.147-11.836a1.64 1.64 0 01.849 3.167zM53.392 75.02l-13.309 3.568a.787.787 0 01-.963-.556l-3.175-11.843a.787.787 0 01.556-.963l13.31-3.569a.787.787 0 01.963.556l3.175 11.843a.787.787 0 01-.556.963zM48.005 54.926l-13.31 3.569a.787.787 0 01-.962-.556l-3.175-11.843a.787.787 0 01.555-.963l13.31-3.569a.787.787 0 01.964.556l3.175 11.843a.787.787 0 01-.557.963zM59.346 97.56l-13.31 3.569a.791.791 0 01-.962-.556l-3.177-11.848a.788.788 0 01.556-.963l13.31-3.569a.787.787 0 01.963.556l3.177 11.848a.787.787 0 01-.557.963zM64.817 117.629l-13.31 3.569a.787.787 0 01-.962-.556l-3.177-11.848a.786.786 0 01.556-.963l13.31-3.568a.786.786 0 01.963.555l3.177 11.848a.788.788 0 01-.557.963z"
        ></path>
        <g filter="url(#filter1_d_4783_14339)">
          <path
            fill="#fff"
            d="M150.468 27.548h-79a7 7 0 00-7 7v99a7 7 0 007 7h79a7 7 0 007-7v-99a7 7 0 00-7-7z"
          ></path>
          <path
            stroke="#93B0ED"
            strokeWidth="1.5"
            d="M150.468 28.298h-79a6.25 6.25 0 00-6.25 6.25v99a6.25 6.25 0 006.25 6.25h79a6.25 6.25 0 006.25-6.25v-99a6.25 6.25 0 00-6.25-6.25z"
          ></path>
        </g>
        <path
          fill="#E6E6E6"
          d="M137.963 75.515H99.029a1.64 1.64 0 010-3.279h38.935a1.637 1.637 0 011.599 1.64 1.64 1.64 0 01-1.599 1.64h-.001zM144.727 82.048h-45.7a1.64 1.64 0 110-3.279h45.7a1.639 1.639 0 010 3.28zM137.963 97.721H99.028a1.64 1.64 0 010-3.279h38.934a1.637 1.637 0 011.599 1.64 1.639 1.639 0 01-1.598 1.64zM137.963 122.02H99.028a1.638 1.638 0 01-1.133-2.785 1.643 1.643 0 011.133-.494h38.934a1.64 1.64 0 01.001 3.279zM144.726 104.255h-45.7a1.639 1.639 0 010-3.279h45.7a1.639 1.639 0 011.599 1.64 1.638 1.638 0 01-1.599 1.639zM144.726 128.555h-45.7a1.639 1.639 0 010-3.279h45.7a1.639 1.639 0 011.599 1.64 1.638 1.638 0 01-1.599 1.639z"
        ></path>
        <path
          fill="#4061C7"
          d="M137.39 48.888h-34.247a1.641 1.641 0 010-3.279h34.247a1.643 1.643 0 010 3.28zM146.81 55.422h-43.667a1.641 1.641 0 010-3.279h43.667a1.643 1.643 0 011.102 2.768 1.643 1.643 0 01-1.102.511z"
        ></path>
        <path
          fill="url(#paint1_linear_4783_14339)"
          d="M144.012 107.509a19.244 19.244 0 0117.781 11.881 19.241 19.241 0 01-4.172 20.974 19.24 19.24 0 01-20.974 4.172 19.242 19.242 0 01-11.881-17.781 19.25 19.25 0 0119.246-19.246z"
        ></path>
        <path
          stroke="#fff"
          strokeLinecap="round"
          strokeWidth="3"
          d="M144.012 118.511v16.488M152.256 126.755h-16.488"
        ></path>
        <path
          fill="url(#paint2_linear_4783_14339)"
          d="M83.418 50.246a6.108 6.108 0 100-12.216 6.108 6.108 0 000 12.216zm5.43 1.357h-2.337a7.385 7.385 0 01-6.185 0h-2.337a5.429 5.429 0 00-5.43 5.426v.68a2.037 2.037 0 002.036 2.035h17.646a2.037 2.037 0 002.036-2.036v-.679a5.43 5.43 0 00-5.429-5.426z"
        ></path>
        <path
          fill="#E6E6E6"
          d="M83.417 77.29a4.349 4.349 0 10.002-8.698 4.349 4.349 0 00-.002 8.697zm3.865.965h-1.663a5.257 5.257 0 01-4.4 0h-1.666a3.864 3.864 0 00-3.865 3.861v.483a1.45 1.45 0 001.449 1.45h12.561a1.45 1.45 0 001.449-1.45v-.483a3.867 3.867 0 00-3.865-3.865v.004zM83.417 99.496a4.348 4.348 0 10.002-8.696 4.348 4.348 0 00-.002 8.696zm3.865.966h-1.663a5.256 5.256 0 01-4.4 0h-1.666a3.866 3.866 0 00-3.865 3.861v.483a1.45 1.45 0 001.449 1.449h12.561a1.451 1.451 0 001.449-1.449v-.483a3.867 3.867 0 00-2.386-3.571 3.87 3.87 0 00-1.479-.294v.004zM83.417 122.795a4.345 4.345 0 003.077-7.42 4.35 4.35 0 00-4.737-.946 4.35 4.35 0 00-.004 8.034 4.342 4.342 0 001.664.332zm3.865.966h-1.663a5.256 5.256 0 01-4.4 0h-1.666a3.866 3.866 0 00-3.865 3.861v.483a1.45 1.45 0 001.449 1.449h12.561a1.451 1.451 0 001.449-1.449v-.483a3.867 3.867 0 00-2.386-3.571 3.87 3.87 0 00-1.479-.294v.004z"
        ></path>
      </g>
      <defs>
        <filter
          id="filter0_d_4783_14339"
          width="127.639"
          height="141.899"
          x="11.344"
          y="4.782"
          colorInterpolationFilters="sRGB"
          filterUnits="userSpaceOnUse"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix"></feFlood>
          <feColorMatrix
            in="SourceAlpha"
            result="hardAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          ></feColorMatrix>
          <feOffset dy="1.004"></feOffset>
          <feGaussianBlur stdDeviation="3.013"></feGaussianBlur>
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.18 0"></feColorMatrix>
          <feBlend
            in2="BackgroundImageFix"
            result="effect1_dropShadow_4783_14339"
          ></feBlend>
          <feBlend
            in="SourceGraphic"
            in2="effect1_dropShadow_4783_14339"
            result="shape"
          ></feBlend>
        </filter>
        <filter
          id="filter1_d_4783_14339"
          width="105"
          height="125"
          x="58.468"
          y="22.548"
          colorInterpolationFilters="sRGB"
          filterUnits="userSpaceOnUse"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix"></feFlood>
          <feColorMatrix
            in="SourceAlpha"
            result="hardAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          ></feColorMatrix>
          <feOffset dy="1"></feOffset>
          <feGaussianBlur stdDeviation="3"></feGaussianBlur>
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.18 0"></feColorMatrix>
          <feBlend
            in2="BackgroundImageFix"
            result="effect1_dropShadow_4783_14339"
          ></feBlend>
          <feBlend
            in="SourceGraphic"
            in2="effect1_dropShadow_4783_14339"
            result="shape"
          ></feBlend>
        </filter>
        <linearGradient
          id="paint0_linear_4783_14339"
          x1="96.469"
          x2="96.469"
          y1="35.726"
          y2="156.428"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFF0FC"></stop>
          <stop offset="1" stopColor="#CED4F9"></stop>
        </linearGradient>
        <linearGradient
          id="paint1_linear_4783_14339"
          x1="129.231"
          x2="167.3"
          y1="136.532"
          y2="110.204"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#524396"></stop>
          <stop offset="1" stopColor="#CC71D6"></stop>
        </linearGradient>
        <linearGradient
          id="paint2_linear_4783_14339"
          x1="75.078"
          x2="96.555"
          y1="54.403"
          y2="39.547"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#524396"></stop>
          <stop offset="1" stopColor="#CC71D6"></stop>
        </linearGradient>
        <clipPath id="clip0_4783_14339">
          <path
            fill="#fff"
            d="M0 0H205V163H0z"
            transform="translate(.5 .5)"
          ></path>
        </clipPath>
      </defs>
    </svg>
  );
}

export default React.memo(EmptyStateNoUserAdded);
