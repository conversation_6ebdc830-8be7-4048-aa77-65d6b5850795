import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import Chip from './chip';

describe('Chip Component', () => {
  test('renders with default props', () => {
    render(<Chip text="Test Chip" />);
    
    const chipElement = screen.getByText('Test Chip');
    expect(chipElement).toBeInTheDocument();
    
    // Check default styling
    const buttonElement = chipElement.closest('button');
    expect(buttonElement).toHaveClass('text-sm');
    expect(buttonElement).toHaveClass('rounded-md');
    expect(buttonElement).not.toHaveClass('rounded-full');
  });
  
  test('renders with custom size', () => {
    render(<Chip text="Large Chip" size="large" />);
    
    const chipElement = screen.getByText('Large Chip');
    const buttonElement = chipElement.closest('button');
    expect(buttonElement).toHaveClass('text-lg');
  });
  
  test('renders with outline fill mode', () => {
    render(<Chip text="Outline Chip" fillMode="outline" themeColor="blue" />);
    
    const chipElement = screen.getByText('Outline Chip');
    const buttonElement = chipElement.closest('button');
    expect(buttonElement).toHaveClass('border');
    expect(buttonElement).toHaveClass('border-blue');
    expect(buttonElement).toHaveClass('text-blue');
  });
  
  test('renders with rounded corners', () => {
    render(<Chip text="Rounded Chip" rounded />);
    
    const chipElement = screen.getByText('Rounded Chip');
    const buttonElement = chipElement.closest('button');
    expect(buttonElement).toHaveClass('rounded-full');
    expect(buttonElement).not.toHaveClass('rounded-md');
  });
  
  test('calls onClick handler when clicked', () => {
    const handleClick = jest.fn();
    render(<Chip text="Clickable Chip" onClick={handleClick} />);
    
    const chipElement = screen.getByText('Clickable Chip');
    fireEvent.click(chipElement);
    
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
  
  test('renders close icon when selected', () => {
    render(<Chip text="Selected Chip" isSelected />);
    
    const chipElement = screen.getByText('Selected Chip');
    const buttonElement = chipElement.closest('button');
    
    // Check that it has the selected styling
    expect(buttonElement).toHaveClass('text-white');
    
    // Check that the close icon is rendered
    const closeIcon = buttonElement.querySelector('svg');
    expect(closeIcon).toBeInTheDocument();
  });
  
  test('is disabled when cursor-not-allowed class is present', () => {
    render(<Chip text="Disabled Chip" className="cursor-not-allowed" />);
    
    const chipElement = screen.getByText('Disabled Chip');
    const buttonElement = chipElement.closest('button');
    
    expect(buttonElement).toBeDisabled();
  });
  
  test('close icon stops propagation and triggers onClick', () => {
    const handleClick = jest.fn();
    render(<Chip text="Closable Chip" isSelected onClick={handleClick} />);
    
    const chipElement = screen.getByText('Closable Chip');
    const buttonElement = chipElement.closest('button');
    const closeIcon = buttonElement.querySelector('svg');
    
    // Simulate click on the close icon
    fireEvent.click(closeIcon);
    
    // The event should be stopped from propagating to the button
    // But the onClick handler should still be called
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
}); 