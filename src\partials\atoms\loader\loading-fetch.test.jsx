import React from "react";
import { render, screen } from "@testing-library/react";
import LoadingFetch from "./loading-fetch";
import { useIsFetching } from "@tanstack/react-query";

jest.mock("@tanstack/react-query");

describe("renders LoadingFetch component", () => {
  afterEach(() => {
    jest.resetAllMocks();
  });

  it("render null if useIsFetching is 0", () => {
    useIsFetching.mockReturnValue(0);
    render(<LoadingFetch />);
  });

  it("renders Loading when useIsFetching not 0", () => {
    useIsFetching.mockReturnValue(1);
    render(<LoadingFetch />);
    expect(screen.getByText("Loading...")).toBeInTheDocument();
  });
});
