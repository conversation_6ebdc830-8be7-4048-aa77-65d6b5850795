import React from "react";
import { render, screen } from "@testing-library/react";
import DocumentViewerParser from "./document-viewer-html-parser";
import TestAppRenderer from "../../../../infra/test-utils/test-app-renderer";
import { marked } from "marked";

global.Document = {
  parseHTMLUnsafe: jest.fn(() => ({
    // Return a mock value or behavior
    getElementsByTagName: jest.fn().mockReturnValue([
      {
        getAttribute: jest.fn(),
        classList: {
          add: jest.fn()
        }
      }
    ]),
    documentElement: { outerHTML: jest.fn().mockReturnValue("<div>ok</div>") }
  }))
};

jest.mock("marked", () => ({
  setOptions: jest.fn().mockReturnValue("<div>ok</div>"),
  parse: jest.fn().mockReturnValue("<div>ok</div>")
}));

describe("DocumentViewerParser", () => {
  it("renders the DocumentViewerParser with txt", () => {
    render(
      <TestAppRenderer>
        <DocumentViewerParser
          metaData={"test"}
          readOnly={false}
          fileData={"test"}
          fileExtension={"txt"}
          filingsUrl={"https://test.com"}
        />
      </TestAppRenderer>
    );
  });
  it("renders the DocumentViewerParser with paper", () => {
    render(
      <TestAppRenderer>
        <DocumentViewerParser
          metaData={"test"}
          readOnly={false}
          fileData={"test"}
          fileExtension={"paper"}
          filingsUrl={"https://test.com"}
        />
      </TestAppRenderer>
    );
  });
  it("renders the DocumentViewerParser with test", () => {
    render(
      <TestAppRenderer>
        <DocumentViewerParser
          metaData={"test"}
          readOnly={false}
          fileData={"test"}
          fileExtension={"test"}
          filingsUrl={"https://test.com"}
        />
      </TestAppRenderer>
    );
  });
  it("renders the DocumentViewerParser with html", () => {
    render(
      <TestAppRenderer>
        <DocumentViewerParser
          metaData={"test"}
          readOnly={false}
          fileData={"test"}
          fileExtension={"html"}
          filingsUrl={"https://test.com"}
        />
      </TestAppRenderer>
    );
  });
});
