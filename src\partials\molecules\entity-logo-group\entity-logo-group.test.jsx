import React from "react";
import { render, screen } from "@testing-library/react";
import EntityLogoGroup, { getBorderClass } from "./entity-logo-group"; // Updated import
import { GET_WATCH_LIST_ENTITY_API } from "../../../infra/api/watch-list-service";
import { ENTITY_LOGO_API } from "../../../infra/api/data-lake-service";
import LogoPlaceholder from "../../../resources/images/icon-logo-placeholder";

jest.mock("../../../infra/api/watch-list-service", () => ({
  GET_WATCH_LIST_ENTITY_API: jest.fn()
}));

jest.mock("../../../infra/api/data-lake-service", () => ({
  ENTITY_LOGO_API: jest.fn()
}));

jest.mock("../../../resources/images/icon-logo-placeholder", () => () => (
  <div>LogoPlaceholder</div>
));

describe("EntityLogoGroup", () => {
  const mockWatchlists = ["watchlist1", "watchlist2"];
  const mockEntities = [
    { acuityId: "12345" },
    { acuityId: "67890" },
    { acuityId: "54321" },
    { acuityId: "09876" }
  ];

  beforeEach(() => {
    GET_WATCH_LIST_ENTITY_API.mockImplementation((watchlist) => ({
      data: {
        entities: mockEntities
      }
    }));

    ENTITY_LOGO_API.mockImplementation((acuityId) => ({
      data: {
        data: [{ logo_url: `http://example.com/logo-${acuityId}.png` }]
      },
      isLoading: false
    }));
  });

  test("renders with different watchlists", () => {
    render(<EntityLogoGroup watchlists={mockWatchlists} />);
    expect(screen.getAllByTestId("entity-logo-group")).toHaveLength(
      mockWatchlists.length
    );
  });

  test("renders LogoPlaceholder on image error", () => {
    ENTITY_LOGO_API.mockImplementationOnce((acuityId) => ({
      data: {
        data: [{ logo_url: "" }]
      },
      isLoading: false
    }));
    render(<EntityLogoGroup watchlists={mockWatchlists} />);
    expect(screen.getByText("LogoPlaceholder")).toBeInTheDocument();
  });

  test("renders with empty watchlists", () => {
    render(<EntityLogoGroup watchlists={[]} />);
    expect(screen.queryByTestId("entity-logo-group")).not.toBeInTheDocument();
  });

  test("getBorderClass returns correct class", () => {
    expect(getBorderClass(0)).toBe(
      "rounded-ss border-l border-t border-neutral-5"
    );
    expect(getBorderClass(1)).toBe(
      "rounded-se border-l border-r border-t border-neutral-5"
    );
    expect(getBorderClass(2)).toBe(
      "rounded-es border border-r-0 border-neutral-5"
    );
    expect(getBorderClass(3)).toBe(
      "rounded-ee border border-r border-t border-neutral-5"
    );
    expect(getBorderClass(4)).toBe("");
  });
});
