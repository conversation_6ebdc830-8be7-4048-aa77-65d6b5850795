import { useState, useEffect } from "react";
import { FaRegFlag } from "react-icons/fa";

// Interface for the props
interface CircularStatusButtonsProps {
  financialData?: any; // Type could be refined based on your actual data structure
  selectedTab?: number; // The currently selected tab
  hasMappedChanges?: number; // Made optional
}

export const CircularStatusButtons = ({ financialData, selectedTab = 0, hasMappedChanges = 0 }: CircularStatusButtonsProps = {}) => {
  // Initialize with default values that will be updated
  const [statusCounts, setStatusCounts] = useState({
    mapped: 0,
    matchFound: 0,
    unmapped: 0,
    noMatchFound: 0
  });

  // Effect to calculate counts whenever financialData, selectedTab, or hasMappedChanges changes
  useEffect(() => {
    if (financialData) {
      // Calculate counts based on financial data and current tab
      calculateMappingCounts(financialData, selectedTab);
    }
  }, [financialData, selectedTab, hasMappedChanges]);

  // Function to calculate counts from financial data for the selected tab
  const calculateMappingCounts = (data: any, tabIndex: number) => {
    try {
      // Get all rows that have mapping data
      let mapped = 0;
      let matchFound = 0;
      let unmapped = 0;
      let noMatchFound = 0;
      // Check if we have tableGroups and process them
      if (data?.tableGroups && data.tableGroups.length > 0) {
        // Only process the currently selected tab's data
        const currentGroup = data.tableGroups[tabIndex];
        if (currentGroup && currentGroup.tables) {
          currentGroup.tables.forEach((table: any) => {
            if (table.rows) {
              table.rows.forEach((row: any) => {
                // Check for mappingId to determine status
                if (row.label && row.label.mappingId !== undefined) {
                  if (row.label.mappingId&&row.label.mappingScore===1) {
                    mapped++; // Has a mappingId
                  } else if (row.label.mappingScore && (row.label.mappingScore >= 0.7 && row.label.mappingScore <= 1)&& row.label.mappingId) {
                    matchFound++; // Has a mapping score but no mappingId
                  } else {
                    unmapped++; // No mappingId and no score
                  }
                } else if (row.label && row.label.text && !row.label.isEmptyRow) {
                  noMatchFound++; // Has text but no mapping information
                }
              });
            }
          });
        }
      }

      setStatusCounts({
        mapped,
        matchFound,
        unmapped,
        noMatchFound
      });
    } catch (error) {
      console.error("Error calculating mapping counts:", error);
      // Fallback to default values if there's an error
      setStatusCounts({
        mapped: 0,
        matchFound: 0,
        unmapped: 0,
        noMatchFound: 0
      });
    }
  };

  const statusButtons = [
    { label: "NC", value: "Need Changes", font: "caption-r", bgColor: "bg-noticeable-50", borderColor: "border-noticeable-70" },
    { label: "Mapped", value: statusCounts.mapped.toString(), font: "caption-r", bgColor: "bg-info-50", borderColor: "border-primary-78" },
    { label: "Match Found", value: statusCounts.matchFound.toString(), font: "caption-r", bgColor: "bg-noticeable-50", borderColor: "border-noticeable-90" },
    { label: "Unmapped", value: statusCounts.unmapped.toString(), font: "caption-r", bgColor: "bg-negative-50", borderColor: "border-negative-100" },
    { label: "No Match Found", value: statusCounts.noMatchFound.toString(), font: "caption-r", bgColor: "bg-[#FAF8EE]", borderColor: "border-yellow-pale-100" }
  ];

  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  
  return (
    <div className="flex items-center space-x-2 px-2">
      {statusButtons.map((button, index) => (
        <button 
          key={index}
          className={`h-6 px-2 flex items-center justify-center ${button.font} rounded-[14px] text-[#1A1A1A] ${button.bgColor} ${button.borderColor} border ${button.label === 'NC' ? 'cursor-default' : 'cursor-pointer'}`}
          title={button.label === "NC" ? button.label : `${button.label} ${button.value}`}
          onMouseEnter={() => setHoveredIndex(index)}
          onMouseLeave={() => setHoveredIndex(null)}
        >
          {button.label === 'NC'
            ? (hoveredIndex === index
                ? <><FaRegFlag className="pr-2 w-6 text-negative-80"/>{button.value}</>
                : <FaRegFlag className="w-6 text-negative-80"/>)
            : (hoveredIndex === index
                ? `${button.value} ${button.label}`
                : button.value)
          }
        </button>
      ))}
    </div>
  );
};
// Default export for easier imports
export default CircularStatusButtons;