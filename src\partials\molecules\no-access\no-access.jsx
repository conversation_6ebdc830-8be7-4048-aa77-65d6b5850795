import React from "react";
import { useAuth } from "react-oidc-context";
import ImageNoAccess from "../../../resources/images/image-no-access";

const NoAccess = () => {
  const auth = useAuth();

  return (
    <div className="flex min-h-screen flex-col items-center justify-center gap-2">
      <ImageNoAccess />
      <span className="text-lg text-primary-90">Unauthorized Access</span>
      <span>You do not have permission to access this application.</span>
      <button
        type="button"
        id="logout-button"
        data-testid="logout-button"
        className="s-r focus-visible:ring-info-500 ml-2 flex h-8 items-center rounded-md border px-4 py-2 text-white focus:outline-none focus-visible:ring-2 focus-visible:ring-offset-2 enabled:bg-primary-78 enabled:hover:bg-primary-90 enabled:focus:bg-primary-100 disabled:bg-primary-60"
        onClick={() => auth.signoutRedirect()}
      >
        Take me to homepage
      </button>
    </div>
  );
};

export default NoAccess;
