import React, { useState } from "react";
import PropTypes from "prop-types";
import { pencilIcon } from "@progress/kendo-svg-icons";
import { HiOutlineTrash } from "react-icons/hi";
import { BiComment } from "react-icons/bi";
import { FiEdit2 } from "react-icons/fi";
import ButtonIcon from "../../partials/atoms/button/button-icon";
import {
  AiOutlineInsertRowLeft,
  AiOutlineInsertRowRight
} from "react-icons/ai";
import { Button } from "../../partials/atoms/button";
import { FaTimes } from "react-icons/fa";
import PeriodHeaderSidebar from '../period-header-sidebar/period-header-sidebar';

/**
 * Parse date string and extract data type, period type, month, quarter, and year.
 * Accepts format like "(Actual) Q1 2022", "(Budget) Jan 2022", "(Forecast) 2022"
 * @param {string|object} date - The date string to parse or columnDetails object
 * @returns {object} - Extracted date components with Period_Type, Data_Type, Period_Year, Period_Month, Period_Quarter
 */
export const parseDateString = (date) => {
  // Default return object for empty or invalid inputs
  const defaultResult = {
    Period_Type: "",
    Data_Type: "",
    Period_Year: new Date().getFullYear(),
    Period_Month: null,
    Period_Quarter: ""
  };
  
  // Handle null/undefined input
  if (!date) return defaultResult;
  
  // Extract string from input (might be object with header/title/Filing_Date properties)
  let dateStr = "";
  if (typeof date === 'string') {
    dateStr = date;
  } else if (typeof date === 'object') {
    // Try to find the date string from various possible properties
    dateStr = date.Filing_Date || date.header || date.title || '';
  }
  
  // If we couldn't extract a valid string, return default
  if (!dateStr) return defaultResult;
  
  // Use regex to extract all parts in a single pass where possible
  const dataTypeMatch = dateStr.match(/\(([^)]+)\)/);
  const dataType = dataTypeMatch ? dataTypeMatch[1] : "";
  
  const quarterMatch = dateStr.match(/Q([1-4])/);
  const quarter = quarterMatch ? quarterMatch[0] : "";
  
  // More efficient month detection with regex
  const monthRegex = /\b(Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec)\b/;
  const monthMatch = dateStr.match(monthRegex);
  const monthStr = monthMatch ? monthMatch[1] : "";
  const month = monthStr ? getMonthName(monthStr) : null;
  
  // Extract year (4 digit number starting with 20)
  const yearMatch = dateStr.match(/\b(20\d{2})\b/);
  const year = yearMatch ? parseInt(yearMatch[1]) : 0;
  
  // Determine period type based on detected components
  let periodType = "";
  if (quarter) {
    periodType = "Quarterly";
  } else if (monthStr) {
    periodType = "Monthly";
  } else if (year > 0) {
    periodType = "Annually";
  }
  
  return {
    Period_Type: periodType,
    Data_Type: dataType,
    Period_Year: year || new Date().getFullYear(),
    Period_Month: month,
    Period_Quarter: quarter
  };
};

const getMonthName = (month) => {
  switch (month) {
    case "Jan":
      return 0;
    case "Feb":
      return 1;
    case "Mar":
      return 2;
    case "Apr":
      return 3;
    case "May":
      return 4;
    case "Jun":
      return 5;
    case "Jul":
      return 6;
    case "Aug":
      return 7;
    case "Sep":
      return 8;
    case "Oct":
      return 9;
    case "Nov":
      return 10;
    case "Dec":
      return 11;
    default:
      return "";
  }
};

const PopupcolumnComponent = ({
  isCheckboxHeader,
  onSave,
  editingColumnKey,
  editingColumnIds,
  selectedTab,
  checkedColumnHeaders,
  columnDetails,
  totalColumns,
  kpiConfig,
  extractionType
}) => {
  const [showEditColumnPopup, setShowEditColumnPopup] = useState(false);
  const [actionType, setActionType] = useState("edit");
  const columnIds = editingColumnIds || (editingColumnKey ? [editingColumnKey] : []);
  if (!isCheckboxHeader) return null;
  const checkedCount = checkedColumnHeaders
    ? Object.values(checkedColumnHeaders).filter(Boolean).length
    : 0;
  const isMultiSelect = checkedCount > 1;
  const handleEditClick = () => {
    setActionType("edit");
    setShowEditColumnPopup(true);
  };
  const handleInsertLeftClick = () => {
    setActionType("insertLeft");
    setShowEditColumnPopup(true);
  };
  const handleInsertRightClick = () => {
    setActionType("insertRight");
    setShowEditColumnPopup(true);
  };
  const handleSave = (values) => {
    if (onSave) {
      onSave({
        Data_Type: values.Data_Type,
        Period_Month: values.Period_Month,
        Period_Quarter: values.Period_Quarter,
        Period_Type:values.Period_Type,
        Period_Year: values.Period_Year,
        columnKey:
          editingColumnKey ||
          (editingColumnIds && editingColumnIds.length === 1
            ? editingColumnIds[0]
            : null),
        tabIndex: selectedTab,
        actionType: actionType
      });
    }

    setShowEditColumnPopup(false);
  };

  const handleCancel = () => {
    setShowEditColumnPopup(false);
    if (onSave) {
      onSave();
    }
  };

  const handleClosePopup = () => {
    if (onSave) {
      onSave();
    }
  };
  const handleDeleteClick = () => {
    if (onSave) {
      onSave({
        actionType: "delete",
        columnKey:
          editingColumnIds && editingColumnIds.length === 1
            ? editingColumnIds[0]
            : null,
        columnIds:
          Object.keys(checkedColumnHeaders).filter(
            (id) => checkedColumnHeaders[id]
          ) || columnIds,
        tabIndex: selectedTab
      });
    }
  };
  const isDeleteDisabled = () => {
    return checkedCount == totalColumns;
  };

  return (
    <>
      <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 !z-[9999]">
        <div className="bg-white flex gap-4 flex-row items-center rounded-lg border border-neutral-10 py-3 px-5 shadow-lg ">
          <div className="font-body-b text-body-b whitespace-nowrap text-neutral-80">
            {checkedCount} column(s) selected:
          </div>
          <div className="flex flex-row items-center gap-2">
            <h2 className="text-body-r font-body-r text-neutral-90 whitespace-nowrap">
              Add Column:
            </h2>
            <ButtonIcon
              intent={"teritory"}
              onClick={handleInsertLeftClick}
              disabled={isMultiSelect}
            >
              <AiOutlineInsertRowLeft className="h-4 w-4" />
            </ButtonIcon>

            <ButtonIcon
              intent={"teritory"}
              onClick={handleInsertRightClick}
              disabled={isMultiSelect}
            >
              <AiOutlineInsertRowRight className="h-4 w-4" />
            </ButtonIcon>
          </div>
          <div className="border border-neutral-10 h-[24px]"></div>

          <Button
            intent={"secondary"}
            className="whitespace-nowrap flex items-center gap-2 text-neutral-90"
            onClick={handleEditClick}
            disabled={isMultiSelect}
          >
            <FiEdit2 icon={pencilIcon} className="w-4 h-4" />
            Edit
          </Button>
          <div className="border border-neutral-10 h-[24px]"></div>
          <div className="flex flex-row items-center gap-2">
            <ButtonIcon intent={"teritory"} disabled={true}>
              <BiComment></BiComment>
            </ButtonIcon>
            <ButtonIcon
              intent={"teritorynegative"}
              onClick={handleDeleteClick}
              disabled={isDeleteDisabled()}
            >
              <HiOutlineTrash />
            </ButtonIcon>
          </div>
          <div className="border border-neutral-10 h-[24px]"></div>
          <div>
            <ButtonIcon intent={"teritory"} onClick={handleClosePopup}>
              <FaTimes className="text-neutral-60" />
            </ButtonIcon>
          </div>
        </div>
      </div>

      {showEditColumnPopup && (
        <PeriodHeaderSidebar
        isOpen={true}
        onClose={handleCancel}
        onSave={handleSave}
        columnId={columnIds.length === 1 ? columnIds[0] : undefined}
        initialValues={parseDateString(columnDetails?.Filing_Date)}
        selectedTab={selectedTab}
        kpiConfig={kpiConfig}
        extractionType={extractionType}
      />
      )}
    </>
  );
};

PopupcolumnComponent.propTypes = {
  isCheckboxHeader: PropTypes.bool,
  onSave: PropTypes.func,
  editingColumnKey: PropTypes.string,
  editingColumnIds: PropTypes.array,
  selectedTab: PropTypes.number,
  checkedColumnHeaders: PropTypes.object,
  columnDetails: PropTypes.object,
  totalColumns: PropTypes.number,
  kpiConfig: PropTypes.arrayOf(PropTypes.shape({
    moduleId: PropTypes.number,
    pageConfigAliasName: PropTypes.string,
    moduleName: PropTypes.string,
    subSectionFields: PropTypes.arrayOf(PropTypes.shape({
      valueTypId: PropTypes.number,
      name: PropTypes.string,
      aliasName: PropTypes.string,
      chartValue: PropTypes.string
    }))
  })),
  extractionType: PropTypes.string
};


export default PopupcolumnComponent;
