import React from "react";
import { render, screen } from "@testing-library/react";
import LoaderWithSpinner from "./loader-with-spinner";

// Mock the SpinnerCircle component
jest.mock("./spinner-circle", () => () => <div data-testid="spinner-circle" />);

describe("LoaderWithSpinner Component", () => {
  test("renders without crashing", () => {
    render(<LoaderWithSpinner />);
    expect(screen.getByTestId("spinner-circle")).toBeInTheDocument();
    expect(screen.getByText("Loading...")).toBeInTheDocument();
  });

  test("displays the SpinnerCircle component", () => {
    render(<LoaderWithSpinner />);
    expect(screen.getByTestId("spinner-circle")).toBeInTheDocument();
  });

  test("displays the loading text", () => {
    render(<LoaderWithSpinner />);
    expect(screen.getByText("Loading...")).toBeInTheDocument();
  });

  test("matches snapshot", () => {
    const { asFragment } = render(<LoaderWithSpinner />);
    expect(asFragment()).toMatchSnapshot();
  });
});
