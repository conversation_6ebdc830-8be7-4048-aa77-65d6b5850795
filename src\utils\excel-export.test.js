import { generateExcelDownload } from './excel-export';

// Mock ExcelJS and file-saver dependencies
jest.mock('exceljs', () => {
  const mockWorksheet = {
    addRow: jest.fn().mockReturnValue({
      height: 30,
      getCell: jest.fn().mockReturnValue({
        style: {}
      })
    }),
    getColumn: jest.fn().mockReturnValue({
      letter: 'A'
    }),
    mergeCells: jest.fn(),
    columns: [],
    views: []
  };

  const mockWorkbook = {
    addWorksheet: jest.fn().mockReturnValue(mockWorksheet),
    xlsx: {
      writeBuffer: jest.fn().mockResolvedValue(new ArrayBuffer(8))
    },
    creator: '',
    lastModifiedBy: '',
    created: null
  };

  return {
    __esModule: true,
    default: jest.fn().mockImplementation(() => mockWorkbook)
  };
});

jest.mock('file-saver', () => ({
  saveAs: jest.fn()
}));

const ExcelJS = require('exceljs');
const { saveAs } = require('file-saver');

describe('Excel Export Utils', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('generateExcelDownload', () => {
    const mockTabData = [
      {
        sheetName: 'Financial Data',
        isCompanyLevel: false,
        currency: 'USD',
        unit: 'Million',
        periods: [
          {
            label: '2024 Q1',
            columns: 2,
            periodId: 'p1',
            selectedKpis: [
              { kpiId: 'k1', text: 'Revenue' },
              { kpiId: 'k2', text: 'Profit' }
            ],
            documentKpis: [
              { kpiId: 'k1', text: 'Total Revenue' },
              { kpiId: 'k2', text: 'Net Profit' }
            ]
          }
        ],
        data: [
          {
            label: 'Company A',
            values: {
              'p1_k1': { value: 1000 },
              'p1_k2': { value: 200 }
            }
          }
        ]
      }
    ];

    test('should create workbook with correct properties', () => {
      generateExcelDownload(mockTabData, 'Test Fund');

      const ExcelJSConstructor = ExcelJS.default || ExcelJS;
      expect(ExcelJSConstructor).toHaveBeenCalled();
    });

    test('should add worksheet for each tab', () => {
      generateExcelDownload(mockTabData, 'Test Fund');

      const workbookInstance = ExcelJS.default.mock.results[0].value;
      expect(workbookInstance.addWorksheet).toHaveBeenCalledWith('Financial Data', {
        properties: {
          tabColor: { argb: '4061C7' }
        }
      });
    });

    test('should handle company level data correctly', () => {
      const companyLevelData = [
        {
          ...mockTabData[0],
          isCompanyLevel: true,
          data: [
            {
              label: 'Company A',
              currencyCode: 'EUR',
              unit: 'Thousand',
              values: {
                'p1_k1': { value: 500 }
              }
            }
          ]
        }
      ];

      generateExcelDownload(companyLevelData, 'Test Fund');

      const workbookInstance = ExcelJS.default.mock.results[0].value;
      const worksheetInstance = workbookInstance.addWorksheet.mock.results[0].value;
      
      // Should call addRow multiple times for headers and data
      expect(worksheetInstance.addRow).toHaveBeenCalled();
    });

    test('should handle array-based data structure', () => {
      const arrayBasedData = [
        {
          ...mockTabData[0],
          data: [
            {
              label: 'Company A',
              values: [
                [
                  { value: 1000 },
                  { value: 200 }
                ]
              ]
            }
          ]
        }
      ];

      expect(() => {
        generateExcelDownload(arrayBasedData, 'Test Fund');
      }).not.toThrow();
    });

    test('should use default fund name when not provided', () => {
      generateExcelDownload(mockTabData);

      // Should not throw error and use default name
      expect(ExcelJS.default).toHaveBeenCalled();
    });

    test('should handle empty periods array', () => {
      const dataWithEmptyPeriods = [
        {
          ...mockTabData[0],
          periods: []
        }
      ];

      expect(() => {
        generateExcelDownload(dataWithEmptyPeriods, 'Test Fund');
      }).not.toThrow();
    });

    test('should handle empty data array', () => {
      const dataWithEmptyRows = [
        {
          ...mockTabData[0],
          data: []
        }
      ];

      expect(() => {
        generateExcelDownload(dataWithEmptyRows, 'Test Fund');
      }).not.toThrow();
    });

    test('should handle missing KPI information gracefully', () => {
      const dataWithMissingKpis = [
        {
          ...mockTabData[0],
          periods: [
            {
              label: '2024 Q1',
              columns: 1,
              periodId: 'p1'
              // Missing selectedKpis and documentKpis
            }
          ]
        }
      ];

      expect(() => {
        generateExcelDownload(dataWithMissingKpis, 'Test Fund');
      }).not.toThrow();
    });

    test('should generate Excel buffer and trigger download', async () => {
      generateExcelDownload(mockTabData, 'Test Fund');

      const workbookInstance = ExcelJS.default.mock.results[0].value;
      
      // Wait for the async operation to complete
      await new Promise(resolve => setTimeout(resolve, 0));
      
      expect(workbookInstance.xlsx.writeBuffer).toHaveBeenCalled();
    });

    test('should create filename with timestamp', async () => {
      generateExcelDownload(mockTabData, 'Test Fund');

      const workbookInstance = ExcelJS.default.mock.results[0].value;
      
      // Wait for the async operation to complete
      await new Promise(resolve => setTimeout(resolve, 0));
      
      expect(saveAs).toHaveBeenCalledWith(
        expect.any(Blob),
        expect.stringMatching(/Test_Fund_\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}\.xlsx/)
      );
    });

    test('should handle fund names with spaces', async () => {
      generateExcelDownload(mockTabData, 'My Test Fund Name');

      // Wait for the async operation to complete
      await new Promise(resolve => setTimeout(resolve, 0));
      
      expect(saveAs).toHaveBeenCalledWith(
        expect.any(Blob),
        expect.stringMatching(/My_Test_Fund_Name_\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}\.xlsx/)
      );
    });

    test('should handle null or undefined cell values', () => {
      const dataWithNullValues = [
        {
          ...mockTabData[0],
          data: [
            {
              label: 'Company A',
              values: {
                'p1_k1': { value: null },
                'p1_k2': { value: undefined }
              }
            }
          ]
        }
      ];

      expect(() => {
        generateExcelDownload(dataWithNullValues, 'Test Fund');
      }).not.toThrow();
    });

    test('should set correct workbook metadata', () => {
      generateExcelDownload(mockTabData, 'Test Fund');

      const workbookInstance = ExcelJS.default.mock.results[0].value;
      expect(workbookInstance.creator).toBe('Foliosure');
      expect(workbookInstance.lastModifiedBy).toBe('Foliosure');
      expect(workbookInstance.created).toBeInstanceOf(Date);
    });
  });
});