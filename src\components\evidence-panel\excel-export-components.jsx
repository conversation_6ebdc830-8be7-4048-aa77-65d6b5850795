import React from "react";
import PropTypes from "prop-types";
import { ExcelExport } from "@progress/kendo-react-excel-export";
import { renderExcelExportColumns } from "./utils";

const ExcelExportComponents = ({
  exportGridOneRef,
  exportGridTwoRef,
  exportGridThreeRef,
  exportGridFourRef,
  incomeStatementData,
  balanceSheetData,
  cashFlowData,
  notesData,
  columnNamesForIncomeStatementData,
  columnNamesForBalanceSheetData,
  columnNamesForCashFlowData,
  columnNamesForNotesData,
  ticker,
  isMapped,
  companyName
}) => {
  // Track header row indices for styling
  const headerRowIndices = [];
  const combinedNotesData = [];
  let currentRowIndex = 0;

  // Create unified columns for notes
  const unifiedNotesColumns = [];

  // Process and collect all unique columns from note collections
  if (columnNamesForNotesData && columnNamesForNotesData.length > 0) {
    // Start with the first collection that has columns
    for (const columns of columnNamesForNotesData) {
      if (columns && columns.length > 0) {
        unifiedNotesColumns.push(...columns);
        break; // Still need to break after finding the first non-empty collection
      }
    }
  }

  // Function to format column headers
  const formatColumnHeader = (key) => {
    if (key.includes("|")) {
      const parts = key.split("|");
      // Use the date part (parts[2]) if it exists, otherwise use "null"
      const datePart = parts.length >= 3 && parts[2] ? parts[2] : "null";
      return `${datePart} | Original | ANL`;
    }
    return key;
  };

  // Prepare the combined notes data with proper header rows
  notesData.forEach((noteCollection, idx) => {
    if (!noteCollection || noteCollection.length === 0) return;

    // Add separator row if not the first collection
    if (idx > 0) {
      // Add empty separator row
      combinedNotesData.push({
        label: "",
        status: "",
        ...Object.fromEntries(
          Object.keys(noteCollection[0]?.cellIds || {}).map((key) => [key, ""])
        )
      });
      currentRowIndex++;
    }

    // Add header row for this note collection and track its index
    headerRowIndices.push(currentRowIndex);

    combinedNotesData.push({
      label: "",
      status: noteCollection.noteLabel,
      ...Object.fromEntries(
        Object.keys(noteCollection[0]?.cellIds || {}).map((key) => [
          key,
          formatColumnHeader(key)
        ])
      )
    });
    currentRowIndex++;

    // Add the collection's data rows
    combinedNotesData.push(...noteCollection);
    currentRowIndex += noteCollection.length;
  });

  // Custom export function for notes data
  const handleNotesExport = () => {
    if (!exportGridFourRef.current) return;

    const workbook = exportGridFourRef.current.workbookOptions();

    // Apply styling to header rows
    if (workbook?.sheets?.[0]) {
      const sheet = workbook.sheets[0];

      headerRowIndices.forEach((index) => {
        if (sheet.rows[index]) {
          if (!sheet.rows[index].cells) {
            sheet.rows[index].cells = [];
          }

          // Apply styling to all cells in header row
          sheet.rows[index].cells.forEach((cell) => {
            if (cell) {
              cell.background = "#0078D7";
              cell.color = "#FFFFFF";
              cell.bold = true;
            }
          });
        }
      });
    }

    exportGridFourRef.current.save(workbook);
  };

  return (
    <>
      <ExcelExport
        data={incomeStatementData}
        fileName={
          isMapped
            ? `${ticker || companyName}_Financials_with_mapping.xlsx`
            : `${ticker || companyName}_Financials_without_mapping.xlsx`
        }
        ref={exportGridOneRef}
      >
        {renderExcelExportColumns(columnNamesForIncomeStatementData, isMapped)}
      </ExcelExport>
      <ExcelExport data={balanceSheetData} ref={exportGridTwoRef}>
        {renderExcelExportColumns(columnNamesForBalanceSheetData, isMapped)}
      </ExcelExport>
      <ExcelExport data={cashFlowData} ref={exportGridThreeRef}>
        {renderExcelExportColumns(columnNamesForCashFlowData, isMapped)}
      </ExcelExport>
    </>
  );
};

ExcelExportComponents.propTypes = {
  exportGridOneRef: PropTypes.object.isRequired,
  exportGridTwoRef: PropTypes.object.isRequired,
  exportGridThreeRef: PropTypes.object.isRequired,
  exportGridFourRef: PropTypes.object.isRequired,
  incomeStatementData: PropTypes.array.isRequired,
  balanceSheetData: PropTypes.array.isRequired,
  cashFlowData: PropTypes.array.isRequired,
  notesData: PropTypes.array.isRequired,
  columnNamesForIncomeStatementData: PropTypes.array.isRequired,
  columnNamesForBalanceSheetData: PropTypes.array.isRequired,
  columnNamesForCashFlowData: PropTypes.array.isRequired,
  columnNamesForNotesData: PropTypes.array.isRequired,
  ticker: PropTypes.string.isRequired,
  isMapped: PropTypes.bool.isRequired,
  companyName: PropTypes.string.isRequired
};

export default ExcelExportComponents;
