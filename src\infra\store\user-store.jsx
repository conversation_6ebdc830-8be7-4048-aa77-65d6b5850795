import { User } from "oidc-client-ts";
import { getEnvironmentConfig } from "./token-keys";

export const getTokenKey = () => {
  const oidcConfig = getEnvironmentConfig();
  return `oidc.user:${oidcConfig.authority}:${oidcConfig.client_id}`;
};

/**
 * Function to get the current user object
 * @returns {User|null} - The user object or null if not found
 */
export const getUser = () => {
  const oidcConfig = getEnvironmentConfig();
  const token_name = `${oidcConfig.client_id}_access_token`;
  const token = getCookie(token_name);
  if (!token) {
    return null;
  }
  return token;
};

export const getToken = () => {
  const token = getUser();
  return token;
};

export const isTokenExist = () => {
  const token = getUser();
  return token !== null;
};

export const getUserName = (auth) => {
  try {
    const user = getUser();
    return user?.profile.preferred_username;
  } catch (error) {
    return "";
  }
};

export const getRoles = () => {
  const user = getUser();
  let roles = user?.profile?.role || [];
  return Array.isArray(roles) ? roles : [roles];
};

/**
 * Function to get the refresh token of the current user
 * @returns {string|null} - The access token or null if not found
 */
export const getRefreshToken = () => {
  const user = getUser();
  return user?.refresh_token;
};

/**
 * Function to get the avatar picture of the current user
 * @returns {string|null} - The avatar picture URL or null if not found
 */
export const getUserAvatarPicture = () => {
  const user = getUser();
  return user?.profile?.picture;
};

/**
 * Function to get the user ID of the current user
 * @returns {string|null} - The user ID or null if not found
 */
export const getUserId = () => {
  const user = getUser();
  return user?.profile?.sub;
};

/**
 * Function to get the full name of the current user
 * @returns {string|null} - The full name or null if not found
 */
export const getUserFullName = () => {
  const user = getUser();
  return user?.profile?.name;
};

/**
 * Function to get the email of the current user
 * @returns {string|null} - The email or null if not found
 */
export const getUserEmail = () => {
  const user = getUser();
  return user?.profile?.email;
};

/**
 * Function to get the token expiration time of the current user
 * @returns {number|null} - The token expiration time in milliseconds or null if not found
 */
export const getTokenTime = () => {
  const user = getUser();
  return user.expires_at * 1000;
};

/**
 * Get a cookie value by name
 * @param {string} cname - The name of the cookie to retrieve
 * @returns {string|null} - The cookie value or null if not found
 */
export const getCookie = (cname) => {
  let name = cname + "=";
  let ca = document.cookie.split(";");
  for (let c of ca) {
    while (c.charAt(0) === " ") {
      c = c.substring(1);
    }
    if (c.indexOf(name) === 0) {
      return c.substring(name.length, c.length);
    }
  }
  return null;
};
