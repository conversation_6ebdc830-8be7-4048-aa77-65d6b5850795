import React from "react";
import PropTypes from "prop-types";
import "./spinner-circle.css";

const SpinnerCircle = ({ size = 6, Loading = "" }) => {
  return (
    <div 
      id="loaderContainer" 
      className="nep-modal nep-modal-show" 
      style={{ display: "block", background: "rgba(0, 0, 0, 0.25)" }}
      data-testid="spinner-circle"
    >
      <div className="nep-modal-mask"></div>
      <div className="loader-body" style={{ display: "inline-flex" }}>
        <div style={{ textAlign: "center" }}>
          <div style={{ display: "flex" }}>
            <div className="nep-spin-ring"></div> 
          </div>
          {Loading && <div className="mt-2">{Loading}</div>}
        </div>
      </div>
    </div>
  );
};

SpinnerCircle.propTypes = {
  size: PropTypes.number,
  Loading: PropTypes.string
};

export default SpinnerCircle;
