import React from 'react';
import { render, screen } from '@testing-library/react';
import GlobalLoadingIndicator from './GlobalLoadingIndicator';

// Mock the loadingEvents module
jest.mock('../../infra/events/loading-events', () => ({
  loadingEvents: {
    subscribe: jest.fn(() => jest.fn()), // Return a mock unsubscribe function
    isLoading: jest.fn(() => false)
  }
}));

// Mock the SpinnerCircle component
jest.mock('../../partials/atoms/loader', () => ({
  SpinnerCircle: () => <div data-testid="spinner-circle">Loading...</div>
}));

describe('GlobalLoadingIndicator Component', () => {
  beforeEach(() => {
    // Mock document.readyState
    Object.defineProperty(document, 'readyState', {
      configurable: true,
      get: jest.fn().mockReturnValue('complete')
    });
    
    // Mock setTimeout
    jest.useFakeTimers();
    
    // Mock window event listeners
    window.addEventListener = jest.fn();
    window.removeEventListener = jest.fn();
  });
  
  afterEach(() => {
    jest.useRealTimers();
    jest.clearAllMocks();
  });
  
  test('renders spinner on initial load', () => {
    render(<GlobalLoadingIndicator />);
    expect(screen.getByTestId('spinner-circle')).toBeInTheDocument();
  });
  
  test('subscribes to loading events', () => {
    const { unmount } = render(<GlobalLoadingIndicator />);
    
    // Should call subscribe
    expect(require('../../infra/events/loading-events').loadingEvents.subscribe).toHaveBeenCalled();
    
    // Unmount to test cleanup
    unmount();
  });

  test('adds event listener when document is loading', () => {
    // Set readyState to 'loading'
    Object.defineProperty(document, 'readyState', {
      configurable: true,
      get: jest.fn().mockReturnValue('loading')
    });
    
    render(<GlobalLoadingIndicator />);
    
    // Should add event listener
    expect(window.addEventListener).toHaveBeenCalledWith('load', expect.any(Function));
  });
  
  test('removes event listener on unmount', () => {
    const { unmount } = render(<GlobalLoadingIndicator />);
    
    // Unmount component
    unmount();
    
    // Should remove event listener
    expect(window.removeEventListener).toHaveBeenCalledWith('load', expect.any(Function));
  });
}); 