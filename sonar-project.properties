# SonarQube server settings
sonar.host.url=https://sast.beatapps.net
sonar.token=sqp_d2b64d16f4a395bb719e68708fb7bd7cc2b932e1

# Project settings
sonar.projectKey=BEAT-FOLIOSURE-INGESTION
sonar.projectVersion=2.30.0

# Source configuration
sonar.sources=src
sonar.exclusions=**/node_modules/**,src/assets/**,**/*.spec.ts,**/*.mock.ts

# Test configuration
sonar.tests=src
sonar.test.inclusions=**/*.spec.ts
sonar.test.exclusions=**/node_modules/**

# Coverage reporting
sonar.javascript.lcov.reportPaths=coverage/BEAT-FOLIOSURE-INGESTION/lcov.info
sonar.typescript.exclusions=**/node_modules/**,**/*.spec.ts
sonar.coverage.exclusions=**/*.mock.ts,**/*.module.ts,**/index.ts
sonar.login=squ_208f7ce9b547abce220a3a8cc78bb748d6863681