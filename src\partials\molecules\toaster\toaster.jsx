import { cva } from "class-variance-authority";
import React from "react";
import toast from "react-hot-toast";
import {
  FaCheckCircle,
  FaExclamationCircle,
  FaInfoCircle
} from "react-icons/fa";
import { FaCircleXmark, FaXmark } from "react-icons/fa6";

export const notify = {
  success: (message) => ToastMessage(message, INTENT.SUCCESS),
  info: (message) => ToastMessage(message, INTENT.INFO),
  warning: (message) => ToastMessage(message, INTENT.WARNING),
  error: (message) => ToastMessage(message, INTENT.ERROR)
};

const config = {
  duration: 3000
};

const INTENT = {
  SUCCESS: "success",
  INFO: "info",
  WARNING: "warning",
  ERROR: "error"
};

const cva_toast_div = cva(
  "body-r pointer-events-auto flex min-h-12 w-auto items-center justify-center gap-3 rounded border p-3 text-neutral-90",
  {
    variants: {
      intent: {
        success: ["bg-positive-60", "border-positive-100"],
        info: ["bg-info-50", "border-info-100"],
        warning: ["bg-noticeable-50", "border-noticeable-100"],
        error: ["bg-negative-60", "border-negative-100"]
      }
    },
    defaultVariants: {
      intent: "success"
    }
  }
);

const ToastMessage = (message, intent) => {
  toast.custom(
    (t) => (
      <div
        className={`${t.visible ? "animate-enter" : "animate-leave"} transition duration-300 ease-in-out ${cva_toast_div({ intent })}`}
      >
        <div>
          {intent === INTENT.SUCCESS && (
            <FaCheckCircle className={`h-3 w-3 text-positive-100`} />
          )}
          {intent === INTENT.INFO && (
            <FaInfoCircle className={`h-3 w-3 text-info-100`} />
          )}
          {intent === INTENT.WARNING && (
            <FaExclamationCircle className={`h-3 w-3 text-noticeable-100`} />
          )}
          {intent === INTENT.ERROR && (
            <FaCircleXmark className={`h-3 w-3 text-negative-100`} />
          )}
        </div>
        <div>{message}</div>
        <div className="pt-0.5">
          <button onClick={() => toast.dismiss(t.id)}>
            <FaXmark className="h-3 w-3 border-neutral-80" />
          </button>
        </div>
      </div>
    ),
    config
  );
};
