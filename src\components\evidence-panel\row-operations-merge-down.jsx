import { notify } from "../../partials/molecules/toaster";
import { EXCLUDED_MERGE_FIELDS, EXCLUDED_FIELD_PREFIXES, NOTIFICATION_MESSAGES, MERGE_ROW_MESSAGES } from "../.././constants";

export const mergeRowBelow = (
  selectedRowIds,
  checkedItems,
  currentTableData,
  setCurrentTableData,
  setTableDataByTab,
  selectedTab,
  setCheckedItems,
  setShowPopup,
  setActiveCheckboxType
) => {
  function handleStateSet()
  {
    setCheckedItems({});
    setShowPopup(false);
  }
  const scrollPosition = document.querySelector('.k-grid-content')?.scrollTop;
  if (selectedRowIds.length !== 1) {
    notify.warning(MERGE_ROW_MESSAGES.SELECT_ONE_ROW);
    handleStateSet();
    return;
  }
  
  const selectedRowId = selectedRowIds[0];
  const selectedIndex = currentTableData.findIndex(row => row.id === selectedRowId);
  
  if (selectedIndex === currentTableData.length - 1) {
    notify.error(MERGE_ROW_MESSAGES.CANNOT_MERGE_LAST_ROW);
    handleStateSet()
    return;
  }
  
  const selectedRow = currentTableData[selectedIndex];
  const rowBelow = currentTableData[selectedIndex + 1];
  
   const hasNonLabelValues = Object.entries(selectedRow)
     .filter(([key]) => {
       return key !== EXCLUDED_MERGE_FIELDS.STATUS && 
              key !== EXCLUDED_MERGE_FIELDS.LABEL && 
              !key.startsWith(EXCLUDED_FIELD_PREFIXES.CELL_IDS) && 
              !key.startsWith(EXCLUDED_FIELD_PREFIXES.PDF_HIGHLIGHTS) && 
              key !== EXCLUDED_MERGE_FIELDS.ID && 
              key !== EXCLUDED_MERGE_FIELDS.DEFINED_NAME && 
              key !== EXCLUDED_MERGE_FIELDS.STYLE &&
              key !== EXCLUDED_MERGE_FIELDS.PERIOD_DATES &&
              key !== EXCLUDED_MERGE_FIELDS.COLUMN_IDS
        ;
     })
     .some(([_, value]) => value !== null && value !== undefined && value !== "");
   
   if (hasNonLabelValues) {
     notify.error(NOTIFICATION_MESSAGES.ROW_HAS_VALUES);
     handleStateSet()
     return;
   }
   const mergedRow = {
     ...rowBelow,
     label: `${rowBelow.label} ${selectedRow.label}`.trim()
   };
  const newData = [...currentTableData];
  newData[selectedIndex] = mergedRow;
  newData.splice(selectedIndex + 1, 1);
   setCurrentTableData(newData);
   setTableDataByTab((prev) => ({
     ...prev,
     [selectedTab]: newData
   }));
   
   handleStateSet()
   setActiveCheckboxType("none");
   notify.success(NOTIFICATION_MESSAGES.MERGE_SUCCESS);
   setHasMappedChanges(prev => prev + 1);
   setHasChanges(true);
   setTimeout(() => {
     const gridContent = document.querySelector('.k-grid-content');
     if (gridContent && scrollPosition !== undefined) {
       gridContent.scrollTop = scrollPosition;
     }
   }, 0);
 };