import React, { useRef, useEffect } from "react";
import PropTypes from "prop-types";
import { Combobox, ComboboxInput, ComboboxButton } from "@headlessui/react";
import { FaTimes } from "react-icons/fa";
import { HiChevronDown } from "react-icons/hi";

const InputComboBox = ({
  query,
  setQuery,
  dropdownOpen,
  setDropdownOpen,
  selectedButton,
  trigger,
  placeHolderText = "",
  headerText = ""
}) => {
  const inputRef = useRef(null);
  const setRefs = (element) => {
    trigger.current = element;
    inputRef.current = element;
  };

  useEffect(() => {
    if (dropdownOpen === true) {
      if (selectedButton?.current !== null || HTMLButtonElement) {
        inputRef.current.focus();
      }
    }
  }, [selectedButton]);

  const handleDropdownToggle = (event) => {
    setQuery("");
    setDropdownOpen(true);
    event.preventDefault();
  };
  return (
    <>
      <div className="caption-m pb-2">{headerText}</div>
      <Combobox as={"div"} className={"relative"} value={query}>
        <ComboboxInput
          id="header-search-input"
          data-testid="header-search-input"
          value={query}
          maxLength={40}
          className="body-r relative h-8 w-full rounded border border-neutral-10 px-4 py-1.5 text-neutral-80 placeholder-neutral-30 hover:border-primary-78 focus:border-primary-90 focus:outline-none focus:ring-0"
          placeholder={placeHolderText}
          autoComplete="off"
          ref={setRefs}
          onClick={() => setDropdownOpen(true)}
          onChange={(e) => {
            setQuery(e.target.value);
            setDropdownOpen(true);
          }}
        ></ComboboxInput>
        <ComboboxButton
          data-testid="header-search-cross"
          className="absolute right-4 top-1 z-30 size-3"
        >
          {query?.length > 0 ? (
            <FaTimes
              id="header-search-cross"
              onClick={(event) => {
                handleDropdownToggle(event);
              }}
              className="absolute size-3 cursor-pointer text-neutral-60"
              strokeWidth={1}
            />
          ) : (
            <HiChevronDown
              onClick={() => setDropdownOpen(true)}
              className="absolute size-4 cursor-pointer text-neutral-60"
              strokeWidth={1}
            />
          )}
        </ComboboxButton>
      </Combobox>
    </>
  );
};

InputComboBox.propTypes = {
  query: PropTypes.string.isRequired,
  setQuery: PropTypes.func.isRequired,
  setDropdownOpen: PropTypes.func.isRequired,
  trigger: PropTypes.oneOfType([
    PropTypes.func,
    PropTypes.shape({ current: PropTypes.instanceOf(Element) })
  ]),
  placeHolderText: PropTypes.string,
  headerText: PropTypes.string,
  dropdownOpen: PropTypes.bool,
  selectedButton: PropTypes.oneOfType([
    PropTypes.func,
    PropTypes.shape({ current: PropTypes.instanceOf(Element) })
  ])
};

export default InputComboBox;
