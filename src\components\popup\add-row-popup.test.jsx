/**
 * @jest-environment jsdom
 */
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import PopupComponent from './add-row-popup';

// Mock the ButtonIcon and Button components
jest.mock('../../partials/atoms/button/button-icon', () => {
  return ({ children, onClick, disabled }) => (
    <button 
      onClick={onClick} 
      disabled={disabled} 
      data-testid="button-icon"
    >
      {children}
    </button>
  );
});

jest.mock('../../partials/atoms/button', () => ({
  Button: ({ children, onClick, disabled }) => (
    <button 
      onClick={onClick} 
      disabled={disabled} 
      data-testid="button"
    >
      {children}
    </button>
  )
}));

describe('PopupComponent', () => {
  // Default props for the component
  const defaultProps = {
    showPopup: true,
    setShowPopup: jest.fn(),
    addRow: jest.fn(),
    addRowToBottom: jest.fn(),
    deleteRow: jest.fn(),
    mergeRowUp: jest.fn(),
    mergeRowBelow: jest.fn(),
    makeHeader: jest.fn(),
    selectedCount: 1,
    clearAllCheckedItems: jest.fn(),
    isAllRowsSelected: false
  };

  // Helper function to render with default or custom props
  const renderPopup = (props = {}) => {
    return render(
      <PopupComponent 
        {...defaultProps}
        {...props}
      />
    );
  };

  // Test basic rendering
  test('renders when showPopup is true', () => {
    renderPopup();
    
    // Check that the popup content is rendered
    expect(screen.getByText('1 row(s) selected:')).toBeInTheDocument();
    expect(screen.getByText('Merge row:')).toBeInTheDocument();
    expect(screen.getByText('Add row:')).toBeInTheDocument();
    expect(screen.getByText('Make Header')).toBeInTheDocument();
  });

  // Test that popup doesn't render when showPopup is false
  test('does not render when showPopup is false', () => {
    renderPopup({ showPopup: false });
    
    // Check that the popup content is not rendered
    expect(screen.queryByText('row(s) selected:')).not.toBeInTheDocument();
  });

  // Test that the selectedCount is displayed correctly
  test('displays the correct number of selected rows', () => {
    renderPopup({ selectedCount: 3 });
    
    // Check that the count is displayed
    expect(screen.getByText('3 row(s) selected:')).toBeInTheDocument();
  });

  // Test the merge row up button
  test('calls mergeRowUp and closes popup when merge up button is clicked', () => {
    renderPopup();
    
    // Find all button-icon elements (there are multiple)
    const buttons = screen.getAllByTestId('button-icon');
    
    // The first button should be the merge up button (based on component structure)
    fireEvent.click(buttons[0]);
    
    // Check that the function was called and popup was closed
    expect(defaultProps.mergeRowUp).toHaveBeenCalledTimes(1);
    expect(defaultProps.setShowPopup).toHaveBeenCalledWith(false);
  });

  // Test the merge row down button
  test('calls mergeRowBelow and closes popup when merge down button is clicked', () => {
    renderPopup();
    
    // Find all button-icon elements
    const buttons = screen.getAllByTestId('button-icon');
    
    // The second button should be the merge down button
    fireEvent.click(buttons[1]);
    
    // Check that the function was called and popup was closed
    expect(defaultProps.mergeRowBelow).toHaveBeenCalledTimes(1);
    expect(defaultProps.setShowPopup).toHaveBeenCalledWith(false);
  });

  // Test the add row above button
  test('calls addRow and closes popup when add row above button is clicked', () => {
    renderPopup();
    
    // Find all button-icon elements
    const buttons = screen.getAllByTestId('button-icon');
    
    // Find the add row above button (third button after merge up/down)
    fireEvent.click(buttons[2]);
    
    // Check that the function was called and popup was closed
    expect(defaultProps.addRow).toHaveBeenCalledTimes(1);
    expect(defaultProps.setShowPopup).toHaveBeenCalledWith(false);
  });

  // Test the add row below button
  test('calls addRowToBottom and closes popup when add row below button is clicked', () => {
    renderPopup();
    
    // Find all button-icon elements
    const buttons = screen.getAllByTestId('button-icon');
    
    // Find the add row below button (fourth button)
    fireEvent.click(buttons[3]);
    
    // Check that the function was called and popup was closed
    expect(defaultProps.addRowToBottom).toHaveBeenCalledTimes(1);
    expect(defaultProps.setShowPopup).toHaveBeenCalledWith(false);
  });

  // Test the make header button
  test('calls makeHeader when make header button is clicked', () => {
    renderPopup();
    
    // Find the button
    const makeHeaderButton = screen.getByText('Make Header');
    
    // Click the button
    fireEvent.click(makeHeaderButton);
    
    // Check that the function was called
    expect(defaultProps.makeHeader).toHaveBeenCalledTimes(1);
  });

  // Test the delete button
  test('calls deleteRow and closes popup when delete button is clicked', () => {
    renderPopup();
    
    // Find all button-icon elements
    const buttons = screen.getAllByTestId('button-icon');
    
    // Find the delete button (sixth button)
    fireEvent.click(buttons[5]);
    
    // Check that the function was called and popup was closed
    expect(defaultProps.deleteRow).toHaveBeenCalledTimes(1);
    expect(defaultProps.setShowPopup).toHaveBeenCalledWith(false);
  });

  // Test the close button
  test('calls clearAllCheckedItems and closes popup when close button is clicked', () => {
    renderPopup();
    
    // Find all button-icon elements
    const buttons = screen.getAllByTestId('button-icon');
    
    // Find the close button (last button)
    fireEvent.click(buttons[6]);
    
    // Check that the functions were called
    expect(defaultProps.clearAllCheckedItems).toHaveBeenCalledTimes(1);
    expect(defaultProps.setShowPopup).toHaveBeenCalledWith(false);
  });

  // Test multi-select disables certain buttons
  test('disables merge and add row buttons when multiple rows are selected', () => {
    renderPopup({ selectedCount: 2 });
    
    // Find all button-icon elements
    const buttons = screen.getAllByTestId('button-icon');
    
    // The first four buttons should be disabled (merge up/down, add above/below)
    expect(buttons[0]).toBeDisabled();
    expect(buttons[1]).toBeDisabled();
    expect(buttons[2]).toBeDisabled();
    expect(buttons[3]).toBeDisabled();
    
    // Make header button should also be disabled
    const makeHeaderButton = screen.getByText('Make Header');
    expect(makeHeaderButton).toBeDisabled();
  });

  // Test delete button is disabled when all rows are selected
  test('disables delete button when all rows are selected', () => {
    renderPopup({ isAllRowsSelected: true });
    
    // Find all button-icon elements
    const buttons = screen.getAllByTestId('button-icon');
    
    // The delete button (sixth button) should be disabled
    expect(buttons[5]).toBeDisabled();
  });
}); 