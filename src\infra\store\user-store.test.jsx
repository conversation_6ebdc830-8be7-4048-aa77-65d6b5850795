import {
  getToken,
  isTokenExist,
  getTokenKey,
  getUserName,
  getR<PERSON>s,
  getUser,
  getRefreshToken,
  getUserAvatarPicture,
  getUserId,
  getUserEmail,
  getTokenTime
} from "./user-store";
import { oidcConfig, config } from "../../constants/config";

// jest.mock('../../user-store.jsx', () => ({
//   getToken: jest.fn(),
// }));
const token = {
  token_type: "Bearer",
  access_token: "",
  profile: {
    email: "test",
    role: config.adminRole
  }
};

describe("user-store", () => {
  beforeEach(() => {
    Object.defineProperty(window, "localStorage", {
      value: {
        getItem: jest.fn(() => {
          return JSON.stringify(token);
        }),
        setItem: jest.fn(() => null)
      },
      writable: true
    });
    
    // Mock document.cookie
    Object.defineProperty(document, 'cookie', {
      writable: true,
      value: `${getTokenKey()}_access_token=mockToken`
    });
  });

  describe("getToken", () => {
    it("should return the token as a string", () => {
      const result = getToken();
      expect(result).toBe("mockToken");
    });
  });

  describe("isTokenExist", () => {
    it("should return true if the token exists", () => {
      const result = isTokenExist();
      expect(result).toBe(true);
    });
  });

  describe("getUserName", () => {
    it("should return the user email", () => {
      const expected = "test";
      const result = getUserName();
      expect(result).toBe(expected);
    });
  });

  describe("getRoles", () => {
    it("getRoles should return roles", () => {
      const tokenType = "Bearer";
      const accessToken = "123abc";
      const mockLocalStorageItem = JSON.stringify({
        token_type: tokenType,
        access_token: accessToken,
        role: config.adminRole
      });
      global.localStorage.setItem(
        `oidc.user:${oidcConfig.authority}:${oidcConfig.client_id}`,
        mockLocalStorageItem
      );

      const result = getRoles();

      expect(result).toStrictEqual([config.adminRole]);
    });
  });

  describe("getUser", () => {
    it("should return the user", () => {
      const result = getUser();
      expect(result).not.toBeNull();
    });
  });

  describe("getRefrshToken", () => {
    it("should return the user", () => {
      const result = getRefreshToken();
      expect(result).not.toBeNull();
    });
  });

  describe("getUserAvatarPicture", () => {
    it("should return the getUserAvatarPicture", () => {
      const result = getUserAvatarPicture();
      expect(result).not.toBeNull();
    });
  });

  describe("getUserId", () => {
    it("should return the getUserId", () => {
      const result = getUserId();
      expect(result).not.toBeNull();
    });
  });

  describe("getUserEmail", () => {
    it("should return the getUserEmail", () => {
      const result = getUserEmail();
      expect(result).not.toBeNull();
    });
  });

  describe("getTokenTime", () => {
    it("should return the getTokenTime", () => {
      const result = getTokenTime();
      expect(result).not.toBeNull();
    });
  });
});
