import { fireEvent, render, screen } from "@testing-library/react";
import KendoGrid from "./kendo-grid";
import TestAppRenderer from "../../../infra/test-utils/test-app-renderer";
import { process } from "@progress/kendo-data-query";
import { setGroupIds } from "@progress/kendo-react-data-tools";
describe("Component KendoGrid render", () => {
  it("should render KendoGrid", () => {
    const mockData = [
      {
        acuityId: "INPU51653",
        acuitySecurityId: "XNSE0060333PUTCS",
        companyName: "Tata Consultancy Services",
        doc: null,
        docDescription: "",
        docId:
          "3I6n7cTZyeaLm3-GwuDG5MLZydPVxqXI2tPUys2MyMLowo6Ni4es0d6am46Gwq2n0pOWrJeelJ6VmJrV3MeQ4Q",
        fileName: "Tata Consultancy Services_TCS_ESG Report_2024-05-07",
        filingDate: "May 07, 2024",
        filingType: "ESG Report",
        id: "668c255a7dac50066bac7dc8",
        industry: "Information Technology",
        reportDate: null,
        ticker: "TCS"
      }
    ];
    let dataState = {
      take: 100,
      skip: 0,
      group: [],
      sort: [{ field: "userName", dir: "asc" }]
    };
    let dataResult = process(mockData, dataState);
    const processWithGroups = (data, dataState) => {
      const newDataState = process(data, dataState);
      setGroupIds({
        data: newDataState.data,
        group: dataState.group
      });
      return newDataState;
    };
    render(
      <TestAppRenderer>
        <KendoGrid
          dataId={"userId"}
          data={mockData}
          dataState={dataState}
          dataResult={dataResult}
          resultState={{}}
          dataStateChange={{}}
          newData={mockData}
          currentSelectedState={{}}
          setCurrentSelectedState={() => {}}
          processWithGroups={processWithGroups}
          setDataResult={() => {}}
          filterValue={""}
        />
      </TestAppRenderer>
    );
    expect(screen.getByTestId("kendo-grid")).toBeInTheDocument();

    const checkBox = screen.getAllByLabelText("Select Row")[0];
    fireEvent.click(checkBox);
    fireEvent.click(checkBox);
    const checkBox1 = screen.getAllByLabelText("Select Row")[1];
    fireEvent.click(checkBox1);
  });
});
