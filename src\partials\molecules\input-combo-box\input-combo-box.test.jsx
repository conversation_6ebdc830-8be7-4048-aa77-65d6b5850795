import { render, screen, fireEvent } from "@testing-library/react";
import InputComboBox from "./input-combo-box";
import React from "react";

describe("InputComboBox component", () => {
  test("renders the component correctly", () => {
    const mockSetQuery = jest.fn();
    const mockSetDropdownOpen = jest.fn();
    const mockTriggerRef = { current: null };
    const mockSelectedButtonRef = { current: null };

    render(
      <InputComboBox
        query=""
        setQuery={mockSetQuery}
        dropdownOpen={false}
        setDropdownOpen={mockSetDropdownOpen}
        trigger={mockTriggerRef}
        selectedButton={mockSelectedButtonRef}
        placeHolderText="Search..."
        headerText="Search Header"
      />
    );

    fireEvent.change(screen.getByTestId("header-search-input"), {
      target: { value: "test" }
    });
    expect(mockSetQuery).toHaveBeenCalledWith("test");
  });
});
