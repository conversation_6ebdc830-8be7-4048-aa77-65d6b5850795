import { DeleteFile } from "../../../infra/api/company/delete-document-service";

const CONSTANTS = {
  DELETE_FAILED_ERROR: "Failed to delete file",
};

export const useFileDelete = (
  files,
  setFiles,
  fileDocumentMap,
  setFileDocumentMap,
  setProgress,
  setFileCount,
  onUploadedFilesCountChange,
  uploadDocumentDetails,
  setUploadDocumentDetails,
  setUploadKey
) => {
  // Helper function to update UI after successful deletion
  const updateUIAfterSuccessfulDeletion = (fileUid) => {
    // Use functional updates to ensure we're working with the latest state
    setFiles(prevFiles => prevFiles.filter(file => file.uid !== fileUid));

    // Update document mappings
    setFileDocumentMap(prevMap => {
      const newMap = { ...prevMap };
      delete newMap[fileUid];
      return newMap;
    });

    // Update progress tracking
    setProgress(prevProgress => prevProgress.filter(p => p.fileId !== fileUid));

    // Update file count
    setFileCount(prevCount => {
      const newCount = Math.max(0, prevCount - 1);
      onUploadedFilesCountChange(newCount);
      return newCount;
    });

    // Force re-render
    setUploadKey(prevKey => prevKey + 1);
  };

  // Helper function to mark file with error
  const markFileWithError = (fileUid, errorMessage) => {
    setFiles(prevFiles => {
      return prevFiles.map(file => {
        if (file.uid === fileUid) {
          return {
            ...file,
            errorMessage,
            deleteFailed: true
          };
        }
        return file;
      });
    });
    
    setUploadKey(prevKey => prevKey + 1);
  };

  const handleDeleteFile = async (fileUid) => {
    // Get file details early
    const fileWithDetails = files.find(file => file.uid === fileUid);
    
    if (!fileWithDetails) {
      console.error("File not found in state:", fileUid);
      return;
    }

    // For files with errors or non-PDF files, just remove from UI
    if (fileWithDetails.errorMessage || !fileWithDetails.uploadComplete) {
      updateUIAfterSuccessfulDeletion(fileUid);
      return;
    }

    // Try to get document ID from all possible sources
    const documentIdFromMap = fileDocumentMap[fileUid];
    const documentIdFromFile = fileWithDetails.documentDetails?.documentId;
    const documentId = documentIdFromMap || documentIdFromFile;

    if (!documentId) {
      console.warn("No document ID found for file:", fileUid);
      updateUIAfterSuccessfulDeletion(fileUid);
      return;
    }

    // Find document details
    const documentToDelete = uploadDocumentDetails.find(
      doc => doc.documentId === documentId
    );

    // If document details not found in parent state
    if (!documentToDelete) {
      if (fileWithDetails.documentDetails) {
        try {
          const response = await DeleteFile(
            documentIdFromFile,
            fileWithDetails.documentDetails.url
          );

          if (response === true) {
            updateUIAfterSuccessfulDeletion(fileUid);
            setUploadDocumentDetails(prevDocs => 
              prevDocs.filter(doc => doc.documentId !== documentId)
            );
          } else {
            markFileWithError(fileUid, CONSTANTS.DELETE_FAILED_ERROR);
          }
        } catch (error) {
          markFileWithError(fileUid, CONSTANTS.DELETE_FAILED_ERROR);
        }
        return;
      }
      
      updateUIAfterSuccessfulDeletion(fileUid);
      return;
    }

    // Use document details from parent state
    try {
      const response = await DeleteFile(
        documentToDelete.documentId,
        documentToDelete.url
      );

      if (response === true) {
        updateUIAfterSuccessfulDeletion(fileUid);
        setUploadDocumentDetails(prevDocs => 
          prevDocs.filter(doc => doc.documentId !== documentId)
        );
      } else {
        markFileWithError(fileUid, CONSTANTS.DELETE_FAILED_ERROR);
      }
    } catch (error) {
      markFileWithError(fileUid, CONSTANTS.DELETE_FAILED_ERROR);
    }
  };

  return {
    handleDeleteFile
  };
};