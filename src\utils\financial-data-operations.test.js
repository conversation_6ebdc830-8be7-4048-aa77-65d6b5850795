import {
  updateFinancialDataAfterRowAddition,
  updateFinancialDataAfterRowDeletion,
  updateCircularStatusButtonDataMapping,
  convertPositionToPercentage,
  createPdfHighlight,
  convertBoundingRectToPercentage,
  SpecificKpiY2Add
} from './financial-data-operations';

describe('Financial Data Operations', () => {
  const mockFinancialData = {
    tableGroups: [
      {
        tables: [
          {
            rows: [
              {
                label: {
                  id: 'row1',
                  text: 'Revenue',
                  style: 'lineitem',
                  mapping: 'revenue',
                  mappingId: 1,
                  mappingScore: 0.9
                },
                cells: [
                  { columnKey: 'value_1', value: 1000 },
                  { columnKey: 'value_2', value: 1200 }
                ]
              },
              {
                label: {
                  id: 'row2',
                  text: 'Expenses',
                  style: 'lineitem',
                  mapping: 'expenses',
                  mappingId: 2,
                  mappingScore: 0.8
                },
                cells: [
                  { columnKey: 'value_1', value: 800 },
                  { columnKey: 'value_2', value: 900 }
                ]
              }
            ]
          }
        ]
      }
    ]
  };

  const mockSetStatusFinancialData = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('updateFinancialDataAfterRowAddition', () => {
    const newRow = {
      id: 'row3',
      label: 'Profit',
      style: 'lineitem',
      status: 'profit',
      mappingId: 3,
      mappingScore: 0.95,
      value_1: 200,
      value_2: 300
    };

    test('should add new row at correct position', () => {
      const result = updateFinancialDataAfterRowAddition(
        mockFinancialData,
        0, // selectedTab
        1, // selectedIndex
        newRow,
        mockSetStatusFinancialData
      );

      expect(result.tableGroups[0].tables[0].rows).toHaveLength(3);
      expect(result.tableGroups[0].tables[0].rows[1].label.text).toBe('Profit');
      expect(mockSetStatusFinancialData).toHaveBeenCalledWith(result);
    });

    test('should create correct row structure for new row', () => {
      const result = updateFinancialDataAfterRowAddition(
        mockFinancialData,
        0,
        0,
        newRow,
        mockSetStatusFinancialData
      );

      const addedRow = result.tableGroups[0].tables[0].rows[0];
      expect(addedRow.label).toEqual({
        id: 'row3',
        text: 'Profit',
        style: 'lineitem',
        mapping: 'profit',
        mappingId: 3,
        mappingScore: 0.95
      });
      
      expect(addedRow.cells).toEqual([
        { columnKey: 'value_1', value: 200 },
        { columnKey: 'value_2', value: 300 }
      ]);
    });

    test('should handle header style correctly', () => {
      const headerRow = { ...newRow, style: 'header' };
      const result = updateFinancialDataAfterRowAddition(
        mockFinancialData,
        0,
        0,
        headerRow,
        mockSetStatusFinancialData
      );

      expect(result.tableGroups[0].tables[0].rows[0].label.style).toBe('header');
    });

    test('should return null for invalid financial data', () => {
      const invalidData = { tableGroups: [] };
      const result = updateFinancialDataAfterRowAddition(
        invalidData,
        0,
        0,
        newRow,
        mockSetStatusFinancialData
      );

      expect(result).toBeNull();
      expect(mockSetStatusFinancialData).not.toHaveBeenCalled();
    });

    test('should handle missing values in new row', () => {
      const incompleteRow = {
        id: 'row4',
        label: 'Incomplete'
      };

      const result = updateFinancialDataAfterRowAddition(
        mockFinancialData,
        0,
        0,
        incompleteRow,
        mockSetStatusFinancialData
      );

      const addedRow = result.tableGroups[0].tables[0].rows[0];
      expect(addedRow.label.mappingId).toBe(0);
      expect(addedRow.label.mappingScore).toBeNull();
      expect(addedRow.cells).toEqual([]);
    });
  });

  describe('updateFinancialDataAfterRowDeletion', () => {
    test('should delete rows with specified IDs', () => {
      const result = updateFinancialDataAfterRowDeletion(
        mockFinancialData,
        0,
        ['row1'],
        mockSetStatusFinancialData
      );

      expect(result.tableGroups[0].tables[0].rows).toHaveLength(1);
      expect(result.tableGroups[0].tables[0].rows[0].label.id).toBe('row2');
      expect(mockSetStatusFinancialData).toHaveBeenCalledWith(result);
    });

    test('should delete multiple rows', () => {
      const result = updateFinancialDataAfterRowDeletion(
        mockFinancialData,
        0,
        ['row1', 'row2'],
        mockSetStatusFinancialData
      );

      expect(result.tableGroups[0].tables[0].rows).toHaveLength(0);
    });

    test('should handle invalid financial data gracefully', () => {
      const invalidData = { tableGroups: [] };
      const result = updateFinancialDataAfterRowDeletion(
        invalidData,
        0,
        ['row1'],
        mockSetStatusFinancialData
      );

      expect(result).toBeUndefined();
      expect(mockSetStatusFinancialData).not.toHaveBeenCalled();
    });

    test('should not delete non-existent row IDs', () => {
      const result = updateFinancialDataAfterRowDeletion(
        mockFinancialData,
        0,
        ['non-existent'],
        mockSetStatusFinancialData
      );

      expect(result.tableGroups[0].tables[0].rows).toHaveLength(2);
    });
  });

  describe('updateCircularStatusButtonDataMapping', () => {
    test('should update mapping for specified row', () => {
      const dataItem = { id: 'row1' };
      const mappingValue = { 
        stext: 'new_mapping',
        mappingId: 10
      };

      updateCircularStatusButtonDataMapping(
        mockFinancialData,
        dataItem,
        mappingValue,
        0
      );

      const updatedRow = mockFinancialData.tableGroups[0].tables[0].rows.find(
        row => row.label.id === 'row1'
      );
      
      expect(updatedRow.label.mapping).toBe('new_mapping');
      expect(updatedRow.label.mappingId).toBe(10);
      expect(updatedRow.label.mappingScore).toBe(1);
    });

    test('should handle mapping_id field name', () => {
      const dataItem = { id: 'row1' };
      const mappingValue = { 
        stext: 'new_mapping',
        mapping_id: 15
      };

      updateCircularStatusButtonDataMapping(
        mockFinancialData,
        dataItem,
        mappingValue,
        0
      );

      const updatedRow = mockFinancialData.tableGroups[0].tables[0].rows.find(
        row => row.label.id === 'row1'
      );
      
      expect(updatedRow.label.mappingId).toBe(15);
    });
  });

  describe('convertPositionToPercentage', () => {
    test('should convert position to percentage correctly', () => {
      const position = {
        boundingRect: {
          x1: 100,
          y1: 50,
          x2: 200,
          y2: 150,
          width: 612,
          height: 792
        },
        pageNumber: 1
      };

      const result = convertPositionToPercentage(position);

      expect(result.bounds).toEqual([
        100 / 612,
        50 / 792,
        100 / 612,  // width percentage
        100 / 792   // height percentage
      ]);
      expect(result.pageNumber).toBe(1);
    });

    test('should handle invalid position object', () => {
      const invalidPosition = { someOtherProp: 'value' };
      
      const result = convertPositionToPercentage(invalidPosition);

      expect(result.bounds).toEqual([0, 0, 0, 0]);
      expect(result.pageNumber).toBe(1);
    });

    test('should use default dimensions when not provided', () => {
      const position = {
        boundingRect: {
          x1: 100,
          y1: 50,
          x2: 200,
          y2: 150
        },
        pageNumber: 2
      };

      const result = convertPositionToPercentage(position);

      expect(result.pageNumber).toBe(2);
      expect(result.bounds).toHaveLength(4);
    });
  });

  describe('createPdfHighlight', () => {
    test('should create PDF highlight object', () => {
      const pdfHighlights = {
        column1: {
          text: 'Sample text',
          bounds: [0.1, 0.2, 0.3, 0.4],
          pageNumber: 1,
          fileKey: 'file123'
        }
      };

      const result = createPdfHighlight(pdfHighlights, 'column1');

      expect(result).toEqual({
        text: 'Sample text',
        bounds: [0.1, 0.2, 0.3, 0.4],
        pageNumber: 1,
        fileKey: 'file123'
      });
    });

    test('should return null for non-existent column', () => {
      const pdfHighlights = {};
      const result = createPdfHighlight(pdfHighlights, 'nonExistent');

      expect(result).toBeNull();
    });

    test('should handle missing pageNumber with default', () => {
      const pdfHighlights = {
        column1: {
          text: 'Sample text',
          bounds: [0.1, 0.2, 0.3, 0.4],
          fileKey: 'file123'
        }
      };

      const result = createPdfHighlight(pdfHighlights, 'column1');

      expect(result.pageNumber).toBe(1);
    });
  });

  describe('convertBoundingRectToPercentage', () => {
    test('should convert bounding rect to percentage', () => {
      const position = {
        boundingRect: {
          x1: 100,
          y1: 50,
          x2: 200,
          y2: 150,
          width: 612,
          height: 792
        },
        pageNumber: 2
      };

      const result = convertBoundingRectToPercentage(position);

      expect(result.bounds).toEqual([
        100 / 612,
        50 / 792,
        200 / 612,
        150 / 792
      ]);
      expect(result.pageNumber).toBe(2);
    });

    test('should handle invalid bounding rect', () => {
      const position = { pageNumber: 1 };
      
      const result = convertBoundingRectToPercentage(position);

      expect(result.bounds).toEqual([0, 0, 0, 0]);
      expect(result.pageNumber).toBe(1);
    });
  });

  describe('SpecificKpiY2Add constant', () => {
    test('should have correct value', () => {
      expect(SpecificKpiY2Add).toBe(0.01);
    });
  });
});