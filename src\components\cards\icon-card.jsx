import React from "react";
import PropTypes from "prop-types";

const IconCard = ({ icon: Icon, header, subtitle, onClick, disabled }) => {
  const handleClick = (e) => {
    e.preventDefault();
    if (onClick && !disabled) {
      onClick();
    }
  };

  return (
    <div
      onClick={handleClick}
      className={`flex flex-col  w-[424px] rounded-xl border border-neutral-5 px-6 py-8  cursor-pointer ${
        disabled
          ? "bg-zinc-50 cursor-not-allowed"
          : "hover:bg-primary-35 active:bg-primary-40 cursor-pointer"
      }`}
    >
      <div className="flex items-center gap-4">
        <div
          className="flex-none rounded-full bg-info-50
         hover:bg-blue-200"
        >
          {typeof Icon === "string" ? (
            <img src={Icon} alt="icon" className="" />
          ) : (
            <div className=" text-blue-clear-100">
              <Icon size={24} />
            </div>
          )}
        </div>
        <h3
          className=" text-heading-2-m font-heading-2-m
        text-neutral-90 "
        >
          {header}
        </h3>
      </div>
      <div className=" gap-2 pl-14  ">
        <div className=" text-body-r font-body-r text-neutral-60">
          {subtitle}
        </div>
      </div>
    </div>
  );
};

IconCard.propTypes = {
  icon: PropTypes.oneOfType([PropTypes.elementType, PropTypes.string])
    .isRequired,
  header: PropTypes.string.isRequired,
  subtitle: PropTypes.string.isRequired,
  onClick: PropTypes.func,
  disabled: PropTypes.bool
};

IconCard.defaultProps = {
  disabled: false
};
export default IconCard;
