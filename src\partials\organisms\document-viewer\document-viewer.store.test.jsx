import { act } from "@testing-library/react";
import useDocumentViewerStore from "./document-viewer.store";

describe("screenerStore", () => {
  let store;

  beforeEach(() => {
    store = useDocumentViewerStore.getState();
    act(() => {
      store.initState();
    });
  });

  test("should clear all filters", () => {
    act(() => {
      store.setPreviewOpen(true);
      store.setPreviewClose(true);
      store.setPreviewFilingsId("testis");
      store.setFileUrl("http:test.amazonaws.com");
      store.getFileExtension("http:test.amazonaws.com");
    });
  });
});
