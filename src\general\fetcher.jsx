import request from "./request";

const fetcher = (url, signal) =>
  request.get(url, { signal }).then((res) => res.data);

const post = (url, body, signal) =>
  request.post(url, body, { signal }).then((res) => res.data);

const postFile = (url, body) =>
  request
    .post(url, body, { headers: { "Content-Type": "multipart/form-data" } })
    .then((res) => res.data);

const put = (url, body) => request.put(url, body).then((res) => res.data);

const remove = (url, body) =>
  request.delete(url, { data: body }).then((res) => res.data);

export { fetcher, post, postFile, put, remove };
