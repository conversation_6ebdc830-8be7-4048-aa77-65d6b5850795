import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import CompanyCard from "./CompanyCard";

// Mock the EntityLogo component
jest.mock("../../partials/molecules/entity-logo/entity-logo", () => {
  return function MockedEntityLogo() {
    return <div data-testid="mocked-entity-logo" />;
  };
});

// Mock the StatusBadge component
jest.mock("./StatusBadge", () => {
  return function MockedStatusBadge({ status }) {
    return <div data-testid="mocked-status-badge">{status}</div>;
  };
});

describe("CompanyCard", () => {
  const mockCompany = {
    processId: "123",
    jobId: "JOB123",
    extractionType: "PDF",
    acuityId: "ACU123",
    name: "Test Company",
    ticker: "TST",
    createdOn: "2023-01-01T12:00:00Z",
    status: "Completed"
  };

  const mockOnClick = jest.fn();

  // Save original Date methods
  const originalToLocaleString = Date.prototype.toLocaleString;

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock Date.toLocaleString for consistent output in tests
    Date.prototype.toLocaleString = jest.fn(() => "Jan 01, 2023, 12:00 PM");
  });

  afterEach(() => {
    // Restore original Date methods
    Date.prototype.toLocaleString = originalToLocaleString;
  });

  test("renders company card with correct information", () => {
    render(
      <CompanyCard
        company={mockCompany}
        isSelected={false}
        onClick={mockOnClick}
      />
    );

    expect(screen.getByText(`ID: ${mockCompany.jobId}`)).toBeInTheDocument();
    expect(screen.getByText(mockCompany.extractionType)).toBeInTheDocument();
    expect(screen.getByText(mockCompany.ticker)).toBeInTheDocument();
    expect(screen.getByText(mockCompany.name)).toBeInTheDocument();
    expect(screen.getByTestId("mocked-entity-logo")).toBeInTheDocument();
    expect(screen.getByTestId("mocked-status-badge")).toBeInTheDocument();
  });

  test("calls onClick handler when clicked", () => {
    render(
      <CompanyCard
        company={mockCompany}
        isSelected={false}
        onClick={mockOnClick}
      />
    );

    const card = screen.getByText(mockCompany.name).closest("div");
    fireEvent.click(card);
    expect(mockOnClick).toHaveBeenCalledWith(mockCompany.processId);
  });

  test("applies correct background class based on status", () => {
    // Test with Failed status
    const failedCompany = { ...mockCompany, status: "Failed" };
    const { container, rerender } = render(
      <CompanyCard
        company={failedCompany}
        isSelected={false}
        onClick={mockOnClick}
      />
    );

    const mainDiv = container.firstChild;
    expect(mainDiv.className).toContain("bg-zinc-50");

    // Test with Completed status
    rerender(
      <CompanyCard
        company={{ ...mockCompany, status: "Completed" }}
        isSelected={false}
        onClick={mockOnClick}
      />
    );
    expect(mainDiv.className).toContain("bg-white");
    expect(mainDiv.className).toContain("hover:bg-primary-35");

    // Test with In Progress status
    rerender(
      <CompanyCard
        company={{ ...mockCompany, status: "In Progress" }}
        isSelected={false}
        onClick={mockOnClick}
      />
    );
    expect(mainDiv.className).toContain("bg-zinc-50");
  });
});
