import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import DropDown from "./drop-down";
import { FaAccusoft } from "react-icons/fa";

describe("render Dropdown", () => {
  const setDropdownOpen = jest.fn();
  const triggerRef = { current: document.createElement("div") };

  it("should render DropDown right side correctly", () => {
    render(
      <DropDown align={"right"} icon={<FaAccusoft />}>
        <div>Menu</div>
      </DropDown>
    );
    expect(screen.getByText("Menu")).toBeInTheDocument();
  });

  it("should render DropDown center side correctly", () => {
    render(
      <DropDown align={"center"} icon={<FaAccusoft />}>
        <div>Menu</div>
      </DropDown>
    );
    expect(screen.getByText("Menu")).toBeInTheDocument();
  });

  it("should render DropDown left side correctly", () => {
    render(
      <DropDown align={""} icon={<FaAccusoft />}>
        <div>Menu</div>
      </DropDown>
    );
    expect(screen.getByText("Menu")).toBeInTheDocument();
  });

  it("should render DropDown", () => {
    setDropdownOpen.mockReturnValue(true);
    render(
      <DropDown align={""} icon={<FaAccusoft />}>
        <div>Menu</div>
      </DropDown>
    );
    expect(screen.getByText("Menu")).toBeInTheDocument();

    fireEvent.click(screen.getByTestId("drop-down-list"));

    fireEvent.keyDown(screen.getByTestId("drop-down-list"), {
      key: "Escape",
      code: "Escape",
      keyCode: 27,
      charCode: 27
    });
  });

  it("should close DropDown on pressing Escape key", () => {
    render(
      <DropDown
        align={"right"}
        icon={<FaAccusoft />}
        dropdownOpen={true}
        setDropdownOpen={setDropdownOpen}
        trigger={triggerRef}
      >
        <div>Menu</div>
      </DropDown>
    );
    fireEvent.keyDown(document, {
      key: "Escape",
      code: "Escape",
      keyCode: 27,
      charCode: 27
    });
    expect(setDropdownOpen).toHaveBeenCalledWith(false);
  });
});
