import React, { useState, useEffect } from "react";
import { <PERSON>u, <PERSON>uButton, MenuItem, MenuItems } from "@headlessui/react";
import { HiChevronDown } from "react-icons/hi2";
import PropTypes from "prop-types";


const CurrencyDropdown = ({
  currency,
  currencyList,
  onCurrencyChange
}) => {
  const [currencyItems, setCurrencyItems] = useState([]);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedCurrency, setselectedCurrency] = useState(currency);
  const [searchTerm, setSearchTerm] = useState("");
  
  useEffect(() => {
          setCurrencyItems(currencyList);
          setselectedCurrency(currencyList?.find((opt) => opt.currencyCode === currency)?.currencyCode || "No Currency");
  }, [currencyList, currency]);
  const handleChange = (selectedValue) => {
    // Find the corresponding value for the selected label
    const newCurrency = selectedValue?.currencyCode || "No Currency";
    setselectedCurrency(newCurrency);
    
    // Clear the search term after selection
    setSearchTerm("");
    
    // Call the parent callback to pass the selected currency
    if (onCurrencyChange) {
      onCurrencyChange(selectedValue);
    }
  };
  
  // Filter currency items based on search term
  const filteredCurrencyItems = searchTerm
    ? currencyItems.filter(item => 
        item?.currencyCode?.toLowerCase().includes(searchTerm.toLowerCase()))
    : currencyItems;
  
  return (
    <Menu as="div" className="relative">
      <div>
        <MenuButton
          className={`h-8 w-[135px] body-r items-center px-4 py-1.5 gap-2 border border-neutral-20 currency-radius inline-flex justify-between gap-x-1.5 font-normal ring-gray-300 ring-inset transition-colors 
            ${selectedCurrency == "No Currency" ? "text-neutral-40" : "text-neutral-80"}`}
        >
          {selectedCurrency}
          <HiChevronDown aria-hidden="true" className="text-gray-400" />
        </MenuButton>

        <MenuItems
          transition
          className="absolute min-w-[135px] z-99 mt-2 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black/5 transition focus:outline-none data-[closed]:scale-95 data-[closed]:transform data-[closed]:opacity-0 data-[enter]:duration-100 data-[leave]:duration-75 data-[enter]:ease-out data-[leave]:ease-in max-h-[300px] overflow-y-auto"
        >
          <div className="p-2 border-b border-neutral-20">
            <input
              type="text"
              placeholder="Search..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-2 py-1 text-sm border border-neutral-30 rounded focus:outline-none focus:ring-1 focus:ring-primary-60"
              onClick={(e) => e.stopPropagation()} // Prevent menu from closing on input click
            />
          </div>
          <div className="py-1">
            {filteredCurrencyItems?.length > 0 ? (
              filteredCurrencyItems.map((item, index) => (
                <MenuItem key={index}>
                  {({ focus }) => (
                    <button
                      role="menuitem"
                      className={`block w-[135px] text-left px-4 py-2 text-body-r font-body-r text-neutral-90 ${
                        focus ? "bg-primary-40 text-gray-900" : ""
                      } hover:bg-primary-40 hover:text-primary-78`}
                      onClick={() => handleChange(item)}
                    >
                      {item?.currencyCode}
                    </button>
                  )}
                </MenuItem>
              ))
            ) : (
              <div className="px-4 py-2 text-sm text-neutral-60 text-center">No matches found</div>
            )}
          </div>
        </MenuItems>
      </div>
    </Menu>
  );
};

CurrencyDropdown.propTypes = {
  currency: PropTypes.string.isRequired,
  currencyList: PropTypes.array,
  onCurrencyChange: PropTypes.func
};

export default CurrencyDropdown;
