import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import DocumentViewerLoading from "./document-viewer-loading";
import useDocumentViewerStore from "./document-viewer.store";

jest.mock("./document-viewer.store");
jest.mock("../../../partials/atoms/loader", () => ({
  SpinnerCircle: () => <div data-testid="spinner-circle" />
}));

describe("DocumentViewerLoading", () => {
  test("renders without crashing", () => {
    useDocumentViewerStore.mockReturnValue({ setPreviewClose: jest.fn() });
    render(<DocumentViewerLoading />);
  });

  test("calls setPreviewClose when close button is clicked", () => {
    const setPreviewClose = jest.fn();
    useDocumentViewerStore.mockReturnValue({ setPreviewClose });
    render(<DocumentViewerLoading />);

    const closeButton = screen.getByTestId("close-button");
    fireEvent.click(closeButton);
    expect(setPreviewClose).toHaveBeenCalled();
  });

  test("displays loading spinner", () => {
    useDocumentViewerStore.mockReturnValue({ setPreviewClose: jest.fn() });
    render(<DocumentViewerLoading />);
    expect(screen.getByTestId("spinner-circle")).toBeInTheDocument();
  });

  test("displays loading message", () => {
    useDocumentViewerStore.mockReturnValue({ setPreviewClose: jest.fn() });
    render(<DocumentViewerLoading />);
    expect(screen.getByTestId("loading-message")).toBeInTheDocument();
  });

  it("renders the loading spinner and message", () => {
    render(<DocumentViewerLoading />);
    expect(screen.getByTestId("spinner-circle")).toBeInTheDocument();
    expect(
      screen.getByText("Please wait, we are getting the file ready")
    ).toBeInTheDocument();
  });

  it("has the correct container attributes", () => {
    render(<DocumentViewerLoading />);
    const container = screen.getByTestId("document-viewer-loading");
    expect(container).toBeInTheDocument();
    expect(container).toHaveClass(
      "my-auto flex h-full w-full grow flex-col justify-center text-center"
    );
  });
});
