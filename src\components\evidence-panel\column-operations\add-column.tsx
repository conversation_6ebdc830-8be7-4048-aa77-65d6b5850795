import { v4 as uuidv4 } from "uuid";
import { formatPeriodTypeWithDataType } from "../../../utils/formatPeriodType.tsx";

const getReportingPeriod = (filingType: string): number => {
  switch (filingType) {
    case "ANL":
      return 1;
    case "NME":
      return 2;
    case "HYL":
      return 3;
    case "QTR":
      return 4;
    default:
      return 0;
  }
};

// Format date to DD-MM-YYYY
const formatPeriodDate = (dateString: string) => {
  if (!dateString) return "";

  const date = new Date(dateString);
  const day = String(date.getDate()).padStart(2, "0");
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const year = date.getFullYear();
  return `${day}-${month}-${year}`;
};

interface ColumnDetails {
  Filing_Version: string;
  Filing_Type: string;
  Filing_Date: string;
  Data_Type: string;
  Period_Month: string;
  Period_Quarter: string;
  Period_Type: string;
  Period_Year: string;
}

interface FinancialsData {
  tableGroups: Array<{
    tables: Array<{
      columns: Array<{ columnKey: string }>;
    }>;
  }>;
}

interface ColumnIdMapping {
  [key: string]: string;
}

interface TableDataRow {
  [key: string]: any;
  cellIds: { [key: string]: string };
}

interface Callbacks {
  setColumnIdMapping: (
    callback: (prev: ColumnIdMapping) => ColumnIdMapping
  ) => void;
  setColumnHeaderInfo: (callback: (prev: any) => any) => void;
  setCurrentTableData: (data: TableDataRow[]) => void;
  setTableDataByTab: (callback: (prev: any) => any) => void;
  clearAllCheckedItems: () => void;
  generateColumnId: () => string;
}

export const insertNewColumn = (
  direction: "insertLeft" | "insertRight",
  columnKey: string,
  columnDetails: ColumnDetails,
  financialsData: FinancialsData,
  selectedTab: number,
  columnIdMapping: ColumnIdMapping,
  currentTableData: TableDataRow[],
  callbacks: Callbacks
) => {
  const {
    setColumnIdMapping,
    setColumnHeaderInfo,
    setCurrentTableData,
    setTableDataByTab,
    clearAllCheckedItems,
    generateColumnId
  } = callbacks;
  // Generate a new unique column ID
  const newColumnId = generateColumnId();

  // Find the current table group for the selected tab
  const currentTableGroup = financialsData?.tableGroups?.[selectedTab];
  // Find the index of the selected column
  const columns = currentTableGroup.tables[0].columns;
  let targetColumnIndex = -1;

  // Find target column index based on its UUID
  for (let i = 0; i < columns.length; i++) {
    if (columnIdMapping[columns[i].columnKey] === columnKey) {
      targetColumnIndex = i;
      break;
    }
  }

  // Create new column object with the correct format
  const newColumnKey = newColumnId;
  const { Data_Type, Period_Month, Period_Quarter, Period_Type,Period_Year } = columnDetails;
  const newColumn = {
    columnKey: newColumnId,
    dskey: null,
    title: null,
    filingType: 0,
    reportingPeriod: 0,
    monthEnding: "null",
    periodDate: formatPeriodTypeWithDataType(Period_Type, Period_Month, Period_Quarter, Period_Year, Data_Type),
    periodInfo: "null",
  };

  // Add new column to the columns array at the correct position
  const insertIndex =
    direction === "insertLeft" ? targetColumnIndex : targetColumnIndex + 1;
  const updatedColumns = [
    ...columns.slice(0, insertIndex),
    newColumn,
    ...columns.slice(insertIndex)
  ];

  // Update the financialsData with the new column
  const updatedTableGroups = [...financialsData.tableGroups];
  updatedTableGroups[selectedTab].tables[0].columns = updatedColumns;

  // Add the new column ID to the mapping
  setColumnIdMapping((prev) => ({
    ...prev,
    [newColumnKey]: newColumnId
  }));

  // Store column header info
  setColumnHeaderInfo((prev) => ({
    ...prev,
    [newColumnId]: {
      periodDate: formatPeriodTypeWithDataType(Period_Type, Period_Month, Period_Quarter, Period_Year, Data_Type),
      filingType: "",
      documentStatus: ""
    }
  }));

  // Add new column to all rows in the current table data
  const updatedTableData = currentTableData.map((row) => {
    const updatedRow = { ...row };
    // Add empty value for the new column
    updatedRow[newColumnKey] = "";
    // Add a new unique cellId for the new column
    updatedRow.cellIds = {
      ...updatedRow.cellIds,
      [newColumnKey]: uuidv4()
    };
    return updatedRow;
  });

  // Update the table data
  setCurrentTableData(updatedTableData);
  setTableDataByTab((prev) => ({
    ...prev,
    [selectedTab]: updatedTableData
  }));

  // Clear selection state
  clearAllCheckedItems();

  return newColumnId;
};
