import React from "react";
import {
  render,
  screen,
  fireEvent,
  waitFor,
  act
} from "@testing-library/react";
import SearchCompany from "./search-company";
import { ENTITY_API } from "../../infra/api/company/get-company-service";

jest.mock("../../infra/api/company/get-company-service", () => ({
  ENTITY_API: jest.fn()
}));

jest.mock("../../partials/molecules/entity-logo/entity-logo", () => {
  return {
    __esModule: true,
    default: (props) => <div data-testid="entity-logo">EntityLogo</div>
  };
});
jest.mock("../../partials/molecules/pop-up", () => ({
  PopUp: ({ children, header }) => (
    <div data-testid="confirmation-dialog">
      <h2>{header}</h2>
      {children}
    </div>
  )
}));
jest.mock("../../resources/images/NoResults.svg", () => "no-results.svg");

describe("SearchCompany Component", () => {
  const mockOnCompanySelect = jest.fn();
  const mockCompanyData = [
    {
      acuity_id: "123",
      name: "Test Company",
      security_ticker: "TEST",
      primary_ticker: "TEST",
      is_public: true,
      primary_exchange: "NASDAQ",
      sector: [{ value: "Technology" }],
      headquarter: { country: [{ value: "USA" }] }
    },
    {
      acuity_id: "456",
      name: "Private Corp",
      security_ticker: "",
      primary_ticker: "",
      is_public: false,
      sector: [{ value: "Finance" }],
      headquarter: { country: [{ value: "UK" }] }
    }
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    ENTITY_API.mockImplementation(() => ({
      data: { data: mockCompanyData }
    }));
  });

  test("renders the search input initially", () => {
    render(<SearchCompany onCompanySelect={mockOnCompanySelect} />);

    expect(screen.getByLabelText(/Company name/i)).toBeInTheDocument();
    expect(
      screen.getByPlaceholderText(/Search by company name or ticker/i)
    ).toBeInTheDocument();
  });

  test("shows loading state when searching", async () => {
    ENTITY_API.mockImplementation(
      () =>
        new Promise((resolve) => {
          setTimeout(() => resolve({ data: null }), 500);
        })
    );

    render(<SearchCompany onCompanySelect={mockOnCompanySelect} />);

    const input = screen.getByPlaceholderText(
      /Search by company name or ticker/i
    );
    await act(async () => {
      fireEvent.change(input, { target: { value: "test" } });
    });

    await waitFor(() => {
      expect(screen.getByTestId("spinner-circle")).toBeInTheDocument();
    });
  });
  test("clicking edit button returns to search mode", async () => {
    render(<SearchCompany onCompanySelect={mockOnCompanySelect} />);

    const input = screen.getByPlaceholderText(
      /Search by company name or ticker/i
    );

    await act(async () => {
      const event = { target: { value: mockCompanyData[0] } };
      fireEvent.change(input, event);
    });

    await waitFor(() => {
      expect(
        screen.getByPlaceholderText(/Search by company name or ticker/i)
      ).toBeInTheDocument();
    });
  });
});
