import * as React from "react";
const IconIllustration = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={169}
    height={110}
    fill="none"
    {...props}
  >
    <g clipPath="url(#a)">
      <path
        fill="url(#b)"
        d="M83.579 28.227S51.379 3 45.744 2.117C40.107 1.237 35.2-.64 24.368 1.13 13.537 2.9 4.4 14.18 2.969 20.516c-1.432 6.336-3.466 9.172-.95 20.992 2.518 11.82 7.007 17.465 7.007 17.465l25.18 37.518s6.614 9.895 13.4 12.207c6.786 2.312 17.18-3.746 21.776-5.637C73.978 101.17 90.2 83.637 97.658 84.11c7.46.473 33.468 4.336 33.468 4.336s30.428 13.18 35.256-2.688c4.828-15.868-2.187-39.348-11.705-52.465-9.518-13.117-23.98 1.332-26.361 0-2.381-1.332-44.737-5.066-44.737-5.066Z"
      />
      <g filter="url(#c)">
        <path
          fill="#fff"
          d="M140.158 64.735H28.067a2 2 0 0 0-2 2v21.968a2 2 0 0 0 2 2h112.091a2 2 0 0 0 2-2V66.735a2 2 0 0 0-2-2Z"
        />
        <path
          stroke="#E6E6E6"
          d="M140.158 65.235H28.067a1.5 1.5 0 0 0-1.5 1.5v21.968a1.5 1.5 0 0 0 1.5 1.5h112.091a1.5 1.5 0 0 0 1.5-1.5V66.735a1.5 1.5 0 0 0-1.5-1.5Z"
        />
      </g>
      <path
        fill="#E6E6E6"
        d="M40.578 83.83a6.11 6.11 0 1 0 0-12.22 6.11 6.11 0 0 0 0 12.22ZM64.636 73.9h-8.4a1.146 1.146 0 1 0 0 2.291h8.4a1.146 1.146 0 1 0 0-2.29ZM84.493 73.9h-14.51a1.146 1.146 0 1 0 0 2.291h14.51a1.146 1.146 0 1 0 0-2.29ZM89.84 79.246H55.47a1.146 1.146 0 1 0 0 2.291H89.84a1.146 1.146 0 1 0 0-2.29ZM131.992 71.61h-28.55a1 1 0 0 0-1 1v10.22a1 1 0 0 0 1 1h28.55a1 1 0 0 0 1-1V72.61a1 1 0 0 0-1-1Z"
      />
      <g filter="url(#d)">
        <path
          fill="#fff"
          d="M147.031 41.822H34.94a2 2 0 0 0-2 2V65.79a2 2 0 0 0 2 2H147.03a2 2 0 0 0 2-2V43.822a2 2 0 0 0-2-2Z"
        />
        <path
          stroke="#B3B3B3"
          d="M147.031 42.322H34.94a1.5 1.5 0 0 0-1.5 1.5V65.79a1.5 1.5 0 0 0 1.5 1.5H147.03a1.5 1.5 0 0 0 1.5-1.5V43.822a1.5 1.5 0 0 0-1.5-1.5Z"
        />
      </g>
      <path
        fill="#E6E6E6"
        d="M47.45 60.916a6.11 6.11 0 1 0 0-12.22 6.11 6.11 0 0 0 0 12.22ZM71.51 50.987h-8.402a1.146 1.146 0 1 0 0 2.291h8.402a1.146 1.146 0 1 0 0-2.29ZM91.367 50.987H76.856a1.146 1.146 0 1 0 0 2.291h14.51a1.146 1.146 0 1 0 0-2.29ZM96.713 56.333H62.345a1.146 1.146 0 1 0 0 2.291h34.368a1.146 1.146 0 1 0 0-2.291ZM138.865 48.696h-28.55a1 1 0 0 0-1 1v10.22a1 1 0 0 0 1 1h28.55a1 1 0 0 0 1-1v-10.22a1 1 0 0 0-1-1Z"
      />
      <g filter="url(#e)">
        <path
          fill="#fff"
          d="M138.158 18.91H30.067a4 4 0 0 0-4 4v17.968a4 4 0 0 0 4 4h108.091a4 4 0 0 0 4-4V22.91a4 4 0 0 0-4-4Z"
        />
        <path
          stroke="#93B0ED"
          strokeWidth={1.5}
          d="M138.158 19.66H30.067a3.25 3.25 0 0 0-3.25 3.25v17.968a3.25 3.25 0 0 0 3.25 3.25h108.091a3.25 3.25 0 0 0 3.25-3.25V22.91a3.25 3.25 0 0 0-3.25-3.25Z"
        />
      </g>
      <path
        fill="url(#f)"
        d="M40.578 38.004a6.11 6.11 0 1 0 0-12.22 6.11 6.11 0 0 0 0 12.22Z"
      />
      <path
        fill="#3562CE"
        d="M64.636 28.075h-8.4a1.146 1.146 0 1 0 0 2.291h8.4a1.146 1.146 0 1 0 0-2.29ZM84.493 28.075h-14.51a1.146 1.146 0 1 0 0 2.291h14.51a1.146 1.146 0 1 0 0-2.29ZM89.84 33.42H55.47a1.146 1.146 0 1 0 0 2.292H89.84a1.146 1.146 0 1 0 0-2.291Z"
      />
      <path
        fill="url(#g)"
        d="M131.992 25.784h-28.55a1 1 0 0 0-1 1v10.22a1 1 0 0 0 1 1h28.55a1 1 0 0 0 1-1v-10.22a1 1 0 0 0-1-1Z"
      />
    </g>
    <defs>
      <linearGradient
        id="b"
        x1={84.388}
        x2={84.388}
        y1={0.396}
        y2={109.216}
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#FFF0FC" />
        <stop offset={1} stopColor="#CED4F9" />
      </linearGradient>
      <linearGradient
        id="f"
        x1={35.885}
        x2={47.971}
        y1={34.998}
        y2={26.64}
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#BE439D" />
        <stop offset={1} stopColor="#E5B471" />
      </linearGradient>
      <linearGradient
        id="g"
        x1={105.985}
        x2={117.181}
        y1={34.998}
        y2={15.64}
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#BE439D" />
        <stop offset={1} stopColor="#E5B471" />
      </linearGradient>
      <filter
        id="c"
        width={128.091}
        height={37.968}
        x={20.067}
        y={59.735}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={1} />
        <feGaussianBlur stdDeviation={3} />
        <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.18 0" />
        <feBlend
          in2="BackgroundImageFix"
          result="effect1_dropShadow_1213_11732"
        />
        <feBlend
          in="SourceGraphic"
          in2="effect1_dropShadow_1213_11732"
          result="shape"
        />
      </filter>
      <filter
        id="d"
        width={128.091}
        height={37.968}
        x={26.939}
        y={36.822}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={1} />
        <feGaussianBlur stdDeviation={3} />
        <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.18 0" />
        <feBlend
          in2="BackgroundImageFix"
          result="effect1_dropShadow_1213_11732"
        />
        <feBlend
          in="SourceGraphic"
          in2="effect1_dropShadow_1213_11732"
          result="shape"
        />
      </filter>
      <filter
        id="e"
        width={128.091}
        height={37.968}
        x={20.067}
        y={13.91}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={1} />
        <feGaussianBlur stdDeviation={3} />
        <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.18 0" />
        <feBlend
          in2="BackgroundImageFix"
          result="effect1_dropShadow_1213_11732"
        />
        <feBlend
          in="SourceGraphic"
          in2="effect1_dropShadow_1213_11732"
          result="shape"
        />
      </filter>
      <clipPath id="a">
        <path fill="#fff" d="M.51 0h167.98v109.635H.51z" />
      </clipPath>
    </defs>
  </svg>
);
export default React.memo(IconIllustration);
