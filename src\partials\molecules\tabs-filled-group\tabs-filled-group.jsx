import { Tab, TabGroup, Tab<PERSON>ist, Tab<PERSON>anel, Tab<PERSON>anels } from "@headlessui/react";
import PropTypes from "prop-types";

const TabsFilledGroup = ({
  tabs,
  activeTab,
  setActiveTab,
  disabledTab,
  tabClassName
}) => {
  const EMPTY_STRING = "";

  return (
    <TabGroup className="h-full w-full">
      <TabList className="flex flex-nowrap ">
        {tabs.map((tab) => (
          <Tab
            key={tab.key}
            title={tab.name}
            disabled={tab.key === disabledTab}
            onClick={() => {
              setActiveTab(tab.key);
            }}
            className={`${tabClassName(
              tab
            )} text-neutral-60 body-r flex h-8 border-collapse items-center border px-3 py-1.5 first:rounded-l-md last:rounded-r-md disabled:text-neutral-60 disabled:bg-neutral-5 disabled:cursor-not-allowed ${
              activeTab === tab.key ? "bg-primary-78 text-white" : EMPTY_STRING
            } focus:outline-none  `}
          >
            <span className="inline-block items-center truncate">
              {tab.name}
            </span>
          </Tab>
        ))}
      </TabList>

      <TabPanels>
        {tabs.map((tab) => (
          <TabPanel key={tab.key}>{tab.component}</TabPanel>
        ))}
      </TabPanels>
    </TabGroup>
  );
};

TabsFilledGroup.propTypes = {
  tabs: PropTypes.arrayOf(
    PropTypes.shape({
      name: PropTypes.string,
      key: PropTypes.string,
      component: PropTypes.node
    })
  ),
  activeTab: PropTypes.string.isRequired,
  setActiveTab: PropTypes.func.isRequired,
  disabledTab: PropTypes.string,
  tabClassName: PropTypes.string
};

export default TabsFilledGroup;
