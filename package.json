{"name": "beat-acuityone-spreading-ui", "description": "A React-based user interface for the BEAT Acuity One Spreading application, designed to facilitate the spreading of financial reports for private and public companies.", "version": "0.0.1", "private": true, "dependencies": {"@babel/runtime": "^7.26.0", "@brui/user-management": "^1.0.1-20250123063844-beta", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@headlessui/react": "^2.2.0", "@mui/material": "^7.2.0", "@progress/kendo-data-query": "^1.7.1", "@progress/kendo-date-math": "^1.5.14", "@progress/kendo-dateinputs-common": "^0.4.1", "@progress/kendo-drawing": "^1.21.2", "@progress/kendo-inputs-common": "^3.1.1", "@progress/kendo-intl": "^3.1.2", "@progress/kendo-licensing": "^1.4.0", "@progress/kendo-popup-common": "^1.9.2", "@progress/kendo-react-animation": "^9.3.1", "@progress/kendo-react-buttons": "^9.3.1", "@progress/kendo-react-common": "^9.3.1", "@progress/kendo-react-data-tools": "^9.4.1", "@progress/kendo-react-dateinputs": "^9.4.1", "@progress/kendo-react-dialogs": "^9.3.1", "@progress/kendo-react-dropdowns": "^9.3.1", "@progress/kendo-react-excel-export": "^9.4.0", "@progress/kendo-react-form": "^9.3.1", "@progress/kendo-react-grid": "^9.3.1", "@progress/kendo-react-indicators": "^9.4.1", "@progress/kendo-react-inputs": "^9.3.1", "@progress/kendo-react-intl": "^9.3.1", "@progress/kendo-react-labels": "^9.3.1", "@progress/kendo-react-layout": "^9.3.1", "@progress/kendo-react-popup": "^9.3.1", "@progress/kendo-react-progressbars": "^9.3.1", "@progress/kendo-react-tooltip": "^9.3.1", "@progress/kendo-react-treeview": "^9.3.1", "@progress/kendo-react-spreadsheet": "^11.2.0", "@progress/kendo-react-upload": "^9.3.1", "@progress/kendo-svg-icons": "^4.0.0", "@progress/kendo-theme-default": "^10.1.0", "@tanstack/react-query": "^5.65.1", "axios": "^1.7.9", "class-variance-authority": "0.7.0", "crypto-js": "^4.2.0", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "mark.js": "^8.11.1", "oidc-client-ts": "^2.4.1", "pdfjs-dist": "^4.4.168", "react": "^18.3.1", "react-dom": "^18.3.1", "react-error-boundary": "^3.1.4", "react-hot-toast": "^2.4.1", "react-idle-timer": "^5.7.2", "react-oidc-context": "^2.4.0", "react-redux": "^8.1.3", "react-rnd": "^10.4.14", "react-router": "^6.8.1", "react-router-dom": "^6.28.2", "react-tooltip": "^5.28.0", "react-transition-group": "^4.4.5", "ts-debounce": "^4.0.0", "typescript": "4.9.5", "web-vitals": "^3.5.2", "xlsx": "^0.18.5", "zustand": "^5.0.3"}, "devDependencies": {"@bui/tailwind-config": "^1.0.5", "@tanstack/react-query-devtools": "5.51.3", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@types/node": "^22.13.1", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@uidotdev/usehooks": "^2.4.1", "@vitejs/plugin-react-swc": "^3.7.2", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "jest-extended": "^4.0.2", "react-icons": "^5.4.0", "react-scripts": "5.0.1", "tailwind-config-viewer": "^2.0.4", "uuid": "^9.0.1", "vite": "^4.5.2", "vite-plugin-pwa": "^0.20.5", "vite-plugin-static-copy": "^0.16.0"}, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "eject": "react-scripts eject", "lint": "eslint src/**/*.jsx src/**/*.js --no-error-on-unmatched-pattern", "stylelint": "stylelint src/**/*.css", "coverage": "set CI=true && react-scripts test --coverage --watchAll=false", "format": "prettier  --write src/**/*.js src/**/*.jsx", "tailwind-viewer": "tailwind-config-viewer -o -p 3003", "test": "react-scripts test", "test:coverage": "set CI=true && npm run test --coverage"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "jest": {"collectCoverageFrom": ["src/**/*.{js,jsx}", "!src/**/*.test.{js,jsx}", "!src/index.js", "!src/serviceWorker.js"], "transform": {"^.+\\.jsx?$": "babel-jest"}, "transformIgnorePatterns": ["/node_modules/(?!axios).+\\.js$"]}}