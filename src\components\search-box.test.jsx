import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import SearchBox from './search-box';

describe('SearchBox Component', () => {
  const mockSetSearchQuery = jest.fn();
  const defaultProps = {
    searchQuery: '',
    setSearchQuery: mockSetSearchQuery,
    currentTabLabel: 'Income Statement'
  };
  
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  test('renders with placeholder text', () => {
    render(<SearchBox {...defaultProps} />);
    
    const inputElement = screen.getByPlaceholderText('Search line item or value in Income Statement');
    expect(inputElement).toBeInTheDocument();
  });
  
  test('renders search icon', () => {
    render(<SearchBox {...defaultProps} />);
    
    // Check that search icon is rendered
    const searchIcon = document.querySelector('svg.text-primary-78');
    expect(searchIcon).toBeInTheDocument();
  });
  
  test('does not render clear button when search query is empty', () => {
    render(<SearchBox {...defaultProps} />);
    
    // Check that clear button is not rendered
    const clearButton = document.querySelector('svg.text-neutral-60');
    expect(clearButton).not.toBeInTheDocument();
  });
  
  test('renders clear button when search query is not empty', () => {
    render(<SearchBox {...defaultProps} searchQuery="test" />);
    
    // Check that clear button is rendered
    const clearButton = document.querySelector('svg.text-neutral-60');
    expect(clearButton).toBeInTheDocument();
  });
  
  test('calls setSearchQuery when input value changes', () => {
    render(<SearchBox {...defaultProps} />);
    
    const inputElement = screen.getByPlaceholderText('Search line item or value in Income Statement');
    fireEvent.change(inputElement, { target: { value: 'test search' } });
    
    expect(mockSetSearchQuery).toHaveBeenCalledTimes(1);
    expect(mockSetSearchQuery).toHaveBeenCalledWith('test search');
  });
  
  test('calls setSearchQuery with empty string when clear button is clicked', () => {
    render(<SearchBox {...defaultProps} searchQuery="test" />);
    
    const clearButton = document.querySelector('svg.text-neutral-60');
    fireEvent.click(clearButton);
    
    expect(mockSetSearchQuery).toHaveBeenCalledTimes(1);
    expect(mockSetSearchQuery).toHaveBeenCalledWith('');
  });
  
  test('displays the search query in the input field', () => {
    render(<SearchBox {...defaultProps} searchQuery="test query" />);
    
    const inputElement = screen.getByPlaceholderText('Search line item or value in Income Statement');
    expect(inputElement.value).toBe('test query');
  });
  
  test('uses the provided currentTabLabel in placeholder', () => {
    render(<SearchBox {...defaultProps} currentTabLabel="Balance Sheet" />);
    
    const inputElement = screen.getByPlaceholderText('Search line item or value in Balance Sheet');
    expect(inputElement).toBeInTheDocument();
  });
}); 