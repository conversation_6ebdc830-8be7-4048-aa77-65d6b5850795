import { render, screen } from "@testing-library/react";
import DocumentViewerPDF from "./document-viewer-pdf";
import TestAppRenderer from "../../../../infra/test-utils/test-app-renderer";
import { GET_PDF_FROM_URL_API } from "../../../../infra/api/miscellaneous-service";

const mockData = {
  pdf: "JVBERi0xLjcNJeLjz9MNCjc5NSAwIG9iag08PC9MaW5lYXJpemVkIDEvTCAyMDkxMzg1L08gNzk3L0UgMTM0NjU0L04gNDcvVCAyMDkwMzgyL0ggWyA1MzcgMTA5MV0"
};
jest.mock("pdfjs-dist", () => ({
  GlobalWorkerOptions: {
    workerSrc: ""
  },
  getDocument: jest.fn().mockReturnValue({
    promise: Promise.resolve({
      pdf: "JVBjz9MNCjc5NSAwIG9iag08PC9MaW5lYXJpemVkIDEvTCAyMDkxMzg1L08gNzk3L0UgMTM0NjU0L04gNDcvVCAyMDkwMzgyL0ggWyA1MzcgMTA5MV0"
    })
  })
}));

jest.mock("pdfjs-dist/web/pdf_viewer.mjs", () => ({
  PDFViewer: jest.fn(),
  EventBus: jest.fn().mockImplementation(() => ({
    on: jest.fn()
  })),
  PDFLinkService: jest.fn(),
  PDFFindController: jest.fn()
}));

jest.mock("../../../../infra/api/miscellaneous-service", () => ({
  GET_PDF_FROM_URL_API: jest.fn()
}));

describe("Component DocumentViewerPDF render", () => {
  beforeEach(() => {
    jest.restoreAllMocks();
  });
  it("should render DocumentViewerPDF ", () => {
    GET_PDF_FROM_URL_API.mockReturnValue({
      isLoading: true,
      data: {
        data: "JVBERi0xLjcNJeLjz9MNCjc5NSAwIG9iag08PC9MaW5lYXJpemVkIDEvTCAyMDkxMzg1L08gNzk3L0UgMTM0NjU0L04gNDcvVCAyMDkwMzgyL0ggWyA1MzcgMTA5MV0"
      }
    });
    render(
      <TestAppRenderer>
        <DocumentViewerPDF metaData={null} readOnly={false} fileUrl={"test"} />
      </TestAppRenderer>
    );
    expect(screen.getByTestId("document-viewer-loading")).toBeInTheDocument();
  });

  it("should render DocumentViewerPDF with  data", () => {
    GET_PDF_FROM_URL_API.mockReturnValue({
      isLoading: false,
      data: {
        data: "JVBERi0xLjcNJeLjz9MNCjc5NSAwIG9iag08PC9MaW5lYXJpemVkIDEvTCAyMDkxMzg1L08gNzk3L0UgMTM0NjU0L04gNDcvVCAyMDkwMzgyL0ggWyA1MzcgMTA5MV0"
      }
    });
    render(
      <TestAppRenderer>
        <DocumentViewerPDF metaData={null} readOnly={true} fileUrl={"test"} />
      </TestAppRenderer>
    );
    expect(screen.getByTestId("document-viewer-loading")).toBeInTheDocument();
  });
});
