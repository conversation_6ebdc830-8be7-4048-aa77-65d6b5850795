import React from "react";
import { render, screen } from "@testing-library/react";
import ExcelExportComponents from "../excel-export-components";
import { renderExcelExportColumns } from "../utils";

// Mock dependencies
jest.mock("@progress/kendo-react-excel-export", () => ({
  ExcelExport: ({ children, data, fileName }) => (
    <div
      data-testid="excel-export"
      data-rows={data ? data.length : 0}
      data-filename={fileName} // Include fileName in the output
    >
      {children}
    </div>
  )
}));

jest.mock("../utils", () => ({
  renderExcelExportColumns: jest
    .fn()
    .mockImplementation((columns) => (
      <div
        data-testid="excel-columns"
        data-column-count={columns ? columns.length : 0}
      />
    ))
}));

describe("ExcelExportComponents", () => {
  const defaultProps = {
    exportGridOneRef: {
      current: { save: jest.fn(), workbookOptions: jest.fn() }
    },
    exportGridTwoRef: {
      current: { save: jest.fn(), workbookOptions: jest.fn() }
    },
    exportGridThreeRef: {
      current: { save: jest.fn(), workbookOptions: jest.fn() }
    },
    exportGridFourRef: {
      current: {
        save: jest.fn(),
        workbookOptions: jest.fn().mockReturnValue({
          sheets: [{ rows: [] }]
        })
      }
    },
    incomeStatementData: [{ id: 1, value: 100 }],
    balanceSheetData: [{ id: 1, assets: 200 }],
    cashFlowData: [{ id: 1, flow: 300 }],
    notesData: [
      [
        {
          label: "Note 1",
          status: "Active",
          cellIds: { "date|type|2023": "value1" }
        }
      ],
      [
        {
          label: "Note 2",
          status: "Active",
          cellIds: { "date|type|2022": "value2" }
        }
      ]
    ],
    columnNamesForIncomeStatementData: ["income", "expenses"],
    columnNamesForBalanceSheetData: ["assets", "liabilities"],
    columnNamesForCashFlowData: ["inflow", "outflow"],
    columnNamesForNotesData: [["date|type|2023"], ["date|type|2022"]],
    ticker: "AAPL",
    isMapped: true
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test("renders all four ExcelExport components", () => {
    render(<ExcelExportComponents {...defaultProps} />);

    const excelExports = screen.getAllByTestId("excel-export");
    expect(excelExports).toHaveLength(4);
  });

  test("passes correct data to each ExcelExport component", () => {
    render(<ExcelExportComponents {...defaultProps} />);

    const excelExports = screen.getAllByTestId("excel-export");
    expect(excelExports[0]).toHaveAttribute("data-rows", "1"); // incomeStatementData
    expect(excelExports[1]).toHaveAttribute("data-rows", "1"); // balanceSheetData
    expect(excelExports[2]).toHaveAttribute("data-rows", "1"); // cashFlowData
    expect(excelExports[3]).toHaveAttribute("data-rows", "5"); // combined notes data
  });

  test("applies correct filename with mapping", () => {
    render(<ExcelExportComponents {...defaultProps} />);
    const excelExports = screen.getAllByTestId("excel-export");
    expect(excelExports[0]).toHaveAttribute(
      "data-filename",
      "AAPL_Financials_with_mapping.xlsx"
    );
  });

  test("applies correct filename without mapping", () => {
    const props = { ...defaultProps, isMapped: false };
    render(<ExcelExportComponents {...props} />);
    const excelExports = screen.getAllByTestId("excel-export");
    expect(excelExports[0]).toHaveAttribute(
      "data-filename",
      "AAPL_Financials_without_mapping.xlsx"
    );
  });

  test("calls renderExcelExportColumns with correct parameters", () => {
    render(<ExcelExportComponents {...defaultProps} />);

    expect(renderExcelExportColumns).toHaveBeenCalledWith(
      defaultProps.columnNamesForIncomeStatementData,
      true
    );
    expect(renderExcelExportColumns).toHaveBeenCalledWith(
      defaultProps.columnNamesForBalanceSheetData,
      true
    );
    expect(renderExcelExportColumns).toHaveBeenCalledWith(
      defaultProps.columnNamesForCashFlowData,
      true
    );
    expect(renderExcelExportColumns).toHaveBeenCalledWith(
      ["date|type|2023"],
      true,
      true
    );
  });

  test("handles empty notesData correctly", () => {
    const props = {
      ...defaultProps,
      notesData: [],
      columnNamesForNotesData: []
    };

    render(<ExcelExportComponents {...props} />);
    const excelExports = screen.getAllByTestId("excel-export");
    expect(excelExports[3]).toHaveAttribute("data-rows", "0");
  });

  test("handles notes with multiple columns in cellIds", () => {
    const props = {
      ...defaultProps,
      notesData: [
        [
          {
            label: "Note 1",
            status: "Active",
            cellIds: {
              "date|type|2023": "value1",
              "date|type|2022": "value2",
              "date|type|2021": "value3"
            }
          }
        ]
      ]
    };

    render(<ExcelExportComponents {...props} />);
    expect(renderExcelExportColumns).toHaveBeenCalledWith(
      ["date|type|2023"],
      true,
      true
    );
  });

  test("unifiedNotesColumns works with empty columnNamesForNotesData", () => {
    const props = {
      ...defaultProps,
      columnNamesForNotesData: []
    };

    render(<ExcelExportComponents {...props} />);
    const excelExports = screen.getAllByTestId("excel-export");
    expect(excelExports[3]).toBeInTheDocument();
  });

  test("formats export references correctly for each export component", () => {
    render(<ExcelExportComponents {...defaultProps} />);

    // Check that each export grid reference is properly constructed
    expect(defaultProps.exportGridOneRef.current).toBeTruthy();
    expect(defaultProps.exportGridTwoRef.current).toBeTruthy();
    expect(defaultProps.exportGridThreeRef.current).toBeTruthy();
    expect(defaultProps.exportGridFourRef.current).toBeTruthy();
  });
});

jest.mock("../utils", () => ({
  renderExcelExportColumns: jest
    .fn()
    .mockImplementation((columns) => (
      <div
        data-testid="excel-columns"
        data-column-count={columns ? columns.length : 0}
      />
    ))
}));

describe("ExcelExportComponents", () => {
  const defaultProps = {
    exportGridOneRef: {
      current: { save: jest.fn(), workbookOptions: jest.fn() }
    },
    exportGridTwoRef: {
      current: { save: jest.fn(), workbookOptions: jest.fn() }
    },
    exportGridThreeRef: {
      current: { save: jest.fn(), workbookOptions: jest.fn() }
    },
    exportGridFourRef: {
      current: {
        save: jest.fn(),
        workbookOptions: jest.fn().mockReturnValue({
          sheets: [{ rows: [] }]
        })
      }
    },
    incomeStatementData: [{ id: 1, value: 100 }],
    balanceSheetData: [{ id: 1, assets: 200 }],
    cashFlowData: [{ id: 1, flow: 300 }],
    notesData: [
      [
        {
          label: "Note 1",
          status: "Active",
          cellIds: { "date|type|2023": "value1" }
        }
      ],
      [
        {
          label: "Note 2",
          status: "Active",
          cellIds: { "date|type|2022": "value2" }
        }
      ]
    ],
    columnNamesForIncomeStatementData: ["income", "expenses"],
    columnNamesForBalanceSheetData: ["assets", "liabilities"],
    columnNamesForCashFlowData: ["inflow", "outflow"],
    columnNamesForNotesData: [["date|type|2023"], ["date|type|2022"]],
    ticker: "AAPL",
    isMapped: true
  };

  test("renders all four ExcelExport components", () => {
    render(<ExcelExportComponents {...defaultProps} />);

    const excelExports = screen.getAllByTestId("excel-export");
    expect(excelExports).toHaveLength(4);
  });

  test("passes correct data to each ExcelExport component", () => {
    render(<ExcelExportComponents {...defaultProps} />);

    const excelExports = screen.getAllByTestId("excel-export");
    expect(excelExports[0]).toHaveAttribute("data-rows", "1"); // incomeStatementData
    expect(excelExports[1]).toHaveAttribute("data-rows", "1"); // balanceSheetData
    expect(excelExports[2]).toHaveAttribute("data-rows", "1"); // cashFlowData

    // Combined notes data should have 4 rows:
    // - 1 header row for first collection
    // - 1 data row from first collection
    // - 1 separator row
    // - 1 header row for second collection
    // - 1 data row from second collection
    expect(excelExports[3]).toHaveAttribute("data-rows", "5");
  });

  test("applies correct filename with mapping", () => {
    const { container } = render(<ExcelExportComponents {...defaultProps} />);
    expect(container.innerHTML).toContain("AAPL_Financials_with_mapping.xlsx");
  });

  test("applies correct filename without mapping", () => {
    const props = { ...defaultProps, isMapped: false };
    const { container } = render(<ExcelExportComponents {...props} />);
    expect(container.innerHTML).toContain(
      "AAPL_Financials_without_mapping.xlsx"
    );
  });

  test("calls renderExcelExportColumns with correct parameters", () => {
    render(<ExcelExportComponents {...defaultProps} />);

    expect(renderExcelExportColumns).toHaveBeenCalledWith(
      defaultProps.columnNamesForIncomeStatementData,
      true
    );
    expect(renderExcelExportColumns).toHaveBeenCalledWith(
      defaultProps.columnNamesForBalanceSheetData,
      true
    );
    expect(renderExcelExportColumns).toHaveBeenCalledWith(
      defaultProps.columnNamesForCashFlowData,
      true
    );
    expect(renderExcelExportColumns).toHaveBeenCalledWith(
      ["date|type|2023"],
      true,
      true
    );
  });

  test("handles empty notesData correctly", () => {
    const props = {
      ...defaultProps,
      notesData: [],
      columnNamesForNotesData: []
    };

    render(<ExcelExportComponents {...props} />);
    const excelExports = screen.getAllByTestId("excel-export");
    expect(excelExports[3]).toHaveAttribute("data-rows", "0");
  });
});
