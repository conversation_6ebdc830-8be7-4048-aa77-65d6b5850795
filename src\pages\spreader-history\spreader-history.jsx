import React, { useCallback, useEffect, useRef, useState } from "react";
import PropTypes from "prop-types";
import "@progress/kendo-theme-default/dist/all.css";
import { GET_SPREADER_HISTORY_API } from "../../infra/api/SpreaderHistory/spreader-history-service";
import CompanyCard from "./CompanyCard"; // Import the new CompanyCard component

const SpreaderHistory = ({ onCardSelect, historyData }) => {
  const [historyList, setHistoryList] = useState([]);
  const [offset, setOffset] = useState(0);
  const observer = useRef();

  const fetchHistoryList = async (page) => {
    const historyResponse = await GET_SPREADER_HISTORY_API(page);
    if (historyResponse) {
      setHistoryList((prevList) => {
        // Update existing items that match processId or append new ones
        const updatedList = [...prevList];
        historyResponse.forEach((newItem) => {
          const existingIndex = updatedList.findIndex(
            (item) => item.processId === newItem.processId
          );
          if (existingIndex !== -1) {
            // Update existing item
            updatedList[existingIndex] = {
              ...updatedList[existingIndex],
              ...newItem
            };
          } else {
            // Append new item
            updatedList.push(newItem);
          }
        });
        return updatedList;
      });
    }
  };

  const lastRef = useCallback((node) => {
    if (observer.current) observer.current.disconnect();
    observer.current = new IntersectionObserver((entries) => {
      if (entries[0].isIntersecting) {
        setOffset((prevOffset) => {
          var nextPage = prevOffset + 1;
          fetchHistoryList(nextPage);
          return nextPage;
        });
      }
    });
    if (node) observer.current.observe(node);
  }, []);
  useEffect(() => {
    const interval = setInterval(async () => {
      let refreshIndex = 0;
      while (refreshIndex <= offset) {
        fetchHistoryList(refreshIndex);
        refreshIndex++;
      }
    }, 30000);

    return () => clearInterval(interval);
  }, [offset]);
  useEffect(() => {
    fetchHistoryList(0);
  }, []);
  const handleCardClick = (company) => {
    const selectedCompany = historyList.find(
      (companyDetails) => companyDetails.processId === company.processId
    );
    if (selectedCompany && selectedCompany.status === "Completed") {
      onCardSelect && onCardSelect(company);
    }
  };
  if (
    historyData?.length > 0 &&
    (historyList === null || historyList.length === 0)
  ) {
    setHistoryList(historyData);
  }
  return (
    <div className="spreader-history">
      {historyList?.map((company, index) => (
        <div ref={lastRef}>
          <CompanyCard
            key={company.processId}
            company={company}
            isSelected={true}
            onClick={() => handleCardClick(company)}
          />
        </div>
      ))}
    </div>
  );
};

SpreaderHistory.propTypes = {
  onCardSelect: PropTypes.func,
  historyData: PropTypes.array
};

export default SpreaderHistory;
