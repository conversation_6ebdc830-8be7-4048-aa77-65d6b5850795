import react from "@vitejs/plugin-react-swc";
import { defineConfig } from "vite";
import { viteStaticCopy } from "vite-plugin-static-copy";
import postcss from "./postcss.config.js";
import path from "path";

export default defineConfig({
  base: "./",
  server: {
    host: "0.0.0.0",
    port: 3000,
    open: false
  },
  preview: {
    port: 3000,
    open: true
  },
  define: {
    "process.env": null
  },
  css: {
    postcss
  },
  plugins: [
    react(),
    viteStaticCopy({
      targets: [
        {
          src: "./web.config",
          dest: "./"
        },
        {
          src: path.resolve(__dirname, "./src/resources/fonts/") + "/[!.]*",
          dest: "./assets/fonts/"
        },
        {
          src: path.resolve(__dirname, "./src/resources/css/") + "/[!.]*",
          dest: "./assets/"
        },
        {
          src: path.resolve(__dirname, "./src/resources/js/") + "/[!.]*",
          dest: "./assets/js/"
        }
      ]
    })
  ],
  build: {
    commonjsOptions: {
      transformMixedEsModules: true
    },
    target: "esnext",
    assetsInlineLimit: 0,
    sourcemap: false,
    outDir: "./build"
  }
});
