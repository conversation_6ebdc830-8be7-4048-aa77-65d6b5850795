/**
 * @jest-environment jsdom
 */
import React from 'react';
import { render, screen, fireEvent, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import PeriodHeaderSidebar from './period-header-sidebar';

// Mock the KpiModule constants
jest.mock('../../constants/kpi-module', () => ({
  KpiModule: {
    ProfitAndLoss: 7,
    BalanceSheet: 8,
    CashFlow: 9,
    TradingRecords: 1
  }
}));

// Mock the ComboBox component from Kendo
jest.mock('@progress/kendo-react-dropdowns', () => ({
  ComboBox: ({ id, data, value, onChange, placeholder, onFilterChange, ...props }) => (
    <select
      data-testid={id}
      value={value?.value || ''}
      onChange={(e) => {
        const selectedOption = data.find(item => item.value.toString() === e.target.value);
        onChange({ target: { value: selectedOption } });
      }}
      onInput={(e) => {
        if (onFilterChange) {
          onFilterChange({ filter: { value: e.target.value } });
        }
      }}
    >
      <option value="">{placeholder}</option>
      {data.map((item, index) => (
        <option key={index} value={item.value}>
          {item.text}
        </option>
      ))}
    </select>
  )
}));

// Mock the Button component
jest.mock('../../partials/atoms/button', () => ({
  Button: ({ children, onClick, disabled, ...props }) => (
    <button
      onClick={onClick}
      disabled={disabled}
      data-testid={`button-${children.toString().toLowerCase()}`}
      {...props}
    >
      {children}
    </button>
  )
}));

// Mock react-icons
jest.mock('react-icons/fa', () => ({
  FaTimes: () => <span data-testid="close-icon">×</span>
}));

// Mock KPI Config data
const mockKpiConfig = [
  {
    moduleId: 7, // ProfitAndLoss
    subSectionFields: [
      {
        name: 'Revenue',
        aliasName: 'Revenue',
        valueTypId: 1,
        chartValue: 'Monthly,Quarterly,Annual'
      },
      {
        name: 'EBITDA',
        aliasName: 'EBITDA',
        valueTypId: 2,
        chartValue: 'Quarterly,Annual'
      }
    ]
  },
  {
    moduleId: 8, // BalanceSheet
    subSectionFields: [
      {
        name: 'Assets',
        aliasName: 'Assets',
        valueTypId: 3,
        chartValue: 'Annual'
      }
    ]
  }
];

describe('PeriodHeaderSidebar Component', () => {
  const defaultProps = {
    isOpen: true,
    onClose: jest.fn(),
    onSave: jest.fn(),
    initialValues: {
      Data_Type: 'Revenue',
      Period_Type: 'Monthly',
      Period_Year: 2023,
      Period_Quarter: '',
      Period_Month: 5 // June
    },
    columnId: 'col1',
    selectedTab: 0, // Income Statement
    kpiConfig: mockKpiConfig
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders when isOpen is true', () => {
    render(<PeriodHeaderSidebar {...defaultProps} />);
    
    expect(screen.getByText('Change Period Header')).toBeInTheDocument();
    expect(screen.getByTestId('data-type')).toBeInTheDocument();
    expect(screen.getByTestId('period-type')).toBeInTheDocument();
  });

  test('does not render when isOpen is false', () => {
    render(<PeriodHeaderSidebar {...defaultProps} isOpen={false} />);
    
    expect(screen.queryByText('Change Period Header')).not.toBeInTheDocument();
  });

  test('displays correct period selector based on period type - Monthly', () => {
    render(<PeriodHeaderSidebar {...defaultProps} />);
    
    // Should show month and year selectors for monthly
    expect(screen.getByTestId('month-select')).toBeInTheDocument();
    expect(screen.getByTestId('year-select-monthly')).toBeInTheDocument();
  });

  test('displays correct period selector when changing to Quarterly', () => {
    render(<PeriodHeaderSidebar {...defaultProps} />);
    
    // Change to quarterly
    fireEvent.change(screen.getByTestId('period-type'), { target: { value: 'quarterly' } });
    
    // Should show quarter and year selectors
    expect(screen.getByTestId('quarter-select')).toBeInTheDocument();
    expect(screen.getByTestId('year-select-quarterly')).toBeInTheDocument();
  });

  test('displays correct period selector when changing to Annual', () => {
    render(<PeriodHeaderSidebar {...defaultProps} />);
    
    // Change to annually
    fireEvent.change(screen.getByTestId('period-type'), { target: { value: 'annually' } });
    
    // Should only show year selector
    expect(screen.getByTestId('year-select-annually')).toBeInTheDocument();
    expect(screen.queryByTestId('month-select')).not.toBeInTheDocument();
    expect(screen.queryByTestId('quarter-select')).not.toBeInTheDocument();
  });

  test('updates period types based on selected data type', () => {
    render(<PeriodHeaderSidebar {...defaultProps} />);
    
    // Change data type to EBITDA which only supports Quarterly and Annual
    fireEvent.change(screen.getByTestId('data-type'), { target: { value: 'EBITDA' } });
    
    // Change to period type dropdown which should now only have Quarterly and Annual
    expect(screen.getByTestId('period-type')).toHaveValue('quarterly');
  });

  test('form validation works correctly - valid form', () => {
    render(<PeriodHeaderSidebar {...defaultProps} />);
    
    // The form should be valid with the default values
    expect(screen.getByTestId('button-save')).not.toBeDisabled();
  });

  test('form validation works correctly - invalid form', () => {
    const props = {
      ...defaultProps,
      initialValues: {
        Data_Type: '',
        Period_Type: 'Monthly',
        Period_Year: 2023,
        Period_Month: 5
      }
    };
    
    render(<PeriodHeaderSidebar {...props} />);
    
    // The form should be invalid without a data type
    expect(screen.getByTestId('button-save')).toBeDisabled();
  });

  test('save button calls onSave with correct data', () => {
    render(<PeriodHeaderSidebar {...defaultProps} />);
    
    // Click the save button
    fireEvent.click(screen.getByTestId('button-save'));
    
    // Check that onSave was called with the correct data
    expect(defaultProps.onSave).toHaveBeenCalledWith({
      Data_Type: 'Revenue',
      Period_Type: 'monthly',
      Period_Year: 2023,
      Period_Quarter: '',
      Period_Month: 5,
      columnKey: 'col1'
    });
  });

  test('close button calls onClose', () => {
    render(<PeriodHeaderSidebar {...defaultProps} />);
    
    // Click the close button
    fireEvent.click(screen.getByTestId('close-icon').parentElement);
    
    // Check that onClose was called
    expect(defaultProps.onClose).toHaveBeenCalled();
  });

  test('cancel button calls onClose', () => {
    render(<PeriodHeaderSidebar {...defaultProps} />);
    
    // Click the cancel button
    fireEvent.click(screen.getByTestId('button-cancel'));
    
    // Check that onClose was called
    expect(defaultProps.onClose).toHaveBeenCalled();
  });

  test('quarterly period selection works correctly', () => {
    render(<PeriodHeaderSidebar {...defaultProps} />);
    
    // Change to quarterly
    fireEvent.change(screen.getByTestId('period-type'), { target: { value: 'quarterly' } });
    
    // Select Q2
    fireEvent.change(screen.getByTestId('quarter-select'), { target: { value: 'Q2' } });
    
    // Select year 2022
    fireEvent.change(screen.getByTestId('year-select-quarterly'), { target: { value: '2022' } });
    
    // Click save
    fireEvent.click(screen.getByTestId('button-save'));
    
    // Check that onSave was called with the correct data
    expect(defaultProps.onSave).toHaveBeenCalledWith({
      Data_Type: 'Revenue',
      Period_Type: 'quarterly',
      Period_Year: 2022,
      Period_Quarter: 'Q2',
      Period_Month: 5,
      columnKey: 'col1'
    });
  });

  test('loads different data types based on selected tab', () => {
    // Render with Balance Sheet tab selected
    render(<PeriodHeaderSidebar {...defaultProps} selectedTab={1} />);
    
    // Change data type and check that it's from the Balance Sheet module
    fireEvent.change(screen.getByTestId('data-type'), { target: { value: 'Assets' } });
    
    // The only period type available should be Annual
    expect(screen.getByTestId('period-type')).toHaveValue('annually');
  });
}); 