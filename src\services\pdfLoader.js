import { GlobalWorkerOptions } from 'pdfjs-dist';
import { getDocument } from 'pdfjs-dist/build/pdf';
import { post } from '../general/fetcher';
import { baseUrl } from "../constants/config";

// Configure the PDF.js worker source
GlobalWorkerOptions.workerSrc = new URL('pdfjs-dist/build/pdf.worker.min.mjs', import.meta.url).toString();

// Load PDF by processId
export const loadPdfByProcessId = async (processId) => {
  try {
    // Construct the file path using the processId
    const DOWNLOAD_FILE_URL = `${baseUrl}extraction/api/download-file/${processId}`;
    
    // Make API call to get the file
    const response = await post(DOWNLOAD_FILE_URL, {});
    // Check if response has base64 data
    if (response && response.file) {
      // Convert base64 to binary data
      const pdfData = atob(response.file);
      const buffer = new Uint8Array(pdfData.length);
      for (let i = 0; i < pdfData.length; i++) {
        buffer[i] = pdfData.charCodeAt(i);
      }
      
      // Load PDF from binary data
      const loadingTask = getDocument({ data: buffer });
      return await loadingTask.promise;
    } else {
      throw new Error('Failed to load PDF data');
    }
  } catch (error) {
    console.error('Error loading PDF by processId:', error);
    throw error;
  }
};