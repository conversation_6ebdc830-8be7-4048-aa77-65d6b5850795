// This is a partial implementation showing just the delete button logic

// In your PopupComponent
const PopupComponent = ({ 
  // ...other props
  deleteRow,
  isAllRowsSelected,
  // ...other props
}) => {
  return (
    // ...existing component structure
    <button
      onClick={deleteRow}
      disabled={isAllRowsSelected} // Disable when all rows are selected
      className={`popup-action-button ${isAllRowsSelected ? 'opacity-50 cursor-not-allowed' : ''}`}
      title={isAllRowsSelected ? "Cannot delete all rows in a note" : "Delete selected row(s)"}
    >
      Delete Row
    </button>
    // ...rest of component
  );
};

export default PopupComponent;
