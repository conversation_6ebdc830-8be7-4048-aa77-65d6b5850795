import React from "react";
import PropTypes from "prop-types";

const HighlightText = ({ text, highlight }) => {
  const escapedHighlight = highlight.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
  const parts = text.split(new RegExp(`(${escapedHighlight})`, "gi"));
  return (
    <span
      data-testid={"highlight-text"}
      className="h-24 overflow-hidden text-ellipsis break-all"
    >
      {parts.map((part, i) =>
        part.toLowerCase() === highlight.toLowerCase() ? (
          <span key={`${part}-${i}`} className={"text-neutral-80"}>
            {part}
          </span>
        ) : (
          part
        )
      )}
    </span>
  );
};

export default HighlightText;

HighlightText.propTypes = {
  text: PropTypes.string.isRequired,
  highlight: PropTypes.string
};
