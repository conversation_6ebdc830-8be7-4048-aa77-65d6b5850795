import React from "react";
import { createRoot } from "react-dom/client";
import App from "./App";
import { ErrorBoundary } from "react-error-boundary";
import { AuthProvider } from "react-oidc-context";
import { HashRouter as Router } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ErrorContextProvider } from "./infra/contexts/error-context";
import "@progress/kendo-theme-default/dist/all.css";
import EvidencePanel from "./components/evidence-panel/evidence-panel";
import PdfPage from "./pages/pdf-preview/PdfPage";
const container = document.getElementById("app");
const root = createRoot(container);

const onSigninCallback = () => {
  window.history.replaceState({}, document.title, window.location.pathname);
};
const mockProps = {
  handleBackfunc: () => console.log("Back button clicked"),
  processId: "f032389a-512d-41ab-9f54-2fd6eb1d5027", // Using the ID from your JSON data
  companyName: "", // From your JSON data
  companyTicker: "A010140", // From your JSON data
  acuityid: "KRPU547147066" // Using companyId from JSON data
};
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      refetchOnReconnect: false,
      retry: false,
      staleTime: 5 * 60 * 1000 // 5 minutes
    }
  }
});
root.render(
  <React.StrictMode>
  {/* <ErrorContextProvider>
    <QueryClientProvider client={queryClient}>
      <AuthProvider {...oidcConfig} onSigninCallback={onSigninCallback}>
        <Router> */}
         <QueryClientProvider client={queryClient}>
         {/* <EvidencePanel  
           handleBackfunc={mockProps.handleBackfunc}
           processId={mockProps.processId}
           companyName={mockProps.companyName}
           companyTicker={mockProps.companyTicker}
           acuityid={mockProps.acuityid}
         /> */}
         {/* <PdfPage/> */}
          <Router>
          <App />
        </Router>
         </QueryClientProvider>
        {/* </Router>
      </AuthProvider>
    </QueryClientProvider>
  </ErrorContextProvider> */}
</React.StrictMode>
);
