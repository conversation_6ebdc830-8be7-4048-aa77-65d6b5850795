/**
 * @jest-environment jsdom
 */
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import CircularStatusButtons from './circular-status-buttons';

describe('CircularStatusButtons Component', () => {
  // Test rendering with default props
  test('renders with default status data', () => {
    render(<CircularStatusButtons />);
    
    // Check that the "NC" button is rendered
    const ncButton = screen.getByTitle('NC');
    expect(ncButton).toBeInTheDocument();
    
    // Should have the flag icon
    expect(ncButton.querySelector('svg')).toBeInTheDocument();
  });
  
  // Test with custom status data
  test('renders with custom status data', () => {
    const customStatusData = {
      mapped: 50,
      matchFound: 25,
      unmapped: 10,
      noMatchFound: 15
    };
    
    render(<CircularStatusButtons statusData={customStatusData} />);
    
    // NC button should still be present
    const ncButton = screen.getByTitle('NC');
    expect(ncButton).toBeInTheDocument();
  });
  
  // Test hover behavior
  test('changes display on hover', () => {
    render(<CircularStatusButtons />);
    
    const ncButton = screen.getByTitle('NC');
    
    // Simulate mouse hover
    fireEvent.mouseEnter(ncButton);
    
    // Verify hover state (NC button doesn't change text on hover, just verify it's there)
    expect(ncButton).toBeInTheDocument();
    
    // Simulate mouse leave
    fireEvent.mouseLeave(ncButton);
    
    // Verify back to normal state
    expect(ncButton).toBeInTheDocument();
  });
  
  // Test with null status data
  test('handles null status data gracefully', () => {
    render(<CircularStatusButtons statusData={null} />);
    
    // Should still render NC button
    const ncButton = screen.getByTitle('NC');
    expect(ncButton).toBeInTheDocument();
  });
  
  // Test with partial status data
  test('merges partial status data with defaults', () => {
    const partialStatusData = {
      mapped: 100
      // Other fields not provided
    };
    
    render(<CircularStatusButtons statusData={partialStatusData} />);
    
    // Should render NC button
    const ncButton = screen.getByTitle('NC');
    expect(ncButton).toBeInTheDocument();
  });
  
  // Test the styling of buttons
  test('applies correct styling to buttons', () => {
    render(<CircularStatusButtons />);
    
    const ncButton = screen.getByTitle('NC');
    
    // Check for expected classes
    expect(ncButton).toHaveClass('bg-noticeable-50');
    expect(ncButton).toHaveClass('border-noticeable-70');
    expect(ncButton).toHaveClass('rounded-[14px]');
  });
  
  // Test the rendering of icon
  test('renders flag icon in NC button', () => {
    render(<CircularStatusButtons />);
    
    const ncButton = screen.getByTitle('NC');
    const icon = ncButton.querySelector('svg');
    
    expect(icon).toBeInTheDocument();
    expect(icon).toHaveClass('text-negative-80');
  });
}); 