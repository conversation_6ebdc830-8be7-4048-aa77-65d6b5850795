import React, { useEffect, useState } from "react";
import parse from "html-react-parser";
import { notify } from "../../../../partials/molecules/toaster";
import DocumentViewerHTMLToolBar from "./document-viewer-html-toolbar";
import useDocumentViewerStore from "../document-viewer.store";
import { marked } from "marked";
import PropTypes from "prop-types";
import DocumentViewerHtmlParserRender from "./document-viewer-html-parser-render";

const DocumentViewerHTMLParser = ({
  metaData,
  readOnly,
  fileData,
  fileExtension,
  filingsUrl
}) => {
  const { previewFilingsDocType } = useDocumentViewerStore((state) => state);

  const [parsedHtml, setParsedHtml] = useState("");
  const [searchQuery, setSearchQuery] = useState("");

  useEffect(() => {
    if (fileData) {
      setParsedHtml("");
      const htmlData = fileData;

      if (!htmlData) return;

      if (["html", "htm", "xml"].includes(fileExtension)) {
        const htmlObject = replaceHtmlData(htmlData);
        updateParsedHtml(htmlObject);
      } else if (fileExtension === "txt") {
        const htmlObject = replaceTextData(htmlData);
        setParsedHtml(parse("<pre>" + htmlObject.toString() + "</pre>"));
      } else if (fileExtension === "paper") {
        updateParsedHtml(htmlData);
      } else if (fileExtension === "md") {
        const htmlObject = replaceHtmlDataMd(metaData.markDown);
        updateParsedHtml(htmlObject);
      } else if (fileExtension === "text/markdown") {
        const htmlObject = replaceHtmlDataMd(htmlData);
        updateParsedHtml(htmlObject);
      } else notify.error("File Format not Supported");
    }
  }, [fileData]);

  const onClickPreviewDocument = (domNode) => {
    //adding nonce to style tag to mitigate csp issue
    if (domNode.attribs && domNode.name === "style") {
      const props = domNode.attribs;
      props.nonce = "sec-gov";
      return domNode;
    }
  };

  const htmlToReactOptions = {
    replace: onClickPreviewDocument
  };

  const updateParsedHtml = (htmlData) => {
    const renderedHtml = parse(htmlData.toString(), htmlToReactOptions);

    setParsedHtml(renderedHtml);
  };

  const replaceTextData = (textData) => {
    const htmlObject = Document?.parseHTMLUnsafe?.(textData);
    // replace the link s with the correct url
    const sTags = htmlObject?.getElementsByTagName("s");
    // Convert the HTMLCollection to Array for iteration since elements will change
    Array.from(sTags || []).forEach((sTag) => {
      // Create new span element
      const spanTag = htmlObject.createElement("span");

      // Copy all attributes
      Array.from(sTag.attributes).forEach((attr) => {
        spanTag.setAttribute(attr.name, attr.value);
      });

      // Copy inner content
      spanTag.innerHTML = sTag.innerHTML;

      // Replace s with span
      sTag.parentNode.replaceChild(spanTag, sTag);
    });

    return htmlObject?.documentElement.outerHTML;
  };
  const replaceHtmlDataMd = (markDownStr) => {
    marked.setOptions({
      breaks: true,
      gfm: true,
      headerIds: true,
      mangle: false,
      sanitize: false
    });

    // Convert markdown to HTML
    const htmlStr = marked.parse(markDownStr);

    const htmlObject = Document?.parseHTMLUnsafe?.(htmlStr);
    return htmlObject?.documentElement.outerHTML;
  };

  // Replace the relative path with the correct path
  const replaceHtmlData = (htmlStr) => {
    const htmlObject = Document?.parseHTMLUnsafe?.(htmlStr);

    const filingsUrlObj = new URL(filingsUrl);
    const filingsUrlPath = `${filingsUrlObj.href.substring(0, filingsUrlObj.href.lastIndexOf("/"))}/`;

    // replace the link href with the correct url
    const links = htmlObject?.getElementsByTagName("link");
    for (let link of links) {
      let urlPath = link.getAttribute("href");
      urlPath = new URL(urlPath, filingsUrlObj.origin).href;
      link.href = urlPath || link.href;
    }

    // replace the image src with the correct url
    const images = htmlObject?.getElementsByTagName("img");
    for (let image of images) {
      let urlPath = image.getAttribute("src");
      const imageSrc = new URL(urlPath, filingsUrlPath).href;
      image.src = imageSrc || image.src;
    }

    // replace the anchor tag with the correct url
    const anchors = htmlObject?.getElementsByTagName("a");
    for (let anchor of anchors) {
      let urlPath = anchor.getAttribute("href");
      if (urlPath && urlPath.substring(0, 1) !== "#") {
        const anchorHref = new URL(urlPath, filingsUrlPath).href;
        anchor.href = anchorHref || anchor.href;
      }
    }

    // add 'filing-table' and 'filing-table-index' class to tables that are valid
    const tables = htmlObject?.getElementsByTagName("table");
    let index = 0;
    for (let table of tables) {
      if (isValidTable(table)) {
        table.classList.add("filing-table");
        table.id = `filing-table-${index}`;
        index++;
      }
    }
    return htmlObject?.documentElement.outerHTML;
  };

  // Find different ways to identify whether the table data is valid or not
  const isValidTable = (element) => {
    // bamsecAllowedFileTypes allowed table highlight filings
    const bamsecAllowedFileTypes = ["10-Q", "10-K"];

    // non bamsecAllowedFileTypes allow all table's to be highlighted
    if (!bamsecAllowedFileTypes.includes(previewFilingsDocType)) return true;
    // detect if the table childrens having anchor tags if so return false
    const anchors = element.getElementsByTagName("a");
    if (anchors.length) return false;
    // detect if the allowedTableActionsFilingTypes and td element has border
    if (bamsecAllowedFileTypes.includes(previewFilingsDocType)) {
      const tds = element.getElementsByTagName("td");
      let isBackgroundExist = false;
      for (let td of tds) {
        const styleValue = td.attributes.getNamedItem("style").nodeValue;
        // detect background-color property in the style attribute
        if (styleValue.includes("background-color")) {
          isBackgroundExist = true;
          break;
        }
      }
      return isBackgroundExist;
    }

    return false;
  };

  return (
    <div
      data-testid="document-viewer-html"
      className="h-full w-full overflow-hidden text-neutral-90 transition-all duration-300 ease-in-out"
    >
      <div>
        <DocumentViewerHTMLToolBar
          searchQuery={searchQuery}
          readOnly={readOnly}
          setSearchQuery={setSearchQuery}
        />
      </div>
      {fileExtension === "txt" && (
        <DocumentViewerHtmlParserRender
          parsedHtml={parsedHtml}
          searchQuery={searchQuery}
          metaData={metaData}
          readOnly={readOnly}
        />
      )}
      {fileExtension === "htm" && (
        <DocumentViewerHtmlParserRender
          parsedHtml={parsedHtml}
          searchQuery={searchQuery}
          metaData={metaData}
          readOnly={readOnly}
        />
      )}
      {fileExtension === "html" && (
        <DocumentViewerHtmlParserRender
          parsedHtml={parsedHtml}
          searchQuery={searchQuery}
          metaData={metaData}
          readOnly={readOnly}
        />
      )}
      {fileExtension === "xml" && (
        <DocumentViewerHtmlParserRender
          parsedHtml={parsedHtml}
          searchQuery={searchQuery}
          metaData={metaData}
          readOnly={readOnly}
        />
      )}
      {fileExtension === "paper" && (
        <DocumentViewerHtmlParserRender
          parsedHtml={parsedHtml}
          searchQuery={searchQuery}
          metaData={metaData}
          readOnly={readOnly}
        />
      )}
      {fileExtension === "md" && (
        <DocumentViewerHtmlParserRender
          parsedHtml={parsedHtml}
          searchQuery={searchQuery}
          metaData={metaData}
          readOnly={readOnly}
        />
      )}
      {fileExtension === "text/markdown" && (
        <DocumentViewerHtmlParserRender
          parsedHtml={parsedHtml}
          searchQuery={searchQuery}
          metaData={metaData}
          readOnly={readOnly}
        />
      )}
    </div>
  );
};

export default DocumentViewerHTMLParser;

DocumentViewerHTMLParser.propTypes = {
  metaData: PropTypes.object,
  readOnly: PropTypes.bool,
  fileData: PropTypes.string,
  fileExtension: PropTypes.string
};
