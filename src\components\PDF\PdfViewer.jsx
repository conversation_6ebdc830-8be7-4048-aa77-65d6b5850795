import React, { useEffect, useRef, useState } from 'react';
import { NumericTextBox } from '@progress/kendo-react-inputs';
import { FiMinus, FiPlus, FiChevronLeft, FiChevronRight } from 'react-icons/fi';
import { Button } from '../../partials/atoms/button';

const PdfCanvasViewer = ({ pdfDoc, totalPages, currentPage, onPageChange, onSave }) => {
  const canvasRefs = useRef([]);
  const containerRef = useRef(null);
  const pageContainerRefs = useRef([]);
  const isScrollingRef = useRef(false);
  const lastPageRef = useRef(currentPage);
  const initialRenderRef = useRef(true);
  const [scale, setScale] = useState(1.0); // Default scale set to 100%
  const [containerWidth, setContainerWidth] = useState(0);
  const [isReversed, setIsReversed] = useState(false); // State to track if view is reversed

  // Reset zoom to 100% on new PDF document load
  useEffect(() => {
    if (pdfDoc) {
      setScale(1.0); // Always reset to 100% when a new document loads
      initialRenderRef.current = true;
    }
  }, [pdfDoc]);

  // Monitor container width changes
  useEffect(() => {
    if (!containerRef.current) return;
    
    const updateContainerWidth = () => {
      if (containerRef.current) {
        const width = containerRef.current.clientWidth;
        setContainerWidth(width);
      }
    };
    
    // Initial width calculation
    updateContainerWidth();
    
    // Setup resize observer to detect container width changes
    const resizeObserver = new ResizeObserver(updateContainerWidth);
    resizeObserver.observe(containerRef.current);
    
    return () => {
      if (containerRef.current) {
        resizeObserver.disconnect();
      }
    };
  }, []);

  // Render PDF pages
  useEffect(() => {
    const renderPages = async () => {
      if (!pdfDoc || containerWidth === 0) return;
      
      // If this is initial render, ensure scale is 1.0 (100%)
      if (initialRenderRef.current) {
        setScale(1.0);
        initialRenderRef.current = false;
      }
      
      for (let i = 0; i < totalPages; i++) {
        const page = await pdfDoc.getPage(i + 1);
        // Set rotation based on user preference
        const rotation = isReversed ? 180 : 0; // 0 for normal, 180 for reversed
        
        // Get base viewport at 100% scale
        const baseViewport = page.getViewport({ scale: 1.0, rotation });
        const canvas = canvasRefs.current[i];
        if (!canvas) continue;
        
        const context = canvas.getContext('2d');
        
        // Calculate available width (accounting for padding/margins)
        const pageContainer = pageContainerRefs.current[i];
        const availableWidth = pageContainer ? pageContainer.clientWidth : containerWidth;
        const maxWidth = availableWidth - 20; // Subtract padding/margin
        
        // Check if we need to constrain the width for 100% scale
        let scaleFactor = 1.0;
        if (baseViewport.width > maxWidth) {
          scaleFactor = maxWidth / baseViewport.width;
        }
        
        // Apply additional zoom if scale is different from 1.0
        const finalScale = scaleFactor * scale;
        
        // Create viewport with final scale
        const viewport = page.getViewport({ scale: finalScale, rotation });
        
        // Set canvas dimensions
        canvas.height = viewport.height;
        canvas.width = viewport.width;
        
        // Apply the render
        await page.render({ canvasContext: context, viewport }).promise;
        
        // Set container overflow based on zoom level
        if (containerRef.current) {
          if (scale > 1.0) {
            containerRef.current.classList.add('overflow-x-auto');
            containerRef.current.classList.remove('overflow-x-hidden');
          } else {
            containerRef.current.classList.add('overflow-x-hidden');
            containerRef.current.classList.remove('overflow-x-auto');
          }
        }
      }
    };

    renderPages();
  }, [pdfDoc, totalPages, scale, containerWidth, isReversed]);

  // Detect visible page during scroll
  useEffect(() => {
    if (!canvasRefs.current.length) return;

    const handleScroll = () => {
      if (isScrollingRef.current) return;
      
      const container = containerRef.current;
      if (!container) return;

      // Find the canvas that is most visible in the viewport
      let maxVisibleHeight = 0;
      let mostVisiblePageIndex = 0;

      canvasRefs.current.forEach((canvas, index) => {
        if (!canvas) return;

        const rect = canvas.getBoundingClientRect();
        const containerRect = container.getBoundingClientRect();
        
        // Calculate how much of the canvas is visible
        const visibleTop = Math.max(rect.top, containerRect.top);
        const visibleBottom = Math.min(rect.bottom, containerRect.bottom);
        const visibleHeight = Math.max(0, visibleBottom - visibleTop);
        
        if (visibleHeight > maxVisibleHeight) {
          maxVisibleHeight = visibleHeight;
          mostVisiblePageIndex = index;
        }
      });

      // Update current page if different
      const newPage = mostVisiblePageIndex + 1;
      if (newPage !== currentPage) {
        onPageChange(newPage);
      }
    };

    const container = containerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll, { passive: true });
      // Initial check
      setTimeout(handleScroll, 100);
    }

    return () => {
      if (container) {
        container.removeEventListener('scroll', handleScroll);
      }
    };
  }, [canvasRefs.current, currentPage, onPageChange]);

  // Jump to currentPage when it changes (e.g., from thumbnail click)
  useEffect(() => {
    if (currentPage <= 0 || currentPage > totalPages) return;
    
    // Only scroll if the page actually changed
    if (lastPageRef.current !== currentPage) {
      const targetCanvas = canvasRefs.current[currentPage - 1];
      if (!targetCanvas || !containerRef.current) return;
      
      // Set flag to prevent scroll handling during programmatic scroll
      isScrollingRef.current = true;
      
      // Scroll to the selected page
      targetCanvas.scrollIntoView({ behavior: 'smooth', block: 'start' });
      
      // Reset flag after scrolling completes
      setTimeout(() => {
        isScrollingRef.current = false;
      }, 500);
      
      lastPageRef.current = currentPage;
    }
  }, [currentPage, totalPages]);

  // Navigation handlers
  const handlePrevPage = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
  };
  
  const handleSave = () => {
    // Call the onSave prop to trigger post message in the parent PdfContainer
    if (onSave && typeof onSave === 'function') {
      onSave();
    }
  };
  
  const handleNextPage = () => {
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1);
    }
  };
  
  // Zoom handlers
  const handleZoomIn = () => {
    setScale(prevScale => Math.min(prevScale + 0.25, 3.0));
  };
  
  const handleZoomOut = () => {
    setScale(prevScale => Math.max(prevScale - 0.25, 0.5));
  };

  // Reset zoom
  const handleResetZoom = () => {
    setScale(1.0); // Reset to 100%
  };

  return (
    <div className="flex flex-col h-full">
      {/* PDF viewer toolbar with zoom and navigation controls */}
      <div className="flex items-center justify-between h-[34px] border-neutral-10 border-t border-b bg-neutral-2">
        
        <div className='text-start flex items-center justify-between'>
          <Button className='h-[24px] pt-0 pb-0' onClick={handleSave}>Save</Button>
        </div>
        
        {/* Page navigation - centered */}
        <div className="flex items-center justify-center flex-1">
          {/* <button 
            onClick={handlePrevPage} 
            disabled={currentPage <= 1}
            className="text-black rounded disabled:opacity-50"
            title="Previous Page"
          >
            <FiChevronLeft className="w-4 h-4 text-black" />
          </button> */}
          
          <div className="flex items-center mx-2">
            {/* <NumericTextBox  
              min={1}
              max={totalPages}
              width="48px"
              height="20px"
              value={currentPage}
              format="n0"
              spinners={false}
              onBlur={(e) => {
                if (e.target.value !== null && e.target.value !== undefined) {
                  onPageChange(parseInt(e.target.value, 10));
                  e.target.element.value = '';
                }
              }}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  if (e.target.value !== null && e.target.value !== undefined) {
                    onPageChange(parseInt(e.target.value, 10));
                    e.target.element.value = '';
                  }
                }
              }}
            /> */}
            {/* <span className="ml-1 caption-r text-neutral-70">/ {totalPages}</span> */}
          </div>
          
          {/* <button 
            onClick={handleNextPage} 
            disabled={currentPage >= totalPages}
            className="text-black rounded disabled:opacity-50"
            title="Next Page"
          >
            <FiChevronRight className="w-4 h-4 text-black" />
          </button> */}
        </div>
        
        {/* Zoom controls */}
        <div className="flex items-center space-x-2">
          <button 
            onClick={handleZoomOut} 
            className="text-black rounded hover:bg-gray-200"
            title="Zoom Out"
          >
            <FiMinus className="w-4 h-4" />
          </button>
          <span className="text-neutral-80 caption-r">{Math.round(scale * 100)}%</span>
          <button 
            onClick={handleZoomIn} 
            className="text-black rounded hover:bg-gray-200 mr-2"
            title="Zoom In"
          >
            <FiPlus className="w-4 h-4" />
          </button>
        </div>
      </div>

      {/* PDF content area - with conditional horizontal scroll */}
      <div 
        ref={containerRef} 
        className={`overflow-y-auto h-full mt-2 flex-1 ${scale > 1.0 ? 'overflow-x-auto' : 'overflow-x-hidden'}`}
      >
        {Array.from({ length: totalPages }, (_, i) => {
          const pageNum = i + 1;
          const isCurrentPageClass = pageNum === currentPage ? 'current-page' : '';
          
          return (
            <div 
              key={pageNum} 
              className={`pdf-page-container mb-3 space-y-2 ${isCurrentPageClass} w-full`}
              ref={(el) => (pageContainerRefs.current[i] = el)}
            >
              <div className="flex justify-center w-full">
                <canvas
                  ref={(el) => (canvasRefs.current[i] = el)}
                  className="preview-shadow max-w-full"
                />
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default PdfCanvasViewer;