import { fireEvent, render, screen } from "@testing-library/react";
import DocumentViewerPopOverHighLight from "./document-viewer-pop-over-highlight";
import TestAppRenderer from "../../../infra/test-utils/test-app-renderer";
import useDocumentViewerStore from "./document-viewer.store";
import { POST_BUCKET_API } from "../../../infra/api/bucket-service";
import { POST_HIGHLIGHT_API } from "../../../infra/api/highlight-service";
jest.mock("../../../infra/api/bucket-service", () => ({
  POST_BUCKET_API: jest.fn()
}));
jest.mock("../../../infra/api/highlight-service", () => ({
  DELETE_HIGHLIGHT_API: jest.fn(),
  POST_HIGHLIGHT_API: jest.fn()
}));

describe("Component DocumentViewerPopOverHighLight render", () => {
  it("should render DocumentViewerPopOverHighLight without value", () => {
    let store = useDocumentViewerStore.getState();
    store.setShowHighLightPopOver(true);
    store.setPreviewFilingsMetaData({
      acuityId: "test",
      acuitySecurityId: "testSecurityId",
      ticker: "AAPL",
      companyName: "Apple",
      id: "testId",
      doc: "https:test.com",
      docId: "testId",
      filingType: "10Q",
      filingDate: new Date()
    });
    store.setHighlightedMetaData('{"test":"test"}');
    store.setSelectedText("copied");

    POST_HIGHLIGHT_API.mockImplementation(() => ({
      mutateAsync: jest
        .fn()
        .mockImplementationOnce(() => ({
          success: false
        }))
        .mockImplementation(() => ({
          success: true
        }))
    }));
    POST_BUCKET_API.mockImplementation(() => ({
      mutateAsync: jest
        .fn()
        .mockImplementationOnce(() => ({
          success: false
        }))
        .mockImplementation(() => ({
          success: true
        }))
    }));

    render(
      <TestAppRenderer>
        <DocumentViewerPopOverHighLight />
      </TestAppRenderer>
    );
    expect(
      screen.getByTestId("document-viewer-pop-over-highlight")
    ).toBeInTheDocument();
    fireEvent.click(
      screen.getByTestId(
        "document-viewer-pop-over-highlight-save-highlight-button"
      )
    );
    fireEvent.click(
      screen.getByTestId(
        "document-viewer-pop-over-highlight-save-highlight-button"
      )
    );

    fireEvent.click(
      screen.getByTestId(
        "document-viewer-pop-over-highlight-copy-highlight-button"
      )
    );
    fireEvent.click(
      screen.getByTestId(
        "document-viewer-pop-over-highlight-copy-highlight-button"
      )
    );

    fireEvent.click(
      screen.getByTestId("document-viewer-pop-over-highlight-close-button")
    );
  });
});
