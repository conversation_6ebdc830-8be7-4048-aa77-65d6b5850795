import { useMutation, useQuery } from "@tanstack/react-query";
import { fetcher, post, remove } from "../../general/fetcher";

const CONTROLLER = "token-manager";

export const tokenURL = `${CONTROLLER}/token`;

export const GET_TOKENS_API = () => {
  return useQuery({
    queryKey: [`GET_TOKENS_URL`],
    queryFn: () => fetcher(`${tokenURL}`),
    cacheTime: 0
  });
};

export const GET_IS_TOKENS_EXISTS_API = () => {
  return useQuery({
    queryKey: [`GET_IS_TOKENS_EXISTS_URL`],
    queryFn: () => fetcher(`${tokenURL}`),
    cacheTime: 0
  });
};

export const POST_SAVE_TOKEN_API = () => {
  return useMutation({
    mutationFn: () => {
      return post(tokenURL);
    }
  });
};

export const DELETE_TOKENS_API = () => {
  return useMutation({
    mutationFn: () => {
      return remove(tokenURL);
    }
  });
};
