import React, { Fragment } from "react";
import {
  Dialog,
  Transition,
  TransitionChild,
  DialogTitle,
  DialogPanel
} from "@headlessui/react";
import { RxCross1 } from "react-icons/rx";
import clsx from "clsx";
import useModalStore from "./modal.store";

const ModalSmall = () => {
  const {
    type,
    open,
    title,
    cancelLabel,
    submitLabel,
    children,
    handleClose,
    handleSubmit,
    MODEL_TYPE
  } = useModalStore();

  return (
    <Transition show={open} as={Fragment}>
      <Dialog
        as="div"
        className="fixed inset-0 z-50 overflow-y-auto"
        onClose={handleClose}
      >
        <div className="flex min-h-screen items-end justify-center px-4 pb-20 pt-4 text-center sm:block sm:p-0">
          <TransitionChild
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <Dialog.Overlay className="fixed inset-0 bg-neutral-500 bg-opacity-75 transition-opacity" />
          </TransitionChild>

          {/* This element is to trick the browser into centering the modal contents. */}
          <span
            className="hidden sm:inline-block sm:h-screen sm:align-middle"
            aria-hidden="true"
          >
            &#8203;
          </span>
          <TransitionChild
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            enterTo="opacity-100 translate-y-0 sm:scale-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100 translate-y-0 sm:scale-100"
            leaveTo="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
          >
            <div className="inline-block max-w-sm transform overflow-hidden rounded-lg bg-white text-left align-bottom shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-md sm:align-middle">
              <div className="relative rounded bg-white">
                <DialogTitle
                  as="div"
                  className="m-m item-center flex h-12 justify-between bg-primary-40 px-6 leading-6 text-neutral-80"
                >
                  <div className="flex items-center justify-start">{title}</div>
                  <div
                    className="flex place-items-end items-center justify-end"
                    data-testid={`popup-${title.toLowerCase().replaceAll(" ", "-")}-close-icon`}
                  >
                    <RxCross1 onClick={handleClose} />
                  </div>
                </DialogTitle>
                <DialogPanel as="div">
                  <div className="px-6 py-4">{children}</div>
                </DialogPanel>
              </div>

              <div className="my-4 flex justify-end px-6">
                <div className="flex justify-end">
                  <button
                    id={`popup-${title.toLowerCase().replaceAll(" ", "-")}-cancel`}
                    data-testid={`popup-${title.toLowerCase().replaceAll(" ", "-")}-cancel`}
                    type="button"
                    className="s-r flex h-8 items-center rounded border border-primary-78 px-4 text-primary-78 outline-none hover:bg-primary-40"
                    onClick={handleClose}
                  >
                    {cancelLabel}
                  </button>
                  <button
                    id={`popup-${title.toLowerCase().replaceAll(" ", "-")}-submit`}
                    data-testid={`popup-${title.toLowerCase().replaceAll(" ", "-")}-submit`}
                    type="button"
                    className={clsx(
                      `s-r ml-2 flex h-8 items-center rounded-md px-4 text-white outline-none focus:outline-none focus-visible:ring-0`,
                      type === MODEL_TYPE.CONFIRM &&
                        `focus-visible:ring-info-500 focus-visible:ring-offset-2 enabled:bg-primary-78 enabled:hover:bg-primary-90 enabled:focus:bg-primary-100 disabled:cursor-pointer disabled:bg-info-60`,
                      type === MODEL_TYPE.ERROR &&
                        `focus-visible:ring-negative-120 focus-visible:ring-offset-2 enabled:bg-negative-100 enabled:hover:bg-negative-110 enabled:focus:bg-negative-120 disabled:cursor-pointer disabled:bg-negative-70`
                    )}
                    onClick={handleSubmit}
                  >
                    {submitLabel}
                  </button>
                </div>
              </div>
            </div>
          </TransitionChild>
        </div>
      </Dialog>
    </Transition>
  );
};

export default ModalSmall;
