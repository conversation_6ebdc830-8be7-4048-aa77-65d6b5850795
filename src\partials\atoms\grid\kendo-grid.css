.k-button-flat-primary {
  color: #4061c7;
}

.k-table-tbody .k-table-row {
  height: 1px;
  border: 1px;
  border-color: #4061c7;
  background-color: #fff;
}

.k-table-body:active {
  background-color: #4061c7;
}
.k-grid .k-grid-header .k-table-th:nth-child(2) {
  border-left-width: 0px;
}

.k-table-row:hover .k-checkbox {
  display: block;
}
.k-grid-md .k-table-th > .k-cell-inner > .k-link {
  padding-block: 11px;
  padding-inline: 12px;
}
.k-grid .k-grid-header {
  padding: 0px 13px 0px 0px;
  border-bottom: 1px solid #e6e6e6 !important;
}

/* Row height */
.k-grid .k-table-tbody > .k-table-row:not(.k-detail-row) {
  height: 40px !important;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.k-grid .k-table-tbody > .k-table-row:not(.k-detail-row):hover {
  height: 40px !important;
}

.k-grid .k-table-tbody > .k-table-row:not(.k-detail-row) {
  height: 40px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.k-grid .k-table-tbody > .k-table-row:not(.k-detail-row):hover {
  height: 40px;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

/* Override alternating row styles */
.k-grid .k-table-row {
  background-color: #fff !important;
  border-bottom: 1px solid #e6e6e6 !important;
}

.k-grid .k-table-row:nth-child(even),
.k-grid .k-table-row:nth-child(odd) {
  background-color: #fff !important;
}

/* Remove row hover styles */
.k-grid .k-table-row:hover,
.k-grid .k-table-tbody > .k-table-row:not(.k-detail-row):hover {
  background-color: transparent !important;
}

/* Add column hover styles */
.k-grid .k-table-td:hover {
  background-color: #f5f9ff !important;
}

/* Preserve first column background */
.k-grid .k-table-td:first-child:hover {
  background-color: #fafafa !important;
}

/* Keep selected cell styles even on hover */
.k-grid .k-table-td.k-selected:hover {
  background-color: #e3eeff !important;
}

/* Remove row selection styles */
.k-grid .k-table-row.k-selected > .k-table-td {
  background-color: inherit;
  color: inherit;
}

/* Add column selection styles */
.k-grid .k-table-td.k-selected {
  background-color: #e3eeff !important;
  color: #333333 !important;
}

.k-grid .k-table-td.k-selected:hover {
  background-color: #d1e3ff !important;
}

/* Cell selection styles - override any existing styles */
.k-grid td.k-selected,
.k-grid .k-table-td.k-selected {
  background-color: #e3eeff !important;
  color: #333333 !important;
  border: 1px solid #c7d7f7 !important;
}

/* Selection hover state */
.k-grid td.k-selected:hover,
.k-grid .k-table-td.k-selected:hover {
  background-color: #d1e3ff !important;
}

/* Make sure selection takes priority over regular hover */
.k-grid .k-table-td:hover.k-selected {
  background-color: #d1e3ff !important;
}

/* Keep first column protected from selection styles */
.k-grid .k-table-td:first-child.k-selected {
  background-color: #fafafa !important;
}

.k-checkbox {
  box-shadow: 0 0 0 0 !important;
  border-color: #666;
}

tbody > tr > td > span > .k-checkbox {
  display: block !important;
}
.k-checkbox:hover {
  box-shadow: 0 0 0 0 !important;
  background-color: #ebf3ff;
  border-color: #4061c7;
}
.k-checkbox:checked:hover {
  box-shadow: 0 0 0 0;
  background-color: #152b7a !important;
}
.k-checkbox:checked {
  border-color: #4061c7 !important;
  color: white;
  background-color: #4061c7 !important;
  display: block;
}

.k-checkbox:indeterminate {
  accent-color: #fff !important;
  background-image: url("../../../resources/images/check-box-disabled-intermediate.svg") !important;
  background-size: 10px;
}
.k-checkbox:indeterminate:hover {
  accent-color: #fff;
  background-color: #152b7a !important;
}

.k-list-item.k-selected,
.k-selected.k-list-optionlabel {
  color: #fff;
  background-color: #4061c7;
}

.k-list-item.k-selected:hover,
.k-selected.k-list-optionlabel:hover,
.k-list-item.k-selected.k-hover,
.k-selected.k-hover.k-list-optionlabel {
  color: #fff;
  background-color: #4061c7;
}

.k-grid .k-table-td {
  border-bottom: solid #e6e6e6;
  border-width: 1px;
  color: #333;
  border-bottom: 1px solid #e6e6e6 !important;
  border-right: 1px solid #e6e6e6 !important;
  transition: background-color 0.2s ease;
}

/* Header columns background */
.k-grid .k-table-th {
  background-color: #fafafa !important;
  border-right: 1px solid #e6e6e6 !important;
  border-bottom: none !important;
  height: 50xp !important;
}

/* Custom header cell styles */
.k-grid .k-table-th {
  padding: 0 !important;
  background-color: #fafafa !important;
}

/* Header font weight */
.k-grid .k-table-th {
  font-weight: 500 !important;
}

.k-grid .k-table-th .k-cell-inner,
.k-grid .k-table-th .k-link {
  font-weight: 500 !important;
}

/* Header text styling */
.k-grid .k-table-th {
  font-weight: 500 !important;
}

.caption-m {
  font-family: "Helvetica Neue", sans-serif;
  font-weight: 500;
  font-size: 12px;
  line-height: 16px;
}

/* Header content alignment */
.k-grid .k-table-th > .k-cell-inner {
  justify-content: center !important;
  height: 54px !important;
}

.k-grid .k-table-th > .k-cell-inner > .k-link {
  justify-content: center !important;
  width: 100% !important;
}

/* Keep hover and selected states consistent */
.k-grid .k-table-th:hover {
  background-color: #fafafa !important;
}

.k-grid .k-table-th {
  border-right: 1px solid #e6e6e6 !important;
}

.k-grid .k-table-row:first-child .k-table-td {
  border-top: none !important;
}

.k-grid .k-table {
  border-bottom-width: 1px;
  border-collapse: collapse;
}

.k-grid-pager {
  position: fixed;
  bottom: 0;
  width: calc(100% - 2.7rem);
}

@media (width < 1517px) {
  .k-grid-pager {
    width: calc(100% - 2.7rem);
  }
}

@media (width > 1439px) {
  .k-grid-pager {
    width: calc(100% - 2.7rem);
  }
}

@media (width < 1280px) {
  .k-grid-pager {
    width: calc(100% - 2.7rem);
  }
}

@media (width > 1919px) {
  .k-grid-pager {
    width: calc(100% - 2.7rem);
  }
}

.k-grid td:first-child,
.k-grid .k-table-td:first-child {
  border-right-width: 0px;
  border-left: none !important;
  background-color: #fafafa !important;
}

.k-grid .k-table-td:nth-child(2) {
  border-left: none !important;
}

/* Add vertical border for the last column */
.k-grid .k-table-td:last-child {
  border-right: 1px solid #e6e6e6 !important;
}

.k-grid .k-table-th:last-child {
  border-right: 1px solid #e6e6e6 !important;
}

.k-grid-norecords-template {
  width: 100%;
  display: block;
  border-width: 0px;
  height: 100%;
}

.k-grid-table-wrap {
  height: 100%;
}

.k-grid {
  border-right-width: 0px;
  border-left-width: 0px;
  border-right-width: 1px !important;
  overflow: visible !important;
}

.k-toolbar {
  background-color: #fff;
  width: 100%;
  justify-content: space-between !important;
}

.k-toolbar::before {
  content: none;
}

.k-grid td:first-child,
.k-grid .k-table-td:nth-child(2) {
  border-left-width: 0px;
}

.k-sorted {
  background-color: #fafafa !important;
}

.k-table-tbody tr:first-child > td {
  border-top-width: 0px;
}

.k-table-row:hover .custom-cell {
  display: flex;
}

.k-grid-content-sticky::before {
  border-right: none !important;
  box-shadow: rgb(100 100 111 / 20%) 0px 20px 30px 0px !important;
  border-top: none !important;
}

.k-table-th.k-grid-header-sticky {
  border: 1px solid #e6e6e6 !important;
  border-top: none !important;
  box-shadow: rgb(100 100 111 / 20%) 0px 20px 30px 0px !important;
  border-right: none !important;
}

.k-grid .k-grid-header .k-table {
  border-collapse: separate !important;
  margin-left: -1px !important;
}

.k-grid .k-table-th {
  border-bottom: none !important;
}

.k-grid .k-grid-header-wrap {
  border-right: none !important;
}

.k-grid-content {
  padding-right: 1px;
  height: 100%;
  overflow-x: visible !important;
}
.k-text-ellipsis {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}

.k-calendar-td.k-selected.k-focus {
  background-color: var(--kendo-color-primary-active, #123227);
}

.k-master-row.k-table-row.k-selected td.k-grid-content-sticky::before {
  background-color: #e3eeff;
}

/* First column (Checkbox column) background */
.k-grid .k-table-td:first-child {
  background-color: #fafafa !important;
}

.k-grid .k-table-th:first-child {
  background-color: #fafafa !important;
}

/* Prevent hover from changing the background */
.k-grid .k-table-row:hover .k-table-td:first-child {
  background-color: #fafafa !important;
}

/* Hide default dropdown icon */
.k-dropdownlist .k-input-button {
  /* display: none !important; */
}

.legends-dropdown .k-input-button {
  display: none !important;
} 

/* Remove dropdown borders and background */
.k-dropdownlist {
  /* border: none; */
  background-color: transparent;
}

.k-dropdownlist:hover,
.k-dropdownlist.k-focus {
  /* border: none !important; */
  box-shadow: none !important;
}

.k-dropdownlist .k-input-inner {
  background-color: transparent !important;
}

/* Completely remove row selection styles */
.k-grid .k-selected,
.k-grid tr.k-selected > td,
.k-grid .k-table-row.k-selected {
  background-color: transparent !important;
  color: inherit !important;
  border-color: inherit !important;
}

/* Enhance cell selection styles */
.k-grid td.k-selected {
  background-color: #e3eeff !important;
  color: #333333 !important;
  border: 1px solid #c7d7f7 !important;
}

.k-grid td.k-selected:hover {
  background-color: !important;
}

/* Prevent row hover from overriding cell selection */
/* .k-grid tr:hover td.k-selected {
  background-color: #d1e3ff !important;
} */

/* Remove all row selection styles */
.k-grid tr.k-selected,
.k-grid .k-table-row.k-selected,
.k-grid tr.k-selected td,
.k-grid .k-table-row.k-selected > .k-table-td {
  background-color: transparent !important;
  color: inherit !important;
}

/* Add cell selection styles */
.k-grid .k-table-td.k-selected {
  background-color: #00000 !important;
  color: #333333 !important;
}

/* Maintain cell selection on hover */
.k-grid .k-table-row:hover .k-table-td.k-selected {
  background-color: #e3eeff !important;
}

/* Cell hover styles */
.k-grid .k-table-td {
  transition: background-color 0.2s ease;
}

.k-grid .k-table-td:hover {
  background-color: #edda70 !important;
  cursor: pointer;
}

/* Preserve first column background */
.k-grid .k-table-td:first-child,
.k-grid .k-table-td:first-child:hover {
  background-color: #fafafa !important;
  cursor: default;
}

/* Selected cell state should override hover */
.k-grid .k-table-td.k-selected,
.k-grid .k-table-td.k-selected:hover {
  background-color: #e3eeff !important;
}

/* Reset all hover styles first */
.k-grid .k-table-row:hover,
.k-grid .k-table-tbody > .k-table-row:not(.k-detail-row):hover,
.k-grid .k-table-row.k-selected:hover {
  background-color: transparent !important;
}

/* Basic cell styles */
.k-grid .k-table-td {
  position: relative;
  cursor: pointer;
}

/* Cell hover effect */
.k-grid .k-table-td:not(:first-child):hover {
  position: relative;
  background-color: #edda70 !important;
}

/* Protect first column */
.k-grid .k-table-td:first-child {
  background-color: #fafafa !important;
  cursor: default;
}

/* Make sure selected state has priority */
.k-grid .k-table-td.k-selected {
  background-color: #e3eeff !important;
}

/* Reset all existing selection and hover styles */
.k-grid .k-selected,
.k-grid tr.k-selected,
.k-grid .k-table-row.k-selected,
.k-grid tr.k-selected > td,
.k-grid .k-table-row.k-selected > .k-table-td {
  background-color: transparent !important;
}

/* Basic cell styles */
.k-grid .k-table-td {
  cursor: pointer;
  transition: background-color 0.2s ease;
}

/* Cell hover */
.k-grid .k-table-td:not(:first-child):hover {
  background-color: #edda70 !important;
}

/* Selected cell */
.k-grid .k-table-td.k-selected,
.k-grid td.k-selected {
  background-color: #e3eeff !important;
  border: 1px solid #c7d7f7 !important;
}

/* First column protection */
.k-grid .k-table-td:first-child,
.k-grid .k-table-td:first-child:hover,
.k-grid .k-table-td:first-child.k-selected {
  background-color: #fafafa !important;
  cursor: default;
}

/* Core cell styles */
.k-grid td,
.k-grid .k-table-td {
  position: relative;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.k-table-td .k-selected {
  background-color: #e3eeff !important;
  border: 1px solid #c7d7f7 !important;
}

/* First column protection */
.k-grid td:first-child,
.k-grid .k-table-td:first-child,
.k-grid-header .k-table-th:first-child {
  background-color: #fafafa !important;
  cursor: default;
}
.Notestable .k-grid td:first-child {
  background-color: #ffffff !important;
  border-top: 1px solid #e6e6e6;
  border-bottom: 1px solid #e6e6e6;
  border-right: 1px solid #e6e6e6;
}

/* Selected cell styles */
.k-grid .k-table-td.k-selected {
  background-color: #e3eeff !important;
  color: #333333 !important;
  border: 1px solid #c7d7f7 !important;
}

/* Selected cell hover state */
.k-grid .k-table-td.k-selected:hover {
  background-color: #d1e3ff !important;
}

/* Make sure first column can't be selected */
.k-grid .k-table-td:first-child.k-selected {
  background-color: #fafafa !important;
}

.k-picker-solid:hover,
.k-picker-solid.k-hover {
  background-color: transparent;
}
