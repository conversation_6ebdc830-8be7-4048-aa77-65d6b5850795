/**
 * @jest-environment jsdom
 */
import React from 'react';
import { render, screen, fireEvent, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import FinancialDocument from './financial-document';

// Mock the imported components
jest.mock('./public-filings', () => {
  return jest.fn(({ onSelectedChipsChange, isFileLimitReached, selectedChipsCount }) => (
    <div data-testid="public-filings-mock">
      <span>Selected Chips: {selectedChipsCount}</span>
      <span>File Limit Reached: {isFileLimitReached ? 'Yes' : 'No'}</span>
      <button 
        data-testid="increment-chips-btn" 
        onClick={() => onSelectedChipsChange(selectedChipsCount + 1)}
      >
        Increment Chips
      </button>
    </div>
  ));
});

jest.mock('../../kendo-upload/upload-box', () => {
  return jest.fn(({ onUploadedFilesCountChange, isFileLimitReached, uploadedFilesCount }) => (
    <div data-testid="upload-box-mock">
      <span>Uploaded Files: {uploadedFilesCount}</span>
      <span>File Limit Reached: {isFileLimitReached ? 'Yes' : 'No'}</span>
      <button 
        data-testid="increment-uploads-btn" 
        onClick={() => onUploadedFilesCountChange(uploadedFilesCount + 1)}
      >
        Increment Uploads
      </button>
    </div>
  ));
});

describe('FinancialDocument Component', () => {
  const defaultProps = {
    selectedCompanyDetails: { acuity_id: '123' },
    publicfilingsref: { current: {} },
    uploadFilesref: { current: {} },
    companyID: 'company123',
    processID: 'process456',
    alexnaderiaUploadDetails: jest.fn(),
    fileDetailsForAlexanderiaUpload: {},
    setUploadDocumentDetails: jest.fn(),
    uploadDocumentDetails: {},
    setSelectedFilingsCount: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders the component with initial state', () => {
    render(<FinancialDocument {...defaultProps} />);
    
    // Check if the title is rendered
    expect(screen.getByText('Financial Document Source')).toBeInTheDocument();
    
    // Check if the file count indicator is rendered
    expect(screen.getByText('0/10')).toBeInTheDocument();
    
    // Check if mocked components are rendered
    expect(screen.getByTestId('public-filings-mock')).toBeInTheDocument();
    expect(screen.getByTestId('upload-box-mock')).toBeInTheDocument();
    
    // Check file limit is not reached initially
    expect(screen.getByText('File Limit Reached: No')).toBeInTheDocument();
  });

  test('updates selectedChipsCount when PublicFillings triggers callback', () => {
    render(<FinancialDocument {...defaultProps} />);
    
    // Check initial count
    expect(screen.getByText('Selected Chips: 0')).toBeInTheDocument();
    
    // Increment the chips count
    fireEvent.click(screen.getByTestId('increment-chips-btn'));
    
    // Check updated count
    expect(screen.getByText('Selected Chips: 1')).toBeInTheDocument();
    
    // Check if setSelectedFilingsCount was called
    expect(defaultProps.setSelectedFilingsCount).toHaveBeenCalledWith(1);
  });

  test('updates uploadedFilesCount when UploadBox triggers callback', () => {
    render(<FinancialDocument {...defaultProps} />);
    
    // Check initial count
    expect(screen.getByText('Uploaded Files: 0')).toBeInTheDocument();
    
    // Increment the uploads count
    fireEvent.click(screen.getByTestId('increment-uploads-btn'));
    
    // Check updated count
    expect(screen.getByText('Uploaded Files: 1')).toBeInTheDocument();
    
    // Check if setSelectedFilingsCount was called
    expect(defaultProps.setSelectedFilingsCount).toHaveBeenCalledWith(1);
  });

  test('sets isFileLimitReached to true when 10 files are selected/uploaded', () => {
    render(<FinancialDocument {...defaultProps} />);
    
    // Initial state
    expect(screen.getAllByText('File Limit Reached: No')[0]).toBeInTheDocument();
    
    // Add 5 chips
    for (let i = 0; i < 5; i++) {
      fireEvent.click(screen.getByTestId('increment-chips-btn'));
    }
    
    // Add 5 uploads to reach the limit
    for (let i = 0; i < 5; i++) {
      fireEvent.click(screen.getByTestId('increment-uploads-btn'));
    }
    
    // Check if file limit is reached
    expect(screen.getAllByText('File Limit Reached: Yes')[0]).toBeInTheDocument();
    
    // Check total count in header
    expect(screen.getByText('10/10')).toBeInTheDocument();
  });

  test('resets state when selectedCompanyDetails changes', () => {
    const { rerender } = render(<FinancialDocument {...defaultProps} />);
    
    // Add some selections
    fireEvent.click(screen.getByTestId('increment-chips-btn'));
    fireEvent.click(screen.getByTestId('increment-uploads-btn'));
    
    // Verify counts
    expect(screen.getByText('Selected Chips: 1')).toBeInTheDocument();
    expect(screen.getByText('Uploaded Files: 1')).toBeInTheDocument();
    
    // Change selected company
    rerender(<FinancialDocument 
      {...defaultProps} 
      selectedCompanyDetails={{ acuity_id: '456' }} 
    />);
    
    // Check if counts are reset
    expect(screen.getByText('Selected Chips: 0')).toBeInTheDocument();
    expect(screen.getByText('Uploaded Files: 0')).toBeInTheDocument();
    
    // Check if setSelectedFilingsCount was called with 0
    expect(defaultProps.setSelectedFilingsCount).toHaveBeenCalledWith(0);
  });

  test('exposes resetfinancialcompanyState method via ref', () => {
    const ref = React.createRef();
    render(<FinancialDocument {...defaultProps} ref={ref} />);
    
    // Add some selections
    fireEvent.click(screen.getByTestId('increment-chips-btn'));
    fireEvent.click(screen.getByTestId('increment-uploads-btn'));
    
    // Verify counts
    expect(screen.getByText('Selected Chips: 1')).toBeInTheDocument();
    expect(screen.getByText('Uploaded Files: 1')).toBeInTheDocument();
    
    // Call reset method via ref
    act(() => {
      ref.current.resetfinancialcompanyState();
    });
    
    // Check if counts are reset
    expect(screen.getByText('Selected Chips: 0')).toBeInTheDocument();
    expect(screen.getByText('Uploaded Files: 0')).toBeInTheDocument();
    
    // Check if setSelectedFilingsCount was called with 0
    expect(defaultProps.setSelectedFilingsCount).toHaveBeenCalledWith(0);
  });

  test('displays info tooltip on hover', () => {
    render(<FinancialDocument {...defaultProps} />);
    
    // Check if info icon is present
    const infoIcon = screen.getByText('0/10').nextSibling;
    expect(infoIcon).toBeInTheDocument();
    
    // Check tooltip text
    const tooltip = infoIcon.querySelector('span');
    expect(tooltip).toHaveTextContent('At max only 10 files can be selected or uploaded.');
    expect(tooltip).toHaveTextContent('Example: 5 files selected from public filing & 5 files uploaded = 10 files');
  });
}); 