import React from "react";
import { SpinnerCircle } from "../../../partials/atoms/loader";
import { FaTimes } from "react-icons/fa";
import useDocumentViewerStore from "./document-viewer.store";

const DocumentViewerLoading = () => {
  const { setPreviewClose } = useDocumentViewerStore((state) => state);
  return (
    <>
      <div className="flex h-12 justify-end border-b">
        <button
          title="Close preview"
          className="flex pr-3 items-center text-neutral-60"
          onClick={setPreviewClose}
          data-testid="close-button"
        >
          <FaTimes title="Close preview" />
        </button>
      </div>

      <div
        data-testid="document-viewer-loading"
        className="my-auto flex h-full w-full grow flex-col justify-center text-center"
      >
        <SpinnerCircle data-testid="spinner-circle" />
        <span
          data-testid="loading-message"
          className="body-r pt-4 text-neutral-60"
        >
          Please wait, we are getting the file ready
        </span>
      </div>
    </>
  );
};

export default DocumentViewerLoading;
