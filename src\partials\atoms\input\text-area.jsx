import React from "react";
import PropTypes from "prop-types";

const TextArea = ({ disabled = false, ...props }) => {
  return (
    <textarea
      data-testid="text-area"
      className={`h-32 w-full rounded border border-neutral-30 p-4 hover:border-primary-78 focus:outline-none focus:ring-0 
        focus-visible:outline-none disabled:border-neutral-20 disabled:bg-neutral-5`}
      disabled={disabled ? true : ""}
      {...props}
    />
  );
};

TextArea.propTypes = {
  disabled: PropTypes.bool,
  props: PropTypes.any
};

export default TextArea;
