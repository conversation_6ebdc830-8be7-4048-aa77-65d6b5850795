import React, { useState, forwardRef, useImperativeHandle } from "react";
import PropTypes from "prop-types";
import Dropdown from "../../components/dropdown";
import { DropdownItemsEnum } from "./dropdown-list";
import RadioButton from "../../partials/atoms/radio-button/radio";
import { radioOptions } from "./radio-option";

const EMPTY_STRING = "";
const Template = forwardRef((props, ref) => {
  const [selectedOption, setSelectedOption] = useState(
    DropdownItemsEnum.TEMPLATE_BASED
  );
  const [selectedTemplate, setSelectedTemplate] = useState(
    DropdownItemsEnum.CHOOSE_TEMPLATES
  );

  useImperativeHandle(ref, () => ({
    resetTemplateState() {
      setSelectedOption(DropdownItemsEnum.TEMPLATE_BASED);
      setSelectedTemplate(DropdownItemsEnum.CHOOSE_TEMPLATES);
      props.setSelectedTemplateType(DropdownItemsEnum.CHOOSE_TEMPLATES);
      props.setSelectedTemplateOption(DropdownItemsEnum.TEMPLATE_BASED);
    }
  }));

  const handleRadioChange = (event) => {
    setSelectedOption(event.target.value);
    props.setSelectedTemplateOption(event.target.value);
    props.setSelectedTemplateType(DropdownItemsEnum.CHOOSE_TEMPLATES);
    setSelectedTemplate(DropdownItemsEnum.CHOOSE_TEMPLATES);
    props.onOptionChange(event.target.value);
  };

  const handleTemplateSelection = (item) => {
    setSelectedTemplate(item);
    props.setSelectedTemplateType(item);
  };

  const dropdownItems = DropdownItemsEnum.TEMPLATE_NAME;
  const dropdownPlaceholder = DropdownItemsEnum.CHOOSE_TEMPLATES;

  const option1 = {
    label: radioOptions[0].TemplateOption1,
    value: radioOptions[0].value
  };
  const option2 = {
    label: radioOptions[1].TemplateOption2,
    value: radioOptions[1].value
  };
  const option3 = {
    label: radioOptions[2].TemplateOption3,
    value: radioOptions[2].value
  };

  return (
    <div className="template" data-testid="template">
      <div
        className={`text-gray-600 text-base flex w-full border-gray-300 ${selectedOption === DropdownItemsEnum.TEMPLATE_BASED ? "heading-2-m" : "h-12 m-m"} border ${selectedOption === DropdownItemsEnum.TEMPLATE_BASED ? "border-gray-300" : "border-gray-300"} rounded-lg flex-col flex-nowrap ${selectedOption === DropdownItemsEnum.TEMPLATE_BASED ? EMPTY_STRING : "hover:bg-primary-35"}`}
      >
        <span
          className={`${selectedOption === DropdownItemsEnum.TEMPLATE_BASED ? "bg-primary-43 heading-2-m border-gray-300" : EMPTY_STRING} h-1/2 rounded-tr-lg rounded-tl-lg hover:bg-white`}
        >
          <span
            className={`h-full flex items-center hover:bg-primary-35 ${selectedOption === DropdownItemsEnum.TEMPLATE_BASED ? "bg-bg-primary-43 heading-2-m border-gray-300" : "pt-3 m-m pt-5"} rounded-tr-lg rounded-tl-md`}
          >
            <RadioButton
              options={[option1]}
              name="group1"
              selectedValue={selectedOption}
              onChange={handleRadioChange}
              data-testid="template-based"
            />
          </span>
        </span>
        {selectedOption === DropdownItemsEnum.TEMPLATE_BASED && (
          <span className="flex justify-center pt-3 pl-4 pr-4 pb-4 hover:bg-white">
            <Dropdown
              items={dropdownItems}
              placeholder={dropdownPlaceholder}
              disabled={false}
              selectedValue={selectedTemplate}
              setSelectedValue={handleTemplateSelection}
            />
          </span>
        )}
      </div>
      <div className="my-2">
        <div
          className={`w-full flex items-center border rounded-lg pr-5 ${selectedOption === DropdownItemsEnum.AS_REPORTED ? "bg-primary-43 heading-2-m" : "m-m"} hover:bg-primary-35`}
        >
          <span className="">
            <RadioButton
              options={[option2]}
              name="group1"
              selectedValue={selectedOption}
              onChange={handleRadioChange}
              data-testid="as-reported"
            />
          </span>
        </div>
      </div>
      <div
        className={`w-full flex items-center border rounded-lg pr-5 ${selectedOption === DropdownItemsEnum.SPECIFIC_KPIS ? "bg-primary-43 heading-2-m" : "m-m"} hover:bg-primary-35`}
      >
        <span
          className={`${selectedOption === DropdownItemsEnum.SPECIFIC_KPIS ? "heading-2-m" : "m-m"}`}
        >
          <RadioButton
            options={[option3]}
            name="group1"
            selectedValue={selectedOption}
            onChange={handleRadioChange}
            data-testid="specific-kpis"
          />
        </span>
      </div>
    </div>
  );
});
Template.propTypes = {
  setSelectedTemplateType: PropTypes.func.isRequired,
  setSelectedTemplateOption: PropTypes.func.isRequired,
  onOptionChange: PropTypes.func.isRequired
};

export default Template;
