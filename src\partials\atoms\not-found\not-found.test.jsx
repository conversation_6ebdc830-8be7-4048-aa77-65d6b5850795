import React from "react";
import { render, screen } from "@testing-library/react";
import NotFound from "./not-found";

describe("NotFound", () => {
  it("should render correctly", () => {
    render(<NotFound />);
    expect(screen.getByText("Not Found")).toBeInTheDocument();
  });

  it("should render data correctly", () => {
    const data = "Data not found";
    render(<NotFound data={data} />);
    expect(screen.getByText("Data not found")).toBeInTheDocument();
  });
});
