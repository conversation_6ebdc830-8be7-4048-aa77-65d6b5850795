import React from "react";
import PropTypes from "prop-types";
import { BsClockHistory, BsArrowLeft } from "react-icons/bs";
import ButtonIcon from "../partials/atoms/button/button-icon";
import Button from "../partials/atoms/button/button";
import { ToolTip } from "../partials/atoms/tool-tip";
import { DropdownItemsEnum } from "../components/Template/dropdown-list";
import { SpinnerCircle } from "../partials/atoms/loader";
import { BiRefresh } from "react-icons/bi";

const HeaderAction = ({
  showHistory,
  handleBackButton,
  handleHistoryVisibility,
  handleExtractVisibility,
  selectedCompany,
  selectedFilingsCount,
  selectedTemplateOption,
  selectedTemplateType,
  isHistoryLoading,
  isExtracting
}) => {
  const isExtractButtonDisabled = () => {
    return (
      !selectedCompany ||
      selectedFilingsCount === 0 ||
      isExtracting ||
      (selectedTemplateOption === DropdownItemsEnum.TEMPLATE_BASED &&
        selectedTemplateType === DropdownItemsEnum.CHOOSE_TEMPLATES)
    );
  };

  return (
    <div className="py-2.5 px-4 flex justify-between items-center">
      <div className="flex items-center">
        {showHistory && (
          <ButtonIcon
            data-testid="back-button"
            intent={"teritory"}
            onClick={handleBackButton}
          >
            <BsArrowLeft className="h-4 w-4" />
          </ButtonIcon>
        )}
        <h1 className="ml-2 heading-2-m text-[16px] font-[500] leading-[28px] text-[#333333]">
          {!showHistory ? "Financial Spreader" : "Spread History"}
        </h1>
      </div>
      <div className="flex flex-row items-center gap-2">
        {showHistory && (
          <ButtonIcon
            data-testid="history-button"
            intent={"secondary"}
            data-tooltip-id="tool-tip-refresh-history-top"
            onClick={handleHistoryVisibility}
          >
            <BiRefresh className="h-4 w-4" />
            <ToolTip
              place={"left"}
              text={"Refresh History"}
              toolTipId={"tool-tip-refresh-history-top"}
            />
          </ButtonIcon>
        )}
        {!showHistory && (
          <>
            <ButtonIcon
              data-testid="history-button"
              intent={"secondary"}
              data-tooltip-id="tool-tip-history-top"
              onClick={() => handleHistoryVisibility()}
            >
              {isHistoryLoading ? (
                <SpinnerCircle size={6} className="flex" />
              ) : (
                <BsClockHistory className="h-4 w-4" />
              )}
              <ToolTip
                place={"left"}
                text={"Spread History"}
                toolTipId={"tool-tip-history-top"}
              />
            </ButtonIcon>
            <Button
              data-testid="extract-button"
              disabled={isExtractButtonDisabled()}
              onClick={() => handleExtractVisibility()}
            >
              {isExtracting ? (
                <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white"></div>
              ) : (
                "Extract"
              )}
            </Button>
          </>
        )}
      </div>
    </div>
  );
};
HeaderAction.propTypes = {
  showHistory: PropTypes.bool.isRequired,
  handleBackButton: PropTypes.func.isRequired,
  handleHistoryVisibility: PropTypes.func.isRequired,
  handleExtractVisibility: PropTypes.func.isRequired,
  selectedCompany: PropTypes.string,
  selectedFilingsCount: PropTypes.number,
  selectedTemplateOption: PropTypes.string,
  selectedTemplateType: PropTypes.string,
  isHistoryLoading: PropTypes.bool,
  isExtracting: PropTypes.bool
};

export default HeaderAction;
