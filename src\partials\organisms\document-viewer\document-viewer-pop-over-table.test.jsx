import { fireEvent, render, screen } from "@testing-library/react";
import DocumentViewerPopOverTable from "./document-viewer-pop-over-table";
import TestAppRenderer from "../../../infra/test-utils/test-app-renderer";
import useDocumentViewerStore from "./document-viewer.store";
import { POST_BUCKET_API } from "../../../infra/api/bucket-service";

jest.mock("../../../infra/api/bucket-service", () => ({
  POST_BUCKET_API: jest.fn()
}));
jest.mock("../../../infra/api/highlight-service", () => ({
  DELETE_HIGHLIGHT_API: jest.fn(),
  POST_HIGHLIGHT_API: jest.fn()
}));

describe("Component DocumentViewerPopOverHighLight render", () => {
  it("should render DocumentViewerPopOverHighLight without value", () => {
    POST_BUCKET_API.mockImplementation(() => ({
      mutateAsync: jest
        .fn()
        .mockImplementationOnce(() => ({
          success: false
        }))
        .mockImplementation(() => ({
          success: true
        }))
    }));

    let store = useDocumentViewerStore.getState();
    store.setShowTablePopOver(true);
    store.setPreviewFilingsMetaData({
      acuityId: "test",
      acuitySecurityId: "testSecurityId",
      ticker: "AAPL",
      companyName: "Apple",
      id: "testId",
      doc: "https:test.com",
      docId: "testId",
      filingType: "10Q",
      filingDate: new Date()
    });
    store.setHighlightedMetaData({ tableId: "testTableId" });
    store.setSelectedText("copied");

    render(
      <TestAppRenderer>
        <DocumentViewerPopOverTable />
      </TestAppRenderer>
    );
    expect(
      screen.getByTestId("document-viewer-pop-over-table")
    ).toBeInTheDocument();

    fireEvent.click(screen.getByTestId("document-viewer-copy-table-button"));
    fireEvent.click(screen.getByTestId("document-viewer-copy-table-button"));

    fireEvent.click(
      screen.getByTestId("document-viewer-download-table-button")
    );
    fireEvent.click(screen.getByTestId("document-viewer-cancel-button"));
  });
});
