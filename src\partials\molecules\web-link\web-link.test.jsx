import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import WebLinkComponent from "./web-link";
import useAiPlaygroundFilterStore from "../../../pages/ai-playground/templates/ai-playground-header/ai-playground-filter/ai-playgroud-filter.store";

jest.mock("../toaster");
jest.mock(
  "../../../pages/ai-playground/templates/ai-playground-header/ai-playground-filter/ai-playgroud-filter.store"
);

const mockStore = {
  selectedWebLinks: [],
  setSelectedWebLinks: jest.fn()
};

useAiPlaygroundFilterStore.mockReturnValue(mockStore);

describe("WebLinkComponent", () => {
  const setWebLinks = jest.fn();
  const webLinks = [1];
  const index = 1;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should render the component", () => {
    render(
      <WebLinkComponent
        setWebLinks={setWebLinks}
        webLinks={webLinks}
        index={index}
      />
    );
    expect(screen.getByPlaceholderText("Enter here")).toBeInTheDocument();
  });

  it("should show an error for an invalid URL", () => {
    render(
      <WebLinkComponent
        setWebLinks={setWebLinks}
        webLinks={webLinks}
        index={index}
      />
    );
    fireEvent.change(screen.getByPlaceholderText("Enter here"), {
      target: { value: "invalid-url" }
    });
    expect(screen.getByPlaceholderText("Enter here")).toHaveClass(
      "border-negative-100"
    );
  });

  it("should clear the query when the cross button is clicked", () => {
    render(
      <WebLinkComponent
        setWebLinks={setWebLinks}
        webLinks={webLinks}
        index={index}
      />
    );
    fireEvent.change(screen.getByPlaceholderText("Enter here"), {
      target: { value: "https://example.com" }
    });
    fireEvent.click(screen.getByTestId("ai-playground-search-cross"));
    expect(screen.getByPlaceholderText("Enter here")).toHaveValue("");
  });
});
