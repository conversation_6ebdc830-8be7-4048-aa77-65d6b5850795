import React, { Fragment } from "react";
import { PropTypes } from "prop-types";
import {
  Listbox,
  Transition,
  ListboxButton,
  ListboxOptions,
  ListboxOption
} from "@headlessui/react";
import clsx from "clsx";

const GridPagerDropdown = ({ selected, setSelected, pagerData }) => {
  return (
    <div>
      <Listbox value={selected} onChange={setSelected} as="ul">
        {({ open }) => (
          <div className="relative">
            <ListboxButton
              id="data-range-button"
              data-testid="data-range-button"
              className={`data-range-button-group flex h-8 w-16 cursor-pointer place-items-center justify-between gap-2 rounded border bg-white px-4 py-1.5 text-neutral-80`}
            >
              <span className="absolute inset-y-0 right-1 flex items-center text-neutral-80">
                {selected}
                <div
                  className={`text-neutral-60 duration-300 hover:text-neutral-60 ${
                    open && "rotate-180"
                  }`}
                  aria-expanded={open}
                  aria-controls={`detail-${selected}`}
                >
                  <span className="sr-only">Menu</span>
                  <svg className="h-8 w-8 fill-current" viewBox="0 0 32 32">
                    <path d="M16 20l-5.4-5.4 1.4-1.4 4 4 4-4 1.4 1.4z" />
                  </svg>
                </div>
              </span>
            </ListboxButton>
            <Transition
              as={Fragment}
              leave="transition ease-in duration-100"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <ListboxOptions
                data-testid="list-item"
                className="absolute -top-2 z-30 mt-1 max-h-60 w-full -translate-y-full transform overflow-auto bg-white shadow-xr"
              >
                {pagerData.map((item) => (
                  <ListboxOption
                    data-testid={`asset-table-data-range-item-${item}`}
                    key={`asset-table-data-range-filter-drop-down-${item}`}
                    className={({ focus }) =>
                      `asset-table-data-range-item flex cursor-pointer select-none place-items-center justify-start gap-2 px-4 py-2.5 text-neutral-80 ${clsx(
                        focus && "bg-primary-35",
                        selected === item
                          ? "s-m bg-primary-40 text-primary-78"
                          : "s-r"
                      )}`
                    }
                    value={item}
                  >
                    <div
                      className={`content-center ${clsx(
                        selected === item
                          ? "s-m bg-primary-40 text-primary-78"
                          : "s-r"
                      )}`}
                    >
                      {item}
                    </div>
                  </ListboxOption>
                ))}
              </ListboxOptions>
            </Transition>
          </div>
        )}
      </Listbox>
    </div>
  );
};

GridPagerDropdown.propTypes = {
  selected: PropTypes.number,
  setSelected: PropTypes.func,
  pagerData: PropTypes.array
};

export default GridPagerDropdown;
