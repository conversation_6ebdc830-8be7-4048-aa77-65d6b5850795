import React from "react";
import PropTypes from "prop-types";
import { HiOutlineTrash } from "react-icons/hi";
import { BsArrowBarUp, BsArrowBarDown } from "react-icons/bs";
import { BiComment } from "react-icons/bi";
import ButtonIcon from "../../partials/atoms/button/button-icon";
import {
  AiOutlineInsertRowAbove,
  AiOutlineInsertRowBelow
} from "react-icons/ai";
import { Button } from "../../partials/atoms/button";
import { FaTimes } from "react-icons/fa";

const PopupComponent = ({
  showPopup,
  setShowPopup,
  addRow,
  addRowToBottom,
  deleteRow,
  mergeRowUp,
  mergeRowBelow,
  makeHeader,
  selectedCount,
  clearAllCheckedItems,
  isAllRowsSelected
}) => {
  if (!showPopup) return null;
  const isMultiSelect = selectedCount > 1;
  return (
    <div className=" fixed bottom-6 left-1/2 transform -translate-x-1/2 !z-[9999]">
      <div className="bg-white flex gap-4 flex-row items-center rounded-lg border border-neutral-10 py-3 px-5 shadow-lg ">
        <div className="font-body-b text-body-b whitespace-nowrap text-neutral-80">
          {selectedCount} row(s) selected:
        </div>
        <div className="flex flex-row items-center gap-2">
          <h2 className="text-body-r font-body-r text-neutral-80 whitespace-nowrap">
            Merge row:
          </h2>
          <ButtonIcon
            intent={"teritory"}
            disabled={isMultiSelect}
            onClick={() => {
              mergeRowUp();
              setShowPopup(false);
            }}
            data-tooltip-id="merge-up-tooltip"
            data-tooltip-content={isMultiSelect ? "Cannot merge multiple rows" : "Merge with row above"}
          >
            <BsArrowBarUp />
          </ButtonIcon>
          <ButtonIcon
            intent={"teritory"}
            disabled={isMultiSelect}
            onClick={() => {
              mergeRowBelow();
              setShowPopup(false);
            }}
            data-tooltip-id="merge-down-tooltip"
            data-tooltip-content={isMultiSelect ? "Cannot merge multiple rows" : "Merge with row below"}
          >
            <BsArrowBarDown />
          </ButtonIcon>
        </div>

        <div className="border border-neutral-10 h-[24px]"></div>
        <div className="flex flex-row items-center gap-2">
          <h2 className="text-body-r font-body-r text-neutral-80 whitespace-nowrap">
            Add row:
          </h2>
          <ButtonIcon
            intent={"teritory"}
            disabled={isMultiSelect}
            onClick={() => {
              addRow();
              setShowPopup(false);
            }}
          >
            <AiOutlineInsertRowAbove className="h-4 w-4" />
          </ButtonIcon>

          <ButtonIcon
            intent={"teritory"}
            disabled={isMultiSelect}
            onClick={() => {
              addRowToBottom();
              setShowPopup(false);
            }}
          >
            <AiOutlineInsertRowBelow className="h-4 w-4" />
          </ButtonIcon>
        </div>
        <div className="border border-neutral-10 h-[24px]"></div>

        <Button
          intent={"teritory"}
          disabled={isMultiSelect}
          onClick={() => {
            makeHeader();
          }}
          className="whitespace-nowrap"
        >
          Make Header
        </Button>
        <div className="border border-neutral-10 h-[24px]"></div>
        <div className="flex flex-row items-center gap-2">
          <ButtonIcon intent={"teritory"} disabled={true}>
            <BiComment></BiComment>
          </ButtonIcon>
          <ButtonIcon
            intent={"teritorynegative"}
            disabled={isAllRowsSelected}
            onClick={() => {
              deleteRow();
              setShowPopup(false); // Close popup after deleting row
            }}
          >
            <HiOutlineTrash />
          </ButtonIcon>
        </div>
        <div className="border border-neutral-10 h-[24px]"></div>
        <div>
          <ButtonIcon
            intent={"teritory"}
            onClick={() => {
              setShowPopup(false);
              // Add a call to a new function that will clear all checkboxes
              clearAllCheckedItems();
            }}
          >
            <FaTimes className="text-neutral-60" />
          </ButtonIcon>
        </div>
      </div>
    </div>
  );
};

PopupComponent.propTypes = {
  showPopup: PropTypes.bool.isRequired,
  setShowPopup: PropTypes.func.isRequired,
  addRow: PropTypes.func.isRequired,
  addRowToBottom: PropTypes.func.isRequired,
  deleteRow: PropTypes.func.isRequired,
  makeHeader: PropTypes.func.isRequired,
  selectedCount: PropTypes.number.isRequired,
  clearAllCheckedItems: PropTypes.func.isRequired,
  mergeRowUp: PropTypes.func.isRequired,
  mergeRowBelow: PropTypes.func.isRequired,
  isAllRowsSelected: PropTypes.bool.isRequired
};

export default PopupComponent;
