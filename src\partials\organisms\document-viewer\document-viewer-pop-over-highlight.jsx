import React from "react";
import { Transition } from "@headlessui/react";
import ButtonIconText from "../../../partials/atoms/button/button-icon-text";
import ButtonIcon from "../../../partials/atoms/button/button-icon";
import { FaTimes } from "react-icons/fa";
import useDocumentViewerStore from "./document-viewer.store";
import { FiCopy, FiSave } from "react-icons/fi";
import { POST_HIGHLIGHT_API } from "../../../infra/api/highlight-service";
import { notify } from "../../../partials/molecules/toaster";
import { POST_BUCKET_API } from "../../../infra/api/bucket-service";
import { viewerUrl } from "../../../constants/config";
import { useCopyToClipboard } from "@uidotdev/usehooks";
import { generateShortId } from "../../../general/utils";
import { useQueryClient } from "@tanstack/react-query";

const DocumentViewerPopOverHighLight = () => {
  const queryClient = useQueryClient();
  const [_copiedText, copyToClipboard] = useCopyToClipboard();

  const {
    setShowHighLightPopOver,
    showHighLightPopOver,
    getPreviewMetaDataBody,
    validatePreviewMetaDataBody
  } = useDocumentViewerStore((state) => state);

  const createHighLight = POST_HIGHLIGHT_API(queryClient);
  const copyHighLightS3 = POST_BUCKET_API("highlights");

  const handleSaveHighlight = async () => {
    const body = getPreviewMetaDataBody();
    if (!validatePreviewMetaDataBody(body)) {
      notify.warning("Highlight did not get saved, please try again.");
      return;
    }
    const { success } = await createHighLight.mutateAsync(body);
    if (success) {
      notify.success("Highlight saved successfully");
      setShowHighLightPopOver(false);
    } else {
      notify.warning("Highlight did not get saved, please try again.");
    }
  };

  const handleCopyHighLightS3 = async () => {
    const body = getPreviewMetaDataBody();
    if (!validatePreviewMetaDataBody(body)) {
      notify.warning("Highlight did not get saved, please try again.");
      return;
    }
    const uid = generateShortId();

    const ifile = new File([JSON.stringify(body)], `${uid}.txt`, {
      type: "text/plain"
    });

    let file = new FormData();
    file.append("file", ifile);

    const { success } = await copyHighLightS3.mutateAsync(file);
    if (success) {
      notify.success(
        `Highlight’s shareable link copied on your clipboard successfully.`
      );
      setShowHighLightPopOver(false);
      getPublicLink(uid);
    } else {
      notify.warning("Highlight link not copied, please try again.");
    }
  };

  const getPublicLink = (id) => {
    const url = `${viewerUrl}/${id}`;
    copyToClipboard(url);
  };

  return (
    <Transition
      show={showHighLightPopOver}
      className={`body-r absolute bottom-14 left-0 right-0 z-50 mx-auto h-14 w-fit overflow-hidden rounded-lg bg-white drop-shadow-3xl`}
      enter="transition ease-out duration-300 transform"
      enterStart="opacity-0 -translate-y-2"
      enterEnd="opacity-100 translate-y-0"
      leave="transition ease-out duration-300"
      leaveStart="opacity-100"
      leaveEnd="opacity-0"
    >
      <ul data-testid="document-viewer-pop-over-highlight">
        <li className="flex w-full items-center justify-between gap-2 rounded-lg border border-neutral-10 px-5 py-3 text-neutral-80">
          <div>
            <ButtonIconText
              data-testid={
                "document-viewer-pop-over-highlight-copy-highlight-button"
              }
              intent={"teritory"}
              onClick={handleCopyHighLightS3}
            >
              <FiCopy className="size-4" />
              <span>Copy highlight link</span>
            </ButtonIconText>
          </div>
          <div>
            <ButtonIconText
              data-testid={
                "document-viewer-pop-over-highlight-save-highlight-button"
              }
              intent={"teritory"}
              onClick={handleSaveHighlight}
            >
              <FiSave className="size-4" />
              <span>Save highlight</span>
            </ButtonIconText>
          </div>
          <div className="mx-2 h-full border-l border-neutral-10">
            <span className="invisible">|</span>
          </div>
          <div>
            <ButtonIcon
              data-testid={"document-viewer-pop-over-highlight-close-button"}
              onClick={() => {
                setShowHighLightPopOver(false);
              }}
              intent={"teritory"}
            >
              <FaTimes className="size-4 text-neutral-60" />
            </ButtonIcon>
          </div>
        </li>
      </ul>
    </Transition>
  );
};

export default DocumentViewerPopOverHighLight;
