import { render, screen } from "@testing-library/react";
import Badge from "./badge";

describe("Badge Component", () => {
  it("should render Badge without value as a dot", () => {
    const { container } = render(<Badge />);
    
    const badge = screen.getByTestId("badge-with-out-value");
    expect(badge).toBeInTheDocument();
    
    // Check proper styling for dot variant
    expect(badge).toHaveClass("h-1.5", "w-1.5", "items-center", "rounded-3xl", "bg-negative-100");
    
    // Make sure the value is not displayed
    expect(container.textContent).toBe("");
  });
  
  it("should render Badge with value", () => {
    const testValue = 100;
    const { container } = render(<Badge value={testValue} />);
    
    const badge = screen.getByTestId("badge-with-value");
    expect(badge).toBeInTheDocument();
    
    // Check that the value is displayed correctly
    expect(badge).toHaveTextContent("100");
    
    // Check proper styling for badge with value
    const badgeContainer = badge.parentElement;
    expect(badgeContainer).toHaveClass(
      "flex", 
      "h-5", 
      "w-auto", 
      "min-w-5", 
      "items-center", 
      "rounded-3xl", 
      "bg-negative-100", 
      "px-1.5"
    );
    
    // Check text styling
    expect(badge).toHaveClass("caption-r", "m-auto", "text-white");
  });
  
  it("should handle zero as a valid value", () => {
    render(<Badge value={0} />);
    
    const badge = screen.getByTestId("badge-with-value");
    expect(badge).toBeInTheDocument();
    expect(badge).toHaveTextContent("0");
  });
});
