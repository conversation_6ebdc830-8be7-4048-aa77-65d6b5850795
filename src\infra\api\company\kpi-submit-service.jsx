import { post, put } from "../../../general/fetcher";
import { serviceUrl, baseUrl } from "../../../constants/config";

const SUBMIT_KPI_API_URL = serviceUrl + "api/data-ingestion/publish";
const UPDATE_JOB_STATUS_URL = baseUrl + "extraction/api/status-update";

/**
 * Service to submit KPI data to the backend
 * @param {Object} kpiPayload - The formatted KPI payload
 * @returns {Promise} - Promise with the API response
 */
export const SUBMIT_KPI_DATA_SERVICE = async (kpiPayload,processID) => {
  try {
    const response = await post(`${SUBMIT_KPI_API_URL}/${processID}`, kpiPayload);
    return response;
  } catch (error) {
    console.error("Error submitting KPI data:", error);
    throw error;
  }
};

export const PUBLISH_KPI_SERVICE = async (kpiPayload) => {
  try {
    const SPECIFIC_KPI_API_URL = serviceUrl + "api/data-ingestion/specific-kpis-publish";
    const response = await post(SPECIFIC_KPI_API_URL, kpiPayload);
    return response;
  } catch (error) {
    console.error("Error submitting KPI data:", error);
    throw error;
  }
};

/**
 * Updates the job status by sending a PUT request to the specified URL.
 *
 * @async
 * @function UPDATE_JOB_STATUS_SERVICE
 * @param {Object} request - The request payload containing the job status details to be updated.
 * @returns {Promise<Object>} The response from the server after updating the job status.
 * @throws {Error} If an error occurs while updating the job status.
 */
export const UPDATE_JOB_STATUS_SERVICE = async (request) => {
    try {
        const response = await post(UPDATE_JOB_STATUS_URL, request);
        return response;
    } catch (error) {
        console.error("Error updating ststus:", error);
        throw error;
    }
};