/**
 * @jest-environment jsdom
 */
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import ButtonIconText from './button-icon-text';
import { FaPlus } from 'react-icons/fa';

describe('ButtonIconText Component', () => {
  test('renders with default props', () => {
    render(<ButtonIconText>Test Button</ButtonIconText>);
    
    const button = screen.getByText('Test Button');
    expect(button).toBeInTheDocument();
    
    // Check default styling (primary intent, medium size)
    expect(button).toHaveClass('bg-primary-78');
    expect(button).toHaveClass('text-white');
    expect(button).toHaveClass('px-4');
    expect(button).toHaveClass('py-2');
  });

  test('renders with secondary intent', () => {
    render(<ButtonIconText intent="secondary">Secondary Button</ButtonIconText>);
    
    const button = screen.getByText('Secondary Button');
    
    // Check secondary styling
    expect(button).toHaveClass('bg-white');
    expect(button).toHaveClass('text-primary-78');
    expect(button).toHaveClass('border-primary-78');
    expect(button).not.toHaveClass('bg-primary-78');
  });

  test('renders with teritory intent', () => {
    render(<ButtonIconText intent="teritory">Teritory Button</ButtonIconText>);
    
    const button = screen.getByText('Teritory Button');
    
    // Check teritory styling
    expect(button).toHaveClass('bg-white');
    expect(button).toHaveClass('text-primary-78');
    expect(button).not.toHaveClass('border-primary-78');
    expect(button).not.toHaveClass('bg-primary-78');
  });

  test('renders with small size', () => {
    render(<ButtonIconText size="small">Small Button</ButtonIconText>);
    
    const button = screen.getByText('Small Button');
    
    // Check small size styling
    expect(button).toHaveClass('px-2');
    expect(button).toHaveClass('py-2');
    expect(button).not.toHaveClass('px-4');
  });

  test('renders with custom className', () => {
    render(<ButtonIconText className="custom-class">Custom Button</ButtonIconText>);
    
    const button = screen.getByText('Custom Button');
    
    // Check if custom class is applied
    expect(button).toHaveClass('custom-class');
  });

  test('handles click events', () => {
    const handleClick = jest.fn();
    render(<ButtonIconText onClick={handleClick}>Click Me</ButtonIconText>);
    
    const button = screen.getByText('Click Me');
    fireEvent.click(button);
    
    // Check if click handler was called
    expect(handleClick).toHaveBeenCalledTimes(1);
  });

  test('applies disabled styling and behavior', () => {
    const handleClick = jest.fn();
    render(
      <ButtonIconText onClick={handleClick} disabled>
        Disabled Button
      </ButtonIconText>
    );
    
    const button = screen.getByText('Disabled Button');
    
    // Check disabled styling
    expect(button).toBeDisabled();
    expect(button).toHaveClass('disabled:cursor-not-allowed');
    
    // Click should not trigger the handler
    fireEvent.click(button);
    expect(handleClick).not.toHaveBeenCalled();
  });

  test('renders with icon and text', () => {
    render(
      <ButtonIconText>
        <FaPlus data-testid="plus-icon" />
        Add Item
      </ButtonIconText>
    );
    
    // Check if both icon and text are rendered
    expect(screen.getByTestId('plus-icon')).toBeInTheDocument();
    expect(screen.getByText('Add Item')).toBeInTheDocument();
    
    // Check if gap is applied
    const button = screen.getByText('Add Item').closest('button');
    expect(button).toHaveClass('gap-2');
  });

  test('passes through additional props', () => {
    render(
      <ButtonIconText data-testid="custom-button" aria-label="Custom Button">
        Button with Props
      </ButtonIconText>
    );
    
    const button = screen.getByTestId('custom-button');
    
    // Check if props are passed through
    expect(button).toHaveAttribute('aria-label', 'Custom Button');
  });

  test('combines intent and size variations', () => {
    render(
      <ButtonIconText intent="secondary" size="small">
        Combined Styles
      </ButtonIconText>
    );
    
    const button = screen.getByText('Combined Styles');
    
    // Check if both intent and size classes are applied
    expect(button).toHaveClass('text-primary-78');
    expect(button).toHaveClass('border-primary-78');
    expect(button).toHaveClass('px-2');
    expect(button).toHaveClass('py-2');
  });
}); 