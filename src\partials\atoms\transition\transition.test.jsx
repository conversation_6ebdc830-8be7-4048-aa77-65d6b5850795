import React from "react";
import { render } from "@testing-library/react";
import Transition from "./transition";

describe("render Transition", () => {
  it("should render correctly", () => {
    render(
      <Transition
        show={true}
        tag="div"
        className="absolute"
        enter="transition ease-out duration-300 transform"
        enterStart="opacity-0 -translate-y-2"
        enterEnd="opacity-100 translate-y-0"
        leave="transition ease-out duration-300"
        leaveStart="opacity-100"
        leaveEnd="opacity-0"
      />
    );
  });
  it("should render correctly with show false", () => {
    render(
      <Transition
        show={false}
        tag="div"
        className="absolute"
        enter="transition ease-out duration-300 transform"
        enterStart="opacity-0 -translate-y-2"
        enterEnd="opacity-100 translate-y-0"
        leave="transition ease-out duration-300"
        leaveStart="opacity-100"
        leaveEnd="opacity-0"
      />
    );
  });
});
