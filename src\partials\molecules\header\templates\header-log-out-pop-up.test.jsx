import { render, fireEvent, waitFor } from "@testing-library/react";
import { useAuth } from "react-oidc-context";
import { DELETE_TOKENS_API } from "../../../../infra/api/token-manager-service";
import HeaderLogOutPopUp from "./header-log-out-pop-up";

jest.mock("react-oidc-context");
jest.mock("../../../../infra/api/token-manager-service");

describe("HeaderLogOutPopUp", () => {
  it("renders correctly and calls signoutRedirect on confirm", async () => {
    const setShowNewUserPopUp = jest.fn();
    const signoutRedirect = jest.fn();
    useAuth.mockReturnValue({ signoutRedirect });
    DELETE_TOKENS_API.mockReturnValue({
      mutateAsync: jest.fn().mockResolvedValue()
    });

    const { getByText } = render(
      <HeaderLogOutPopUp
        showNewUserPopUp={true}
        setShowNewUserPopUp={setShowNewUserPopUp}
      />
    );

    expect(getByText("Log out")).toBeInTheDocument();
    expect(
      getByText("Do you confirm to end this session?")
    ).toBeInTheDocument();

    fireEvent.click(getByText("Confirm"));

    await waitFor(() => {
      expect(signoutRedirect).toHaveBeenCalled();
    });
  });
});
