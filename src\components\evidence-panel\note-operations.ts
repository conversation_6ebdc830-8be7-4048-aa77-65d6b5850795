/**
 * Deletes selected notes from the financial data
 * @param selectedIndices Indices of notes to delete (relative to notes array, starting at 0)
 * @param financialsData The financial data object
 * @param setFinancialsData Function to update financial data
 * @param notesTables Current notes tables
 * @param setNotesTables Function to update notes tables
 */
export const deleteNotes = (
  selectedIndices: number[],
  financialsData: any,
  setFinancialsData: (data: any) => void,
  notesTables: any[],
  setNotesTables: (notes: any[]) => void
) => {
  if (!selectedIndices || selectedIndices.length === 0 || !financialsData?.tableGroups) return;
  
  // Convert relative note indices (where first note is index 0) to actual indices in tableGroups (starting from index 3)
  const tableGroupsIndicesToRemove = selectedIndices.map(index => index + 3);
  
  // Create a filtered version of tableGroups excluding the selected notes
  const updatedTableGroups = financialsData.tableGroups.filter((group, index) => 
    !tableGroupsIndicesToRemove.includes(index)
  );
  
  // Update financialsData with the new tableGroups array
  setFinancialsData(prevData => ({
    ...prevData,
    tableGroups: updatedTableGroups
  }));

  // Also update the notesTables state to stay in sync
  const updatedNotesTables = notesTables.filter((group, index) => 
    !selectedIndices.includes(index)
  );
  
  setNotesTables(updatedNotesTables);
};

/**
 * Adds a new note to the financial data
 * @param noteConfig Configuration for the new note
 * @param notesData Current notes data
 * @param setNotesData Function to update notes data
 * @param notesTables Current notes tables
 * @param setNotesTables Function to update notes tables
 * @param setFinancialsData Function to update financial data
 */
export const addNewNote = (
  noteConfig: any,
  setNotesData: (data: any[]) => void,
  setNotesTables: (notes: any[]) => void,
  setFinancialsData: (callback: (prevData: any) => any) => void
) => {
  // Use a temporary variable to track the state update
  const tempNotes = [];
  tempNotes.push(noteConfig.data);

  // Update notesData state
  setNotesData((prevNotes) => {
    const updatedNotes = [...tempNotes];

    if (Array.isArray(prevNotes)) {
      updatedNotes.push(...prevNotes);
    }

    return updatedNotes;
  });

  // Update notesTables state
  setNotesTables((prevNotes) => [noteConfig, ...prevNotes]);

  // Update financialsData.tableGroups state - insert after index 2
  setFinancialsData((prevData) => {
    // Create a proper tableGroup structure from noteConfig
    const newTableGroup = {
      label: noteConfig.label,
      tables: noteConfig.tables
    };

    // Check if tableGroups exists
    if (!prevData.tableGroups) {
      return {
        ...prevData,
        tableGroups: [newTableGroup]
      };
    }

    // Insert the new note after index 2 (positions 0,1,2 are reserved)
    const updatedTableGroups = [...prevData.tableGroups];
    updatedTableGroups.splice(3, 0, newTableGroup); // Insert at index 3 (which is after index 2)

    return {
      ...prevData,
      tableGroups: updatedTableGroups
    };
  });
};
