import { render, screen } from "@testing-library/react";
import React from "react";
import ThresholdView from "./threshold-view";

describe("validate the threshold component", () => {
  afterEach(() => {
    jest.resetAllMocks();
  });

  test("renders not found text when no assetId is provided", () => {
    render(<ThresholdView />);
    expect(screen.getByText("Environmental Score")).toBeInTheDocument();
  });
});
