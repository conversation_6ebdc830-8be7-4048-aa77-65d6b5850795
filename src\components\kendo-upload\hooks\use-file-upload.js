import { useState } from 'react';
import { UploadFile } from "../../../infra/api/company/upload-document-service";

const CONSTANTS = {
  ACCEPTED_FILE_TEXT: "Only PDF files are allowed",
  FILE_SIZE_LIMIT_TEXT: "Each file size should not exceed 50MB",
};

export const useFileUpload = (companyID, processID, onUploadedFilesCountChange, setUploadDocumentDetails) => {
  const [files, setFiles] = useState([]);
  const [fileCount, setFileCount] = useState(0);
  const [progress, setProgress] = useState([]);
  const [fileDocumentMap, setFileDocumentMap] = useState({});
  const [uploadKey, setUploadKey] = useState(0);

  const resetState = () => {
    setFiles([]);
    setFileCount(0);
    onUploadedFilesCountChange(0);
    setProgress([]);
    setFileDocumentMap({});
  };

  const handleUpload = (event) => {
    const updatedFiles = event.newState.map(newFile => {
      const existingFile = files.find(f => f.uid === newFile.uid);
      if (existingFile) {
        return {
          ...newFile,
          filingDate: existingFile.filingDate,
          dateError: existingFile.dateError,
          uploadComplete: existingFile.uploadComplete,
          documentDetails: existingFile.documentDetails,
          errorMessage: existingFile.errorMessage, // Preserve error message
          progress: existingFile.progress // Preserve progress
        };
      }
      return newFile;
    });

    setFiles(updatedFiles);
  };

  const uploadFiles = async (event) => {
    // Create a copy of the current file state to work with
    const updatedFiles = event?.newState.map(newFile => {
      const existingFile = files.find(f => f.uid === newFile.uid);
      if (existingFile) {
        return {
          ...newFile,
          filingDate: existingFile.filingDate,
          dateError: existingFile.dateError,
          uploadComplete: existingFile.uploadComplete,
          documentDetails: existingFile.documentDetails,
          errorMessage: existingFile.errorMessage, // Preserve error message
          progress: existingFile.progress // Preserve progress
        };
      }
      return { ...newFile, uploadComplete: false };
    });

    // Update files state first
    setFiles(updatedFiles);

    // Process each affected file
    for (const file of event.affectedFiles) {
      let fileblob = file.getRawFile();
      try {
        // Check if file is PDF before uploading
        const allowedFiles = ["pdf"];
        const fileExtension = file.extension?.toLowerCase() ||
          (file.name?.toLowerCase().split('.').pop() || "");
        const isPdf = allowedFiles.includes(fileExtension) ||
          file.name?.toLowerCase().endsWith(".pdf");
        const maxSizeInBytes = 50 * 1024 * 1024; // 50 MB
        const isFileTooLarge = fileblob.size > maxSizeInBytes;

        // Handle invalid files
        if (!isPdf || isFileTooLarge) {
          setFiles(prevFiles => {
            return prevFiles.map(f => {
              if (f.uid === file.uid) {
                return {
                  ...f,
                  errorMessage: !isPdf ? CONSTANTS.ACCEPTED_FILE_TEXT : CONSTANTS.FILE_SIZE_LIMIT_TEXT,
                  progress: 100
                };
              }
              return f;
            });
          });
          continue;
        }

        // Upload the file
        let response = await UploadFile({ companyID, processID, fileblob });

        // Update all states with response data
        setFiles(prevFiles => {
          return prevFiles.map(f => {
            if (f.uid === file.uid) {
              return {
                ...f,
                progress: 100,
                uploadComplete: true,
                documentDetails: {
                  documentId: response.documentId,
                  url: response.url,
                  processId: response.processId
                }
              };
            }
            return f;
          });
        });

        // Update file-document mapping
        setFileDocumentMap(prevMap => ({
          ...prevMap,
          [file.uid]: response.documentId
        }));

        // Update progress tracking
        setProgress(prevProgress => [
          ...prevProgress.filter(p => p.fileId !== file.uid),
          { fileId: file.uid, progress: 100, uploadComplete: true }
        ]);

        // Update file count
        setFileCount(prevCount => {
          const newCount = prevCount + 1;
          onUploadedFilesCountChange(newCount);
          return newCount;
        });

        // Update parent document details
        setUploadDocumentDetails(prevDetails => [
          ...prevDetails,
          {
            documentId: response.documentId,
            processID: response.processId,
            url: response.url
          }
        ]);
      } catch {
        setFiles(prevFiles => {
          return prevFiles.map(f => {
            if (f.uid === file.uid) {
              return {
                ...f,
                errorMessage: "Failed to upload file",
                progress: 100
              };
            }
            return f;
          });
        });
      }
    }
  };

  const handleAddFiles = (event, MAX_FILE_LIMIT, selectedChipsCount) => {
    const remainingCapacity = MAX_FILE_LIMIT - (fileCount + selectedChipsCount);

    if (remainingCapacity <= 0) {
      event.preventDefault();
      event.stopPropagation();
      return;
    }

    if (event.affectedFiles.length > remainingCapacity) {
      const filesToUpload = event.affectedFiles.slice(0, remainingCapacity);
      const rejectedFiles = event.affectedFiles.slice(remainingCapacity);

      const modifiedEvent = {
        ...event,
        affectedFiles: filesToUpload,
        newState: [...files, ...filesToUpload]
      };

      uploadFiles(modifiedEvent);

      setFiles(prevFiles => [
        ...prevFiles,
        ...rejectedFiles.map(file => ({
          ...file,
          errorMessage: "File limit of 10 reached",
          progress: 100
        }))
      ]);
    } else {
      uploadFiles(event);
    }
  };

  return {
    files,
    setFiles,
    fileCount,
    setFileCount,
    progress,
    setProgress,
    fileDocumentMap,
    setFileDocumentMap,
    uploadKey,
    setUploadKey,
    resetState,
    handleUpload,
    handleAddFiles
  };
};