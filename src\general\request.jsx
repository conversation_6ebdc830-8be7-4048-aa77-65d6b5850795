import axios from "axios";
import { baseUrl } from "../constants/config";
import { getToken, isTokenExist } from "../infra/store/user-store";
import { useTokenManagerStore } from "../infra/contexts/token-manager-context";
import { loadingEvents } from "../infra/events/loading-events";

/**
 * Creates a unique identifier for a request to track retry attempts
 * @param {Object} config - Axios request config
 * @returns {string} - Unique request identifier
 */
const getRequestId = (config) => {
  const { url, method, params, data } = config;
  const queryParams = params ? JSON.stringify(params) : '';
  const bodyData = data ? JSON.stringify(data) : '';
  return `${method}:${url}:${queryParams}:${bodyData}`;
};

const request = axios.create({
  baseURL: baseUrl,
  headers: {
    Accept: "application/json"
  }
});

const handleForbidden = () => {
  // refresh token logic
};

const handleUnauthorize = () => {
  useTokenManagerStore.getState().setLogOut();
};

const authRequestInterceptor = (config) => {
  // Show loading spinner when request starts
  loadingEvents.start();
  
  // Add authentication token
  const token = isTokenExist() ? getToken() : null;
  if (token) config.headers.authorization = `Bearer ${token}`;
  return config;
};

request.interceptors.request.use(authRequestInterceptor);

// Create a retry counter store
const retryState = {
  retryMap: new Map(),
  maxRetries: 5
};

request.interceptors.response.use(
  (response) => {
    // Hide loading spinner on successful response
    loadingEvents.end();
    
    // Reset retry counter on successful response
    const requestId = getRequestId(response.config);
    if (retryState.retryMap.has(requestId)) {
      retryState.retryMap.delete(requestId);
    }
    
    if (response?.status === 204) return { data: null };
    return response;
  },
  async (error) => {
    // Hide loading spinner on error response
    loadingEvents.end();
    
    // If it's a 401 error, attempt to retry
    if (error.response?.status === 401) {
      const config = error.config;
      
      // Create unique request identifier
      const requestId = getRequestId(config);
      
      // Get current retry count or initialize to 0
      const currentRetries = retryState.retryMap.get(requestId) || 0;
      
      // Check if we should retry
      if (currentRetries < retryState.maxRetries) {
        retryState.retryMap.set(requestId, currentRetries + 1);
        console.log(`Retrying request (${currentRetries + 1}/${retryState.maxRetries}): ${config.url}`);
        
        // Wait a bit before retrying (exponential backoff)
        const delay = Math.pow(2, currentRetries) * 1000;
        await new Promise(resolve => setTimeout(resolve, delay));
        
        // Add token again in case it was refreshed
        const token = isTokenExist() ? getToken() : null;
        if (token) config.headers.authorization = `Bearer ${token}`;
        
        // Retry the request
        return request(config);
      } else {
        // Max retries reached, log out the user
        console.log(`Max retries reached (${retryState.maxRetries}) for request: ${config.url}`);
        retryState.retryMap.delete(requestId);
        handleUnauthorize();
      }
    }
    
    if (error.response?.status === 403) handleForbidden();
    if (error.response?.status === 422) return error.response;
    if (error.response?.status === 404) return { data: null };
    
    return Promise.reject(error);
  }
);

export default request;
