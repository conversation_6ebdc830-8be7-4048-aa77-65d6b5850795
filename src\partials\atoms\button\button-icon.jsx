import React from "react";
import PropTypes from "prop-types";
import { cva } from "class-variance-authority";

const buttonIcon = cva(
  "body-r disabled:cursor-not-allowed items-center transition duration-300 rounded flex justify-center",
  {
    variants: {
      intent: {
        primary: [
          "bg-primary-78",
          "text-white",
          "border-primary-78",
          "hover:bg-primary-90",
          "focus:border-primary-78",
          "active:bg-primary-100",
          "disabled:bg-primary-60",
          "disabled:border-none"
        ],
        secondary: [
          "border",
          "bg-white",
          "text-primary-78",
          "border-primary-78",
          "hover:bg-primary-40",
          "focus:border-primary-78",
          "active:border-primary-90",
          "active:bg-primary-50",
          "active:text-primary-90",
          "disabled:border-primary-60",
          "disabled:text-primary-60",
          "disabled:bg-white"
        ],
        teritory: [
          "text-primary-78",
          "hover:bg-primary-40",
          "focus:border-primary-78",
          "active:bg-primary-50",
          "active:text-primary-90",
          "disabled:bg-primary-60",
          "disabled:text-primary-60",
          "disabled:bg-white"
        ],
        teritorynegative: [
          "py-1.5",
          "bg-white",
          "text-negative-100",
          "hover:bg-negative-50",
          "focus:border-negative-60",
          "focus:bg-negative-60",
          "active:bg-negative-70",
          "active:text-negative-100",
          "disabled:text-negative-70",
          "disabled:bg-white"
        ]
      },
      size: {
        small: ["size-6"],
        medium: ["size-8"]
      }
    },
    defaultVariants: {
      intent: "primary",
      size: "medium"
    }
  }
);

const ButtonIcon = ({ className, intent, size, children, ...props }) => {
  return (
    <button className={buttonIcon({ intent, size, className })} {...props}>
      {children}
    </button>
  );
};

ButtonIcon.propTypes = {
  className: PropTypes.string,
  intent: PropTypes.string,
  size: PropTypes.string,
  icon: PropTypes.element,
  props: PropTypes.any,
  children: PropTypes.any
};

export default ButtonIcon;
