import { handleExport, makeHeaderRow } from "../utils";
import { jest } from "@jest/globals";

describe('makeHeaderRow', () => {
  // Sample table data for testing
  const sampleTableData = [
    {
      id: '1',
      label: 'Revenue',
      style: 'lineitem',
      status: '',
      value_1: '100.0',
      value_2: '120.0',
      cellIds: { status: 'cell1' }
    },
    {
      id: '2',
      label: 'Expenses',
      style: 'lineitem',
      status: '',
      value_1: '', // Empty string value
      value_2: null, // Already null value
      cellIds: { status: 'cell2' }
    },
    {
      id: '3',
      label: 'Already Header',
      style: 'header', // Already a header
      status: '',
      value_1: null,
      value_2: null,
      cellIds: { status: 'cell3' }
    },
    {
      id: '4',
      label: 'With Status',
      style: 'lineitem',
      status: 'some status',
      value_1: '', 
      value_2: null,
      cellIds: { status: 'cell4' }
    }
  ];

  test('should convert a row with no values to header successfully', () => {
    // Row 2 has empty values that should convert to header
    const result = makeHeaderRow(sampleTableData, '2');
    
    expect(result.success).toBe(true);
    expect(result.message).toBe('Row converted into header successfully.');
    
    // Check that the style was updated and values are null
    const updatedRow = result.updatedData.find(row => row.id === '2');
    expect(updatedRow.style).toBe('header');
    expect(updatedRow.value_1).toBe(null);
    expect(updatedRow.value_2).toBe(null);
    expect(updatedRow.status).toBe(''); // Status should be cleared
    
    // Other rows should remain unchanged
    expect(result.updatedData.find(row => row.id === '1')).toEqual(sampleTableData[0]);
  });

  test('should fail to convert a row with values', () => {
    // Row 1 has values and should not convert
    const result = makeHeaderRow(sampleTableData, '1');
    
    expect(result.success).toBe(false);
    expect(result.message).toBe("Rows with value cell can't be converted into headers.");
    
    // Data should remain unchanged
    expect(result.updatedData).toEqual(sampleTableData);
  });

  test('should fail when row ID is not found', () => {
    // Non-existent row ID
    const result = makeHeaderRow(sampleTableData, 'non-existent');
    
    expect(result.success).toBe(false);
    expect(result.message).toBe('Selected row not found');
    expect(result.updatedData).toEqual(sampleTableData);
  });

  test('should successfully convert a row that already has header style', () => {
    // Create a sample with a "header" style but containing values
    const customSample = [...sampleTableData];
    customSample[2] = {
      ...customSample[2],
      style: 'header',
      value_1: '100.0', // Has a value despite being a header
    };
    
    const result = makeHeaderRow(customSample, '3');
    
    expect(result.success).toBe(false);
    expect(result.message).toBe("Rows with value cell can't be converted into headers.");
  });

  test('should handle whitespace in values', () => {
    // Create data with whitespace values
    const whitespaceData = [...sampleTableData];
    whitespaceData[1] = {
      ...whitespaceData[1],
      value_1: '   ', // Just whitespace
      value_2: null
    };
    
    const result = makeHeaderRow(whitespaceData, '2');
    
    expect(result.success).toBe(true);
    expect(result.message).toBe('Row converted into header successfully.');
    
    // Check the values are properly nullified
    const updatedRow = result.updatedData.find(row => row.id === '2');
    expect(updatedRow.value_1).toBe(null);
  });

  test('should handle various value types', () => {
    // Create data with different types of values
    const mixedValueData = [
      {
        id: '1',
        label: 'Mixed Values',
        style: 'lineitem',
        status: '',
        value_1: 0, // Numeric zero
        value_2: false, // Boolean
        value_3: undefined, // Undefined
        value_4: [], // Empty array
        cellIds: { status: 'cell1' }
      }
    ];
    
    // Zero should be considered a value, so conversion should fail
    const result = makeHeaderRow(mixedValueData, '1');
    expect(result.success).toBe(false);
    
    // Create data where all values are "empty-like"
    const emptyLikeData = [
      {
        id: '1',
        label: 'Empty Values',
        style: 'lineitem',
        status: '',
        value_1: null,
        value_2: undefined,
        value_3: '',
        value_4: '   ',
        cellIds: { status: 'cell1' }
      }
    ];
    
    // All empty values should allow conversion
    const result2 = makeHeaderRow(emptyLikeData, '1');
    expect(result2.success).toBe(true);
  });

  test('should clear status field when converting to header', () => {
    // Row with a non-empty status but empty values should convert successfully
    // and the status should be cleared
    const result = makeHeaderRow(sampleTableData, '4');
    
    expect(result.success).toBe(true);
    expect(result.message).toBe('Row converted into header successfully.');
    
    const updatedRow = result.updatedData.find(row => row.id === '4');
    expect(updatedRow.style).toBe('header');
    expect(updatedRow.status).toBe(''); // Status should be cleared
  });
  
  test('should consider status separately from other values', () => {
    // Create a row with a non-empty status and a value
    const dataWithStatusAndValue = [...sampleTableData];
    dataWithStatusAndValue[3] = {
      ...dataWithStatusAndValue[3],
      status: 'some status',
      value_1: '100.0', // Has a value
    };
    
    const result = makeHeaderRow(dataWithStatusAndValue, '4');
    
    expect(result.success).toBe(false);
    expect(result.message).toBe("Rows with value cell can't be converted into headers.");
  });
});

describe("handleExport", () => {
  let exportGridOneRef,
    exportGridTwoRef,
    exportGridThreeRef,
    exportGridFourRef,
    notify;

  beforeEach(() => {
    exportGridOneRef = {
      current: {
        workbookOptions: jest.fn().mockReturnValue({
          sheets: [{ rows: [{ cells: [{ value: "" }] }] }]
        }),
        save: jest.fn()
      }
    };
    exportGridTwoRef = {
      current: {
        workbookOptions: jest
          .fn()
          .mockReturnValue({ sheets: [{ rows: [{ cells: [{ value: "" }] }] }] })
      }
    };
    exportGridThreeRef = {
      current: {
        workbookOptions: jest
          .fn()
          .mockReturnValue({ sheets: [{ rows: [{ cells: [{ value: "" }] }] }] })
      }
    };
    exportGridFourRef = {
      current: {
        workbookOptions: jest
          .fn()
          .mockReturnValue({ sheets: [{ rows: [{ cells: [{ value: "" }] }] }] })
      }
    };
    notify = { success: jest.fn() };
  });

  it("should add headers and titles to each sheet", () => {
    handleExport(
      exportGridOneRef,
      exportGridTwoRef,
      exportGridThreeRef,
      exportGridFourRef,
      "Test Company",
      notify,
      "USD",
      "Million"
    );

    expect(exportGridOneRef.current.workbookOptions).toHaveBeenCalled();
    expect(exportGridTwoRef.current.workbookOptions).toHaveBeenCalled();
    expect(exportGridThreeRef.current.workbookOptions).toHaveBeenCalled();
    expect(exportGridFourRef.current.workbookOptions).toHaveBeenCalled();

    const optionsGridOne = exportGridOneRef.current.workbookOptions();
    const optionsGridTwo = exportGridTwoRef.current.workbookOptions();
    const optionsGridThree = exportGridThreeRef.current.workbookOptions();
    const optionsGridFour = exportGridFourRef.current.workbookOptions();

    expect(optionsGridOne.sheets[0].title).toBe("Income Statement");
    expect(optionsGridTwo.sheets[0].title).toBe("Balance Sheet");
    expect(optionsGridThree.sheets[0].title).toBe("Cash Flow");
    expect(optionsGridFour.sheets[0].title).toBe("Notes");
  });

  it("should combine sheets into one workbook", () => {
    handleExport(
      exportGridOneRef,
      exportGridTwoRef,
      exportGridThreeRef,
      exportGridFourRef,
      "Test Company",
      notify,
      "USD",
      "Million"
    );

    const optionsGridOne = exportGridOneRef.current.workbookOptions();
    const optionsGridTwo = exportGridTwoRef.current.workbookOptions();
    const optionsGridThree = exportGridThreeRef.current.workbookOptions();
    const optionsGridFour = exportGridFourRef.current.workbookOptions();

    expect(optionsGridOne.sheets[1]).toEqual(optionsGridTwo.sheets[0]);
    expect(optionsGridOne.sheets[2]).toEqual(optionsGridThree.sheets[0]);
    expect(optionsGridOne.sheets[3]).toEqual(optionsGridFour.sheets[0]);
  });

  it("should call save on exportGridOneRef", () => {
    handleExport(
      exportGridOneRef,
      exportGridTwoRef,
      exportGridThreeRef,
      exportGridFourRef,
      "Test Company",
      notify,
      "USD",
      "Million"
    );

    expect(exportGridOneRef.current.save).toHaveBeenCalled();
  });

  it("should notify success after saving", () => {
    jest.useFakeTimers();
    handleExport(
      exportGridOneRef,
      exportGridTwoRef,
      exportGridThreeRef,
      exportGridFourRef,
      "Test Company",
      notify,
      "USD",
      "Million"
    );

    jest.runAllTimers();

    expect(notify.success).toHaveBeenCalledWith(
      "Extraction downloaded successfully"
    );
  });
});
