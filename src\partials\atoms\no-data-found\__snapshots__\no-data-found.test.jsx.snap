// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`NoDataFound Component matches snapshot 1`] = `
<DocumentFragment>
  <div
    class="m-auto flex h-full w-full flex-col items-center justify-center"
    data-testid="grid-no-data"
  >
    <div
      class="m-auto text-center"
      data-testid="grid-no-data-inner"
    >
      <svg
        fill="none"
        height="154"
        viewBox="0 0 206 154"
        width="206"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M107.268 47.674S66.268 28 59.061 26.873c-7.206-1.128-31.213 25.812-31.213 25.812S13.887 66.124 16.936 97.674c3.049 31.55 32.084 14.449 32.084 14.449s20.442 28.469 40.708 35.162c20.266 6.693 25.279-9.378 45.553-15.199 20.274-5.821 16.847-3.2 35.541-8.072 18.694-4.872 24.653-27.764 15.165-41.842s16.994-65.643-19.5-75.42c-36.494-9.777-59.219 40.922-59.219 40.922z"
          fill="url(#paint0_linear_5227_1268)"
        />
        <g
          filter="url(#filter0_d_5227_1268)"
        >
          <path
            d="M171.68 16.112H32.531a4 4 0 00-4 4v109.719a4 4 0 004 4H171.68a4 4 0 004-4V20.112a4 4 0 00-4-4z"
            fill="#fff"
          />
          <path
            d="M171.68 16.612H32.531a3.5 3.5 0 00-3.5 3.5v109.719a3.5 3.5 0 003.5 3.5H171.68a3.5 3.5 0 003.5-3.5V20.112a3.5 3.5 0 00-3.5-3.5z"
            stroke="#B3B3B3"
          />
        </g>
        <path
          d="M37.727 25.309a1.84 1.84 0 100-3.678 1.84 1.84 0 000 3.678zM45.085 25.309a1.84 1.84 0 100-3.678 1.84 1.84 0 000 3.678zM52.442 25.309a1.839 1.839 0 100-3.678 1.839 1.839 0 000 3.678z"
          fill="#4061C7"
        />
        <path
          d="M165.804 36.019H60.479a2 2 0 00-2 2v7.036a2 2 0 002 2h105.325a2 2 0 002-2v-7.036a2 2 0 00-2-2z"
          fill="#ECECEC"
        />
        <path
          d="M71.675 57.252h-25.59a1 1 0 00-1 1v14.554a1 1 0 001 1h25.59a1 1 0 001-1V58.252a1 1 0 00-1-1z"
          fill="#E6E6E6"
        />
        <path
          d="M88.23 82.33H55.282a1 1 0 00-1 1v14.553a1 1 0 001 1H88.23a1 1 0 001-1V83.33a1 1 0 00-1-1z"
          fill="url(#paint1_linear_5227_1268)"
        />
        <path
          d="M109.499 104.402H75.515a1 1 0 00-1 1v14.554a1 1 0 001 1h33.984a1 1 0 001-1v-14.554a1 1 0 00-1-1zM116.82 59.092H78.194a.92.92 0 100 1.839h38.626a.92.92 0 100-1.84z"
          fill="#E6E6E6"
        />
        <path
          d="M134.294 86.008H95.668a.92.92 0 100 1.839h38.626a.92.92 0 100-1.84z"
          fill="#4061C7"
        />
        <path
          d="M154.724 108.08h-38.626a.92.92 0 000 1.839h38.626a.92.92 0 100-1.839zM116.82 62.77H78.194a.92.92 0 100 1.839h38.626a.92.92 0 100-1.84z"
          fill="#E6E6E6"
        />
        <path
          d="M134.294 89.686H95.668a.92.92 0 100 1.84h38.626a.92.92 0 100-1.84z"
          fill="#4061C7"
        />
        <path
          d="M154.724 111.759h-38.626a.92.92 0 100 1.839h38.626a.92.92 0 000-1.839zM94.748 66.45H78.194a.92.92 0 100 1.838h16.554a.92.92 0 100-1.839z"
          fill="#E6E6E6"
        />
        <path
          d="M112.222 93.366H95.668a.92.92 0 100 1.84h16.554a.92.92 0 100-1.84z"
          fill="#4061C7"
        />
        <path
          d="M132.652 115.438h-16.554a.92.92 0 100 1.838h16.554a.92.92 0 100-1.838z"
          fill="#E6E6E6"
        />
        <path
          d="M179.527 142.862l-23.629-25.429-4.5 4.272 23.536 25.544a3.452 3.452 0 002.13.709 3.579 3.579 0 002.147-.747l.269-.248c.395-.614.61-1.328.619-2.056a3.759 3.759 0 00-.572-2.045z"
          fill="url(#paint2_linear_5227_1268)"
        />
        <path
          d="M150.549 123.817l4.734 5.2a.646.646 0 00.451.207.655.655 0 00.468-.182l5.896-5.596a.696.696 0 00.212-.464.67.67 0 00-.169-.474l-4.735-5.199-6.857 6.508z"
          fill="#4061C7"
        />
        <g
          filter="url(#filter1_d_5227_1268)"
        >
          <path
            d="M132.631 130.601c-3.803-.097-7.436-.973-10.799-2.602a26.33 26.33 0 01-8.503-6.561 27.884 27.884 0 01-5.369-9.546c-1.21-3.679-1.68-7.567-1.396-11.556a31.535 31.535 0 013.03-11.442 31.507 31.507 0 016.704-9.236c2.779-2.647 5.943-4.7 9.403-6.101a27.58 27.58 0 0111.128-2.04c3.802.097 7.436.973 10.798 2.603a26.321 26.321 0 018.503 6.56c2.394 2.78 4.201 5.992 5.37 9.546 1.209 3.68 1.679 7.567 1.396 11.556a31.536 31.536 0 01-3.031 11.442 31.5 31.5 0 01-6.703 9.236c-2.78 2.647-5.944 4.7-9.403 6.101a27.567 27.567 0 01-11.128 2.04zm3.829-53.889a22.737 22.737 0 00-9.172 1.682c-2.851 1.155-5.458 2.847-7.749 5.028a25.968 25.968 0 00-5.524 7.611 25.978 25.978 0 00-2.498 9.43c-.234 3.287.154 6.491 1.151 9.524a22.99 22.99 0 004.424 7.867 21.7 21.7 0 007.008 5.407 21.528 21.528 0 008.9 2.145 22.727 22.727 0 009.171-1.681c2.852-1.156 5.459-2.848 7.749-5.029a25.955 25.955 0 005.525-7.611 26 26 0 002.498-9.43c.233-3.287-.154-6.491-1.151-9.524a22.992 22.992 0 00-4.425-7.867 21.684 21.684 0 00-7.008-5.407 21.527 21.527 0 00-8.899-2.145z"
            fill="#fff"
          />
          <path
            d="M136.793 72.024a27.107 27.107 0 00-10.937 2.005c-3.4 1.378-6.51 3.395-9.242 5.997a30.969 30.969 0 00-6.589 9.077 31.014 31.014 0 00-2.978 11.246c-.278 3.92.183 7.741 1.372 11.357 1.149 3.493 2.924 6.65 5.277 9.382a25.851 25.851 0 008.358 6.448c3.304 1.602 6.875 2.462 10.613 2.558a27.09 27.09 0 0010.936-2.005c3.401-1.377 6.51-3.395 9.242-5.996a30.973 30.973 0 006.589-9.078 31.006 31.006 0 002.978-11.245c.279-3.92-.183-7.742-1.372-11.358-1.149-3.493-2.924-6.65-5.277-9.381a25.87 25.87 0 00-8.357-6.449c-3.305-1.601-6.876-2.462-10.613-2.558zm-3.829 53.89a21.972 21.972 0 01-9.086-2.191 22.15 22.15 0 01-7.153-5.519 23.47 23.47 0 01-4.517-8.03c-1.018-3.097-1.413-6.368-1.175-9.723a26.56 26.56 0 012.55-9.627 26.492 26.492 0 015.64-7.77c2.338-2.226 4.999-3.953 7.91-5.132a23.209 23.209 0 019.363-1.717c3.198.082 6.255.819 9.085 2.19a22.132 22.132 0 017.153 5.52 23.452 23.452 0 014.517 8.03c1.018 3.096 1.414 6.368 1.175 9.722a26.518 26.518 0 01-2.55 9.627 26.492 26.492 0 01-5.639 7.77c-2.338 2.226-5 3.953-7.911 5.133a23.214 23.214 0 01-9.362 1.717zm3.901-54.904c15.823.406 27.694 14.188 26.514 30.785-1.179 16.596-14.962 29.72-30.785 29.314-15.823-.406-27.693-14.189-26.514-30.785 1.179-16.596 14.962-29.72 30.785-29.314zm-3.829 53.889c12.553.322 23.487-10.09 24.423-23.256.935-13.167-8.482-24.101-21.035-24.424-12.553-.322-23.488 10.09-24.423 23.257-.936 13.166 8.482 24.101 21.035 24.423z"
            fill="#B3B3B3"
          />
        </g>
        <path
          d="M134.061 124.114c12.821.342 23.701-9.985 24.301-23.065.599-13.08-9.309-23.96-22.13-24.3-12.822-.342-23.701 9.985-24.301 23.065-.599 13.08 9.309 23.959 22.13 24.3z"
          fill="#F0F0F0"
          opacity="0.8"
        />
        <path
          d="M120.144 84.305c-3.517 3.338-5.693 6.778-4.864 7.688.828.909 4.348-1.058 7.863-4.395 3.516-3.337 5.693-6.778 4.866-7.687-.828-.91-4.348 1.057-7.865 4.394z"
          fill="#fff"
        />
        <path
          d="M29.531 29.827h145.688M51.531 29.827V134.13"
          stroke="#E5E5E5"
        />
        <defs>
          <filter
            color-interpolation-filters="sRGB"
            filterUnits="userSpaceOnUse"
            height="129.719"
            id="filter0_d_5227_1268"
            width="159.149"
            x="22.531"
            y="11.112"
          >
            <feflood
              flood-opacity="0"
              result="BackgroundImageFix"
            />
            <fecolormatrix
              in="SourceAlpha"
              result="hardAlpha"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            />
            <feoffset
              dy="1"
            />
            <fegaussianblur
              stdDeviation="3"
            />
            <fecolormatrix
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.18 0"
            />
            <feblend
              in2="BackgroundImageFix"
              result="effect1_dropShadow_5227_1268"
            />
            <feblend
              in="SourceGraphic"
              in2="effect1_dropShadow_5227_1268"
              result="shape"
            />
          </filter>
          <filter
            color-interpolation-filters="sRGB"
            filterUnits="userSpaceOnUse"
            height="72.123"
            id="filter1_d_5227_1268"
            width="69.466"
            x="99.996"
            y="65.999"
          >
            <feflood
              flood-opacity="0"
              result="BackgroundImageFix"
            />
            <fecolormatrix
              in="SourceAlpha"
              result="hardAlpha"
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            />
            <feoffset
              dy="1"
            />
            <fegaussianblur
              stdDeviation="3.001"
            />
            <fecolormatrix
              values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.18 0"
            />
            <feblend
              in2="BackgroundImageFix"
              result="effect1_dropShadow_5227_1268"
            />
            <feblend
              in="SourceGraphic"
              in2="effect1_dropShadow_5227_1268"
              result="shape"
            />
          </filter>
          <lineargradient
            gradientUnits="userSpaceOnUse"
            id="paint0_linear_5227_1268"
            x1="103.47"
            x2="103.47"
            y1="5.5"
            y2="148.887"
          >
            <stop
              stop-color="#FFF0FC"
            />
            <stop
              offset="1"
              stop-color="#CED4F9"
            />
          </lineargradient>
          <lineargradient
            gradientUnits="userSpaceOnUse"
            id="paint1_linear_5227_1268"
            x1="58.336"
            x2="73.021"
            y1="94.811"
            y2="67.611"
          >
            <stop
              stop-color="#EDCF28"
            />
            <stop
              offset="1"
              stop-color="#DEA509"
            />
          </lineargradient>
          <lineargradient
            gradientUnits="userSpaceOnUse"
            id="paint2_linear_5227_1268"
            x1="158.543"
            x2="181.002"
            y1="128.829"
            y2="144.207"
          >
            <stop
              stop-color="#EDCF28"
            />
            <stop
              offset="1"
              stop-color="#DEA509"
            />
          </lineargradient>
        </defs>
      </svg>
      <p
        class="body-r text-neutral-60"
        data-testid="no-data-text"
      >
        No Data found!
      </p>
    </div>
  </div>
</DocumentFragment>
`;
