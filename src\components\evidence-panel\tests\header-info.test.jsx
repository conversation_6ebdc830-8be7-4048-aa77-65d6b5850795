import React from "react";
import { render, screen } from "@testing-library/react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import HeaderInfo from "../header-info";

describe("HeaderInfo", () => {
  const queryClient = new QueryClient();
  const defaultProps = {
    companyName: "Test Company",
    companyTicker: "TST",
    acuityid: "12345",
    currencyUnit: "USD",
    currency: "USD"
  };

  const renderWithQueryClient = (ui) => {
    return render(
      <QueryClientProvider client={queryClient}>{ui}</QueryClientProvider>
    );
  };

  it("renders company name and ticker", () => {
    renderWithQueryClient(<HeaderInfo {...defaultProps} />);

    expect(screen.getByText("Test Company")).toBeInTheDocument();
    expect(screen.getByText("TST")).toBeInTheDocument();
  });

  it("renders currency and currency unit", () => {
    renderWithQueryClient(<HeaderInfo {...defaultProps} />);

    expect(screen.getAllByText("USD").length).toBe(2); // currency and currency unit
  });

  it("renders tooltip text", () => {
    renderWithQueryClient(<HeaderInfo {...defaultProps} />);

    expect(
      screen.getByText("except share, per share information")
    ).toBeInTheDocument();
  });
});
