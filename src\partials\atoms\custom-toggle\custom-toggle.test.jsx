import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import CustomToggleButton from "./custom-toggle";

describe("CustomToggleButton Component", () => {
  test("renders the button with calendar icon", () => {
    render(<CustomToggleButton />);
    
    // Check if button is rendered
    const button = screen.getByRole("button");
    expect(button).toBeInTheDocument();
    
    // Check if calendar icon is rendered
    const calendarIcon = document.querySelector("svg");
    expect(calendarIcon).toBeInTheDocument();
    expect(calendarIcon).toHaveClass("m-auto");
  });

  test("applies proper styling", () => {
    render(<CustomToggleButton />);
    
    const button = screen.getByRole("button");
    
    // Check if proper styles are applied
    expect(button).toHaveStyle({
      width: "2rem",
      height: "2rem",
      border: "none"
    });
    expect(button).toHaveClass("size-2");
  });

  test("passes additional props to button", () => {
    const testId = "test-button";
    const onClickMock = jest.fn();
    
    render(
      <CustomToggleButton 
        data-testid={testId}
        onClick={onClickMock}
        aria-label="Calendar toggle"
      />
    );
    
    const button = screen.getByTestId(testId);
    expect(button).toHaveAttribute("aria-label", "Calendar toggle");
    
    // Test onClick handler
    fireEvent.click(button);
    expect(onClickMock).toHaveBeenCalledTimes(1);
  });

  test("renders children correctly", () => {
    render(
      <CustomToggleButton>
        <span data-testid="child-element">Child Content</span>
      </CustomToggleButton>
    );
    
    const childElement = screen.getByTestId("child-element");
    expect(childElement).toBeInTheDocument();
    expect(childElement).toHaveTextContent("Child Content");
  });
}); 