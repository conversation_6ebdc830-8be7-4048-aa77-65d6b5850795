import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
import { Combobox, ComboboxButton, ComboboxInput } from "@headlessui/react";
import CheckBox from "../../../atoms/check-box";
import EmptySearchImage from "../../../../resources/images/empty-search-image";
import { FaSearch, FaTimes } from "react-icons/fa";
import EntityLogo from "../../entity-logo/entity-logo";

const FilterMultiSelect = ({
  data = [],
  onSelect,
  selectedFilterId,
  clearAll
}) => {
  const [searchedData, setSearchedData] = useState([]);
  const [matchedData, setMatchedData] = useState([]);
  const [query, setQuery] = useState("");
  const [selectedAll, setSelectedAll] = useState(false);
  const [indeterminate, setIndeterminate] = useState(false);
  const [appliedFilterCount, setAppliedFilterCount] = useState(0);

  useEffect(() => {
    if (Array.isArray(data)) {
      //setting checked and indeterminate state for selectAll checkbox
      const selectedCount = data.filter((x) => x.selected).length;
      if (selectedCount === data.length) {
        setSelectedAll(true);
        setIndeterminate(false);
      } else if (selectedCount < data.length && selectedCount > 0) {
        setSelectedAll(false);
        setIndeterminate(true);
      } else {
        setSelectedAll(false);
        setIndeterminate(false);
      }
      setSearchedData(data);
    }
  }, [data, selectedFilterId, clearAll]);

  //function for on click event for checkboxes
  const onSelection = (e) => {
    const selectedData = searchedData.map((item) => {
      if (item.value == e.target.value) {
        return { ...item, selected: e.target.checked };
      } else {
        return item;
      }
    });

    const selectedCount = selectedData.filter((x) => x.selected).length;

    if (selectedCount === searchedData.length) {
      setSelectedAll(true);
      setIndeterminate(false);
    } else if (selectedCount < searchedData.length && selectedCount > 0) {
      setIndeterminate(true);
    } else {
      setIndeterminate(false);
      setSelectedAll(false);
    }
    setSearchedData(selectedData);
    const matchedData = selectedData.filter((x) =>
      x.label.toLowerCase().includes(query.toLowerCase())
    );
    setMatchedData(matchedData);

    onSelect(selectedData, selectedFilterId, selectedCount);
  };

  useEffect(() => {
    if (query.length > 0) {
      const searchedData = data.filter((x) =>
        x.label.toLowerCase().includes(query.toLowerCase())
      );
      setMatchedData(searchedData);
    } else {
      setMatchedData(data);
    }
  }, [query]);

  const onSelectAll = (e) => {
    setSelectedAll(!selectedAll);
    setSearchedData(
      searchedData.map((item) => ({
        ...item,
        ["selected"]: e.target.checked
      }))
    );
    setMatchedData(
      searchedData.map((item) => ({
        ...item,
        ["selected"]: e.target.checked
      }))
    );

    onSelect(
      searchedData.map((item) => ({
        ...item,
        ["selected"]: e.target.checked
      })),
      selectedFilterId,
      !selectedAll ? searchedData.length : 0
    );
  };
  useEffect(() => {
    const countSelectedTrue = (dataArray) => {
      let count = 0;
      dataArray.forEach((item) => {
        if (item.selected === true) {
          count++;
        }
      });
      return count;
    };
    countSelectedTrue(matchedData);
    setAppliedFilterCount(countSelectedTrue(matchedData));
  }, [matchedData]);

  return (
    <div className="flex h-full w-full flex-col">
      <div className="relative border-b px-2 py-3">
        <Combobox value={query}>
          <ComboboxInput
            data-testid="filter-pop-over-multi-select-search-input"
            value={query}
            maxLength={40}
            className="body-r relative h-8 w-full rounded border border-neutral-10 px-9 py-1.5 text-neutral-80 placeholder-neutral-30 hover:border-primary-78 focus:border-primary-90 focus:outline-none focus:ring-0"
            placeholder={"Search here...."}
            autoComplete="off"
            onChange={(e) => {
              setQuery(e.target.value);
            }}
          ></ComboboxInput>
          <ComboboxButton className="absolute left-6 top-4 z-30 h-3 w-3 pr-2">
            <FaSearch
              data-testid={"filter-pop-over-multi-select-search-icon"}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
              }}
              className="absolute h-3 w-3 cursor-pointer text-primary-78 focus:text-neutral-60"
              strokeWidth={1}
            />
          </ComboboxButton>
          <ComboboxButton
            data-testid="filter-pop-over-multi-select-search-cross"
            className="absolute right-4 top-1 z-30 h-3 w-3"
          >
            {query.length > 0 && (
              <FaTimes
                id="filter-pop-over-multi-select-search-cross"
                onClick={(e) => {
                  setQuery("");
                  e.stopPropagation();
                  e.preventDefault();
                }}
                className="absolute top-[1.15rem] h-3 w-3 cursor-pointer text-neutral-60"
                strokeWidth={1}
              />
            )}
          </ComboboxButton>
        </Combobox>
      </div>
      <div className="h-full overflow-hidden">
        {searchedData?.length > 0 && matchedData?.length > 0 && (
          <div className="flex h-10 items-center gap-3 border-b pl-7">
            <CheckBox
              indeterminate={indeterminate}
              data-testid={"filter-pop-over-multi-select-select-all"}
              checked={selectedAll}
              onChange={(e) => {
                onSelectAll(e);
              }}
            ></CheckBox>
            <div className="body-m text-neutral-90">
              Select All <span className="body-r">({appliedFilterCount})</span>
            </div>
          </div>
        )}
        <div className="h-[calc(100%-2.5rem)] w-full overflow-auto">
          {searchedData?.length > 0 &&
            query.length === 0 &&
            searchedData.map((item) => (
              <div
                key={item.value}
                className="flex h-10 items-center gap-3 border-b pl-7"
              >
                <CheckBox
                  data-testid={"filter-pop-over-multi-select-check-box"}
                  value={item.value ?? ""}
                  checked={item?.selected ?? false}
                  onChange={onSelection}
                ></CheckBox>
                {selectedFilterId === "1" && (
                  <EntityLogo
                    acuityID={item.acuityID}
                    companyName={item.label}
                  />
                )}
                <div
                  className="body-r truncate text-neutral-90"
                  title={item.label}
                >
                  {item.label}
                </div>
              </div>
            ))}
          {searchedData?.length > 0 &&
            query.length > 0 &&
            matchedData.map((item) => (
              <div
                key={item.value}
                className="flex h-10 items-center gap-3 border-b pl-7"
              >
                <CheckBox
                  data-testid={"filter-pop-over-multi-select-check-box"}
                  value={item.value ?? ""}
                  checked={item?.selected ?? false}
                  onChange={onSelection}
                ></CheckBox>
                {selectedFilterId === "1" && (
                  <EntityLogo
                    acuityID={item.acuityID}
                    companyName={item.label}
                  />
                )}
                <div
                  className="body-r truncate text-neutral-90"
                  title={item.label}
                >
                  {item.label}
                </div>
              </div>
            ))}
          {searchedData?.length > 0 &&
            query.length > 0 &&
            matchedData.length === 0 && (
              <div className="flex h-full flex-col items-center justify-center gap-3 overflow-auto text-center">
                <EmptySearchImage className="" />
                <span className="body-r">No Matches Found!</span>
              </div>
            )}
        </div>
      </div>
    </div>
  );
};

export default FilterMultiSelect;

FilterMultiSelect.propTypes = {
  data: PropTypes.array,
  onSelect: PropTypes.func,
  selectedFilterId: PropTypes.string,
  clearAll: PropTypes.bool
};
