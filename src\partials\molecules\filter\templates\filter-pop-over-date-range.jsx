import React, { useEffect } from "react";
import { DateRangePicker } from "@progress/kendo-react-dateinputs";
import PropTypes from "prop-types";
import { getLastYearDate, formatDate } from "../../../../general/utils";
import RadioButton from "../../../atoms/radio-button";
import useWatchListFilterStore from "../../../../pages/watch-list/templates/watch-list-tabs/templates/watch-list-tabs-filter/watch-list-tabs-filter.store";

const defaultData = [
  {
    value: "1year",
    label: "Last 1 Year",
    data: {
      start_date: getLastYearDate(1),
      end_date: formatDate(new Date(), "yyyy-MM-dd")
    }
  },
  {
    value: "5years",
    label: "Last 5 Years",
    data: {
      start_date: getLastYearDate(5),
      end_date: formatDate(new Date(), "yyyy-MM-dd")
    }
  },
  {
    value: "10years",
    label: "Last 10 Years",
    data: {
      start_date: getLastYearDate(10),
      end_date: formatDate(new Date(), "yyyy-MM-dd")
    }
  },
  {
    value: "custom",
    label: "Custom Range",
    data: {
      start_date: formatDate(getLastYearDate(30), "yyyy-MM-dd"),
      end_date: formatDate(new Date(), "yyyy-MM-dd")
    }
  }
];

const FilterDataRange = ({ data, selectedFilterId, onSelect, dateRange }) => {
  const { value, selected, setSelected, setValue, appliedFilter } =
    useWatchListFilterStore((state) => state);
  useEffect(() => {
    if (data.length > 0) {
      const selectedDate =
        defaultData.find((item) => item.value === data[0].selectedId) ||
        defaultData[0];
      setSelected(selectedDate.value);
    }
  }, [data, setSelected]);

  useEffect(() => {
    appliedFilter.forEach((list) => {
      list.filingData.forEach((item) => {
        if (
          item.label === "Period" &&
          item.data.some(
            (period) => period.selected && period.selectedId !== "custom"
          )
        ) {
          setValue({ start: null, end: null });
        }
      });
    });
  }, [appliedFilter]);

  const onSelection = (event) => {
    const selectedOption = defaultData.find(
      (option) => option.value === event.target.value
    );
    setSelected(selectedOption.value);
    if (selectedOption.label !== "Custom Range") {
      onSelect(
        [
          {
            data: selectedOption.data,
            selected: true,
            selectedId: selectedOption.value
          }
        ],
        selectedFilterId,
        1
      );
    }
  };

  const handleChange = (event) => {
    setValue(event.value);
    if (event.value.end !== null) {
      const updatedData = {
        data: {
          start_date: formatDate(event.value.start, "yyyy-MM-dd"),
          end_date: formatDate(event.value.end, "yyyy-MM-dd")
        },
        selected: true,
        selectedId: "custom"
      };
      onSelect([updatedData], selectedFilterId, 1);
    }
  };

  return (
    <div ref={dateRange} className="h-full w-full">
      <div className="h-full w-full overflow-auto">
        <RadioButton
          name="timePeriod"
          options={defaultData}
          selectedValue={selected}
          onChange={onSelection}
        />
        <div className="custom-range-button">
          {selected === "custom" && (
            <button className="p-3" onClick={(e) => e.stopPropagation()}>
              <DateRangePicker
                max={new Date()}
                value={value}
                onChange={handleChange}
              />
            </button>
          )}
        </div>
      </div>
    </div>
  );
};

FilterDataRange.propTypes = {
  data: PropTypes.array,
  onSelect: PropTypes.func,
  selectedFilterId: PropTypes.string,
  dateRange: PropTypes.object
};

export default FilterDataRange;
