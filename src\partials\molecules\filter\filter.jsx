import React, { useState, useEffect, Fragment, useRef } from "react";
import PropTypes from "prop-types";
import { Transition } from "@headlessui/react";
import { FaTimes } from "react-icons/fa";
import { Button, ButtonIconText } from "../../atoms/button";
import FilterMultiSelect from "./templates/filter-pop-over-multi-select";
import FilterAccordianPanel from "./templates/filter-pop-over-accordian-panel";
import FilterDateRange from "./templates/filter-pop-over-date-range";
import { FiFilter } from "react-icons/fi";
import useWatchListFilterStore from "../../../pages/watch-list/templates/watch-list-tabs/templates/watch-list-tabs-filter/watch-list-tabs-filter.store";

const filterTypes = {
  MULTI_SELECT: "multi-select",
  DATE_RANGE: "date-range",
  ACCORDIAN_PANEL: "accordian-panel"
};

const Filter = ({
  data,
  onFilter,
  disabled = false,
  onSubmitText = "Apply"
}) => {
  const { clearedAllFilters, toggleClearedAllFilters, setSelected } =
    useWatchListFilterStore((state) => state);
  const [showFilter, setShowFilter] = useState(false);
  const [filterData, setFilterData] = useState([]);
  const [selectedFilterId, setSelectedFilterId] = useState();
  const [buttonYPosition, setButtonYPosition] = useState();
  const [buttonXPosition, setButtonXPosition] = useState();
  const [selectedFilterData, setSelectedFilterData] = useState([]);
  const [selectedFilterType, setSelectedFilterType] = useState([]);
  const [disableApply, setDisableApply] = useState(true);
  const [clearAll, setClearAll] = useState(false);
  const [overAllSelectedCount, setOverAllSelectedCount] = useState(0);

  const dropdown = useRef(null);
  const trigger = useRef(null);
  const dateRange = useRef(null);

  const initializeData = () => {
    // sorting based on label name
    for (let item of data) {
      if (item.data) {
        item.data.sort((a, b) => {
          if (a.label < b.label) return -1;
          if (a.label > b.label) return 1;
          return 0;
        });
      }
    }
    setFilterData(data);
    const initialFilterId = data.map((x) => x.value)[0];
    const selectedCount = data.filter((x) => x.selectedCount).length;
    setOverAllSelectedCount(selectedCount);
    setDisableApply(true);
    setSelectedFilterId(initialFilterId);
  };

  useEffect(() => {
    initializeData();
  }, [data, showFilter]);

  useEffect(() => {
    const selectedFilterData = filterData
      .filter((x) => x.value === selectedFilterId)
      .map(({ data }) => data);
    const selectedFilterType = filterData
      .filter((x) => x.value === selectedFilterId)
      .map(({ type }) => type);

    setSelectedFilterData(selectedFilterData);
    setSelectedFilterType(selectedFilterType);
  }, [selectedFilterId, filterData]);

  useEffect(() => {
    // sorting based on selected is true
    for (let item of filterData) {
      if (item.label !== "Period" && item.data) {
        item.data.sort((a) => {
          return a.selected ? -1 : 1;
        });
      }
    }
  }, [selectedFilterId]);

  const onSelect = (updatedData, id, selectedCount) => {
    setDisableApply(true);
    const selectedData = filterData.map((item) => {
      if (item.value === id) {
        const isSame = (a, b) => JSON.stringify(a) === JSON.stringify(b);
        if (!isSame(item.data, updatedData)) {
          setDisableApply(false);
        }
        return { ...item, data: updatedData, selectedCount: selectedCount };
      } else {
        return item;
      }
    });

    setFilterData(selectedData);
  };

  // close on click outside
  useEffect(() => {
    const clickHandler = ({ target }) => {
      if (!dropdown.current) return;
      if (
        !showFilter ||
        dropdown.current.contains(target) ||
        trigger.current?.contains(target) ||
        dateRange.current?.contains(target)
      )
        return;
      setShowFilter(false);
    };
    document.addEventListener("click", clickHandler);
    return () => document.removeEventListener("click", clickHandler);
  });

  // close if the esc key is pressed
  useEffect(() => {
    const keyHandler = ({ keyCode }) => {
      if (!showFilter || keyCode !== 27) return;
      setShowFilter(false);
    };
    document.addEventListener("keydown", keyHandler);
    return () => document.removeEventListener("keydown", keyHandler);
  });

  //Reset flag to clear all applied filters
  useEffect(() => {
    if (clearedAllFilters) {
      onClearAll();
      toggleClearedAllFilters();
    }
  }, [clearedAllFilters, toggleClearedAllFilters]);

  const onClearAll = () => {
    setSelected("");
    for (let item of filterData) {
      if (item.label === "Period") item.data = [];
      item.selectedCount = 0;
      item.data.map((item) => {
        item.selected = false;
        if (item.data !== undefined && Array.isArray(item.data)) {
          item.data.map((item) => (item.selected = false));
        }
      });
    }
    onFilter(filterData);
    setClearAll(!clearAll);
    setShowFilter(false);
  };

  return (
    <div ref={trigger} className="">
      <ButtonIconText
        data-testid={"filter-button"}
        disabled={disabled}
        intent={"secondary"}
        onClick={(e) => {
          const buttonClickedXPosition = e.target.getBoundingClientRect().x;
          const buttonClickedYPosition = e.target.getBoundingClientRect().y;
          setButtonYPosition(buttonClickedYPosition);
          setButtonXPosition(buttonClickedXPosition);
          setShowFilter(!showFilter);
        }}
      >
        {overAllSelectedCount > 0 ? (
          <span className="body-r flex size-4 items-center justify-center rounded-full bg-primary-78 text-white">
            {overAllSelectedCount}
          </span>
        ) : (
          <FiFilter />
        )}
        <span className="">Filter</span>
      </ButtonIconText>
      <Transition
        as={Fragment}
        enter="transition ease-out duration-100"
        enterFrom="opacity-0 translate-y-1"
        enterTo="opacity-100 translate-y-0"
        leave="transition ease-in duration-100"
        leaveFrom="opacity-100 translate-y-0"
        leaveTo="opacity-0 translate-y-1"
        show={showFilter}
        className={`absolute z-50 mt-2 ${window.innerHeight - buttonYPosition < window.innerHeight / 2 && "-translate-y-[calc(100%+3rem)]"} ${window.innerWidth - buttonXPosition < window.innerWidth / 3 && "-translate-x-[calc(100%-5.5rem)]"} h-[calc(100vh-20rem)] rounded border border-neutral-10 bg-white drop-shadow-lg 3xl:h-[calc(100vh_-_30rem)]`}
      >
        <div
          ref={dropdown}
          className="flex h-[calc(100vh-20rem)] w-[34rem] flex-col"
        >
          <div className="flex h-14 w-full justify-between border-b">
            <div className="heading-2-m flex h-14 justify-start px-6 py-4 text-neutral-90">
              Filters
            </div>
            <button
              data-testid={"filter-button-close"}
              onClick={() => {
                setFilterData(data);
                setSelectedFilterId(data.map((x) => x.value)[0]);
                setShowFilter(false);
              }}
              className="heading-2-m mx-6 my-4 flex size-6 items-center justify-end rounded text-left text-neutral-80 hover:bg-neutral-10 active:text-neutral-20"
            >
              <FaTimes className="m-auto size-4" />
            </button>
          </div>
          <div className="flex h-full w-full overflow-hidden">
            <div className="h-full w-2/6 overflow-auto border-r">
              {filterData.map((result) => (
                <button
                  data-testid={"filter-button-selected"}
                  key={result.value}
                  onClick={() => {
                    setSelectedFilterId(result.value);
                  }}
                  className={`body-r flex w-full flex-col justify-between border-b ${result.value === selectedFilterId ? "body-m body-r bg-primary-40 text-primary-78" : "bg-white"} px-6 py-2.5 text-neutral-90 hover:bg-primary-35 active:bg-primary-40`}
                >
                  <div className="flex w-full justify-between">
                    <div className="flex justify-start">{result.label}</div>
                    {result?.selectedCount > 0 && (
                      <div className="text flex h-5 w-fit justify-end rounded-2xl border border-gray-300 bg-white px-2 text-gray-800">
                        {result?.selectedCount}
                      </div>
                    )}
                  </div>
                </button>
              ))}
            </div>
            <div className="w-4/6 border-r">
              {selectedFilterType[0] === filterTypes.MULTI_SELECT && (
                <FilterMultiSelect
                  data={selectedFilterData[0]}
                  onSelect={onSelect}
                  selectedFilterId={selectedFilterId}
                  clearAll={clearAll}
                />
              )}
              {selectedFilterType[0] === filterTypes.ACCORDIAN_PANEL && (
                <FilterAccordianPanel
                  data={selectedFilterData[0]}
                  onSelect={onSelect}
                  selectedFilterId={selectedFilterId}
                  clearAll={clearAll}
                  context="watchlist"
                />
              )}
              {selectedFilterType[0] === filterTypes.DATE_RANGE && (
                <FilterDateRange
                  dateRange={dateRange}
                  data={selectedFilterData[0]}
                  onSelect={onSelect}
                  selectedFilterId={selectedFilterId}
                />
              )}
            </div>
          </div>
          <div className="h-16 w-full border-t">
            <div className="mx-6 my-4 flex justify-between">
              <Button
                data-testid={"filter-button-clear"}
                onClick={() => {
                  onClearAll();
                }}
                intent={"secondary"}
                size={"medium"}
                className={"my-auto"}
              >
                Clear all filters
              </Button>
              <ButtonIconText
                data-testid="filter-apply"
                disabled={disableApply}
                onClick={() => {
                  setShowFilter(false);
                  onFilter(filterData);
                }}
                className="heading-2-m body-r flex h-14 justify-start px-6 py-4 text-neutral-90"
              >
                {onSubmitText}
              </ButtonIconText>
            </div>
          </div>
        </div>
      </Transition>
    </div>
  );
};

export default Filter;

Filter.propTypes = {
  data: PropTypes.array.isRequired,
  onFilter: PropTypes.func.isRequired,
  disabled: PropTypes.bool,
  onSubmitText: PropTypes.func
};
