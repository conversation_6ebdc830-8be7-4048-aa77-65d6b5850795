import React from "react";
import PropTypes from "prop-types";
import { cva } from "class-variance-authority";
import { FaExclamationCircle } from "react-icons/fa";

const input = cva(
  "body-r translate w-full bg-white text-neutral-80 focus:ring-0 h-8 disabled:border-neutral-20 disabled:bg-neutral-5 disabled:text-neutral-20 placeholder-neutral-30 placeholder-body-r",
  {
    variants: {
      intent: {
        filled: ["px-4", "rounded", "border"],
        outline: [
          "border-0",
          "border-b",
          "px-0",
          "border-neutral-10",
          "focus:border-primary-78",
          "hover:border-primary-78"
        ]
      },
      size: {
        medium: ["py-1.5"]
      },
      error: {
        true: [
          "border-negative-100",
          "focus:border-negative-100",
          "hover:border-negative-100"
        ]
      }
    },
    compoundVariants: [
      {
        intent: ["filled", "outline"],
        size: ["medium"],
        error: ["", undefined],
        class:
          "border-neutral-10 focus:border-primary-78 hover:border-primary-78"
      }
    ],
    defaultVariants: {
      intent: "filled",
      size: "medium"
    }
  }
);

const InputBox = ({
  className,
  intent,
  size,
  label,
  error,
  message,
  mandatory = false,
  ...props
}) => {
  return (
    <div className="flex flex-col gap-1">
      {label && label.length > 0 && (
        <label
          className={`caption-m ${error ? "text-negative-100" : "text-neutral-60"}`}
        >
          {label}
          {mandatory && <span className="pl-0.5 text-negative-100">*</span>}
        </label>
      )}
      <input
        data-testid="input-box"
        className={input({ className, intent, size, error })}
        type="text"
        {...props}
        autoComplete="off"
      />
      {message && (
        <div className="caption-r flex items-center gap-1 text-negative-100">
          <FaExclamationCircle className="h-3 w-3" />
          <div>{message || "Required"}</div>
        </div>
      )}
    </div>
  );
};

InputBox.propTypes = {
  className: PropTypes.string,
  intent: PropTypes.string,
  size: PropTypes.string,
  label: PropTypes.string,
  error: PropTypes.bool,
  props: PropTypes.any,
  message: PropTypes.string,
  mandatory: PropTypes.bool
};

export default InputBox;
