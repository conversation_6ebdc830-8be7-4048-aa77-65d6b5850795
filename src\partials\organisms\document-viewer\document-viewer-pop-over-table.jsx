import React from "react";
import { Transition } from "@headlessui/react";
import ButtonIconText from "../../../partials/atoms/button/button-icon-text";
import ButtonIcon from "../../../partials/atoms/button/button-icon";
import { FaTimes } from "react-icons/fa";
import { FiCopy, FiDownload } from "react-icons/fi";
import useDocumentViewerStore from "./document-viewer.store";
import { POST_BUCKET_API } from "../../../infra/api/bucket-service";
import { useCopyToClipboard } from "@uidotdev/usehooks";
import { generateShortId } from "../../../general/utils";
import { notify } from "../../molecules/toaster";
import { viewerUrl } from "../../../constants/config";
import * as XLSX from "xlsx";
import { saveAs } from "file-saver";

const DocumentViewerPopOverTable = () => {
  const [_copiedText, copyToClipboard] = useCopyToClipboard();

  const {
    highlightedMetaData,
    previewFilingsMetaData,
    setShowTablePopOver,
    showTablePopOver,
    getPreviewMetaDataBody,
    validatePreviewMetaDataBody
  } = useDocumentViewerStore((state) => state);

  const copyHighLightS3 = POST_BUCKET_API("highlights");

  const handleCopyHighLightS3 = async () => {
    const body = getPreviewMetaDataBody();
    if (!validatePreviewMetaDataBody(body)) {
      notify.warning("Highlight did not get saved, please try again.");
      return;
    }
    const uid = generateShortId();

    const ifile = new File([JSON.stringify(body)], `${uid}.txt`, {
      type: "text/plain"
    });

    let file = new FormData();
    file.append("file", ifile);

    const { success } = await copyHighLightS3.mutateAsync(file);
    if (success) {
      notify.success(
        `Table’s shareable link copied on your clipboard successfully.`
      );
      setShowTablePopOver(false);
      getPublicLink(uid);
    } else {
      notify.warning("Highlight link not copied, please try again.");
    }
  };

  const getPublicLink = (id) => {
    const url = `${viewerUrl}/${id}`;
    copyToClipboard(url);
  };

  const handleDownloadTable = () => {
    try {
      const { companyName, filingType, ticker, filingDate } =
        previewFilingsMetaData;

      const { tableId } = highlightedMetaData.data;
      const iframe = document.getElementById(
        "iframe-document-viewer-html-render"
      );
      const iframeDocument =
        iframe.contentDocument || iframe.contentWindow.document;
      const div = iframeDocument.getElementById(tableId);

      const worksheet = XLSX.utils.table_to_sheet(div);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, ticker);

      const excelBuffer = XLSX.write(workbook, {
        bookType: "xlsx",
        type: "array"
      });
      const dataBlob = new Blob([excelBuffer], {
        type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8"
      });

      const fileName = `${companyName}_${filingType}_${filingDate}.xlsx`
        .toLowerCase()
        .replace(/ /g, "_");

      saveAs(dataBlob, fileName);
      setShowTablePopOver(false);
      notify.success("Table Downloaded successfully.");
    } catch (error) {
      notify.info("Table did not get downloaded, please try again.");
    }
  };

  return (
    <Transition
      show={showTablePopOver}
      className={`body-r absolute bottom-14 left-0 right-0 z-50 mx-auto h-14 w-fit overflow-hidden rounded-lg bg-white drop-shadow-3xl`}
      enter="transition ease-out duration-300 transform"
      enterStart="opacity-0 -translate-y-2"
      enterEnd="opacity-100 translate-y-0"
      leave="transition ease-out duration-300"
      leaveStart="opacity-100"
      leaveEnd="opacity-0"
    >
      <ul data-testid="document-viewer-pop-over-table">
        <li className="flex w-full items-center justify-between gap-2 rounded-lg border border-neutral-10 px-5 py-3 text-neutral-80">
          <div>
            <ButtonIconText
              data-testid={"document-viewer-copy-table-button"}
              intent={"teritory"}
              onClick={handleCopyHighLightS3}
            >
              <FiCopy className="size-4" />
              <span>Copy table link</span>
            </ButtonIconText>
          </div>
          <div>
            <ButtonIconText
              data-testid={"document-viewer-download-table-button"}
              intent={"teritory"}
              onClick={handleDownloadTable}
            >
              <FiDownload className="size-4" />
              <span>Download table</span>
            </ButtonIconText>
          </div>
          <div className="mx-2 h-full border-l border-neutral-10">
            <span className="invisible">|</span>
          </div>
          <div>
            <ButtonIcon
              data-testid={"document-viewer-cancel-button"}
              onClick={() => {
                setShowTablePopOver(false);
              }}
              intent={"teritory"}
            >
              <FaTimes className="size-4 text-neutral-60" />
            </ButtonIcon>
          </div>
        </li>
      </ul>
    </Transition>
  );
};

export default DocumentViewerPopOverTable;
