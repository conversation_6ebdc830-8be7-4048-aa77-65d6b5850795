import React from "react";

const ImageAddDoc = () => (
  <svg
    width="205"
    height="163"
    viewBox="0 0 205 163"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_1700_9596)">
      <path
        d="M96.2518 53.5028C96.2518 53.5028 132.021 36.4476 138.311 35.4676C144.6 34.4876 150.079 32.409 162.171 34.368C174.263 36.327 184.461 48.845 186.064 55.868C187.667 62.891 189.932 66.0421 187.123 79.1551C184.315 92.2681 179.302 98.5282 179.302 98.5282L151.195 140.141C151.195 140.141 143.811 151.116 136.235 153.682C128.659 156.248 117.058 149.525 111.928 147.429C106.797 145.333 84.6755 132.06 76.3481 132.583C68.0206 133.106 33.0862 130.495 33.0862 130.495C33.0862 130.495 15.4754 130.464 11.5285 123.255C7.58164 116.046 10.7949 104.214 19.0761 92.0086C27.3572 79.8036 43.4873 71.5189 46.147 70.0399C48.8068 68.5609 96.2518 53.5028 96.2518 53.5028Z"
        fill="url(#paint0_linear_1700_9596)"
      />
      <g filter="url(#filter0_d_1700_9596)">
        <path
          d="M94.8888 7.65651L26.623 24.3609C21.4583 25.6247 18.615 31.1302 20.2724 36.6577L48.1809 129.737C49.8382 135.265 55.3686 138.721 60.5333 137.458L128.799 120.753C133.964 119.489 136.807 113.984 135.15 108.456L107.241 15.3768C105.584 9.84922 100.053 6.39273 94.8888 7.65651Z"
          fill="white"
        />
        <path
          d="M95.0375 8.15968L26.7763 24.8633C21.8701 26.0639 19.1692 31.2939 20.7436 36.5448L48.6502 129.62C50.2246 134.871 55.4781 138.155 60.3842 136.954L128.645 120.251C133.552 119.05 136.252 113.82 134.678 108.569L106.771 15.4936C105.197 10.2427 99.9436 6.95914 95.0375 8.15968Z"
          stroke="#B3B3B3"
          strokeWidth="1.00862"
        />
      </g>
      <path
        d="M100.037 53.0117L62.2812 62.8288C61.8633 62.9256 61.4171 62.8412 61.0381 62.5936C60.659 62.3461 60.377 61.9551 60.2523 61.504C60.1276 61.053 60.1701 60.5776 60.3706 60.1793C60.5711 59.7811 60.9139 59.4916 61.3257 59.3726L99.0821 49.5552C99.5001 49.4585 99.9462 49.5429 100.325 49.7904C100.704 50.0379 100.986 50.429 101.111 50.88C101.236 51.3311 101.193 51.8065 100.993 52.2047C100.792 52.6029 100.449 52.8925 100.038 53.0115L100.037 53.0117Z"
        fill="#E6E6E6"
      />
      <path
        d="M94.6157 32.9199L56.8602 42.737C56.4422 42.8338 55.9961 42.7494 55.617 42.5018C55.238 42.2543 54.956 41.8633 54.8313 41.4122C54.7066 40.9612 54.749 40.4858 54.9496 40.0875C55.1501 39.6893 55.4929 39.3998 55.9046 39.2808L93.6611 29.4634C94.0791 29.3667 94.5252 29.4511 94.9042 29.6986C95.2833 29.9461 95.5653 30.3372 95.69 30.7882C95.8147 31.2393 95.7722 31.7147 95.5717 32.1129C95.3712 32.5111 95.0284 32.8007 94.6167 32.9197L94.6157 32.9199Z"
        fill="#E6E6E6"
      />
      <path
        d="M108.054 56.6174L63.7224 68.1746C63.5118 68.2356 63.2895 68.2501 63.0687 68.217C62.8478 68.184 62.6329 68.1041 62.4364 67.9822C62.24 67.8603 62.066 67.6988 61.9249 67.5072C61.7837 67.3155 61.6781 67.0977 61.6143 66.8664C61.5505 66.6351 61.5298 66.395 61.5535 66.1604C61.5772 65.9258 61.6447 65.7014 61.7521 65.5002C61.8594 65.2991 62.0045 65.1254 62.1787 64.9894C62.3529 64.8534 62.5528 64.7577 62.7665 64.7081L107.098 53.1508C107.516 53.0538 107.963 53.1385 108.342 53.3867C108.721 53.635 109.003 54.0272 109.128 54.4796C109.252 54.932 109.21 55.4088 109.009 55.8082C108.809 56.2076 108.466 56.498 108.054 56.6174Z"
        fill="#E6E6E6"
      />
      <path
        d="M102.633 36.5246L58.301 48.0818C58.0904 48.1429 57.8681 48.1573 57.6473 48.1243C57.4264 48.0912 57.2115 48.0114 57.0151 47.8894C56.8186 47.7675 56.6447 47.606 56.5035 47.4144C56.3623 47.2227 56.2567 47.0049 56.1929 46.7736C56.1292 46.5423 56.1085 46.3023 56.1321 46.0677C56.1558 45.833 56.2233 45.6086 56.3307 45.4075C56.438 45.2064 56.5831 45.0327 56.7573 44.8966C56.9315 44.7606 57.1314 44.6649 57.3452 44.6153L101.677 33.0581C102.095 32.9611 102.541 33.0457 102.92 33.294C103.3 33.5422 103.582 33.9344 103.706 34.3868C103.831 34.8393 103.789 35.316 103.588 35.7154C103.387 36.1148 103.045 36.4052 102.633 36.5246Z"
        fill="#E6E6E6"
      />
      <path
        d="M106.029 75.5525L68.2725 85.3698C67.8545 85.4666 67.4084 85.3821 67.0294 85.1346C66.6503 84.8871 66.3684 84.4961 66.2436 84.045C66.1189 83.594 66.1614 83.1186 66.3619 82.7203C66.5625 82.3221 66.9052 82.0326 67.3169 81.9136L105.072 72.0965C105.49 71.9997 105.937 72.0842 106.316 72.3317C106.695 72.5792 106.977 72.9703 107.101 73.4213C107.226 73.8724 107.184 74.3477 106.983 74.7459C106.783 75.1442 106.441 75.4335 106.029 75.5525Z"
        fill="#E6E6E6"
      />
      <path
        d="M111.534 95.623L73.7775 105.44C73.3595 105.537 72.9134 105.453 72.5344 105.205C72.1553 104.958 71.8734 104.567 71.7486 104.116C71.6239 103.665 71.6664 103.189 71.8669 102.791C72.0675 102.393 72.4102 102.103 72.8219 101.984L110.577 92.167C110.995 92.0703 111.442 92.1547 111.821 92.4022C112.2 92.6498 112.482 93.0408 112.606 93.4919C112.731 93.943 112.689 94.4183 112.488 94.8165C112.288 95.2147 111.946 95.504 111.534 95.623Z"
        fill="#E6E6E6"
      />
      <path
        d="M114.046 79.159L69.7141 90.716C69.296 90.813 68.8497 90.7284 68.4705 90.4801C68.0913 90.2319 67.8092 89.8397 67.6845 89.3873C67.5597 88.9349 67.6022 88.4581 67.8028 88.0587C68.0034 87.6593 68.3463 87.3689 68.7582 87.2495L113.09 75.6926C113.508 75.5956 113.954 75.6802 114.333 75.9285C114.712 76.1767 114.995 76.5689 115.119 77.0213C115.244 77.4737 115.202 77.9505 115.001 78.3499C114.8 78.7493 114.457 79.0397 114.046 79.159Z"
        fill="#E6E6E6"
      />
      <path
        d="M119.55 99.2284L75.2187 110.785C74.8006 110.882 74.3543 110.798 73.9751 110.549C73.596 110.301 73.3139 109.909 73.1891 109.457C73.0644 109.004 73.1068 108.527 73.3074 108.128C73.508 107.729 73.8509 107.438 74.2628 107.319L118.594 95.7619C119.012 95.6649 119.459 95.7496 119.838 95.9978C120.217 96.2461 120.499 96.6382 120.624 97.0906C120.749 97.543 120.706 98.0198 120.506 98.4192C120.305 98.8186 119.962 99.109 119.55 99.2284Z"
        fill="#E6E6E6"
      />
      <path
        d="M55.82 73.1707L42.7337 76.4353C42.5357 76.4844 42.3223 76.4496 42.1403 76.3386C41.9583 76.2275 41.8225 76.0493 41.7628 75.843L38.2635 63.6997C38.2043 63.4932 38.226 63.2757 38.3239 63.0948C38.4218 62.914 38.5879 62.7845 38.7857 62.7349L51.873 59.4701C52.071 59.421 52.2844 59.4558 52.4664 59.5668C52.6484 59.6778 52.7841 59.8561 52.8439 60.0624L56.3431 72.2057C56.4024 72.4123 56.3806 72.63 56.2825 72.811C56.1844 72.9919 56.0181 73.1213 55.82 73.1707Z"
        fill="#E6E6E6"
      />
      <path
        d="M50.3987 53.0779L37.3124 56.3425C37.1144 56.3916 36.901 56.3568 36.719 56.2458C36.537 56.1348 36.4012 55.9565 36.3415 55.7502L32.8423 43.6069C32.783 43.4004 32.8048 43.1829 32.9027 43.0021C33.0006 42.8212 33.1666 42.6918 33.3645 42.6421L46.4518 39.3773C46.6498 39.3282 46.8631 39.363 47.0452 39.474C47.2272 39.5851 47.3629 39.7633 47.4226 39.9696L50.9219 52.1129C50.9811 52.3196 50.9594 52.5373 50.8613 52.7182C50.7632 52.8991 50.5968 53.0285 50.3987 53.0779Z"
        fill="#E6E6E6"
      />
      <path
        d="M61.8113 95.7115L48.7251 98.976C48.5271 99.0251 48.3137 98.9904 48.1317 98.8793C47.9497 98.7683 47.8139 98.59 47.7542 98.3837L44.2535 86.2356C44.1943 86.0291 44.216 85.8115 44.3139 85.6307C44.4118 85.4498 44.5779 85.3204 44.7757 85.2708L57.8629 82.006C58.0609 81.9569 58.2743 81.9916 58.4563 82.1027C58.6383 82.2137 58.774 82.392 58.8338 82.5983L62.3344 94.7465C62.3937 94.9531 62.3719 95.1708 62.2738 95.3517C62.1757 95.5326 62.0094 95.662 61.8113 95.7115Z"
        fill="#E6E6E6"
      />
      <path
        d="M67.3169 115.781L54.2307 119.045C54.0327 119.094 53.8193 119.06 53.6373 118.949C53.4553 118.838 53.3195 118.659 53.2598 118.453L49.7591 106.305C49.6999 106.098 49.7216 105.881 49.8195 105.7C49.9174 105.519 50.0835 105.39 50.2813 105.34L63.3685 102.075C63.5665 102.026 63.7799 102.061 63.9619 102.172C64.1439 102.283 64.2797 102.461 64.3394 102.668L67.84 114.816C67.8993 115.022 67.8775 115.24 67.7794 115.421C67.6813 115.602 67.515 115.731 67.3169 115.781Z"
        fill="#E6E6E6"
      />
      <g filter="url(#filter1_d_1700_9596)">
        <path
          d="M153.493 25.3784H73.9925C70.102 25.3784 66.9481 28.5124 66.9481 32.3784V131.378C66.9481 135.244 70.102 138.378 73.9925 138.378H153.493C157.384 138.378 160.538 135.244 160.538 131.378V32.3784C160.538 28.5124 157.384 25.3784 153.493 25.3784Z"
          fill="white"
        />
        <path
          d="M153.493 26.1284H73.9925C70.5188 26.1284 67.7029 28.9266 67.7029 32.3784V131.378C67.7029 134.83 70.5188 137.628 73.9925 137.628H153.493C156.967 137.628 159.783 134.83 159.783 131.378V32.3784C159.783 28.9266 156.967 26.1284 153.493 26.1284Z"
          stroke="#93B0ED"
          strokeWidth="1.5"
        />
      </g>
      <path
        d="M140.909 75.3454H101.728C101.298 75.3349 100.888 75.1575 100.588 74.8512C100.287 74.5449 100.119 74.1339 100.119 73.7059C100.119 73.278 100.287 72.867 100.588 72.5607C100.888 72.2544 101.298 72.077 101.728 72.0664H140.91C141.341 72.077 141.75 72.2544 142.051 72.5607C142.352 72.867 142.52 73.278 142.52 73.7059C142.52 74.1339 142.352 74.5449 142.051 74.8512C141.75 75.1575 141.341 75.3349 140.91 75.3454H140.909Z"
        fill="#E6E6E6"
      />
      <path
        d="M147.716 81.8784H101.726C101.506 81.8838 101.287 81.8454 101.082 81.7654C100.877 81.6855 100.691 81.5655 100.533 81.4128C100.375 81.26 100.25 81.0774 100.165 80.8758C100.079 80.6742 100.035 80.4577 100.035 80.2389C100.035 80.0201 100.079 79.8035 100.165 79.6019C100.25 79.4003 100.375 79.2178 100.533 79.065C100.691 78.9122 100.877 78.7923 101.082 78.7124C101.287 78.6324 101.506 78.594 101.726 78.5994H147.716C148.147 78.6099 148.556 78.7873 148.857 79.0936C149.157 79.4 149.326 79.811 149.326 80.2389C149.326 80.6668 149.157 81.0778 148.857 81.3841C148.556 81.6905 148.147 81.8678 147.716 81.8784Z"
        fill="#E6E6E6"
      />
      <path
        d="M140.909 95.5515H101.727C101.297 95.5409 100.887 95.3635 100.587 95.0572C100.286 94.7509 100.118 94.3399 100.118 93.912C100.118 93.484 100.286 93.073 100.587 92.7667C100.887 92.4604 101.297 92.283 101.727 92.2725H140.908C141.339 92.283 141.748 92.4604 142.049 92.7667C142.35 93.073 142.518 93.484 142.518 93.912C142.518 94.3399 142.35 94.7509 142.049 95.0572C141.748 95.3635 141.34 95.5409 140.909 95.5515Z"
        fill="#E6E6E6"
      />
      <path
        d="M140.909 116.85H101.727C101.297 116.84 100.887 116.662 100.587 116.356C100.286 116.05 100.118 115.639 100.118 115.211C100.118 114.783 100.286 114.372 100.587 114.066C100.887 113.759 101.297 113.582 101.727 113.571H140.908C141.339 113.582 141.748 113.759 142.049 114.066C142.35 114.372 142.518 114.783 142.518 115.211C142.518 115.639 142.35 116.05 142.049 116.356C141.748 116.662 141.34 116.84 140.909 116.85Z"
        fill="#E6E6E6"
      />
      <path
        d="M147.715 102.085H101.725C101.295 102.075 100.885 101.898 100.585 101.591C100.284 101.285 100.116 100.874 100.116 100.446C100.116 100.018 100.284 99.607 100.585 99.3007C100.885 98.9943 101.295 98.817 101.725 98.8064H147.715C148.146 98.817 148.555 98.9943 148.856 99.3007C149.156 99.607 149.325 100.018 149.325 100.446C149.325 100.874 149.156 101.285 148.856 101.591C148.555 101.898 148.146 102.075 147.715 102.085Z"
        fill="#E6E6E6"
      />
      <path
        d="M147.715 123.385H101.725C101.295 123.375 100.885 123.197 100.585 122.891C100.284 122.585 100.116 122.174 100.116 121.746C100.116 121.318 100.284 120.907 100.585 120.6C100.885 120.294 101.295 120.117 101.725 120.106H147.715C148.146 120.117 148.555 120.294 148.856 120.6C149.156 120.907 149.325 121.318 149.325 121.746C149.325 122.174 149.156 122.585 148.856 122.891C148.555 123.197 148.146 123.375 147.715 123.385Z"
        fill="#E6E6E6"
      />
      <path
        d="M92.9509 83.2973H79.0856C78.8759 83.2971 78.6748 83.2142 78.5265 83.0668C78.3783 82.9195 78.2948 82.7197 78.2946 82.5113V70.2513C78.2948 70.043 78.3783 69.8432 78.5265 69.6958C78.6748 69.5485 78.8759 69.4656 79.0856 69.4653H92.9519C93.1616 69.4656 93.3627 69.5485 93.5109 69.6958C93.6592 69.8432 93.7426 70.043 93.7429 70.2513V82.5113C93.7426 82.7199 93.6591 82.9198 93.5106 83.0672C93.3621 83.2145 93.1608 83.2973 92.9509 83.2973Z"
        fill="#E6E6E6"
      />
      <path
        d="M92.8645 104.096H78.9991C78.7894 104.096 78.5884 104.013 78.4401 103.866C78.2918 103.718 78.2084 103.519 78.2081 103.31V91.0453C78.2084 90.8369 78.2918 90.6371 78.4401 90.4897C78.5884 90.3424 78.7894 90.2595 78.9991 90.2593H92.8655C93.0752 90.2595 93.2762 90.3424 93.4245 90.4897C93.5728 90.6371 93.6562 90.8369 93.6565 91.0453V103.31C93.6562 103.519 93.5727 103.719 93.4242 103.866C93.2757 104.013 93.0744 104.096 92.8645 104.096Z"
        fill="#E6E6E6"
      />
      <path
        d="M92.9509 124.896H79.0856C78.8759 124.896 78.6748 124.813 78.5265 124.666C78.3783 124.518 78.2948 124.319 78.2946 124.11V111.845C78.2948 111.637 78.3783 111.437 78.5265 111.29C78.6748 111.142 78.8759 111.06 79.0856 111.059H92.9519C93.1616 111.06 93.3627 111.142 93.5109 111.29C93.6592 111.437 93.7426 111.637 93.7429 111.845V124.11C93.7426 124.319 93.6591 124.519 93.5106 124.666C93.3621 124.814 93.1608 124.896 92.9509 124.896Z"
        fill="#E6E6E6"
      />
      <path
        d="M141.339 46.7185H113.919C113.497 46.6953 113.1 46.5125 112.81 46.2076C112.52 45.9026 112.358 45.4988 112.358 45.0789C112.358 44.6591 112.52 44.2553 112.81 43.9504C113.1 43.6455 113.497 43.4626 113.919 43.4395H141.339C141.761 43.4626 142.158 43.6455 142.448 43.9504C142.738 44.2553 142.9 44.6591 142.9 45.0789C142.9 45.4988 142.738 45.9026 142.448 46.2076C142.158 46.5125 141.761 46.6953 141.339 46.7185Z"
        fill="#4061C7"
      />
      <path
        d="M147.8 53.2524H113.919C113.497 53.2292 113.1 53.0464 112.81 52.7415C112.52 52.4366 112.358 52.0327 112.358 51.6129C112.358 51.1931 112.52 50.7892 112.81 50.4843C113.1 50.1794 113.497 49.9965 113.919 49.9734H147.8C148.221 49.9965 148.618 50.1794 148.909 50.4843C149.199 50.7892 149.361 51.1931 149.361 51.6129C149.361 52.0327 149.199 52.4366 148.909 52.7415C148.618 53.0464 148.221 53.2292 147.8 53.2524Z"
        fill="#4061C7"
      />
      <path
        d="M105.63 59.4584H78.9991C78.7894 59.4582 78.5884 59.3753 78.4401 59.2279C78.2918 59.0806 78.2084 58.8808 78.2081 58.6724V38.0184C78.2084 37.8101 78.2918 37.6103 78.4401 37.4629C78.5884 37.3156 78.7894 37.2327 78.9991 37.2324H105.63C105.84 37.2327 106.041 37.3156 106.189 37.4629C106.337 37.6103 106.421 37.8101 106.421 38.0184V58.6714C106.421 58.88 106.338 59.08 106.189 59.2276C106.041 59.3751 105.84 59.4582 105.63 59.4584Z"
        fill="url(#paint1_linear_1700_9596)"
      />
      <path
        d="M146.996 105.339C150.827 105.339 154.572 106.468 157.757 108.583C160.942 110.698 163.424 113.703 164.89 117.22C166.356 120.737 166.74 124.607 165.992 128.34C165.245 132.073 163.4 135.503 160.692 138.194C157.983 140.886 154.532 142.719 150.775 143.462C147.018 144.204 143.124 143.823 139.585 142.366C136.046 140.91 133.021 138.443 130.893 135.278C128.764 132.113 127.628 128.392 127.628 124.585C127.628 119.481 129.669 114.586 133.301 110.976C136.933 107.367 141.86 105.339 146.996 105.339Z"
        fill="url(#paint2_linear_1700_9596)"
      />
      <path
        d="M146.997 116.341V132.829"
        stroke="white"
        strokeWidth="3"
        strokeLinecap="round"
      />
      <path
        d="M155.293 124.585H138.7"
        stroke="white"
        strokeWidth="3"
        strokeLinecap="round"
      />
    </g>
    <defs>
      <filter
        id="filter0_d_1700_9596"
        x="13.7595"
        y="2.3742"
        width="127.903"
        height="142.374"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="1.00436" />
        <feGaussianBlur stdDeviation="3.01308" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.18 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_1700_9596"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_1700_9596"
          result="shape"
        />
      </filter>
      <filter
        id="filter1_d_1700_9596"
        x="60.9481"
        y="20.3784"
        width="105.59"
        height="125"
        filterUnits="userSpaceOnUse"
        colorInterpolationFilters="sRGB"
      >
        <feFlood floodOpacity="0" result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          result="hardAlpha"
        />
        <feOffset dy="1" />
        <feGaussianBlur stdDeviation="3" />
        <feColorMatrix
          type="matrix"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.18 0"
        />
        <feBlend
          mode="normal"
          in2="BackgroundImageFix"
          result="effect1_dropShadow_1700_9596"
        />
        <feBlend
          mode="normal"
          in="SourceGraphic"
          in2="effect1_dropShadow_1700_9596"
          result="shape"
        />
      </filter>
      <linearGradient
        id="paint0_linear_1700_9596"
        x1="99.1521"
        y1="33.5564"
        x2="99.1521"
        y2="154.258"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#FFF0FC" />
        <stop offset="1" stopColor="#CED4F9" />
      </linearGradient>
      <linearGradient
        id="paint1_linear_1700_9596"
        x1="81.4808"
        y1="53.9908"
        x2="104.776"
        y2="33.5401"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#67CCD9" />
        <stop offset="1" stopColor="#A9C97F" />
      </linearGradient>
      <linearGradient
        id="paint2_linear_1700_9596"
        x1="132.122"
        y1="134.362"
        x2="170.275"
        y2="107.808"
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#67CCD9" />
        <stop offset="1" stopColor="#A9C97F" />
      </linearGradient>
      <clipPath id="clip0_1700_9596">
        <rect width="205" height="163" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export default React.memo(ImageAddDoc);
