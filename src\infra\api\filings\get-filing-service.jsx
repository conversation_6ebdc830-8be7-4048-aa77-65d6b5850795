import { useQuery } from "@tanstack/react-query";
import { post } from "../../../general/fetcher";
import { minutesToMilliseconds } from "date-fns";
import { baseUrl } from "../../../constants/config";

const CONTROLLER = "datalake";

export const GET_FINANCIALS_API_URL = `${baseUrl}/${CONTROLLER}/filings/files`;

export const FILINGS_API = (acuityid, start_date, end_date) => {
  const body = {
    app_name: "AcuityOne",
    filter: {
      acuity_ids: [acuityid],
      filing: {
        start_date: start_date,
        end_date: end_date
      },
      filing_type: [
        "10-K",
        "10-K/A",
        "20-F",
        "20-F/A",
        "40-F",
        "40-F/A",
        "Annual Reports",
        "AR",
        "AA",
        "ACS",
        "202",
        "1st Quarter results",
        "3rd Quarter results",
        "Quarterly Reports",
        "10-Q/A",
        "10-Q"
      ]
    },
    category: "Filings"
  };
  return useQuery({
    queryKey: [GET_FINANCIALS_API_URL, acuityid, start_date, end_date],
    queryFn: async ({ signal }) => {
      let response = await post(GET_FINANCIALS_API_URL, body, signal);
      return response;
    },
    staleTime: minutesToMilliseconds(5),
    cacheTime: minutesToMilliseconds(30),
    retry: 1,
    refetchOnWindowFocus: false,
    refetchOnMount: true
  });
};
