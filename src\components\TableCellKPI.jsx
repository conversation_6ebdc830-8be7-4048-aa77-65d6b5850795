import React from 'react';
import PropTypes from 'prop-types';
import { FiInfo } from 'react-icons/fi';
import AI_icon from "../resources/images/ai-star.svg";

const TableCellKPI = ({ isLabel, kpiName }) => {
  return (
    <div className="flex flex-row items-center px-4 py-2 gap-2 w-80 h-8 bg-white ">
      <div className="flex flex-row justify-center items-center px-1 gap-2 w-[19px] h-[18px] bg-[#FFF0E4] rounded">
        <span className="w-[11px] h-[18px] font-['Helvetica_Neue'] font-normal text-xs leading-[18px] text-[#783F10]">
          {isLabel}
        </span>
      </div>

      <span 
        className="flex-grow font-['Helvetica_Neue'] font-normal text-xs leading-4 text-[#1A1A1A] truncate min-w-0"
        title={kpiName}
      >
        {kpiName}
      </span>

      <div className="flex items-center gap-1 flex-none">
        <img
          src={AI_icon}
          alt="AI Suggestion"
          className="w-4 h-4"
        />
        <FiInfo className="w-4 h-4 text-[#808080]" />
      </div>
    </div>
  );
};

TableCellKPI.propTypes = {
  isLabel: PropTypes.string.isRequired,
  kpiName: PropTypes.string.isRequired
};

export default TableCellKPI;