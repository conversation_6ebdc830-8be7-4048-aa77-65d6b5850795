import React from "react";
import { NavLinkComponent } from "../../../atoms/nav-link";

const navigationItems = [
  { text: "Home", to: "home" },
  { text: "Spreader", to: "spreader" },
  { text: "Financials", to: "financials" },
  { text: "Template", to: "templates" },
  { text: "Companies", to: "companies" }
];

const HeaderMenuBar = () => {
  return (
    <div className="flex pt-[1.38rem]">
      <ul className="s-r relative flex flex-nowrap">
        {navigationItems.map((item, index) => (
          <NavLinkComponent key={index} text={item.text} to={item.to} />
        ))}
      </ul>
    </div>
  );
};

export default HeaderMenuBar;
