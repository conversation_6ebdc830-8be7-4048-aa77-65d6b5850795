import { useEffect, useRef, useLayoutEffect, useState } from "react";

const useIsomorphicLayoutEffect =
  typeof window !== "undefined" ? useLayoutEffect : useEffect;

const useInterval = (callback, delay) => {
  const savedCallback = useRef(callback);

  const [intervalId, setIntervalId] = useState();

  // Remember the latest callback if it changes.
  useIsomorphicLayoutEffect(() => {
    savedCallback.current = callback;
  }, [callback]);

  // Set up the interval.
  useEffect(() => {
    // Don't schedule if no delay is specified.
    // Note: 0 is a valid value for delay.
    if (!delay && delay !== 0) {
      return;
    }

    const id = setInterval(() => savedCallback.current(), delay);
    setIntervalId(id);

    return () => clearInterval(id);
  }, [delay]);

  //return
  return intervalId;
};

export default useInterval;
