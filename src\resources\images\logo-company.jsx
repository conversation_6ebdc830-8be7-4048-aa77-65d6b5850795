import * as React from "react";
const LogoCompany = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="103px"
    height="24px"
    style={{
      shapeRendering: "geometricPrecision",
      textRendering: "geometricPrecision",
      imageRendering: "optimizeQuality",
      fillRule: "evenodd",
      clipRule: "evenodd"
    }}
    xmlnsXlink="http://www.w3.org/1999/xlink"
    {...props}
  >
    <g>
      <path
        style={{
          opacity: 0.965
        }}
        fill="#071f89"
        d="M 23.5,-0.5 C 26.1667,-0.5 28.8333,-0.5 31.5,-0.5C 34.5153,2.53217 36.182,6.19883 36.5,10.5C 36.6495,11.552 36.4828,12.552 36,13.5C 31.6298,9.2978 27.4632,4.96447 23.5,0.5C 23.5,0.166667 23.5,-0.166667 23.5,-0.5 Z"
      />
    </g>
    <g>
      <path
        style={{
          opacity: 0.847
        }}
        fill="#292929"
        d="M 44.5,2.5 C 45.8221,2.33003 46.9887,2.66337 48,3.5C 49.8542,6.78162 50.6876,10.115 50.5,13.5C 47.6689,9.5825 44.8355,9.5825 42,13.5C 41.5,13.1667 41,12.8333 40.5,12.5C 42.0355,9.22246 43.3689,5.88913 44.5,2.5 Z"
      />
    </g>
    <g>
      <path
        style={{
          opacity: 0.866
        }}
        fill="#262626"
        d="M 56.5,1.5 C 64.3373,3.94746 64.3373,5.11413 56.5,5C 53.8503,7.55842 54.3503,9.39175 58,10.5C 59.2259,10.3552 60.3926,10.0219 61.5,9.5C 62.5937,10.868 62.2603,12.0347 60.5,13C 53.4403,14.2714 50.9403,11.4381 53,4.5C 54.1451,3.36362 55.3117,2.36362 56.5,1.5 Z"
      />
    </g>
    <g>
      <path
        style={{
          opacity: 0.83
        }}
        fill="#2c2c2c"
        d="M 63.5,2.5 C 64.5,2.5 65.5,2.5 66.5,2.5C 66.3367,4.85684 66.5034,7.19018 67,9.5C 68.7141,10.9255 70.0475,10.5922 71,8.5C 71.1922,6.4233 71.6922,4.4233 72.5,2.5C 73.6548,5.44587 73.8214,8.44587 73,11.5C 70.3333,14.1667 67.6667,14.1667 65,11.5C 63.8512,8.6074 63.3512,5.6074 63.5,2.5 Z"
      />
    </g>
    <g>
      <path
        style={{
          opacity: 0.768
        }}
        fill="#262626"
        d="M 75.5,2.5 C 76.5,2.5 77.5,2.5 78.5,2.5C 78.8226,6.22605 78.4892,9.89272 77.5,13.5C 76.914,13.2907 76.414,12.9574 76,12.5C 75.5017,9.18323 75.335,5.8499 75.5,2.5 Z"
      />
    </g>
    <g>
      <path
        style={{
          opacity: 0.834
        }}
        fill="#262626"
        d="M 89.5,2.5 C 89.5,2.83333 89.5,3.16667 89.5,3.5C 88.2921,4.23411 86.9587,4.56745 85.5,4.5C 85.8174,7.57186 85.4841,10.5719 84.5,13.5C 83.914,13.2907 83.414,12.9574 83,12.5C 82.5026,9.85397 82.3359,7.1873 82.5,4.5C 81.325,4.71901 80.325,4.38568 79.5,3.5C 82.7683,2.51298 86.1016,2.17964 89.5,2.5 Z"
      />
    </g>
    <g>
      <path
        style={{
          opacity: 0.884
        }}
        fill="#292929"
        d="M 89.5,2.5 C 92.0495,2.56075 93.5495,3.89409 94,6.5C 94.5,5.5 95,4.5 95.5,3.5C 96.5,2.16667 97.5,2.16667 98.5,3.5C 96.02,6.36089 95.02,9.69422 95.5,13.5C 94.5,13.5 93.5,13.5 92.5,13.5C 92.6498,9.80534 91.6498,6.47201 89.5,3.5C 89.5,3.16667 89.5,2.83333 89.5,2.5 Z"
      />
    </g>
    <g>
      <path
        style={{
          opacity: 1
        }}
        fill="#0b5499"
        d="M 22.5,1.5 C 22.4326,2.95875 22.7659,4.29208 23.5,5.5C 20.8361,7.48441 19.1695,10.1511 18.5,13.5C 18.1667,13.5 17.8333,13.5 17.5,13.5C 17.573,11.9731 16.9063,10.9731 15.5,10.5C 17.1108,6.94852 19.4442,3.94852 22.5,1.5 Z"
      />
    </g>
    <g>
      <path
        style={{
          opacity: 0.997
        }}
        fill="#08318f"
        d="M 23.5,0.5 C 27.4632,4.96447 31.6298,9.2978 36,13.5C 36.4828,12.552 36.6495,11.552 36.5,10.5C 37.5729,12.3469 37.5729,14.3469 36.5,16.5C 33.8333,14.8333 31.1667,13.1667 28.5,11.5C 28.2283,8.26779 26.5616,6.26779 23.5,5.5C 22.7659,4.29208 22.4326,2.95875 22.5,1.5C 22.5,0.833333 22.8333,0.5 23.5,0.5 Z"
      />
    </g>
    <g>
      <path
        style={{
          opacity: 0.989
        }}
        fill="#e68c1d"
        d="M 11.5,8.5 C 12.7095,8.93205 13.7095,9.59872 14.5,10.5C 14.2624,12.4044 13.2624,13.7377 11.5,14.5C 9.7162,14.1075 8.04953,13.4408 6.5,12.5C 6.38377,9.08968 8.05044,7.75635 11.5,8.5 Z"
      />
    </g>
    <g>
      <path
        style={{
          opacity: 0.995
        }}
        fill="#e26427"
        d="M 11.5,8.5 C 8.05044,7.75635 6.38377,9.08968 6.5,12.5C 6.62867,14.2161 6.962,15.8828 7.5,17.5C 6.08134,18.4511 4.41467,18.7845 2.5,18.5C 2.8064,15.6146 2.47307,12.9479 1.5,10.5C 2.05773,9.21009 3.05773,8.37675 4.5,8C 7.03851,7.20419 9.37184,7.37086 11.5,8.5 Z"
      />
    </g>
    <g>
      <path
        style={{
          opacity: 1
        }}
        fill="#ec951b"
        d="M 1.5,10.5 C 2.47307,12.9479 2.8064,15.6146 2.5,18.5C 1.83333,18.5 1.16667,18.5 0.5,18.5C 0.5,17.8333 0.166667,17.5 -0.5,17.5C -0.5,15.5 -0.5,13.5 -0.5,11.5C 0.166667,11.1667 0.833333,10.8333 1.5,10.5 Z"
      />
    </g>
    <g>
      <path
        style={{
          opacity: 1
        }}
        fill="#0a4e97"
        d="M 28.5,11.5 C 31.1667,13.1667 33.8333,14.8333 36.5,16.5C 36.1298,19.267 34.7964,21.267 32.5,22.5C 31.1667,20.8333 29.8333,19.1667 28.5,17.5C 28.5,15.5 28.5,13.5 28.5,11.5 Z"
      />
    </g>
    <g>
      <path
        style={{
          opacity: 0.964
        }}
        fill="#35a2b4"
        d="M 14.5,10.5 C 14.8333,10.5 15.1667,10.5 15.5,10.5C 16.9063,10.9731 17.573,11.9731 17.5,13.5C 15.5989,14.935 15.2655,16.6017 16.5,18.5C 11.1571,22.4979 5.8238,22.4979 0.5,18.5C 1.16667,18.5 1.83333,18.5 2.5,18.5C 4.41467,18.7845 6.08134,18.4511 7.5,17.5C 9.23033,16.9744 10.5637,15.9744 11.5,14.5C 13.2624,13.7377 14.2624,12.4044 14.5,10.5 Z"
      />
    </g>
    <g>
      <path
        style={{
          opacity: 0.982
        }}
        fill="#d4502b"
        d="M 17.5,13.5 C 17.8333,13.5 18.1667,13.5 18.5,13.5C 20.756,16.4053 23.756,18.072 27.5,18.5C 26.7002,20.7409 25.0336,21.7409 22.5,21.5C 20.2163,21.0248 18.2163,20.0248 16.5,18.5C 15.2655,16.6017 15.5989,14.935 17.5,13.5 Z"
      />
    </g>
    <g>
      <path
        style={{
          opacity: 0.145
        }}
        fill="#1e1e1e"
        d="M 56.5,18.5 C 56.5,19.1667 56.5,19.8333 56.5,20.5C 56.5654,20.9382 56.3988,21.2716 56,21.5C 55.3333,18.8333 54.6667,18.8333 54,21.5C 49.516,21.0397 45.016,21.0397 40.5,21.5C 40.5,19.8333 40.5,18.1667 40.5,16.5C 45.6309,17.628 50.4643,17.628 55,16.5C 55.2784,17.4158 55.7784,18.0825 56.5,18.5 Z M 49.5,18.5 C 52.0009,18.5109 52.1676,19.1776 50,20.5C 49.5357,19.9056 49.369,19.2389 49.5,18.5 Z"
      />
    </g>
    <g>
      <path
        style={{
          opacity: 0.369
        }}
        fill="#1a1a1a"
        d="M 56.5,20.5 C 56.5,19.8333 56.5,19.1667 56.5,18.5C 56.8212,16.9519 57.8212,16.2852 59.5,16.5C 59.8333,21.8333 60.1667,21.8333 60.5,16.5C 65.0731,17.736 69.4064,17.736 73.5,16.5C 75.8718,17.3628 75.8718,18.5295 73.5,20C 74.1667,20.3333 74.8333,20.6667 75.5,21C 69.1782,21.2364 62.8449,21.0698 56.5,20.5 Z"
      />
    </g>
    <g>
      <path
        style={{
          opacity: 0.118
        }}
        fill="#1b1b1b"
        d="M 93.5,17.5 C 93.5,18.8333 93.5,20.1667 93.5,21.5C 92.1529,20.8288 90.9862,20.8288 90,21.5C 89.1932,17.551 88.6932,17.551 88.5,21.5C 84.2483,20.9509 80.4149,20.9509 77,21.5C 76.2025,19.7412 76.3691,18.0745 77.5,16.5C 78.7202,17.7111 79.8869,17.7111 81,16.5C 81.3333,17.1667 81.6667,17.8333 82,18.5C 83.1803,17.3159 84.6803,16.6493 86.5,16.5C 89.1744,16.7881 91.5078,17.1215 93.5,17.5 Z"
      />
    </g>
    <g>
      <path
        style={{
          opacity: 0.122
        }}
        fill="#1b1b1b"
        d="M 102.5,16.5 C 102.5,18.1667 102.5,19.8333 102.5,21.5C 99.5,20.1667 96.5,20.1667 93.5,21.5C 93.5,20.1667 93.5,18.8333 93.5,17.5C 96.4787,16.834 99.4787,16.5007 102.5,16.5 Z"
      />
    </g>
    <g>
      <path
        style={{
          opacity: 1
        }}
        fill="#2277b1"
        d="M 28.5,17.5 C 29.8333,19.1667 31.1667,20.8333 32.5,22.5C 32.5,22.8333 32.5,23.1667 32.5,23.5C 29.8333,23.5 27.1667,23.5 24.5,23.5C 23.8333,22.8333 23.1667,22.1667 22.5,21.5C 25.0336,21.7409 26.7002,20.7409 27.5,18.5C 27.5,17.8333 27.8333,17.5 28.5,17.5 Z"
      />
    </g>
  </svg>
);
export default React.memo(LogoCompany);
