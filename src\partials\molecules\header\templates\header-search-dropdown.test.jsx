import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { BrowserRouter as Router } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import {
  HeaderSearchDropDownResults,
  HeaderSearchDropdownLoader,
  HeaderSearchDropdownNoResults
} from "./header-search-dropdown";

const mockResults = [
  {
    acuity_security_id: 1,
    name: "Apple Inc",
    primary_ticker: "AAPL",
    is_public: true,
    logo_url: [{ value: "https://example.com/apple.png" }]
  }
];

const queryClient = new QueryClient();

describe("HeaderSearchDropDownResults", () => {
  it("renders without crashing", () => {
    const mockProps = {
      results: [],
      query: "",
      setAddWatchListPopUp: jest.fn(),
      setResult: jest.fn(),
      isKeywordSearch: false,
      setDropdownOpen: jest.fn(),
      setQuery: jest.fn()
    };
    render(
      <Router>
        <QueryClientProvider client={queryClient}>
          <HeaderSearchDropDownResults {...mockProps} />
        </QueryClientProvider>
      </Router>
    );
  });

  it("renders keyword results when isKeywordSearch is true", () => {
    const mockProps = {
      results: [],
      query: "",
      setAddWatchListPopUp: jest.fn(),
      setResult: jest.fn(),
      isKeywordSearch: true,
      setDropdownOpen: jest.fn(),
      setQuery: jest.fn()
    };
    render(
      <Router>
        <QueryClientProvider client={queryClient}>
          <HeaderSearchDropDownResults {...mockProps} />
        </QueryClientProvider>
      </Router>
    );
    expect(screen.getByText("Keyword Results")).toBeInTheDocument();
  });

  it("renders company/ticker results when isKeywordSearch is false", () => {
    const mockProps = {
      results: [],
      query: "",
      setAddWatchListPopUp: jest.fn(),
      setResult: jest.fn(),
      isKeywordSearch: false,
      setDropdownOpen: jest.fn(),
      setQuery: jest.fn()
    };
    render(
      <Router>
        <QueryClientProvider client={queryClient}>
          <HeaderSearchDropDownResults {...mockProps} />
        </QueryClientProvider>
      </Router>
    );
    expect(screen.getByText("Company/Ticker Results")).toBeInTheDocument();
  });

  it("renders results and highlights query", () => {
    const mockProps = {
      results: mockResults,
      query: "app",
      setAddWatchListPopUp: jest.fn(),
      setResult: jest.fn(),
      isKeywordSearch: false,
      setDropdownOpen: jest.fn(),
      setQuery: jest.fn()
    };
    render(
      <Router>
        <QueryClientProvider client={queryClient}>
          <HeaderSearchDropDownResults {...mockProps} />
        </QueryClientProvider>
      </Router>
    );

    // Expect list items to be rendered
    const listItems = screen.getAllByTestId("header-search-dropdown-result");
    expect(listItems.length).toBe(mockResults.length);
  });

  it("renders without crashing", () => {
    render(
      <Router>
        <HeaderSearchDropdownNoResults />
      </Router>
    );
  });

  it("renders the no results message", () => {
    render(
      <Router>
        <HeaderSearchDropdownNoResults />
      </Router>
    );
    expect(screen.getByText("Oops!!! No results found!")).toBeInTheDocument();
  });

  it("renders without crashing", () => {
    render(
      <Router>
        <HeaderSearchDropdownLoader />
      </Router>
    );
  });

  it("renders the loading message", () => {
    render(
      <Router>
        <HeaderSearchDropdownLoader />
      </Router>
    );
    expect(
      screen.getByText("Please wait, we are getting the results ready")
    ).toBeInTheDocument();
  });
});
