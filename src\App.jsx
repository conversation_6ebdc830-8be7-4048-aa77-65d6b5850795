import React from "react";
import "./global.css";
import "@progress/kendo-theme-default/dist/all.css";
import "../src/partials/atoms/grid/kendo-grid.css";
import "react-tooltip/dist/react-tooltip.css";
import QueryClientContextProvider from "./infra/contexts/react-query-context";
import AuthContextProvider from "./infra/contexts/auth-context";
import QueueContextProvider from "./infra/contexts/queue-context";
import TokenManagerContextProvider from "./infra/contexts/token-manager-context";
import RoutesConfig from "./pages/routes";
import { ErrorContextProvider } from "./infra/contexts/error-context";
import GlobalLoadingIndicator from "./components/GlobalLoadingIndicator/GlobalLoadingIndicator";
import "./kendo.css";
import "./var.css";
import "./acuityone.css";
import "../src/resources/css/fonts.css";
const App = () => {
  return (
    <ErrorContextProvider>
      {/* <AuthContextProvider> */}
        <QueryClientContextProvider>
          {/* <TokenManagerContextProvider> */}
            {/* <AppRoleAccessProvider> */}
            {/* <AppContextProvider> */}
            <QueueContextProvider>
              <>
                <GlobalLoadingIndicator />
                <RoutesConfig />
              </>
            </QueueContextProvider>
            {/* </AppContextProvider> */}
            {/* </AppRoleAccessProvider> */}
          {/* </TokenManagerContextProvider> */}
        </QueryClientContextProvider>
      {/* </AuthContextProvider> */}
    </ErrorContextProvider>
  );
};

export default App;
