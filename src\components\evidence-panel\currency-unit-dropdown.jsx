import React, { useState, useEffect } from "react";
import { <PERSON>u, <PERSON>uButton, MenuItem, MenuItems } from "@headlessui/react";
import { HiChevronDown } from "react-icons/hi2";
import PropTypes from "prop-types";
import { FINANCIAL_VALUE_UNIT_ENUM } from "./utilities/financial-value-unit-enum";


const CurrencyUnitDropdown = ({
  currencyUnit,
  onCurrencyUnitChange
}) => {
  const currencyUnitItems = FINANCIAL_VALUE_UNIT_ENUM;
  const [isOpen, setIsOpen] = useState(false);
  
  // Initialize with a proper match or fallback to "No Unit"
  const [selectedCurrencyUnit, setSelectedCurrencyUnit] = useState("No Unit");
  
  // Use useEffect to update the selectedCurrencyUnit when currencyUnit prop changes
  useEffect(() => {
    // Look for a case-insensitive match to be safe
    const matchingUnit = FINANCIAL_VALUE_UNIT_ENUM.find(
      (opt) => opt.label.toLowerCase() === currencyUnit?.toLowerCase()
    );
    
    setSelectedCurrencyUnit(matchingUnit?.label || "No Unit");
  }, [currencyUnit]);

  const handleChange = (selectedValue) => {
    // Find the corresponding value for the selected label
    setSelectedCurrencyUnit(selectedValue?.label || "No Unit");

    if (onCurrencyUnitChange) {
      onCurrencyUnitChange(selectedValue);
    }
  };
  
  return (
    <Menu as="div" className="ml-[-9px] relative">
      <div>
        <MenuButton
          className={`h-8 w-[135px] body-r items-center px-4 py-1.5 gap-2 border border-neutral-20 currency-unit-radius inline-flex justify-between gap-x-1.5 font-normal ring-gray-300 ring-inset transition-colors 
          ${selectedCurrencyUnit == "No Unit" ? "text-neutral-40" : "text-neutral-80"}`}
        >
          {selectedCurrencyUnit}
          <HiChevronDown aria-hidden="true" className=" text-gray-400" />
        </MenuButton>

        <MenuItems
          transition
          className="absolute z-99 w-[135px] mt-2 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black/5 transition focus:outline-none data-[closed]:scale-95 data-[closed]:transform data-[closed]:opacity-0 data-[enter]:duration-100 data-[leave]:duration-75 data-[enter]:ease-out data-[leave]:ease-in"
        >
          <div className="py-1 ">
            {currencyUnitItems.map((item, index) => (
              <MenuItem key={index}>
                {({ focus }) => (
                  <button
                    role="menuitem"
                    className={`block w-[135px] text-left px-4 py-2 text-body-r font-body-r text-neutral-90 ${
                      focus ? "bg-primary-40 text-gray-900" : ""
                    } hover:bg-primary-40 hover:text-primary-78`}
                    onClick={() => handleChange(item)}
                  >
                    {item.label}
                  </button>
                )}
              </MenuItem>
            ))}
          </div>
        </MenuItems>
      </div>
    </Menu>
  );
};

CurrencyUnitDropdown.propTypes = {
  currencyUnit: PropTypes.string.isRequired,
  onCurrencyUnitChange: PropTypes.func
};

export default CurrencyUnitDropdown;
