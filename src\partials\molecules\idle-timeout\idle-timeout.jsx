import React, { Fragment, useEffect, useState } from "react";
import {
  Dialog,
  Transition,
  TransitionChild,
  DialogPanel,
  DialogTitle
} from "@headlessui/react";
import { useIdleTimer } from "react-idle-timer";
import { useAuth } from "react-oidc-context";
import { RxCross1 } from "react-icons/rx";
import { DELETE_TOKENS_API } from "../../../infra/api/token-manager-service";
import { config } from "../../../constants/config";

const minute = 1000 * 60;
let timeout = minute * config.idleTimeout;
let promptBeforeIdle = minute;

const IdleTimeout = () => {
  const [model, setModel] = useState(false);
  const [remaining, setRemaining] = useState(timeout);
  const [remainingSuffix, setRemainingSuffix] = useState(timeout);
  const auth = useAuth();

  const deleteTokenAPI = DELETE_TOKENS_API();

  const handleOnPrompt = () => {
    setModel(true);
  };

  const onLogout = async () => {
    await deleteTokenAPI.mutateAsync();
    auth.signoutRedirect();
  };

  const handleOnIdle = () => {
    onLogout();
  };

  const handleOnActive = () => {
    setModel(false);
  };

  const { getRemainingTime, activate } = useIdleTimer({
    promptBeforeIdle: promptBeforeIdle,
    timeout: timeout,
    onPrompt: handleOnPrompt,
    onIdle: handleOnIdle,
    onActive: handleOnActive,
    debounce: 500,
    crossTab: true,
    leaderElection: true,
    syncTimers: 200
  });

  useEffect(() => {
    const interval = setInterval(() => {
      let remainingMilliSeconds = getRemainingTime();
      if (remainingMilliSeconds > minute) {
        remainingMilliSeconds = remainingMilliSeconds / 1000 / 60;
        setRemainingSuffix("minutes");
      } else {
        remainingMilliSeconds = remainingMilliSeconds / 1000;
        setRemainingSuffix("seconds");
      }
      setRemaining(Math.ceil(remainingMilliSeconds));
    }, 500);

    return () => {
      clearInterval(interval);
    };
  });

  const onContinue = () => {
    activate();
  };

  return (
    <Transition appear show={model} as={Fragment}>
      <Dialog as="div" className="relative z-50" onClose={() => {}}>
        <TransitionChild
          as={Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="fixed inset-0 bg-black bg-opacity-25" />
        </TransitionChild>

        <div className="fixed inset-0 overflow-y-auto">
          <div
            data-testid={"idle-timeout-popup"}
            className="grid min-h-full grid-cols-12 items-center justify-center gap-4 p-4 text-center"
          >
            <TransitionChild
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0 scale-95"
              enterTo="opacity-100 scale-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100 scale-100"
              leaveTo="opacity-0 scale-95"
            >
              <DialogPanel className="col-start-5 col-end-9 grid transform overflow-hidden rounded-xl bg-white text-left align-middle shadow-xl transition-all 3xl:col-start-6">
                <DialogTitle
                  as="h3"
                  className="m-m flex justify-between bg-primary-40 p-4 px-6 leading-6 text-neutral-90"
                >
                  <div className="flex items-center justify-start">
                    Stay connected
                  </div>
                  <div
                    className="flex place-items-end items-center justify-end"
                    data-testid={`popup-idle-timeout-close-icon`}
                  >
                    <RxCross1 onClick={() => onContinue()} />
                  </div>
                </DialogTitle>
                <div className="py-4 ">
                  <p className="s-r px-6 text-neutral-90">
                    Session is expiring in {remaining} {remainingSuffix}
                  </p>
                </div>

                <div className="my-4 flex justify-between px-6">
                  <div className="flex justify-start"></div>
                  <div className="flex justify-end">
                    <button
                      type="button"
                      id="popup-delete-cancel-asset-button"
                      data-testid="popup-delete-cancel-asset-button"
                      className="s-r inline-flex justify-end rounded border border-primary-78 px-4 py-2 text-primary-78 outline-none hover:bg-primary-40"
                      onClick={() => onLogout()}
                    >
                      Logout
                    </button>
                    <button
                      type="button"
                      id="popup-delete-asset-button"
                      data-testid="popup-delete-asset-button"
                      className="s-r ml-2 inline-flex justify-end rounded-md bg-primary-78 px-4 py-2 text-white outline-none hover:bg-primary-90 focus:bg-primary-100"
                      onClick={() => onContinue()}
                    >
                      Stay Connected
                    </button>
                  </div>
                </div>
              </DialogPanel>
            </TransitionChild>
          </div>
        </div>
      </Dialog>
    </Transition>
  );
};

export default IdleTimeout;
