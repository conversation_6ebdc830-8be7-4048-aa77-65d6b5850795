import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import { ComboBox } from "@progress/kendo-react-dropdowns";
import { Button } from "../../partials/atoms/button";
import { FaTimes } from "react-icons/fa";
import { KpiModule,FundFinancial } from "../../constants/kpi-module";
// Constants for the new dropdown options
const DEFAULT_DATA_TYPES = [];

const DEFAULT_PERIOD_TYPES = [
  { text: "Monthly", value: "monthly" },
  { text: "Quarterly", value: "quarterly" },
  { text: "Annual", value: "annually" }
];

const QUARTERS = [
  { text: "Q1", value: "Q1" },
  { text: "Q2", value: "Q2" },
  { text: "Q3", value: "Q3" },
  { text: "Q4", value: "Q4" }
];

// Array of month names for the month dropdown
const MONTHS = [
  { text: "January", value: 0 },
  { text: "February", value: 1 },
  { text: "March", value: 2 },
  { text: "April", value: 3 },
  { text: "May", value: 4 },
  { text: "June", value: 5 },
  { text: "July", value: 6 },
  { text: "August", value: 7 },
  { text: "September", value: 8 },
  { text: "October", value: 9 },
  { text: "November", value: 10 },
  { text: "December", value: 11 }
];

// Generate years for dropdown (current year - 20 to current year + 5)
const generateYearOptions = () => {
  const currentYear = new Date().getFullYear();
  const years = [];
  
  for (let year = currentYear - 20; year <= currentYear + 5; year++) {
    years.push({ text: year.toString(), value: year });
  }
  
  return years;
};

const YEARS = generateYearOptions();

const PeriodHeaderSidebar = ({ isOpen, onClose, onSave, initialValues = {}, columnId, selectedTab, kpiConfig, extractionType }) => {
  // Initialize state with provided values or defaults
  const [values, setValues] = useState({
    Data_Type: initialValues.Data_Type || "",
    Period_Type: initialValues.Period_Type ? initialValues.Period_Type.toLowerCase() : "",
    Period_Year: initialValues.Period_Year || new Date().getFullYear(),
    Period_Quarter: initialValues.Period_Quarter || "",
    Period_Month: initialValues.Period_Month !== undefined ? 
      initialValues.Period_Month : new Date().getMonth()
  });
  // State to track selected period type for dynamic calendar
  const [selectedPeriodType, setSelectedPeriodType] = useState(
    initialValues.Period_Type ? initialValues.Period_Type.toLowerCase() : ""
  );

  // State to track form validity
  const [isFormValid, setIsFormValid] = useState(false);
  const [allDataTypes, setAllDataTypes] = useState(DEFAULT_DATA_TYPES);
  const [filteredDataTypes, setFilteredDataTypes] = useState(DEFAULT_DATA_TYPES);
  const [periodTypes, setPeriodTypes] = useState(DEFAULT_PERIOD_TYPES);
  const [filteredMonths, setFilteredMonths] = useState(MONTHS);
  const [filteredYears, setFilteredYears] = useState(YEARS);


  // Effect to validate form when values change
  useEffect(() => {
    let valid = !!values.Data_Type;

    // Add period-specific validation
    if (valid && values.Period_Type) {
      switch (values.Period_Type) {
        case "monthly":
          valid = values.Period_Month !== undefined && values.Period_Month !== "" && values.Period_Month !== null && values.Period_Year;
          break;
        case "quarterly":
          valid = !!values.Period_Quarter && values.Period_Year;
          break;
        case "annually":
          valid = !!values.Period_Year;
          break;
        default:
          valid = false;
      }
    } else {
      valid = false;
    }

    setIsFormValid(valid);
  }, [values]);

  // Handle period type selection change
  const handlePeriodTypeChange = (event) => {
    const selectedValue = event.target.value;
   if(selectedValue==null || selectedValue.value === ""){
      setSelectedPeriodType(null);
      setValues(prevValues => ({
        ...prevValues,        
        Period_Type: "",
        Period_Quarter: "", // Reset quarter  
        Period_Month: "", // Reset month to current month
        Period_Year: "" // Reset year to current year
      })); 
    } 
    else{
      setSelectedPeriodType(selectedValue.value);
    
    // Reset period-specific fields when period type changes
    setValues(prevValues => ({
      ...prevValues,
      Period_Type: selectedValue.value,
      Period_Quarter: "", // Reset quarter
      Period_Month: new Date().getMonth() // Reset month to current month
    }));
    }
  };
    // Handle data type selection change
  const handleDataTypeChange = (event) => {
    const selectedValue = event.target.value;
    
    if(selectedValue==null || selectedValue.value === ""){
      setValues(prevValues => ({
        ...prevValues,
        Data_Type: ""
      }));
      
    }
    else{
      // Find the selected data type from the dataTypes array
    const selectedDataType = filteredDataTypes.find(dt => dt.value === selectedValue.value);
    
    // Update the period types based on the chartValue of the selected data type
    if (selectedDataType?.chartValue) {
      const availablePeriods = selectedDataType.chartValue.split(',');
      const filteredPeriodTypes = DEFAULT_PERIOD_TYPES.filter(periodType => {
        // Convert period type value to match the format in chartValue (capitalize first letter)
        const periodTypeText = periodType.text;
        return availablePeriods.includes(periodTypeText);
      });
      
      setPeriodTypes(filteredPeriodTypes);
      
      // Reset the Period_Type if the current selection is no longer available
      const currentPeriodType = values.Period_Type;
      const periodTypeStillAvailable = filteredPeriodTypes.some(pt => pt.value === currentPeriodType);
      
      if (!periodTypeStillAvailable && filteredPeriodTypes.length > 0) {
        setValues(prevValues => ({
          ...prevValues,
          Period_Type: filteredPeriodTypes[0].value
        }));
        setSelectedPeriodType(filteredPeriodTypes[0].value);
      }
    }
    
    setValues(prevValues => ({
      ...prevValues,
      Data_Type: selectedValue.value
    }));
    }
  };
  
  // Add filter change handler
  const handleDataTypeFilter = (event) => {
    if (!event.filter.value) {
      setFilteredDataTypes(allDataTypes);
    } else {
      const filtered = allDataTypes.filter(item => 
        item.text.toLowerCase().includes(event.filter.value.toLowerCase())
      );
      setFilteredDataTypes(filtered);
    }
  };

  // Handle quarter selection
  const handleQuarterChange = (event) => {
    const selectedValue = event.target.value;
    if(selectedValue==null || selectedValue.value === ""){
      setValues(prevValues => ({
        ...prevValues,
        Period_Quarter: ""
      }));
      
    }
    else{
      setValues(prevValues => ({
      ...prevValues,
      Period_Quarter: selectedValue.value
    }));
    }
  };
  
  // Handle month selection
  const handleMonthChange = (event) => {
    const selectedItem = event.value;
    setValues(prevValues => ({
      ...prevValues,
      Period_Month: selectedItem ? selectedItem.value : ""
    }));
  };
  
  // Handle year selection for dropdown
  const handleYearDropdownChange = (event) => {
    const selectedValue = event.target.value;
    const currentYear = new Date().getFullYear();    
    // If no value is selected or value is cleared, default to current year
    let yearValue = "";
    if (selectedValue == null || selectedValue.value === "") {
      yearValue = "";
    } else {
      yearValue = selectedValue ? selectedValue.value : currentYear;
    }
    setValues(prevValues => ({
      ...prevValues,
      Period_Year: yearValue
    }));
  };
    
  // Handle month filter change
  const handleMonthFilter = (event) => {
    if (!event.filter.value) {
      setFilteredMonths(MONTHS);
    } else {
      const filtered = MONTHS.filter(item => 
        item.text.toLowerCase().includes(event.filter.value.toLowerCase())
      );
      setFilteredMonths(filtered);
    }
  };

  // Handle year filter change
  const handleYearFilter = (event) => {
    if (!event.filter.value) {
      setFilteredYears(YEARS);
    } else {
      const filtered = YEARS.filter(item => 
        item.text.toLowerCase().includes(event.filter.value.toLowerCase())
      );
      setFilteredYears(filtered);
    }
  };

  const getModuleId = () => {
    switch (selectedTab) {
      case 0:
        return KpiModule.ProfitAndLoss; // For Income Statement
      case 1:
        return KpiModule.BalanceSheet; // For Balance Sheet
      case 2:
        return KpiModule.CashFlow; // For Cash Flow
      default:
        return KpiModule.ProfitAndLoss; // Default to Income Statement if tab is undefined
    }
  };

  // Update dataTypes when kpiConfig changes
  useEffect(() => {
    let configData = [];
    if (kpiConfig) {
      switch (extractionType) {
        case "AsIsExtraction":
          const moduleId = getModuleId();
          configData = kpiConfig.find(x => x.moduleId == moduleId);
          break;
        case "SpecificKpi":
          configData = kpiConfig.find(x => x.moduleId == selectedTab);
          break;
      }
      if(configData){
        // Map the subSectionFields to data types where text is name and value is aliasName
        const typesFromConfig = configData?.subSectionFields?.map(field => ({
          name: field.name,
          text: field.aliasName,
          value: field.aliasName,
          valueTypId: field.valueTypId,
          chartValue: field.chartValue
        }));
        
        setAllDataTypes(typesFromConfig);
        setFilteredDataTypes(typesFromConfig);
      }
    }
  }, [kpiConfig, selectedTab]);

  const handleSaveClick = () => {
    // Format the data before sending it back
    const formattedData = {
      ...values,
      columnKey: columnId
    };
    onSave(formattedData);
  };

  // Render period selector based on period type
  const renderPeriodSelector = () => {
    switch (selectedPeriodType) {
      case "monthly":
        return (
          <>
            <div className="mb-6">
              <label htmlFor="month-select" className="block xs-m font-medium text-neutral-60 mb-1">
                Select Month <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <ComboBox clearButton={false}
                 filterable={false}
                  id="month-select"
                  data={filteredMonths}
                  textField="text"
                  dataItemKey="value"
                  value={MONTHS.find(m => m.value === values.Period_Month) || null}
                  onChange={handleMonthChange}
                  onFilterChange={handleMonthFilter}
                  placeholder="Select month"
                  className="w-full py-1.5 px-4 border border-neutral-20 rounded-md focus:outline-none"
                />
              </div>
            </div>
            
            <div className="mb-6">
              <label htmlFor="year-select-monthly" className="block xs-m font-medium text-neutral-60 mb-1">
                Select Year
              </label>
              <div className="relative">
                <ComboBox clearButton={false}
                  id="year-select-monthly"
                  data={filteredYears}
                  textField="text"
                  dataItemKey="value"
                  value={YEARS.find(y => y.value === values.Period_Year) || null}
                  onChange={handleYearDropdownChange}
                  onFilterChange={handleYearFilter}
                  placeholder="Select year"
                  className="w-full py-1.5 px-4 border border-neutral-20 rounded-md focus:outline-none"
                  filterable={false}
                />
              </div>
            </div>
          </>
        );
      
      case "quarterly":
        return (
          <>
            <div className="mb-6">
              <label htmlFor="quarter-select" className="block xs-m font-medium text-neutral-60 mb-1">
                Select Quarter <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <ComboBox clearButton={false}
                 filterable={false}
                  id="quarter-select"
                  data={QUARTERS}
                  textField="text"
                  dataItemKey="value"
                  value={QUARTERS.find(q => q.value === values.Period_Quarter) || null}
                  onChange={handleQuarterChange}
                  placeholder="Select quarter"
                  className="w-full py-1.5 px-4 border border-neutral-20 rounded-md focus:outline-none"
                />
              </div>
            </div>
            
            <div className="mb-6">
              <label htmlFor="year-select-quarterly" className="block xs-m font-medium text-neutral-60 mb-1">
                  Select Year <span className="text-red-500">*</span>
              </label>
              <div className="relative">
                <ComboBox clearButton={false}
                  id="year-select-quarterly"
                  data={filteredYears}
                  textField="text"
                  dataItemKey="value"
                  value={YEARS.find(y => y.value === values.Period_Year) || null}
                  onChange={handleYearDropdownChange}
                  onFilterChange={handleYearFilter}
                  placeholder="Select year"
                  className="w-full py-1.5 px-4 border border-neutral-20 rounded-md focus:outline-none"
                  filterable={false}
                />
              </div>
            </div>
          </>
        );
      
      case "annually":
        return (
          <div className="mb-6">
            <label htmlFor="year-select-annually" className="block xs-m font-medium text-neutral-60 mb-1">
              Select Year
            </label>
            <div className="relative">
              <ComboBox clearButton={false}
                id="year-select-annually"
                data={filteredYears}
                textField="text"
                dataItemKey="value"
                value={YEARS.find(y => y.value === values.Period_Year) || null}
                onChange={handleYearDropdownChange}
                onFilterChange={handleYearFilter}
                placeholder="Select year"
                className="w-full py-1.5 px-4 border border-neutral-20 rounded-md focus:outline-none"
                filterable={false}
              />
            </div>
          </div>
        );
      
      default:
        return null;
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed right-0 top-0 h-full w-96 bg-white shadow-lg z-50 flex flex-col border-l border-neutral-20">
      {/* Header */}
      <div className="pr-1 pt-4 pl-6 flex-1  overflow-y-auto">
      <div className="border-b-2 flex justify-between items-centers pb-2">
        <h2 className="m-m  border-b-neutral-20">Change Period Header</h2>
        <button
          onClick={onClose}
          className="text-neutral-60 hover:text-neutral-80 p-1"
          aria-label="Close"
        >
          <FaTimes />
        </button>
      </div>

      {/* Content */}
      
        {/* Data Type dropdown */}
        <div className="mb-6">
        <label htmlFor="data-type" className="block xs-m body-m mb-1 text-neutral-60 pt-3">
        Data Type <span className="text-red-500">*</span>
        </label>
          <div className="relative">
            <ComboBox clearButton={false}
              id="data-type"
              data={filteredDataTypes}
              textField="text"
              dataItemKey="value"
              value={allDataTypes.find(dt => dt.value === values.Data_Type) || null}
              onChange={handleDataTypeChange}
              onFilterChange={handleDataTypeFilter}
              placeholder="Select data type"
              className="w-full py-1.5 px-4 border border-neutral-20 rounded-md focus:outline-none"
               filterable={false}
            />
          </div>
        </div>

        {/* Period Type dropdown */}
        <div className="mb-6">
          <label htmlFor="period-type" className="block xs-m body-m mb-1 text-neutral-60">
             Period Type <span className="text-red-500">*</span>
          </label>
          <div className="relative">
            <ComboBox clearButton={false}
             filterable={false}
              id="period-type"
              data={periodTypes}
              textField="text"
              dataItemKey="value"
              value={periodTypes.find(pt => pt.value === values.Period_Type) || null}
              onChange={handlePeriodTypeChange}
              placeholder="Select period type"
              className="w-full py-1.5 px-4 border border-neutral-20 rounded-md focus:outline-none"
            />
          </div>
        </div>

        {/* Dynamic Period Selector based on Period Type */}
        {renderPeriodSelector()}
      </div>

      {/* Footer with action buttons */}
      <div className="flex justify-end gap-[12px] pl-4 pr-1">
        <Button 
          intent={"teritory"} 
          onClick={onClose} 
          className="border border-current text-neutral-90"
        >
          Cancel
        </Button>
        <Button 
          intent={"primary"} 
          onClick={handleSaveClick}
          disabled={!isFormValid}
          className={!isFormValid ? "opacity-50 cursor-not-allowed" : ""}
        >
          Save
        </Button>
      </div>
    </div>
  );
};

PeriodHeaderSidebar.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onSave: PropTypes.func.isRequired,
  initialValues: PropTypes.shape({
    Data_Type: PropTypes.string,
    Period_Type: PropTypes.string,
    Period_Year: PropTypes.number,
    Period_Quarter: PropTypes.string,
    Period_Month: PropTypes.number
  }),
  columnId: PropTypes.string,
  selectedTab: PropTypes.number,
  kpiConfig: PropTypes.array,
  extractionType: PropTypes.string
};

export default PeriodHeaderSidebar;