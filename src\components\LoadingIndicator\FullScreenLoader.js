import React from "react";
import PropTypes from "prop-types";
import "./LoadinIndicator.css";
import { Spin } from "@maknowledgeservices/neptune";

const FullScreenLoader = ({ loadingMessage, isLoading, inline }) => (
  <div>
    {isLoading && (
      <div style={{ height: "100%", width: "100%" }}>
        <div
          className={`${inline ? "inline-loader" : " form-status"}`}
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "100vh"
          }}
        >
          <Spin name="default" size={24} color="var(--primary-blue-78)" />

          <span
            className={`loadingmesaage ${inline && "loading-padding-left"}`}
            title={loadingMessage}
            alt={loadingMessage}
          >
            {loadingMessage}
          </span>
        </div>
      </div>
    )}
  </div>
);
FullScreenLoader.propTypes = {
  loadingMessage: PropTypes.string,
  isLoading: PropTypes.bool,
  inline: PropTypes.bool
};

FullScreenLoader.defaultProps = {
  loadingMessage: "",
  isLoading: false,
  inline: false
};

export default FullScreenLoader;
