/**
 * @jest-environment jsdom
 */
import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import CurrencyDropdown from '../currency-dropdown';

// Mock Headless UI components
jest.mock('@headlessui/react', () => ({
  Menu: ({ as: Component = 'div', children, className }) => (
    <Component className={className} data-testid="menu">
      {typeof children === 'function' ? children({ open: true }) : children}
    </Component>
  ),
  MenuButton: ({ className, children, ...props }) => (
    <button className={className} data-testid="menu-button" {...props}>
      {children}
    </button>
  ),
  MenuItem: ({ children, ...props }) => {
    return (
      <div data-testid="menu-item" {...props}>
        {typeof children === 'function' ? children({ active: false, focus: false }) : children}
      </div>
    );
  },
  MenuItems: ({ className, children }) => (
    <div className={className} data-testid="menu-items">
      {children}
    </div>
  ),
}));

// Mock currency data
const mockCurrencyList = [
  { currencyCode: 'USD', currencyName: 'US Dollar' },
  { currencyCode: 'EUR', currencyName: 'Euro' },
  { currencyCode: 'GBP', currencyName: 'British Pound' },
  { currencyCode: 'JPY', currencyName: 'Japanese Yen' },
  { currencyCode: 'CNY', currencyName: 'Chinese Yuan' },
];

describe('CurrencyDropdown Component', () => {
  // Default props
  const defaultProps = {
    currency: 'USD',
    currencyList: mockCurrencyList,
    onCurrencyChange: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  // Basic rendering test
  test('renders with default currency selected', () => {
    render(<CurrencyDropdown {...defaultProps} />);
    
    // Dropdown button should show the selected currency
    const menuButton = screen.getByTestId('menu-button');
    expect(menuButton).toHaveTextContent('USD');
  });

  // Test with empty currency
  test('renders with "No Currency" when currency is empty', () => {
    render(<CurrencyDropdown {...defaultProps} currency="" />);
    
    // Dropdown button should show "No Currency"
    const menuButton = screen.getByTestId('menu-button');
    expect(menuButton).toHaveTextContent('No Currency');
  });

  // Test with empty currency list
  test('handles empty currency list', () => {
    render(<CurrencyDropdown {...defaultProps} currencyList={[]} />);
    
    // Dropdown should still render
    expect(screen.getByTestId('menu')).toBeInTheDocument();
    
    // Should show "No matches found" when dropdown is opened
    const noMatchesText = screen.getByText('No matches found');
    expect(noMatchesText).toBeInTheDocument();
  });

  // Test currency selection
  test('calls onCurrencyChange when a currency is selected', async () => {
    render(<CurrencyDropdown {...defaultProps} />);
    
    // Get all menu items
    const menuItems = screen.getAllByTestId('menu-item');
    
    // Click on the second currency (EUR)
    const euroButton = menuItems[1].querySelector('button');
    fireEvent.click(euroButton);
    
    // onCurrencyChange should be called with the EUR object
    expect(defaultProps.onCurrencyChange).toHaveBeenCalledWith(mockCurrencyList[1]);
  });

  // Test search functionality
  test('filters currency list when searching', () => {
    render(<CurrencyDropdown {...defaultProps} />);
    
    // Get the search input
    const searchInput = screen.getByPlaceholderText('Search...');
    
    // Type in search term
    fireEvent.change(searchInput, { target: { value: 'eu' } });
    
    // Only EUR should be visible in the dropdown
    const menuItems = screen.getAllByTestId('menu-item');
    
    // Get the text of all menu items
    const menuItemTexts = menuItems.map(item => {
      const button = item.querySelector('button');
      return button ? button.textContent : '';
    });
    
    // Only EUR should match the search term "eu"
    expect(menuItemTexts).toContain('EUR');
    expect(menuItemTexts).not.toContain('USD');
  });

  // Test search with no matches
  test('shows "No matches found" when search has no results', () => {
    render(<CurrencyDropdown {...defaultProps} />);
    
    // Get the search input
    const searchInput = screen.getByPlaceholderText('Search...');
    
    // Type in search term that won't match any currency
    fireEvent.change(searchInput, { target: { value: 'xyz' } });
    
    // Should show "No matches found"
    const noMatchesText = screen.getByText('No matches found');
    expect(noMatchesText).toBeInTheDocument();
  });

  // Test search is cleared after selection
  test('clears search term after selecting a currency', () => {
    render(<CurrencyDropdown {...defaultProps} />);
    
    // Get the search input
    const searchInput = screen.getByPlaceholderText('Search...');
    
    // Type in search term
    fireEvent.change(searchInput, { target: { value: 'eu' } });
    
    // Get filtered menu items
    const menuItems = screen.getAllByTestId('menu-item');
    
    // Click on the EUR item
    const euroButton = menuItems[0].querySelector('button');
    fireEvent.click(euroButton);
    
    // Search input should be cleared
    expect(searchInput).toHaveValue('');
  });

  // Test with null values
  test('handles null currency list gracefully', () => {
    render(<CurrencyDropdown {...defaultProps} currencyList={null} />);
    
    // Component should render without errors
    expect(screen.getByTestId('menu')).toBeInTheDocument();
  });

  // Test with non-existing currency code
  test('defaults to "No Currency" when currency code is not in the list', () => {
    render(<CurrencyDropdown {...defaultProps} currency="XYZ" />);
    
    // Should default to "No Currency"
    const menuButton = screen.getByTestId('menu-button');
    expect(menuButton).toHaveTextContent('No Currency');
  });

  // Test component updates when props change
  test('updates when currency prop changes', () => {
    const { rerender } = render(<CurrencyDropdown {...defaultProps} />);
    
    // Initially USD
    let menuButton = screen.getByTestId('menu-button');
    expect(menuButton).toHaveTextContent('USD');
    
    // Change to EUR
    rerender(<CurrencyDropdown {...defaultProps} currency="EUR" />);
    
    // Should now show EUR
    menuButton = screen.getByTestId('menu-button');
    expect(menuButton).toHaveTextContent('EUR');
  });

  // Test without onCurrencyChange callback
  test('works without onCurrencyChange callback', () => {
    const props = {
      ...defaultProps,
      onCurrencyChange: undefined,
    };
    
    render(<CurrencyDropdown {...props} />);
    
    // Get all menu items
    const menuItems = screen.getAllByTestId('menu-item');
    
    // Click on a currency - this should not throw an error
    const euroButton = menuItems[1].querySelector('button');
    expect(() => fireEvent.click(euroButton)).not.toThrow();
  });
}); 