import React, { useState } from "react";
import PropTypes from "prop-types";
import { pencilIcon } from "@progress/kendo-svg-icons";
import Chip from "../chips/Chip";
import { FiEdit2 } from "react-icons/fi";
import EntityLogo from "../../partials/molecules/entity-logo/entity-logo";

const CustomItem = (props) => {
  const [isEditHovered, setIsEditHovered] = useState(false);
  const [isActive, setIsActive] = useState(false);

  const handleMouseDown = () => {
    setIsActive(true);
  };
  const handleMouseUp = () => {
    setIsActive(false);
  };
  const handleMouseLeave = () => {
    if (isActive) {
      setIsActive(false);
    }
  };

  const onEdit = () => {
    props.onEdit();
  };

  return (
    <div
      className={`flex flex-row items-start p-5 gap-4 w-full h-24 border border-(--neutral-gray-10) rounded-lg ${
        isActive ? "bg-primary-43" : "bg-white hover:bg-primary-35"
      }`}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseLeave}
    >
      <div className="flex flex-col justify-center items-start flex-grow gap-5">
        <div className="flex flex-col justify-center items-start gap-2 w-full">
          <div className="flex flex-row items-center gap-2 w-full">
            <div className="relative w-6 h-6 flex-shrink-0">
              <div>
                <EntityLogo
                  acuityID={props.id}
                  companyName={props.companyName}
                  logoSize="medium"
                />
              </div>{" "}
            </div>
            <div className="flex flex-row items-center gap-2 flex-grow">
              <span className="m-r text-neutral-80">{props.ticker}</span>
              {props.type === "Public" ? (
                <span className="text-(--neutral-gray-10)">|</span>
              ) : (
                <></>
              )}
              <span className="m-r text-neutral-80 flex-grow">
                {props.companyName}
              </span>
              <div
                className="p-1 w-6 h-6 flex justify-center items-center cursor-pointer"
                onClick={props.onEdit}
                onMouseEnter={() => setIsEditHovered(true)}
                onMouseLeave={() => setIsEditHovered(false)}
              >
                <FiEdit2
                  onClick={onEdit}
                  icon={pencilIcon}
                  className="w-4 h-4 text-[#666666]"
                />
              </div>
            </div>
          </div>
          <div className="flex flex-row items-start gap-1 pl-8">
            {props.Region && <Chip className="xs-r">{props.Region}</Chip>}
            {props.sector && <Chip className="xs-r">{props.sector}</Chip>}
            {props.type && <Chip className="xs-r">{props.type}</Chip>}
            {props.isExchange && (
              <Chip className="xs-r">{props.isExchange}</Chip>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

CustomItem.propTypes = {
  id: PropTypes.string.isRequired,
  companyName: PropTypes.string.isRequired,
  ticker: PropTypes.string.isRequired,
  Region: PropTypes.string,
  sector: PropTypes.string,
  type: PropTypes.string,
  isExchange: PropTypes.bool,
  onEdit: PropTypes.func.isRequired
};
export default CustomItem;
