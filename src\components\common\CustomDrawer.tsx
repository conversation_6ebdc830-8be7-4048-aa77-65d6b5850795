import React, { useState } from "react";
import "./CustomDrawer.css"; // Re-import the original CSS file

interface CustomDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  width?: string;
  height?: string;
  position?: "left" | "right" | "top" | "bottom";
  children: React.ReactNode;
}

const CustomDrawer: React.FC<CustomDrawerProps> = ({
  isOpen,
  onClose,
  width = "400px",
  height = "100%",
  position = "right",
  children,
}) => {
  const getDrawerStyle = () => {
    switch (position) {
      case "left":
        return { width, height, left: isOpen ? "0" : `-${width}` };
      case "right":
        return { width, height, right: isOpen ? "0" : `-${width}` };
      case "top":
        return { width: "100%", height, top: isOpen ? "0" : `-${height}` };
      case "bottom":
        return { width: "100%", height, bottom: isOpen ? "0" : `-${height}` };
      default:
        return {};
    }
  };

  return (
    <div>
      {/* Overlay */}
      {isOpen && (
        <div className="custom-drawer-overlay" onClick={onClose}></div>
      )}

      {/* Drawer */}
      <div
        className={`custom-drawer custom-drawer-${position}`}
        style={getDrawerStyle()}
      >
        <div className="custom-drawer-content">{children}</div>
      </div>
    </div>
  );
};

export default CustomDrawer;
