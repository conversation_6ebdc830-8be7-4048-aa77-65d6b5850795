import { React, Fragment } from "react";
import PropTypes from "prop-types";
import {
  Dialog,
  Transition,
  TransitionChild,
  DialogPanel
} from "@headlessui/react";
import { FaTimes } from "react-icons/fa";
import { Button, ButtonIconText } from "../../atoms/button";

const PopUp = ({
  header,
  children,
  showPopUp,
  setShowPopUp,
  cancelButtonText,
  submitButtonText,
  onSubmit,
  disabled,
  cols,
  colGrid,
  showBackbutton,
  showFooter = true,
  onClickBack,
  trigger = null,
  disableOutsideClick = false
}) => {
  return (
    <div>
      <Transition appear show={showPopUp} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={disableOutsideClick ? () => {} : setShowPopUp}>
          <TransitionChild
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-neutral-80 bg-opacity-50" />
          </TransitionChild>

          <div className="fixed inset-0 overflow-y-auto">
            <div
              className={`grid min-h-full ${colGrid} items-center justify-center text-center`}
            >
              <TransitionChild
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <DialogPanel
                  ref={trigger}
                  className={`${cols} transform overflow-hidden rounded-lg bg-white text-left align-middle shadow-xl transition-all`}
                >
                  <div className="heading-2-m flex min-h-14 items-center justify-between gap-2 bg-primary-40 px-6 text-left leading-6 text-neutral-90">
                    <div title={header}>{header}</div>
                    <div className="rounded text-neutral-60 hover:bg-neutral-20 hover:bg-opacity-30 focus:bg-neutral-30 active:bg-neutral-30 active:bg-opacity-60">
                      <span className="flex cursor-pointer place-items-end items-center justify-end p-1 text-neutral-60 hover:opacity-100">
                        <FaTimes
                          data-testid="popup-cross"
                          onClick={() => {
                            setShowPopUp(false);
                          }}
                        />
                      </span>
                    </div>
                  </div>

                  {children}
                  {showFooter && (
                    <div className="flex h-16 justify-between border-t border-neutral-10 px-6 py-4">
                      <div className="justify-start">
                        {showBackbutton && (
                          <ButtonIconText
                            data-testid="pop-up-back-button"
                            onClick={onClickBack}
                            intent={"teritory"}
                          >
                            Back
                          </ButtonIconText>
                        )}
                      </div>
                      <div className="flex justify-end gap-2">
                        <div className="h-8">
                          <Button
                            data-testid="pop-up-back-close"
                            onClick={() => {
                              setShowPopUp(false);
                            }}
                            intent={"secondary"}
                          >
                            {cancelButtonText}
                          </Button>
                        </div>
                        <div>
                          <Button
                            data-testid="popup-submit-button"
                            onClick={onSubmit}
                            disabled={disabled}
                            intent={"primary"}
                          >
                            {submitButtonText}
                          </Button>
                        </div>
                      </div>
                    </div>
                  )}
                </DialogPanel>
              </TransitionChild>
            </div>
          </div>
        </Dialog>
      </Transition>
    </div>
  );
};

export default PopUp;

PopUp.propTypes = {
  header: PropTypes.string.isRequired,
  children: PropTypes.node.isRequired,
  showPopUp: PropTypes.bool.isRequired,
  setShowPopUp: PropTypes.func,
  cancelButtonText: PropTypes.string,
  submitButtonText: PropTypes.string,
  onSubmit: PropTypes.func,
  disabled: PropTypes.bool,
  cols: PropTypes.string,
  colGrid: PropTypes.string,
  showBackbutton: PropTypes.bool,
  showFooter: PropTypes.bool,
  onClickBack: PropTypes.func,
  trigger: PropTypes.ref
};
