import React from "react";
import { Transition } from "@headlessui/react";
import ButtonIconText from "../../atoms/button/button-icon-text";
import Button from "../../../partials/atoms/button/button";
import useDocumentViewerStore from "./document-viewer.store";
import { useCopyToClipboard } from "@uidotdev/usehooks";
import { FiCopy, FiXCircle } from "react-icons/fi";
import { POST_BUCKET_API } from "../../../infra/api/bucket-service";
import { DELETE_HIGHLIGHT_API } from "../../../infra/api/highlight-service";
import { notify } from "../../molecules/toaster";
import { useQueryClient } from "@tanstack/react-query";
import { generateShortId } from "../../../general/utils";
import { viewerUrl } from "../../../constants/config";

const DocumentViewerPopOverDeleteHighLight = () => {
  const queryClient = useQueryClient();
  const [_copiedText, copyToClipboard] = useCopyToClipboard();

  const {
    previewFilingsId,
    showHighLightDeletePopOver,
    getPreviewMetaDataBody,
    validatePreviewMetaDataBody
  } = useDocumentViewerStore((state) => state);

  const deleteHighLight = DELETE_HIGHLIGHT_API(queryClient);
  const copyHighLightS3 = POST_BUCKET_API("highlights");
  const handleDeleteHighlight = async (id) => {
    const { success } = await deleteHighLight.mutateAsync(id);
    if (success) {
      notify.info("Highlight deleted successfully");
    } else {
      notify.error("Something went wrong, please try again.");
    }
  };

  const getPublicLink = (id) => {
    const url = `${viewerUrl}/${id}`;
    copyToClipboard(url);
  };

  const handleCopyHighLightS3 = async () => {
    const body = getPreviewMetaDataBody();
    if (!validatePreviewMetaDataBody(body)) {
      notify.warning("Highlight did not get saved, please try again.");
      return;
    }
    const uid = generateShortId();

    const ifile = new File([JSON.stringify(body)], `${uid}.txt`, {
      type: "text/plain"
    });

    let file = new FormData();
    file.append("file", ifile);

    const { success } = await copyHighLightS3.mutateAsync(file);
    if (success) {
      notify.success(
        `Highlight’s shareable link copied on your clipboard successfully.`
      );
      getPublicLink(uid);
    } else {
      notify.warning("Highlight link not copied, please try again.");
    }
  };

  return (
    <Transition
      show={showHighLightDeletePopOver}
      className={`body-r absolute bottom-14 left-0 right-0 z-50 mx-auto h-14 w-fit overflow-hidden rounded-lg bg-white drop-shadow-3xl`}
      enter="transition ease-out duration-300 transform"
      enterStart="opacity-0 -translate-y-2"
      enterEnd="opacity-100 translate-y-0"
      leave="transition ease-out duration-300"
      leaveStart="opacity-100"
      leaveEnd="opacity-0"
    >
      <ul data-testid="document-viewer-pop-over-highlight">
        <li className="flex w-full items-center justify-between gap-2 rounded-lg border border-neutral-10 px-5 py-3 text-neutral-80">
          <div>
            <ButtonIconText
              data-testid={
                "document-viewer-pop-over-highlight-copy-highlight-button"
              }
              intent={"teritory"}
              onClick={handleCopyHighLightS3}
            >
              <FiCopy className="size-4" />
              <span>Copy highlight link</span>
            </ButtonIconText>
          </div>
          <div>
            <Button
              data-testid={
                "document-viewer-pop-over-highlight-save-highlight-button"
              }
              intent={"teritorynegative"}
              onClick={() => handleDeleteHighlight(previewFilingsId)}
              className="body-r flex h-8 items-center gap-2 rounded transition duration-300 disabled:cursor-not-allowed"
            >
              <FiXCircle className="size-4 text-negative-100" />
              <span className="text-negative-100">Remove highlight</span>
            </Button>
          </div>
        </li>
      </ul>
    </Transition>
  );
};

export default DocumentViewerPopOverDeleteHighLight;
