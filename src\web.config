<?xml version="1.0"?>
<configuration>
	<location
		path="."
		inheritInChildApplications="false">
		<system.web>
			<httpRuntime enableVersionHeader="false" />
		</system.web>
		<system.webServer>
			<httpProtocol>
				<customHeaders>
					<clear />
					<remove name="X-Content-Type-Options" />
					<remove name="X-Powered-By" />
					<remove name="Cache-Control" />
					<!-- <remove name="X-Frame-Options" /> -->
					<remove name="X-Xss-Protection" />
					<remove name="Strict-Transport-Security" />
					<!-- <remove name="Referrer-Policy" /> -->
					<remove name="Server" />
					<!-- <remove name="Pragma" /> -->
					<!-- <remove name="Expires" /> -->
					<!-- <remove name="x-xss-protection" /> -->
					<!-- <remove name="X-Permitted-Cross-Domain-Policies" /> -->
					<add
						name="X-Content-Type-Options"
						value="nosniff" />
					<add
						name="Referrer-Policy"
						value="strict-origin-when-cross-origin" />
					<add
						name="Cache-Control"
						value="no-cache, no-store, must-revalidate, pre-check=0, post-check=0, max-age=0, s-maxage=0" />
					<add
						name="Pragma"
						value="no-cache" />
					<add
						name="Expires"
						value="0" />
					<add
						name="Server"
						value="Webserver" />
					<add
						name="X-Frame-Options"
						value="sameorigin" />
					<add
						name="Strict-Transport-Security"
						value="max-age=31536000; includeSubDomains; preload" />

					<add
						name="X-Permitted-Cross-Domain-Policies"
						value="none" />
					<add
						name="x-xss-protection"
						value="1; mode=block" />
				</customHeaders>
			</httpProtocol>
			<directoryBrowse enabled="false" />
			<security>
				<requestFiltering removeServerHeader="true">
					<verbs allowUnlisted="false">
						<clear />
						<add
							verb="GET"
							allowed="true" />
						<add
							verb="POST"
							allowed="true" />
						<add
							verb="PUT"
							allowed="true" />
						<add
							verb="DELETE"
							allowed="true" />
						<add
							verb="PATCH"
							allowed="false" />
						<add
							verb="OPTIONS"
							allowed="false" />
						<add
							verb="HEAD"
							allowed="false" />
						<add
							verb="TRACE"
							allowed="false" />
					</verbs>

					<!-- This will handle requests up to 350MB -->
					<requestLimits maxAllowedContentLength="400000000" />
				</requestFiltering>
			</security>
			<staticContent>
				<mimeMap fileExtension=".webmanifest" mimeType="application/manifest+json" />
				<mimeMap fileExtension=".mjs" mimeType="application/javascript" />
        	</staticContent>
			<rewrite>
				<rules>
					<rule
						name="Host Header Validation"
						stopProcessing="true">
						<match url=".*" />
						<conditions
							logicalGrouping="MatchAll"
							trackAllCaptures="false">
							<add
								input="{HTTP_HOST}"
								pattern="([A-Za-z0-9])\.beatapps\.net$"
								negate="true" />
							<add
								input="{HTTP_HOST}"
								pattern="([A-Za-z0-9])\.acuitykp-AcuityOneSS\.online$"
								negate="true" />
							<add
								input="{HTTP_HOST}"
								pattern="localhost"
								negate="true" />
						</conditions>
						<action type="AbortRequest" />
					</rule>
					<rule
						name="Return 405 for Disallowed Verbs"
						stopProcessing="true">
						<match url=".*" />
						<conditions logicalGrouping="MatchAny">
							<add
								input="{REQUEST_METHOD}"
								pattern="^PATCH$" />
							<add
								input="{REQUEST_METHOD}"
								pattern="^OPTIONS$" />
							<add
								input="{REQUEST_METHOD}"
								pattern="^HEAD$" />
							<add
								input="{REQUEST_METHOD}"
								pattern="^TRACE$" />
						</conditions>
						<action
							type="Rewrite"
							url="/method-not-allowed" />
					</rule>
					<rule
						name="React Routes"
						stopProcessing="true">
						<match url=".*" />
						<conditions logicalGrouping="MatchAll">
							<add
								input="{REQUEST_FILENAME}"
								matchType="IsFile"
								negate="true" />
							<add
								input="{REQUEST_FILENAME}"
								matchType="IsDirectory"
								negate="true" />
							<add
								input="{REQUEST_URI}"
								pattern="^/(api)"
								negate="true" />
						</conditions>
						<action
							type="Rewrite"
							url="./" />
					</rule>
					<!-- <rule name="sigin redirect">
						<match url="/signin.code=[A-Za-z0-9]*&amp;scope=*" />
						<action type="Rewrite" url="/index.html" />
					</rule> -->
				</rules>
			</rewrite>
		</system.webServer>
	</location>
</configuration>