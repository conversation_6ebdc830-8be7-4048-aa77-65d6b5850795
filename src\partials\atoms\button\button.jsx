import React from "react";
import PropTypes from "prop-types";
import { cva } from "class-variance-authority";

const button = cva(
  "disabled:cursor-not-allowed transition duration-300 body-r rounded h-8",
  {
    variants: {
      intent: {
        primary: [
          "py-1.5",
          "bg-primary-78",
          "border-none",
          "text-white",
          "hover:bg-primary-90",
          "focus:border-primary-78",
          "active:bg-primary-100",
          "disabled:bg-primary-60"
        ],
        primarynegative: [
          "py-1.5",
          "bg-negative-100",
          "border-none",
          "text-white",
          "hover:bg-negative-110",
          "focus:border-negative-120",
          "active:bg-negative-120",
          "disabled:bg-negative-60"
        ],
        secondary: [
          "py-[0.3335rem]",
          "bg-white",
          "border",
          "text-primary-78",
          "border-primary-78",
          "hover:bg-primary-40",
          "focus:border-primary-78",
          "active:border-primary-90",
          "active:bg-primary-50",
          "active:text-primary-90",
          "disabled:border-primary-60",
          "disabled:text-primary-60",
          "disabled:bg-white"
        ],
        teritory: [
          "py-1.5",
          "bg-white",
          "text-primary-78",
          "hover:bg-primary-40",
          "focus:border-primary-78",
          "active:bg-primary-50",
          "active:text-primary-90",
          "disabled:bg-primary-60",
          "disabled:text-primary-60",
          "disabled:bg-white"
        ],
        teritorynegative: [
          "py-1.5",
          "bg-white",
          "text-negative-100",
          "hover:bg-negative-50",
          "focus:border-negative-60",
          "focus:bg-negative-60",
          "active:bg-negative-70",
          "active:text-negative-100",
          "disabled:bg-primary-60",
          "disabled:text-primary-60",
          "disabled:bg-white"
        ]
      },
      size: {
        medium: ["px-4"]
      }
    },
    compoundVariants: [],
    defaultVariants: {
      intent: "primary",
      size: "medium"
    }
  }
);

const Button = ({ className, intent, size, ...props }) => {
  return <button className={button({ intent, size, className })} {...props} />;
};

Button.propTypes = {
  className: PropTypes.string,
  intent: PropTypes.string,
  size: PropTypes.string,
  props: PropTypes.any
};

export default Button;
