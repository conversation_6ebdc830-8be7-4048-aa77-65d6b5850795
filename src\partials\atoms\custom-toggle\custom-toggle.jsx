import React from "react";
import PropTypes from "prop-types";
import { FaRegCalendar } from "react-icons/fa";

const CustomToggleButton = (props) => {
  return (
    <button
      style={{
        width: "2rem",
        height: "2rem",
        border: "none"
      }}
      className="size-2"
      {...props}
    >
      {props.children}
      <FaRegCalendar className="m-auto" />
    </button>
  );
};

export default CustomToggleButton;

CustomToggleButton.propTypes = {
  children: PropTypes.any
};
