/**
 * Utility functions for handling company selection logic
 */

/**
 * Determines the currency and unit values to display in the snackbar based on selected companies
 * @param {Array} selectedCompanyIds - Array of selected company IDs
 * @param {Array} companiesData - Array of company data objects
 * @returns {Object} - Object containing currency and unit values to display
 */
export const getSnackbarValues = (selectedCompanyIds, companiesData) => {
  // If no companies are selected, return default values
  if (!selectedCompanyIds.length || !companiesData.length) {
    return {
      currency: "Select Currency",
      unit: "Select Unit"
    };
  }

  // Get data for selected companies
  const selectedCompanies = companiesData.filter(company => 
    selectedCompanyIds.includes(company.companyId)
  );

  // If only one company is selected, use its values
  if (selectedCompanies.length === 1) {
    const company = selectedCompanies[0];
    return {
      currency: company.currencyCode || company.currency || "Select Currency",
      unit: company.unit || "Select Unit"
    };
  }

  // For multiple companies, check if they all have the same values
  const firstCompany = selectedCompanies[0];
  const firstCurrency = firstCompany.currencyCode || firstCompany.currency;
  const firstUnit = firstCompany.unit;
  
  // Check if all selected companies have the same currency
  const allSameCurrency = selectedCompanies.every(
    company => (company.currencyCode || company.currency) === firstCurrency
  );
  
  // Check if all selected companies have the same unit
  const allSameUnit = selectedCompanies.every(
    company => company.unit === firstUnit
  );
  
  return {
    currency: allSameCurrency && firstCurrency ? firstCurrency : "Select Currency",
    unit: allSameUnit && firstUnit ? firstUnit : "Select Unit"
  };
};

/**
 * Updates company data with new currency and unit values for selected companies
 * @param {Array} companiesData - Array of company data objects
 * @param {Array} selectedCompanyIds - Array of selected company IDs
 * @param {string} currency - Currency value to apply
 * @param {string} unit - Unit value to apply
 * @returns {Array} - Updated company data array
 */
export const updateSelectedCompanies = (companiesData, selectedCompanyIds, currency, unit) => {
  // Create a deep copy to avoid direct state mutation
  const updatedData = JSON.parse(JSON.stringify(companiesData));
  
  // Update the selected companies with new currency and unit
  updatedData.forEach(company => {
    if (selectedCompanyIds.includes(company.companyId)) {
      // Update both currency and currencyCode properties if they exist
      if ('currency' in company) {
        company.currency = currency;
      }
      if ('currencyCode' in company) {
        company.currencyCode = currency;
      }
      company.unit = unit;
    }
  });
  
  return updatedData;
};

/**
 * Gets the active tab data and setter function
 * @param {string} activeTabId - ID of the active tab
 * @param {Object} tabData - Object containing data arrays for all tabs
 * @param {Object} [setters] - Optional object containing setter functions for all tabs
 * @returns {Object} - Object containing the active data array and setter function
 */
export const getActiveTabData = (activeTabId, tabData, setters = {}) => {
  const { 
    setConsolidatedData, 
    setFinancialData, 
    setMasterData, 
    setInvestmentData 
  } = setters;

  switch (activeTabId) {
    case "consolidated":
      return {
        data: tabData.consolidatedData,
        setData: setConsolidatedData
      };
    case "financial":
      return {
        data: tabData.financialData,
        setData: setFinancialData
      };
    case "masterData":
      return {
        data: tabData.masterData,
        setData: setMasterData
      };
    case "investmentKpi":
      return {
        data: tabData.investmentData,
        setData: setInvestmentData
      };
    default:
      return {
        data: tabData.consolidatedData,
        setData: setConsolidatedData
      };
  }
};

/**
 * Applies currency and unit changes to selected companies and shows a notification
 * @param {string} activeTabId - ID of the active tab
 * @param {Object} tabData - Object containing data arrays for all tabs
 * @param {Object} setters - Object containing setter functions for all tabs
 * @param {Array} selectedCompanyIds - Array of selected company IDs
 * @param {string} currency - Currency value to apply
 * @param {string} unit - Unit value to apply
 * @param {Function} setHasChanges - Function to set the hasChanges state
 * @param {Function} notifySuccess - Function to show a success notification
 */
export const applyCurrencyAndUnitChanges = (
  activeTabId,
  tabData,
  setters,
  selectedCompanyIds,
  currency,
  unit,
  setHasChanges,
  notifySuccess
) => {
  // Get current data and setter based on active tab
  const { data: currentData, setData: setCurrentData } = getActiveTabData(
    activeTabId,
    tabData,
    setters
  );

  // Update the selected companies with new currency and unit
  const updatedData = updateSelectedCompanies(
    currentData,
    selectedCompanyIds,
    currency,
    unit
  );

  // Update the state
  setCurrentData(updatedData);
  setHasChanges(true);

  // Format the message with proper checks
  const currencyText = currency && currency !== "Select Currency" ? currency : "";
  const unitText = unit && unit !== "Select Unit" ? unit : "";
  const message = `Applied ${currencyText} in ${unitText} to selected companies`;

  // Show success notification
  notifySuccess(message);
}; 