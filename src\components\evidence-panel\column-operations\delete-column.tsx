/**
 * Deletes one or more columns from the financial data
 */
export const deleteColumns = (
  columnKeysToDelete: string[],
  financialsData: any,
  selectedTab: number,
  columnIdMapping: Record<string, string>,
  currentTableData: any[],
  stateFunctions: {
    setCurrentTableData: (data: any[]) => void;
    setTableDataByTab: (callback: (prev: any) => any) => void;
    setisCheckboxHeader: (value: boolean) => void;
    setEditingColumnIds: (value: string[]) => void;
    setCheckedColumnHeaders: (value: Record<string, boolean>) => void;
    setActiveCheckboxType: (value: "none" | "row" | "column") => void;
  }
) => {
  if (columnKeysToDelete.length === 0) return;

  const {
    setCurrentTableData,
    setTableDataByTab,
    setisCheckboxHeader,
    setEditingColumnIds,
    setCheckedColumnHeaders,
    setActiveCheckboxType
  } = stateFunctions;

  // For each financial table group
  if (financialsData?.tableGroups?.[selectedTab]?.tables?.[0]) {
    // Handle regular tables
    const updatedColumns = financialsData.tableGroups[
      selectedTab
    ].tables[0].columns.filter(
      (column) =>
        !columnKeysToDelete.includes(columnIdMapping[column.columnKey])
    );

    financialsData.tableGroups[selectedTab].tables[0].columns = updatedColumns;

    // Update table data to remove deleted columns
    const updatedTableData = currentTableData.map((row) => {
      const newRow = { ...row };

      // Remove the data for deleted columns
      columnKeysToDelete.forEach((colKey) => {
        // Find the original column key from the mapping
        const originalColumnKey = Object.keys(columnIdMapping).find(
          (key) => columnIdMapping[key] === colKey
        );

        if (originalColumnKey && newRow[originalColumnKey] !== undefined) {
          delete newRow[originalColumnKey];
        }

        // Also remove any cell IDs related to this column
        if (newRow.cellIds && newRow.cellIds[originalColumnKey]) {
          delete newRow.cellIds[originalColumnKey];
        }
      });

      return newRow;
    });

    setCurrentTableData(updatedTableData);
    setTableDataByTab((prev) => ({
      ...prev,
      [selectedTab]: updatedTableData
    }));
  }

  // Reset UI state after deletion
  setisCheckboxHeader(false);
  setEditingColumnIds([]);
  setCheckedColumnHeaders({});
  setActiveCheckboxType("none");
};
