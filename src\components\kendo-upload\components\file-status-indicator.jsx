import React from 'react';
import { BsExclamationCircleFill } from "react-icons/bs";
import { FaCheckCircle } from "react-icons/fa";
import { FiX } from "react-icons/fi";

export const FileStatusIndicator = ({ file }) => {
  if (file.errorMessage || file.deleteFailed) {
    return (
      <div className="body-m flex w-fit items-center gap-2 px-3 py-1 text-neutral-80">
        <BsExclamationCircleFill
          data-testid="error-icon"
          className={`mr-1 text-red-500`}
        />
        <label className={"text-red-500 caption-r"}>
          {file.errorMessage || "Error deleting file"}
        </label>
      </div>
    );
  } else if (file.progress !== 100 || !file.uploadComplete) {
    return (
      <div className="flex items-center mt-1">
        <div className="relative flex-grow h-2 bg-blue-100 rounded mr-2">
          <div
            className="absolute h-2 bg-primary-78 rounded"
            style={{ width: `${file.progress || 0}%` }}
          />
        </div>
        <span className="text-xs text-[#666666] font-normal w-6">{`${file.progress || 0}%`}</span>
        <button className="pr-2 pl-5" style={{ fontSize: "1.5rem" }}>
          <FiX className="w-full h-full text-[#666666]" />
        </button>
      </div>
    );
  } else {
    return (
      <div className="body-m flex w-fit items-center gap-2 px-3 py-1 text-neutral-80">
        <FaCheckCircle
          data-testid="fa-check-circle"
          color="#1B873A"
        />
        <label className="text-neutral-60 caption-r">
          {"Uploaded"}
        </label>
      </div>
    );
  }
};