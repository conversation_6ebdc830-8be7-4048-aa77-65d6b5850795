import { mergeRowBelow } from '../row-operations-merge-down';
import { notify } from '../../../partials/molecules/toaster';
import { NOTIFICATION_MESSAGES } from '../../.././constants';

// Mock the notify module
jest.mock("../../../partials/molecules/toaster", () => ({
  notify: {
    warning: jest.fn(),
    error: jest.fn(),
    success: jest.fn()
  }
}));

describe('mergeRowBelow', () => {
  // Sample data for tests
  const mockTableData = [
    { id: 'row1', label: 'Label 1', status: 'active', value: '' },
    { id: 'row2', label: 'Label 2', status: 'active', value: '' },
    { id: 'row3', label: 'Label 3', status: 'active', value: '' }
  ];
  
  let setCurrentTableData;
  let setTableDataByTab;
  let setCheckedItems;
  let setShowPopup;
  
  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();
    
    // Create mock functions
    setCurrentTableData = jest.fn();
    setTableDataByTab = jest.fn();
    setCheckedItems = jest.fn();
    setShowPopup = jest.fn();
  });
  
  test('should display warning when no rows are selected', () => {
    mergeRowBelow(
      [], // no rows selected
      {},
      mockTableData,
      setCurrentTableData,
      setTableDataByTab,
      'tab1',
      setCheckedItems,
      setShowPopup
    );
    
    expect(notify.warning).toHaveBeenCalledWith(expect.any(String));
    expect(setCurrentTableData).not.toHaveBeenCalled();
  });
  
  test('should display warning when multiple rows are selected', () => {
    mergeRowBelow(
      ['row1', 'row2'], // multiple rows selected
      {},
      mockTableData,
      setCurrentTableData,
      setTableDataByTab,
      'tab1',
      setCheckedItems,
      setShowPopup
    );
    
    expect(notify.warning).toHaveBeenCalledWith(expect.any(String));
    expect(setCurrentTableData).not.toHaveBeenCalled();
  });
  
  test('should display error when trying to merge the last row', () => {
    mergeRowBelow(
      ['row3'], // last row selected
      {},
      mockTableData,
      setCurrentTableData,
      setTableDataByTab,
      'tab1',
      setCheckedItems,
      setShowPopup
    );
    
    expect(notify.error).toHaveBeenCalledWith(expect.any(String));
    expect(setCurrentTableData).not.toHaveBeenCalled();
  });
  
  test('should display error when selected row has non-empty values', () => {
    const dataWithValues = [
      { id: 'row1', label: 'Label 1', status: 'active', value: 'Some value' }, // row with value
      { id: 'row2', label: 'Label 2', status: 'active', value: '' }
    ];
    
    mergeRowBelow(
      ['row1'],
      {},
      dataWithValues,
      setCurrentTableData,
      setTableDataByTab,
      'tab1',
      setCheckedItems,
      setShowPopup
    );
    
    expect(notify.error).toHaveBeenCalledWith(NOTIFICATION_MESSAGES.ROW_HAS_VALUES);
    expect(setCurrentTableData).not.toHaveBeenCalled();
  });
  
  test('should successfully merge rows when conditions are met', () => {
    mergeRowBelow(
      ['row1'], // selecting first row to merge with second
      {},
      mockTableData,
      setCurrentTableData,
      setTableDataByTab,
      'tab1',
      setCheckedItems,
      setShowPopup
    );
    
    // Expected merged data
    const expectedData = [
      { 
        id: 'row2', 
        label: 'Label 2 Label 1', 
        status: 'active', 
        value: '' 
      },
      { 
        id: 'row3', 
        label: 'Label 3', 
        status: 'active', 
        value: '' 
      }
    ];
    
    expect(setCurrentTableData).toHaveBeenCalledWith(expect.arrayContaining([
      expect.objectContaining({ label: 'Label 2 Label 1' })
    ]));
    expect(setTableDataByTab).toHaveBeenCalled();
    expect(setCheckedItems).toHaveBeenCalledWith({});
    expect(setShowPopup).toHaveBeenCalledWith(false);
    expect(notify.success).toHaveBeenCalledWith(NOTIFICATION_MESSAGES.MERGE_SUCCESS);
    expect(setCurrentTableData.mock.calls[0][0].length).toBe(mockTableData.length - 1);
  });

  test('should handle empty labels correctly', () => {
    const dataWithEmptyLabels = [
      { id: 'row1', label: '', status: 'active', value: '' },
      { id: 'row2', label: 'Label 2', status: 'active', value: '' }
    ];
    
    mergeRowBelow(
      ['row1'],
      {},
      dataWithEmptyLabels,
      setCurrentTableData,
      setTableDataByTab,
      'tab1',
      setCheckedItems,
      setShowPopup
    );
    
    // Check if the merged row has the correct label
    expect(setCurrentTableData).toHaveBeenCalledWith(expect.arrayContaining([
      expect.objectContaining({ label: 'Label 2' })
    ]));
  });
});