import React from "react";
import PropTypes from "prop-types";
import { cva } from "class-variance-authority";

const buttonIconText = cva(
  "body-r disabled:cursor-not-allowed rounded items-center flex gap-2 transition duration-300 h-8",
  {
    variants: {
      intent: {
        primary: [
          "bg-primary-78",
          "text-white",
          "border-primary-78",
          "hover:bg-primary-90",
          "focus:border-primary-78",
          "active:bg-primary-100",
          "disabled:bg-primary-60",
          "disabled:border-none"
        ],
        secondary: [
          "border",
          "bg-white",
          "text-primary-78",
          "border-primary-78",
          "hover:bg-primary-40",
          "focus:border-primary-78",
          "active:border-primary-90",
          "active:bg-primary-50",
          "active:text-primary-90",
          "disabled:border-primary-60",
          "disabled:text-primary-60",
          "disabled:bg-white"
        ],
        teritory: [
          "bg-white",
          "text-primary-78",
          "hover:bg-primary-40",
          "focus:border-primary-78",
          "active:bg-primary-50",
          "active:text-primary-90",
          "disabled:bg-primary-60",
          "disabled:text-primary-60",
          "disabled:bg-white"
        ]
      },
      size: {
        medium: ["px-4", "py-2"],
        small: ["px-2", "py-2"]
      }
    },
    compoundVariants: [],
    defaultVariants: {
      intent: "primary",
      size: "medium"
    }
  }
);

const ButtonIconText = ({ className, intent, size, children, ...props }) => {
  return (
    <button className={buttonIconText({ intent, size, className })} {...props}>
      {children}
    </button>
  );
};

ButtonIconText.propTypes = {
  className: PropTypes.string,
  intent: PropTypes.string,
  size: PropTypes.string,
  icon: PropTypes.element,
  props: PropTypes.any,
  children: PropTypes.any
};

export default ButtonIconText;
