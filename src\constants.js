export const DefaultValues = {
  fileName: "",
  EscapeKey: "Escape"
};

export const FilingPeriods = ["NA", "ANL", "NME", "HYL", "QTR"];
export const OtherKPiModuleNames = ["OtherKPI1",
"OtherKPI2",
"OtherKPI3",
"OtherKPI4",
"OtherKPI5",
"OtherKPI6",
"OtherKPI7",
"OtherKPI8",
"OtherKPI9",
"OtherKPI10"];
export const FinancialStatements = ["Income Statement", "Balance Sheet", "Cash Flow"];
export const FinancialStatementsWithName = [{key:"ProfitAndLoss",value:"Income Statement",tabNo:0},{key:"BalanceSheet",value:"Balance Sheet",tabNo:1},{key:"CashFlow",value:"Cash Flow",tabNo:2}];
export const PdfPageHeight = 792;
export const PdfPageWidth = 612;
export const CommaFormatterRegex = /\B(?<!\.\d*)(?=(\d{3})+(?!\d))/g;
export const QueryWithoutCommaRegex = /[.*+?^${}()|[\]\\]/g;
export const TextWithoutCommaRegex = /,/g;
export const BalanceSheet = "Balance Sheet";
export const CashFlow = "Cash Flow";

export const EXCLUDED_MERGE_FIELDS = {
  STATUS: "status",
  LABEL: "label",
  ID: "id",
  DEFINED_NAME: "definedName",
  STYLE: "style",
  PERIOD_DATES: "periodDates",
  COLUMN_IDS: "columnIds"
};

export const EXCLUDED_FIELD_PREFIXES = {
  CELL_IDS: "cellIds",
  PDF_HIGHLIGHTS: "pdfHighlights"
};

export const NOTIFICATION_MESSAGES = {
  CANNOT_MERGE_FIRST_ROW: "Cannot merge the first row upward.",
  ROW_HAS_VALUES: "Cannot merge rows: Selected row contains values",
  MERGE_SUCCESS: "Rows merged successfully."
};
export const MERGE_ROW_MESSAGES = {
  SELECT_ONE_ROW: "Please select exactly one row to merge.",
  CANNOT_MERGE_LAST_ROW: "Cannot merge: This is the last row in the table.",
  MERGE_SUCCESS: "Row merged successfully."
};

// Success messages
export const RowsSuccessMessages = {
  SINGLE_ROW_DELETED: "Row deleted successfully.",
  MULTIPLE_ROWS_DELETED: "Rows deleted successfully.",
  ROW_ADDED: "Row added successfully."
};
export const ColumnSuccessMessages = {
  SINGLE_COLUMN_DELETED: "Column deleted successfully.",
  MULTIPLE_COLUMNS_DELETED: "Columns deleted successfully.",
  COLUMN_ADDED: "Column added successfully.",
  COLUMN_UPDATED: "Column updated successfully."
};
export const EXPORT_EXCEL_MESSAGES = {
  EXPORT_SUCCESS: "Extraction downloaded successfully.",
}

export const MARK_AS_HEADER_MESSAGES = {
  EXPORT_SUCCESS: "Row converted into header successfully.",
  EXPORT_ERROR: "Rows with value cells cannot be converted into headers."
}