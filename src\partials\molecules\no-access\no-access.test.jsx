import { render, screen, fireEvent } from "@testing-library/react";
import NoAccess from "./no-access";
import { useAuth } from "react-oidc-context";

jest.mock("react-oidc-context", () => ({
  useAuth: jest.fn()
}));

describe("NoAccess component", () => {
  test("renders the component correctly", () => {
    useAuth.mockReturnValue({ signoutRedirect: jest.fn() });

    render(<NoAccess />);

    const image = screen.getByTestId("image-no-access");
    const unauthorizedText = screen.getByText("Unauthorized Access");
    const permissionText = screen.getByText(
      "You do not have permission to access this application."
    );
    const button = screen.getByTestId("logout-button");

    expect(image).toBeInTheDocument();
    expect(unauthorizedText).toBeInTheDocument();
    expect(permissionText).toBeInTheDocument();
    expect(button).toBeInTheDocument();
  });

  test("calls signoutRedirect when the button is clicked", () => {
    const signoutRedirectMock = jest.fn();
    useAuth.mockReturnValue({ signoutRedirect: signoutRedirectMock });

    render(<NoAccess />);

    const button = screen.getByTestId("logout-button");
    fireEvent.click(button);

    expect(signoutRedirectMock).toHaveBeenCalled();
  });
});
