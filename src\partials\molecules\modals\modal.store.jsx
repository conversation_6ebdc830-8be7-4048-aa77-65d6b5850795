import { create } from "zustand";
import { devtools } from "zustand/middleware";

const MODEL_TYPE = {
  CONFIRM: "CONFIRM",
  WARNING: "WARNING",
  ERROR: "ERROR"
};
const modalStore = (set) => ({
  type: MODEL_TYPE.CONFIRM,
  open: false,
  title: "Confirm",
  cancelLabel: "Cancel",
  submitLabel: "Submit",
  children: null,
  handleClose: null,
  handleSubmit: null,
  setModalOpen: (data) =>
    set(() => ({
      type: data.type,
      open: true,
      title: data.title,
      cancelLabel: data.cancelLabel,
      submitLabel: data.submitLabel,
      children: data.children,
      handleClose: data.handleClose,
      handleSubmit: data.handleSubmit
    })),
  toggle: () => set((state) => ({ open: !state.open })),
  MODEL_TYPE: MODEL_TYPE
});

//Configure additional middlewares(Optional)
const useModalStore = create(devtools(modalStore));

export default useModalStore;
