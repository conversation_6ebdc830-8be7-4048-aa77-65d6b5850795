import React from "react";
import { fireEvent, render, screen } from "@testing-library/react";
import Toggle from "./toggle";

describe("Toggle", () => {
  it("should render a Switch component with the correct props", () => {
    const value = true;
    const onChange = jest.fn();
    const { getByRole, getByText } = render(
      <Toggle checked={value} onChange={onChange} />
    );
    const switchElement = getByRole("switch");
    expect(switchElement).toHaveClass("bg-primary-78");
    expect(getByText("Toggle Button")).toBeInTheDocument();
  });

  it("should toggle the value when the Switch is clicked", () => {
    const value = false;
    const onChange = jest.fn();
    const { getByRole } = render(
      <Toggle checked={value} onChange={onChange} />
    );
    const switchElement = getByRole("switch");
    fireEvent.click(switchElement);
    expect(onChange).toHaveBeenCalledWith(true);
    expect(screen.getByRole("switch")).toHaveClass("bg-white");
  });
});
