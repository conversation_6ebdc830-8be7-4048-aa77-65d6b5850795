import React from "react";
import { FaBell } from "react-icons/fa";
import { BsFiles } from "react-icons/bs";
import { Dropdown } from "../../drop-down";
import { ToolTip } from "../../../atoms/tool-tip";

const HeaderHelpIcon = () => {
  return (
    <Dropdown
      align={"center"}
      icon={
        <>
          <div
            id="header-help"
            data-testid="header-help"
            data-tooltip-id="help-toot-tip"
            className="flex size-6 items-center rounded-full border border-neutral-10 p-1 text-primary-78 hover:border-primary-78 hover:bg-primary-40"
          >
            <FaBell className="size-3" />
          </div>
          <ToolTip
            place={"bottom"}
            text={"Notification"}
            toolTipId={"help-toot-tip"}
          />
        </>
      }
      className="relative inline-flex"
      data-testid="header-help-icon"
    >
      <li className="">
        <button
          id="header-help-icon-dropdown"
          data-testid="header-help-icon-dropdown"
          className="s-r flex h-10 items-center justify-start gap-3 px-4 py-2.5 hover:bg-primary-40 active:text-primary-78"
        >
          <BsFiles className="text-bold h-4 w-4 text-primary-78" />
          <span className="s-r flex w-[4rem] items-center text-left text-neutral-80 active:text-primary-78">
            Help
          </span>
        </button>
      </li>
    </Dropdown>
  );
};
export default HeaderHelpIcon;

HeaderHelpIcon.propTypes = {};
