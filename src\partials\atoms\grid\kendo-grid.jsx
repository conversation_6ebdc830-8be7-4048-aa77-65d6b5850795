import React from "react";
import { getter } from "@progress/kendo-react-common";
import {
  Grid,
  GridColumn as Column,
  GridColumnMenuSort,
  GridColumnMenuFilter
} from "@progress/kendo-react-grid";
import EmptySearchImages from "../../../resources/images/emptyy_search";
import PropTypes from "prop-types";

const SELECTED_FIELD = "selected";

const KendoGrid = ({
  dataId,
  data,
  dataState,
  dataResult,
  resultState,
  dataStateChange,
  newData,
  currentSelectedState,
  setCurrentSelectedState,
  processWithGroups,
  setDataResult,
  filterValue,
  columns,
  isLoading,
  isNotes,
  enableColumnLocking = true // Add default true for column locking
}) => {
  const DATA_ITEM_KEY = dataId;
  const idGetter = getter(dataId);

  const onHeaderSelectionChange = React.useCallback(
    (event) => {
      const checkboxElement = event.syntheticEvent.target;
      const checked = checkboxElement.checked;
      const newSelectedState = {};
      data.forEach((item) => {
        newSelectedState[idGetter(item)] = checked;
      });
      setCurrentSelectedState(newSelectedState);
      const newData = data.map((item) => ({
        ...item,
        [SELECTED_FIELD]: checked
      }));
      const newDataResult = processWithGroups(newData, dataState);
      setDataResult(newDataResult);
    },
    [data, dataState]
  );
  const onSelectionChange = (event) => {
    let headerCheckbox = document.querySelector(".k-checkbox");
    const [locked, setLocked] = React.useState < boolean > false;

    // const handleClick = () => {
    //     setLocked(!locked);
    // };
    const selectedProductId = event.dataItem.userId;
    const newSelectedState = {
      ...currentSelectedState,
      [selectedProductId]: !currentSelectedState[selectedProductId]
    };
    setCurrentSelectedState(newSelectedState);
    const newData = data.map((item) => {
      return {
        ...item,
        selected: newSelectedState[idGetter(item)]
      };
    });
    if (
      (newData.filter((x) => x.selected === true).length > 0 &&
        newData.filter((x) => x.selected === true).length < newData.length) ||
      (filterValue && newData.filter((x) => x.selected === true).length > 0)
    ) {
      headerCheckbox.indeterminate = true;
    } else {
      headerCheckbox.indeterminate = false;
    }
    const newDataResult = processWithGroups(newData, dataState);
    setDataResult(newDataResult);
  };
  const getNumberOfItems = (data) => {
    let count = 0;
    data.forEach((item) => {
      if (item.items) {
        count = count + getNumberOfItems(item.items);
      } else {
        count++;
      }
    });
    return count;
  };
  const getNumberOfSelectedItems = (data) => {
    let count = 0;
    data.forEach((item) => {
      if (item.items) {
        count = count + getNumberOfSelectedItems(item.items);
      } else {
        count = count + (item.selected == true ? 1 : 0);
      }
    });
    return count;
  };

  const checkHeaderSelectionValue = () => {
    let selectedItems = getNumberOfSelectedItems(newData);
    return newData.length > 0 && selectedItems == getNumberOfItems(newData);
  };

  const ColumnMenu = (props) => {
    return (
      <div>
        <GridColumnMenuSort {...props} />
        <GridColumnMenuFilter {...props} />
      </div>
    );
  };
  if (isLoading) {
    return (
      <div className="m-auto flex h-full items-center justify-center relative bottom-10 ">
        <div className="m-auto flex flex-col items-center gap-2 relative bottom-10">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary-600 border-t-transparent  "></div>
          <p className="m-xl text-neutral-60  w-[171px] text-center ">
            Loading, please wait..
          </p>
        </div>
      </div>
    );
  }

  if (!dataResult || dataResult.length === 0) {
    return (
      <div className="h-[90%] pb-5 flex flex-col items-center justify-center ">
        <EmptySearchImages />
        <p className="body-r text-neutral-60  ">No data found!</p>
      </div>
    );
  }
  return (
    <div
      data-testid="kendo-grid"
      className={`${isNotes ? "h-100%" : "metta-grid"}`}
    >
      <Grid
        className="bg-white"
        data={dataResult}
        sortable={true}
        onDataStateChange={dataStateChange}
        {...dataState}
        dataItemKey={DATA_ITEM_KEY}
        selectedField={SELECTED_FIELD}
        onHeaderSelectionChange={onHeaderSelectionChange}
        onSelectionChange={onSelectionChange}
        size={"medium"}
        selectable={{
          enabled: true,
          cell: true,
          mode: "single"
        }}
        // Make sure column locking is explicitly enabled
        // scrollable={{
        //   virtual: false,
        //   mode: enableColumnLocking ? "scrollable" : "none"
        // }}
      >
        {columns.map((column) => {
          // Ensure locked property is properly passed
          const isColumnLocked = enableColumnLocking && column.locked === true;

          return (
            <Column
              key={column.field}
              {...column}
              locked={isColumnLocked}
              lockable={enableColumnLocking}
            />
          );
        })}
      </Grid>
    </div>
  );
};

KendoGrid.propTypes = {
  dataId: PropTypes.string.isRequired,
  data: PropTypes.array.isRequired,
  dataState: PropTypes.object.isRequired,
  dataResult: PropTypes.array.isRequired,
  resultState: PropTypes.object.isRequired,
  dataStateChange: PropTypes.func.isRequired,
  newData: PropTypes.array.isRequired,
  currentSelectedState: PropTypes.object.isRequired,
  setCurrentSelectedState: PropTypes.func.isRequired,
  processWithGroups: PropTypes.func.isRequired,
  setDataResult: PropTypes.func.isRequired,
  filterValue: PropTypes.string,
  columns: PropTypes.array.isRequired,
  isNotes: PropTypes.bool,
  isLoading: PropTypes.bool,
  enableColumnLocking: PropTypes.bool // Add this to prop types
};

// Set default prop values for optional props
KendoGrid.defaultProps = {
  data: [],
  dataState: {},
  newData: [],
  currentSelectedState: {},
  processWithGroups: () => {},
  dataStateChange: () => {},
  isNotes: false,
  isLoading: false
};

export default KendoGrid;
