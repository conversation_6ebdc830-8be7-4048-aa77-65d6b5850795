import { formatPeriodTypeWithDataType } from './formatPeriodType';

describe('formatPeriodType Utils', () => {
  describe('formatPeriodTypeWithDataType', () => {
    test('should format monthly period with data type', () => {
      const result = formatPeriodTypeWithDataType('monthly', 0, undefined, 2024, 'actual');
      expect(result).toBe('(actual) Jan 2024');
    });

    test('should format monthly period with different month', () => {
      const result = formatPeriodTypeWithDataType('monthly', 11, undefined, 2024, 'budget');
      expect(result).toBe('(budget) Dec 2024');
    });

    test('should format quarterly period with data type', () => {
      const result = formatPeriodTypeWithDataType('quarterly', undefined, 'Q1', 2024, 'actual');
      expect(result).toBe('(actual) Q1 2024');
    });

    test('should format quarterly period with different quarter', () => {
      const result = formatPeriodTypeWithDataType('quarterly', undefined, 'Q4', 2023, 'forecast');
      expect(result).toBe('(forecast) Q4 2023');
    });

    test('should format yearly period when only year is provided', () => {
      const result = formatPeriodTypeWithDataType(undefined, undefined, undefined, 2024, 'actual');
      expect(result).toBe('(actual) 2024');
    });

    test('should return empty string when no data type is provided', () => {
      const result = formatPeriodTypeWithDataType('monthly', 0, undefined, 2024, undefined);
      expect(result).toBe('');
    });

    test('should return empty string when data type is null', () => {
      const result = formatPeriodTypeWithDataType('monthly', 0, undefined, 2024, null);
      expect(result).toBe('');
    });

    test('should return empty string when data type is empty string', () => {
      const result = formatPeriodTypeWithDataType('monthly', 0, undefined, 2024, '');
      expect(result).toBe('');
    });

    test('should handle all month indices (0-11)', () => {
      const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
      
      for (let i = 0; i < 12; i++) {
        const result = formatPeriodTypeWithDataType('monthly', i, undefined, 2024, 'actual');
        expect(result).toBe(`(actual) ${months[i]} 2024`);
      }
    });

    test('should handle numeric quarter values', () => {
      const result = formatPeriodTypeWithDataType('quarterly', undefined, 2, 2024, 'actual');
      expect(result).toBe('(actual) 2 2024');
    });

    test('should handle string year values', () => {
      const result = formatPeriodTypeWithDataType('yearly', undefined, undefined, '2024', 'budget');
      expect(result).toBe('(budget) 2024');
    });

    test('should handle unknown period type gracefully', () => {
      const result = formatPeriodTypeWithDataType('unknown', undefined, undefined, 2024, 'actual');
      expect(result).toBe('(actual) 2024');
    });

    test('should handle null month value for monthly period', () => {
      const result = formatPeriodTypeWithDataType('monthly', null, undefined, 2024, 'actual');
      expect(result).toBe('(actual) 2024');
    });

    test('should handle null quarter value for quarterly period', () => {
      const result = formatPeriodTypeWithDataType('quarterly', undefined, null, 2024, 'actual');
      expect(result).toBe('(actual) 2024');
    });

    test('should handle null year value', () => {
      const result = formatPeriodTypeWithDataType('monthly', 0, undefined, null, 'actual');
      expect(result).toBe('');
    });

    test('should handle undefined year value', () => {
      const result = formatPeriodTypeWithDataType('monthly', 0, undefined, undefined, 'actual');
      expect(result).toBe('');
    });
  });
});