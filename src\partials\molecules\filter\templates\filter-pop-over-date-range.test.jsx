import React from "react";
import { render, screen, fireEvent, act } from "@testing-library/react";
import FilterDataRange from "./filter-pop-over-date-range";
import useWatchListStore from "../../../../pages/watch-list/watch-list.store";

const mockData = [
  {
    value: "1",
    label: "Category 1",
    data: [
      { value: "1-1", label: "Option 1", selected: false },
      { value: "1-2", label: "Option 2", selected: true }
    ],
    selected: 1
  },
  {
    value: "2",
    label: "Category 2",
    data: [
      { value: "2-1", label: "Option A", selected: false },
      { value: "2-2", label: "Option B", selected: false }
    ],
    selected: 0
  }
];

const mockOnSelect = jest.fn();

describe("FilterDataRange Component", () => {
  let store;

  beforeEach(() => {
    store = useWatchListStore.getState();
  });
  test("renders filter categories", () => {
    render(
      <FilterDataRange
        data={mockData}
        onSelect={mockOnSelect}
        selectedFilterId="1"
        dateRange={null}
      />
    );
    expect(screen.getByText("Last 1 Year")).toBeInTheDocument();
    expect(screen.getByText("Last 5 Years")).toBeInTheDocument();
  });

  test("handles date range selection", () => {
    render(
      <FilterDataRange
        data={mockData}
        onSelect={mockOnSelect}
        selectedFilterId="1"
        dateRange={null}
      />
    );
    fireEvent.click(screen.getAllByTestId("radio-button-input")[0]);
    fireEvent.click(screen.getAllByTestId("radio-button-input")[1]);
  });
});
