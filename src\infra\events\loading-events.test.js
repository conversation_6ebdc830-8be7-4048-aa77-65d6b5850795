import { loadingEvents } from './loading-events';

describe('Loading Events', () => {
  beforeEach(() => {
    // Reset loading state before each test
    loadingEvents.reset();
  });
  
  test('starts with loading state false', () => {
    expect(loadingEvents.isLoading()).toBe(false);
  });
  
  test('sets loading state to true when start is called', () => {
    loadingEvents.start();
    expect(loadingEvents.isLoading()).toBe(true);
  });
  
  test('sets loading state to false when end is called after start', () => {
    loadingEvents.start();
    loadingEvents.end();
    expect(loadingEvents.isLoading()).toBe(false);
  });
  
  test('maintains loading state true when multiple starts are called', () => {
    loadingEvents.start();
    loadingEvents.start();
    expect(loadingEvents.isLoading()).toBe(true);
  });
  
  test('maintains loading state true when not all ends are called', () => {
    loadingEvents.start();
    loadingEvents.start();
    loadingEvents.end();
    expect(loadingEvents.isLoading()).toBe(true);
  });
  
  test('sets loading state to false when all ends are called', () => {
    loadingEvents.start();
    loadingEvents.start();
    loadingEvents.end();
    loadingEvents.end();
    expect(loadingEvents.isLoading()).toBe(false);
  });
  
  test('does not set loading count below zero', () => {
    loadingEvents.end();
    loadingEvents.end();
    expect(loadingEvents.isLoading()).toBe(false);
  });
  
  test('notifies subscribers when loading state changes', () => {
    const mockCallback = jest.fn();
    const unsubscribe = loadingEvents.subscribe(mockCallback);
    
    // Start loading
    loadingEvents.start();
    expect(mockCallback).toHaveBeenCalledWith(true);
    
    // End loading
    loadingEvents.end();
    expect(mockCallback).toHaveBeenCalledWith(false);
    
    unsubscribe();
  });
  
  test('stops notifying unsubscribed callbacks', () => {
    const mockCallback = jest.fn();
    const unsubscribe = loadingEvents.subscribe(mockCallback);
    
    // Unsubscribe before any events
    unsubscribe();
    
    // Start loading
    loadingEvents.start();
    expect(mockCallback).not.toHaveBeenCalled();
  });
  
  test('resets loading state to false', () => {
    loadingEvents.start();
    loadingEvents.start();
    loadingEvents.reset();
    expect(loadingEvents.isLoading()).toBe(false);
  });
  
  test('notifies subscribers when reset is called', () => {
    const mockCallback = jest.fn();
    const unsubscribe = loadingEvents.subscribe(mockCallback);
    
    // Start loading
    loadingEvents.start();
    expect(mockCallback).toHaveBeenCalledWith(true);
    
    // Reset loading
    loadingEvents.reset();
    expect(mockCallback).toHaveBeenCalledWith(false);
    
    unsubscribe();
  });
  
  test('handles multiple subscribers', () => {
    const mockCallback1 = jest.fn();
    const mockCallback2 = jest.fn();
    const unsubscribe1 = loadingEvents.subscribe(mockCallback1);
    const unsubscribe2 = loadingEvents.subscribe(mockCallback2);
    
    // Start loading
    loadingEvents.start();
    expect(mockCallback1).toHaveBeenCalledWith(true);
    expect(mockCallback2).toHaveBeenCalledWith(true);
    
    // Unsubscribe first callback
    unsubscribe1();
    
    // End loading
    loadingEvents.end();
    expect(mockCallback1).not.toHaveBeenCalledWith(false);
    expect(mockCallback2).toHaveBeenCalledWith(false);
    
    unsubscribe2();
  });
}); 