import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import CheckBoxOption from "./check-box-option";

describe("CheckBoxOption", () => {
  const label = "Test Label";
  const testId = "checkbox-option";
  const onChange = jest.fn();

  test("renders with label", () => {
    render(
      <CheckBoxOption
        label={label}
        onChange={onChange}
        testId={testId}
        checked={false}
      />
    );
    expect(screen.getByText(label)).toBeInTheDocument();
  });

  test("calls onChange when clicked", () => {
    render(
      <CheckBoxOption
        label={label}
        onChange={onChange}
        testId={testId}
        checked={false}
      />
    );
    fireEvent.click(screen.getByTestId(testId));
    expect(onChange).toHaveBeenCalled();
  });
});
