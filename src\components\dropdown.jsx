import { <PERSON>u, <PERSON><PERSON><PERSON><PERSON>on, MenuI<PERSON>, MenuItems } from "@headlessui/react";
import { HiChevronDown } from "react-icons/hi2";
import PropTypes from "prop-types";

export default function Dropdown({
  items,
  placeholder,
  disabled = false,
  selectedValue,
  setSelectedValue
}) {
  const handleSelectItem = (item) => {
    setSelectedValue(item);
  };

  return (
    <Menu as="div" className="w-full relative" disabled={disabled}>
      <div>
        <MenuButton
          className={`h-8 body-r items-center w-full px-4 py-1.5 gap-2 border border-neutral-20 rounded-md inline-flex justify-between gap-x-1.5 font-normal ring-gray-300 ring-inset transition-colors ${
            disabled ? "bg-neutral-5 cursor-not-allowed" : ""
          } ${selectedValue === placeholder ? "text-neutral-40" : "text-neutral-80"}`}
          disabled={disabled}
        >
          {selectedValue}
          <HiChevronDown aria-hidden="true" className=" text-gray-400" />
        </MenuButton>

        <MenuItems
          transition
          className="absolute w-full z-10 mt-2 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black/5 transition focus:outline-none data-[closed]:scale-95 data-[closed]:transform data-[closed]:opacity-0 data-[enter]:duration-100 data-[leave]:duration-75 data-[enter]:ease-out data-[leave]:ease-in"
        >
          <div className="py-1 ">
            {items.map((item, index) => (
              <MenuItem key={index}>
                {({ focus }) => (
                  <button
                    role="menuitem"
                    className={`block w-full text-left px-4 py-2 text-body-r font-body-r text-neutral-90 ${
                      focus ? "bg-primary-40 text-gray-900" : ""
                    } hover:bg-primary-40 hover:text-primary-78`}
                    onClick={() => handleSelectItem(item)}
                  >
                    {item}
                  </button>
                )}
              </MenuItem>
            ))}
          </div>
        </MenuItems>
      </div>
    </Menu>
  );
}

Dropdown.propTypes = {
  items: PropTypes.array.isRequired,
  placeholder: PropTypes.string,
  disabled: PropTypes.bool,
  selectedValue: PropTypes.string,
  setSelectedValue: PropTypes.func
};
