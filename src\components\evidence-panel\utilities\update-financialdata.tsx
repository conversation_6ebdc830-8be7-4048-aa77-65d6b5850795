import { FinancialsData } from '../models/FinancialsData';
import { A1TableGroup } from '../models/A1TableGroup';
import { A1TableRow } from '../models/A1TableRow';

export const updateFinancialData = (
  financialsData: FinancialsData,
  statements: Array<{
    index: number;
    data: any[];
    name: string;
  }>,
  notesData?: any[]
): FinancialsData => {
  if (!financialsData) {
    throw new Error('Financial data is not available');
  }

  const updatedData = JSON.parse(JSON.stringify(financialsData));

  if (!updatedData?.tableGroups) {
    throw new Error('Invalid financial data structure');
  }
  const noteTab = updatedData?.tableGroups.find(d => d.label == 'others');
  if(noteTab) {
    updatedData.tableGroups = updatedData.tableGroups.filter(u => u.label !== 'others');
  }

  // Update main statement data
  statements.forEach(({ index, data, name }) => {
    if (!updatedData.tableGroups[index]) {
      console.warn(`${name} table group not found`);
      return;
    }

    const tableGroup: A1TableGroup = updatedData.tableGroups[index];
    if (!tableGroup.tables?.[0]?.rows) {
      console.warn(`${name} rows not found`);
      return;
    }    
    tableGroup.tables[0].rows = data.map((updatedRow: any) => {
      const existingRow = tableGroup.tables[0].rows.find(
        row => row?.label?.id === updatedRow.id
      );

      if (existingRow) {
        // Ensure we have all columns from the table definition
        const allColumns = tableGroup.tables[0].columns || [];
        const updatedCells = allColumns.map(column => {
          const columnKey = column.columnKey;
          // Find existing cell with this column key
          const existingCell = existingRow.cells?.find(c => c.columnKey === columnKey);          // If cell exists, update its value from updatedRow
          if (existingCell) {
            // Ensure value is always at least an empty string, never null or undefined
            const newValue = updatedRow[columnKey] !== undefined ? updatedRow[columnKey] : existingCell.value;
            return {
              ...existingCell,
              value: newValue === null || newValue === undefined ? "" : newValue,
               // Preserve pdfHighlight if it exists in the original cell
              pdfHighlight: updatedRow.pdfHighlights[columnKey] || null
            };
          }
          // If cell doesn't exist, create a new one with all required fields
          else {
            return {
              columnKey,
              value: updatedRow[columnKey] !== undefined && updatedRow[columnKey] !== null ? updatedRow[columnKey] : "",
              type: updatedRow[`${columnKey}_type`] || "text",
              format: updatedRow[`${columnKey}_format`] || "text",
              source: updatedRow[`${columnKey}_source`] || "",
              comments: updatedRow[`${columnKey}_comments`] || [],
              pdfHighlight: updatedRow[`${columnKey}_pdfHighlight`] || { 
                text: "", 
                bounds: [], 
                pageNumber: 1,
                pageHeight: 0,
                pageWidth: 0
              }
            };
          }
        });
       
        // Update existing row
        return {
          ...existingRow,
          cells: updatedCells,
          label: {
            ...existingRow.label,            text: updatedRow.label || existingRow.label.text,
            mapping: updatedRow.status || existingRow.label.mapping,
            mappingId: updatedRow.mappingId !== undefined ? 
              (updatedRow.mappingId === null ? null : 
               typeof updatedRow.mappingId === 'number' ? updatedRow.mappingId : 
               !isNaN(parseInt(updatedRow.mappingId)) ? parseInt(updatedRow.mappingId) : null) : 
              existingRow.label.mappingId,
            mappingScore: updatedRow.mappingScore || existingRow.label.mappingScore,
            style: updatedRow.style || existingRow.label.style,
            isBold: updatedRow.isBold !== undefined ? updatedRow.isBold : existingRow.label.isBold,
            isEmptyRow: updatedRow.isEmptyRow !== undefined ? updatedRow.isEmptyRow : existingRow.label.isEmptyRow
          }
        };
      } else {        // handle newly added rows
        return {
          label: {
            id: updatedRow.id,
            text: updatedRow.label || '',
            mapping: updatedRow.status || '',
            mappingId: updatedRow.mappingId !== undefined ? 
              (updatedRow.mappingId === null ? null : 
               typeof updatedRow.mappingId === 'number' ? updatedRow.mappingId : 
               !isNaN(parseInt(updatedRow.mappingId)) ? parseInt(updatedRow.mappingId) : null) : 
              null,
            mappingScore: updatedRow.mappingScore || 0,
            style: updatedRow.style || 'lineitem',
            isBold: updatedRow.isBold || false,
            isEmptyRow: updatedRow.isEmptyRow || false
          },
          cells: Object.keys(updatedRow)
            .filter(key => key.startsWith('uid'))
            .map(columnKey => ({
              columnKey,
              value: updatedRow[columnKey] !== undefined ? updatedRow[columnKey] : '',
              type: "",
              format: "",
              source: "",
              comments: [],
              pdfHighlight: { 
                text: "", 
                bounds: [], 
                pageNumber: 1,
                pageHeight: 0,
                pageWidth: 0
              }
            }))// Only include cells that have values
        };
      }
    });
  });

  // Handle notes tables
  if (notesData?.length > 0) {
    const notesStartIndex = 3;
    notesData.forEach((noteTable, index) => {
      const tableGroupIndex = notesStartIndex + index;

      if (!updatedData.tableGroups[tableGroupIndex]) {
        console.warn(`Notes table group ${index + 1} not found`);
        return;
      }

      const tableGroup: A1TableGroup = updatedData.tableGroups[tableGroupIndex];
      if (!tableGroup.tables?.[0]?.rows) {
        console.warn(`Notes rows for table ${index + 1} not found`);
        return;
      }

      tableGroup.tables[0].rows = tableGroup.tables[0].rows.map((row: A1TableRow) => {
        if (!row?.label?.id) return row;

        const updatedNote = noteTable?.find(note => note.id === row.label.id);
        if (updatedNote) {
          row.cells = (row.cells || []).map(cell => ({
            ...cell,
            value: updatedNote[cell.columnKey] || cell.value
          }));
        }
        return row;
      });
    });
  }

  updatedData.isPublished = true;
  return updatedData;
};