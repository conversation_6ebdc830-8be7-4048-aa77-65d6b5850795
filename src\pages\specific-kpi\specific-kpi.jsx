import React, { useState, useCallback, useMemo, useEffect } from "react";
import MultiColumnHeaderTable from "../../components/multi-column-header/multi-column-header-table";
import CircularStatusButtons from "../../components/circular-status-buttons/circular-status-buttons";
import { FaTimes } from "react-icons/fa";
import FundCard from "../../components/fund-card/fund-card";
import { Button } from "../../partials/atoms/button";
import ButtonIcon from "../../partials/atoms/button/button-icon";
import { ToolTip } from "../../partials/atoms/tool-tip";
import { BsDownload } from "react-icons/bs";
import { CgSpinner } from "react-icons/cg";
import { FiSettings } from "react-icons/fi";
import { GET_FUND_DETAILS, GET_SPECIFIC_KPI_DETAILS } from "../../infra/api/company/get-company-details-service";
import { useSearchParams } from "react-router-dom";
import { generateExcelDownload } from "../../utils/excel-export";
import { UPDATE_SPECIFIC_KPI_API_SERVICE } from "../../infra/api/financial/update-financials-service"
import { PUBLISH_KPI_SERVICE, UPDATE_JOB_STATUS_SERVICE } from "../../infra/api/company/kpi-submit-service";
import { notify } from "../../partials/molecules/toaster";
import { GET_PAGE_CONFIG_DATA } from "../../infra/api/company/kpi-service";
import { formatKpiValue, validatePeriod, extractPeriodInfo } from "../../constants/commonFunctions";
import { OtherKPiModuleNames } from "../../constants";
import { KpiModule, FundFinancial } from "../../constants/kpi-module";
import SnackbarCurrencyDropdown from "./snackbar-currency-dropdown";
import SnackbarUnitDropdown from "./snackbar-unit-dropdown";
import { getSnackbarValues, getActiveTabData, applyCurrencyAndUnitChanges } from "../../utils/company-selection-utils";
import { DOWNLOAD_FILE_SERVICE } from "../../infra/api/company/download-file-service";
import { Tabs, Tab } from '@mui/material';

const SpecificKpiPanel = () => {
    // Define tab configuration
    const [tabs, setTabs] = useState([]);

    // Active tab state
    const [activeTabId, setActiveTabId] = useState("consolidated");

    // Track data changes across all tabs
    const [hasChanges, setHasChanges] = useState(false);
    const [isPublishLoading, setIsPublishLoading] = useState(false);
    const [inputfundName, setFundName] = useState(null);
    // Dynamic tab data: { [moduleName]: { periods, data, options } }
    const [tabData, setTabData] = useState({});
    const [specificKpiData, setSpecificKpiData] = useState({});
    const [fileData, setFilesData] = useState([]);
    

    const [currencyList, setCurrencyList] = useState([]);
    const [fundDetails, setFundDetails] = useState({});
    const [currency, setCurrency] = useState("USD");
    const [currencyUnit, setCurrencyUnit] = useState("Absolute");
    const [isLoading, setIsLoading] = useState(false);

    const [kpiConfig, setKpiConfig] = useState(null);

    // State for query string parameters
    const [processId, setProcessId] = useState(null);
    const [fundId, setFundId] = useState(null);
    const [env, setEnv] = useState(null);
    const [searchParams] = useSearchParams();
    // State for selected companies and snackbar
    const [selectedCompanies, setSelectedCompanies] = useState([]);
    const [showSnackbar, setShowSnackbar] = useState(false);
    const [snackbarCurrency, setSnackbarCurrency] = useState("Select Currency");
    const [snackbarUnit, setSnackbarUnit] = useState("Select Unit");
    const [isSaved, setIsSaved] = useState(false);
    const [isDeleted, setIsDeleted] = useState(false);


    useEffect(() => {
        const fetchPageConfig = async () => {
            try {
                const data = await GET_PAGE_CONFIG_DATA();
                console.log("Fetched page config data:", data);
                if (Array.isArray(data)) {
                    // Filter tabs to only those present in backend config
                    let loadedTabs = data.map((item, index) => ({
                        id: item.moduleName.toLowerCase(),
                        name: item.pageConfigAliasName,
                        index: index,
                        tabId: item.moduleId,
                        moduleName: item.moduleName == "StaticInformation" ? "staticInformation" : item.moduleName,
                        isCompanyLevel: !item.moduleName.toLowerCase().includes("fund")
                    }));
                    // Sort so isCompanyLevel=false tabs come first, then tabId 7,8,9 at the end in order 7,8,9
                    loadedTabs = loadedTabs.sort((a, b) => {
                        // First, sort by isCompanyLevel
                        if (a.isCompanyLevel !== b.isCompanyLevel) {
                            return a.isCompanyLevel ? 1 : -1;
                        }
                        // Then, sort tabId 7,8,9 to the end in order 7,8,9
                        const specialOrder = [7, 8, 9, 1000];
                        const aIdx = specialOrder.indexOf(Number(a.tabId));
                        const bIdx = specialOrder.indexOf(Number(b.tabId));
                        if (aIdx === -1 && bIdx === -1) return 0;
                        if (aIdx === -1) return -1;
                        if (bIdx === -1) return 1;
                        return aIdx - bIdx;
                    });
                    // After sorting, update index property to be 0,1,2,...
                    loadedTabs = loadedTabs.map((tab, idx) => ({ ...tab, index: idx }));
                    setTabs(loadedTabs);
                    // Set default active tab to first tab if available
                    if (loadedTabs.length > 0) {
                        setActiveTabId(loadedTabs[0].id);
                    }
                    console.log("Fetched page config data:", loadedTabs);
                    setKpiConfig(data);
                }
            } catch (error) {
                console.error("Error fetching page config data:", error);
            }
        };
        fetchPageConfig();
    }, []);

    useEffect(() => {
        const queryProcessId = searchParams.get('processId');
        const queryFundId = searchParams.get('fundId');
        const queryClientEnv = searchParams.get('environment');

        if (queryProcessId) {
            setProcessId(queryProcessId);
        }
        if (queryFundId) {
            setFundId(queryFundId);
        }
        if (queryClientEnv) {
            setEnv(queryClientEnv);
        }
    }, [searchParams]);
    // Load currency data on component mount
    useEffect(() => {
        const fetchCurrencyData = async () => {
            // Only proceed if fundId is available
            if (!fundId) return;

            setIsLoading(true);
            try {
                const response = await GET_FUND_DETAILS(fundId);
                if (response) {
                    setFundDetails(response);
                    setFundName(`${response.fundName}`);
                    setCurrencyList(response.currencyList);
                }
                const specific_kpi_response = await GET_SPECIFIC_KPI_DETAILS(processId);
                if (specific_kpi_response != null) {
                    setSpecificKpiData(specific_kpi_response);
                    setCurrency(specific_kpi_response.currencyCode || "USD");
                    setCurrencyUnit(specific_kpi_response.unit || "Absolute");
                    // Map customSections to tabData
                    const newTabData = {};
                    if (Array.isArray(specific_kpi_response.customSections)) {
                        specific_kpi_response.customSections.forEach(section => {
                            var module = tabs.find(tab => tab.moduleName.toLowerCase() === section.sectionType.toLowerCase());
                            newTabData[section.sectionType] = {
                                periods: section.periods || [],
                                data: section.data || [],
                                options: section.options || [],
                                moduleId: module ? module.tabId : null
                            };
                        });
                    }
                    setTabData(newTabData);
                    // Download files as before
                    const responses = await downloadAllFiles(filterUniqueFiles(specific_kpi_response.files));
                    setFilesData(responses);
                }
            } catch (error) {
                console.error("Error fetching currency details:", error);
            } finally {
                setIsLoading(false);
            }
        };

        fetchCurrencyData();
    }, [fundId, tabs]);
    
    useEffect(() => {
        let kpi_data = specificKpiData;
        kpi_data.customSections = specificKpiData.customSections || [];
       setSpecificKpiData(kpi_data);
       if(isDeleted === true){
        handleSaveAllChanges(true);
        setIsDeleted(false);
       }    
    }, [isDeleted]);
    
    // Handlers for currency changes
    const handleCurrencyChange = (value) => {
        setCurrency(value?.currencyCode);
        setHasChanges(true);
    };

    const handleCurrencyUnitChange = (value) => {
        setCurrencyUnit(value.label);
        setHasChanges(true);
    };
    const downloadSingleFile = async (fileKey) => {
        try {
          const response = await DOWNLOAD_FILE_SERVICE({ fileKey });
    
          return response;
        } catch (error) {
        }
      };
    // Helper function to download all files - extracted to top level
  const downloadAllFiles = async (files) => {
    if (!files || files.length === 0) return [];
    
    try {
      // Create an array of promises for all downloads
      const downloadPromises = files.map((file) =>{        
        if (file.fileName.toLowerCase().endsWith('.pdf')) {
            return downloadSingleFile(file.url);
        } 
        else {
        // Remove Excel extension (.xlsx, .xls) from fileName
        const fileNameWithoutExt = file.fileName.replace(/\.(xlsx|xls)$/i, '');
        return {
          ...file,
          fileId: fileNameWithoutExt,
          fileKey: file.url
        };
      }
        }

      );
      // Wait for all downloads to complete
      return await Promise.all(downloadPromises);
    } catch (error) {
      console.error("Error downloading files:", error);
      return [];
    }
  };
const filterUniqueFiles = (files) => {
    const seen = new Set();
    return files
      .filter(file => {
        // Only process files with .pdf extension (case-insensitive)
        if (!file.file_name) return false;
        if (seen.has(file.file_name)) return false;
        seen.add(file.file_name);
        return true;
      })
      .map(file => ({
        url: file.s3_path,
        fileName: file.file_name
      }));
};
    // Helper function to parse period labels (like "Actual Q1 2024" or "Actual Jan 2024")
    const parsePeriodLabel = (periodLabel) => {
        if (!periodLabel) return { month: null, quarter: "", year: null };

        // Strip "Actual" prefix if present for cleaner parsing
        const cleanedLabel = periodLabel.replace(/^Actual\s+/i, '');

        // Extract components
        let month = null;
        let quarter = "";
        let year = null;

        // Get year - look for 4 digit year
        const yearMatch = cleanedLabel.match(/\b(20\d{2})\b/);
        if (yearMatch) {
            year = Number(yearMatch[1]);
        }

        // Get quarter - look for Q1, Q2, Q3, Q4 pattern (case insensitive)
        const quarterMatch = cleanedLabel.match(/\b[Qq]([1-4])\b/);
        if (quarterMatch) {
            quarter = `Q${quarterMatch[1]}`;
        }

        // Get month - check for month names (both full and abbreviated)
        const monthPatterns = {
            'Jan': 1, 'January': 1,
            'Feb': 2, 'February': 2,
            'Mar': 3, 'March': 3,
            'Apr': 4, 'April': 4,
            'May': 5,
            'Jun': 6, 'June': 6,
            'Jul': 7, 'July': 7,
            'Aug': 8, 'August': 8,
            'Sep': 9, 'September': 9, 'Sept': 9,
            'Oct': 10, 'October': 10,
            'Nov': 11, 'November': 11,
            'Dec': 12, 'December': 12
        };

        // Check for month names in the period label
        for (const [monthName, monthNum] of Object.entries(monthPatterns)) {
            if (cleanedLabel.includes(monthName)) {
                month = monthNum;
                break; // Found the month, no need to continue checking
            }
        }

        return { month, quarter, year };
    };

    // Get current active tab config using useMemo for better performance
    const activeTabConfig = useMemo(() => {
        // Find the active tab's moduleName
        const activeTab = tabs.find(tab => tab.id === activeTabId);
        if (!activeTab) return { periods: [], data: [], options: [], setPeriods: () => {}, setData: () => {} };
        const tabModuleName = activeTab.moduleName;
        const tabSection = tabData[tabModuleName] || { periods: [], data: [], options: [] };
        // Dynamic setters for periods and data
        const setPeriods = (newPeriods) => {
            setTabData(prev => ({
                ...prev,
                [tabModuleName]: {
                    ...prev[tabModuleName],
                    periods: newPeriods
                }
            }));
        };
        
        const setData = (newData) => {
            setTabData(prev => ({
                ...prev,
                [tabModuleName]: {
                    ...prev[tabModuleName],
                    data: newData
                }
            }));
        };
        return {
            periods: tabSection.periods,
            setPeriods,
            kpiOptions: tabSection.options,
            data: tabSection.data,
            setData
        };
    }, [activeTabId, tabData, tabs]);

    // Handler for KPI selection changes (dynamic)
    const handleKpiChange = useCallback((periodIndex, columnIndex, selectedKpi) => {
        const activeTab = tabs.find(tab => tab.id === activeTabId);
        if (!activeTab) return;
        const tabModuleName = activeTab.moduleName;
        const currentPeriods = tabData[tabModuleName]?.periods || [];
        const updatedPeriods = JSON.parse(JSON.stringify(currentPeriods));
        if (!updatedPeriods[periodIndex].selectedKpis) {
            updatedPeriods[periodIndex].selectedKpis = [];
        }
        updatedPeriods[periodIndex].selectedKpis[columnIndex] = selectedKpi;
        // Update periods in tabData
        setTabData(prev => ({
            ...prev,
            [tabModuleName]: {
                ...prev[tabModuleName],
                periods: updatedPeriods
            }
        }));
        setHasChanges(true);
        console.log("Changes detected - enabling save button");
    }, [activeTabId, tabData, tabs]);

    const handleValueEmptyObj = {
        confidence_score: 0,
        excelHighlight: {
            reference: null,
            sheet: null
        },
        fileType: "",
        kpiInfo: "",
        pageNumber: 0,
        pdfHighlight: {
            bounds: [0, 0, 0, 0],
            pageHeight: 0,
            pageNumber: 0,
            pageWidth: 0,
            text: ""
        },
        source: null,
        unit: "",
        unit_scale: "",
        value: ""
    };
    // Handle cell value changes (dynamic)
    const handleCellValueChange = useCallback((rowIndex, periodId, kpiId, newValue, convertedPosition = null, fileType = null, source = null) => {
        setTabData(prev => {
            const activeTab = tabs.find(tab => tab.id === activeTabId);
            if (!activeTab) return prev;
            const tabModuleName = activeTab.moduleName;
            const currentData = prev[tabModuleName]?.data || [];
            const updatedData = JSON.parse(JSON.stringify(currentData));
            const parsedValue = !isNaN(Number(newValue)) ? Number(newValue) : newValue;
            const valueKey = `${periodId}_${kpiId}`;
            let existingValueObj = updatedData[rowIndex].values[valueKey] || {};
            if (!existingValueObj || Object.keys(existingValueObj).length === 0) {
                existingValueObj = { ...handleValueEmptyObj };
            }
            if (existingValueObj) {
                if (existingValueObj.pdfHighlight && convertedPosition) {
                    existingValueObj.pdfHighlight = {
                        ...existingValueObj.pdfHighlight,
                        bounds: convertedPosition.bounds,
                        pageNumber: convertedPosition.pageNumber ?? 1,
                        text: String(parsedValue),
                        pageHeight: -1,
                    };
                }
                if (source && source !== null && source !== undefined && fileType && fileType !== null && fileType !== undefined) {
                    existingValueObj.fileType = fileType;
                    existingValueObj.source = source;
                }
            }
            updatedData[rowIndex].values[valueKey] = {
                ...existingValueObj,
                value: String(parsedValue)
            };
            const newTabData = {
                ...prev,
                [tabModuleName]: {
                    ...prev[tabModuleName],
                    data: updatedData
                }
            };
            return newTabData;
        });
        setHasChanges(true);
    }, [activeTabId, tabs]);

    const processTabData = (data, periods, moduleId) => {
        const kpis = [];

        // Loop through each row in the data
        data.forEach(row => {
            const curr = currencyList.find(item => item.currencyCode === row?.currencyCode ?? "") || {};
            // Loop through all values in the row
            Object.entries(row.values || {}).forEach(([key, value]) => {
                // Skip null/undefined values
                if (value === null || value === undefined) return;

                // The key is in format "periodId_kpiId"
                const [periodId, kpiId] = key.split('_');

                // Find the period details from the periodId
                const period = periods.find(p => p.periodId === periodId);
                if (!period) return;
                
                let kpi = {};
                if(period?.selectedKpis && period?.selectedKpis.length > 0) {
                    kpi = period?.selectedKpis.find(k => k.kpiId === Number(kpiId));
                }
                //validate kpi
                if (!kpi) return;
                // Validate the period label
                if (validatePeriod(period.label)) return;
                // Parse the period label to extract date components
                const extractedPeriod = extractPeriodInfo(period, moduleId, kpiConfig);
                const kpiValue = formatKpiValue(kpi?.kpiInfo, value?.value);
                // Create the payload structure for this value
                kpis.push({
                    kpiId: Number(kpiId),
                    kpiValue: kpiValue == "NA" ? null : kpiValue,
                    valueType: extractedPeriod?.valueType ?? "",
                    valueTypeId: extractedPeriod?.valueTypeId ?? 4,
                    month: extractedPeriod?.month ?? null,
                    year: extractedPeriod?.year ?? null,
                    quarter: extractedPeriod?.quarter ?? null,
                    kpiInfo: kpi?.kpiInfo != undefined ? String(kpi?.kpiInfo) : "$",
                    moduleId: moduleId,
                    methodologyId: kpi?.methodologyId ?? 0,
                    currencyId: (curr.currencyId ?? currencyList.find(item => item.currencyCode === currency)?.currencyId) || 0,
                    unit: (row?.unit ?? currencyUnit) || "Absolute",
                    // mappingId: Number(kpi?.mappingId ?? 0),
                    companyId: Number(row?.companyId),
                });
            });
        });

        return kpis;
    };
    const handlePublish = async () => {
        // Always use the latest currency and currencyUnit state
        const apiPayload = prepareKpiPayload();
        const response = await PUBLISH_KPI_SERVICE(apiPayload);
        if (response && response.success) {
            let status = "";
            if (response && response.success) {
                status = "Completed";
                notify.success(response?.message);
            }
            else {
                status = "Failed";
                const errorMessage = response?.message || "Unknown error occurred";
                notify.error(`Failed to update status: ${errorMessage}`);
            }
            const statusRequest = {
                processId: processId,
                status: status
            }
            const statusResponse = await UPDATE_JOB_STATUS_SERVICE(statusRequest);
            if (statusResponse && statusResponse.isSuccess) {
                notify.success(statusResponse?.message);
                return true;
            } else {
                const errorMessage = statusResponse?.message || "Unknown error occurred";
                notify.error(`Failed to update status: ${errorMessage}`);
                return false;
            }
        }
        // Wait a moment for the notification to be visible
        setTimeout(() => {
            // Force a full page refresh - the most reliable way to ensure Excel export updates
            window.location.reload();
        }, 100);
        // Reset the changes flag
        setHasChanges(false);
    };

    const prepareKpiPayload = useCallback(() => {
        // Loop through all tabs and collect their data
        const allFundKpis = {};
        const allCompanyKpis = {};
        const staticData = {};
        Object.entries(tabData).forEach(([sectionType, sectionObj]) => {
            const tabSection = tabData[sectionType] || { periods: [], data: [] };
            if(tabSection.moduleId < 1000){
                allCompanyKpis[sectionType] = processTabData(tabSection.data, tabSection.periods, tabSection.moduleId);
            }
            else if(tabSection.moduleId === 1000){
                staticData[sectionType] = processTabData(tabSection.data, tabSection.periods, tabSection.moduleId);
            }
            else if(tabSection.moduleId > 1000){
                allFundKpis[sectionType] = processTabData(tabSection.data, tabSection.periods, tabSection.moduleId);
            }
        });
        const companyIds = [
            ...new Set(
                Object.values(allCompanyKpis)
                    .flat()
                    .map(kpi => kpi.companyId)
                    .filter(id => id != null)
            )
        ];
        let otherKPi
        const companies = companyIds.map(companyId => {
            // Build kpi properties dynamically from allCompanyKpis keys
            const kpiProps = Object.entries(allCompanyKpis).reduce((acc, [kpiKey, kpiArray]) => {
                acc["OtherKPI"] = [];
                if (OtherKPiModuleNames.includes(kpiKey)) {
                    acc["OtherKPI"].push(...kpiArray
                        .filter(kpi => kpi.companyId === companyId)
                        .map(({ companyId, ...rest }) => rest));
                }
                else {
                    acc[kpiKey] = kpiArray
                        .filter(kpi => kpi.companyId === companyId)
                        .map(({ companyId, ...rest }) => rest);
                }
                return acc;
            }, {});
            // Add static fields
            return {
                companyId,
                ...kpiProps,
                staticFields: [],
            };
        });
        let fundKpis = [];
        Object.entries(allFundKpis).forEach(([sectionType, sectionObj]) => {
            if (Array.isArray(sectionObj)) {
                fundKpis.push(...sectionObj);
            }
        });
        // Example: you may need to map these to the expected payload structure
        // For now, just collect all kpis per tab
        return {
            CompaniesDetails: companies,
            fundKpiSections: {
                fundKpis: fundKpis.map(({ companyId, ...rest }) => rest)
            },
            fundId: Number(specificKpiData?.company_id),
            isFinancial: false,
            fundUnitCurrency: currencyUnit || "Absolute",
            userId: 0,
            fundCurrencyId: currencyList.find(item => item.currencyCode === currency)?.currencyId || 0,
            connectionString: "",
            processId: processId
        };
    }, [currency, currencyUnit, currencyList, specificKpiData]);


    // Handle save for all tabs (dynamic)
    const handleSaveAllChanges = useCallback(async () => {
        // Prepare dynamic payload
        const kpi_data = specificKpiData;
        // Restructure tabData object to array of customSections
        kpi_data.customSections = Object.entries(tabData).map(([sectionType, sectionObj]) => ({
            sectionType: sectionType,
            periods: sectionObj.periods || [],
            data: sectionObj.data || [],
            options: sectionObj.options || []
        }));
        kpi_data.currencyCode = currency;
        kpi_data.unit = currencyUnit;
        const response = await UPDATE_SPECIFIC_KPI_API_SERVICE(processId, kpi_data);
        if (response.id != null && response.id != undefined) {
            if(isDeleted===true){
                setIsSaved(false);
                notify.success("Columns deleted successfully");
            } else{
            // Show a success message with instructions
            setIsSaved(true);
            notify.success("Saved successfully.");
            }
            setHasChanges(false);
            // Workaround: re-trigger useEffect by toggling fundId
            const originalFundId = fundId;
            setFundId(null);
            setTimeout(() => setFundId(originalFundId), 0);
        }
    }, [tabData, tabs, processId, fundId, currency, currencyUnit, specificKpiData]);
    // Handle company selection (dynamic)
    const handleCompanySelection = (companyId, isSelected) => {
        const activeTab = tabs.find(tab => tab.id === activeTabId);
        const currentData = activeTab ? tabData[activeTab.moduleName]?.data || [] : [];
        if (isSelected) {
            const newSelectedCompanies = [...selectedCompanies, companyId];
            setSelectedCompanies(newSelectedCompanies);
            setShowSnackbar(true);
            const { currency: selectedCurrency, unit: selectedUnit } = getSnackbarValues(newSelectedCompanies, currentData);
            setSnackbarCurrency(selectedCurrency);
            setSnackbarUnit(selectedUnit);
        } else {
            const newSelectedCompanies = selectedCompanies.filter(id => id !== companyId);
            setSelectedCompanies(newSelectedCompanies);
            if (newSelectedCompanies.length === 0) {
                setShowSnackbar(false);
            } else {
                const { currency: selectedCurrency, unit: selectedUnit } = getSnackbarValues(newSelectedCompanies, currentData);
                setSnackbarCurrency(selectedCurrency);
                setSnackbarUnit(selectedUnit);
            }
        }
    };

    // Handle select all companies (dynamic)
    const handleSelectAll = () => {
        const activeTab = tabs.find(tab => tab.id === activeTabId);
        const currentData = activeTab ? tabData[activeTab.moduleName]?.data || [] : [];
        const allCompanyIds = currentData.map(row => row.companyId);
        setSelectedCompanies(allCompanyIds);
        setShowSnackbar(true);
        const { currency: selectedCurrency, unit: selectedUnit } = getSnackbarValues(allCompanyIds, currentData);
        setSnackbarCurrency(selectedCurrency);
        setSnackbarUnit(selectedUnit);
    };

    // Apply changes to selected companies (dynamic)
    const applyChangesToSelected = useCallback((currency, unit) => {
        // Update currencyCode and unit for each data object in tabData of current selected tab
        const activeTab = tabs.find(tab => tab.id === activeTabId);
        if (activeTab) {
            const tabModuleName = activeTab.moduleName;
            setTabData(prev => ({
                ...prev,
                [tabModuleName]: {
                    ...prev[tabModuleName],
                    data: (prev[tabModuleName]?.data || []).map(row =>
                        selectedCompanies.includes(row.companyId)
                            ? { ...row, currencyCode: currency, unit: unit }
                            : row
                    )
                }
            }));
        }
        setHasChanges(true);
        notify.success("Applied currency/unit changes to selected companies.");
    }, [tabData, activeTabId, selectedCompanies, tabs]);

    // Handle snackbar close and deselect all
    const handleSnackbarClose = () => {
        setSelectedCompanies([]);
        setShowSnackbar(false);
    };

    // Helper functions for status counts
    const getStatusCounts = (tabId) => {
        switch (tabId) {
            case 'consolidated':
                return { mapped: 12, matchFound: 5, unmapped: 2, noMatchFound: 3 };
            case 'financial':
                return { mapped: 8, matchFound: 3, unmapped: 1, noMatchFound: 2 };
            case 'masterData':
                return { mapped: 5, matchFound: 2, unmapped: 3, noMatchFound: 1 };
            case 'investmentKpi':
                return { mapped: 7, matchFound: 4, unmapped: 2, noMatchFound: 3 };
            default:
                return { mapped: 0, matchFound: 0, unmapped: 0, noMatchFound: 0 };
        }
    };

    // Custom Snackbar component (memoized to avoid re-renders, dynamic)
    const companySelectionSnackbar = useMemo(() => {
        if (!showSnackbar) return null;
        
        // Get current tab data to check if all companies are selected
        const activeTab = tabs.find(tab => tab.id === activeTabId);
        const currentData = activeTab ? tabData[activeTab.moduleName]?.data || [] : [];
        const allCompanyIds = currentData.map(row => row.companyId);
        const allCompaniesSelected = allCompanyIds.length > 0 && 
            allCompanyIds.length === selectedCompanies.length && 
            allCompanyIds.every(id => selectedCompanies.includes(id));
        
        return (
            <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50 bg-white rounded-lg shadow-lg border-neutral-10 border p-4 flex items-center justify-between w-[600px]">
                <div className="flex items-center gap-4 pl-4">
                    <button 
                        className={`${allCompaniesSelected ? "text-neutral-40 cursor-not-allowed" : "text-primary-78 hover:underline"} s-r`}
                        onClick={() => !allCompaniesSelected && handleSelectAll()}
                        disabled={allCompaniesSelected}
                    >
                        Select All
                    </button>
                    <button 
                        className="text-negative-70 hover:underline s-r"
                        onClick={() => handleSnackbarClose()}
                    >
                        Deselect All
                    </button>
                </div>
                <div className="flex items-center gap-2 ml-2 pl-2 custom-unit-currency">
                    <SnackbarCurrencyDropdown 
                        currency={snackbarCurrency}
                        currencyList={currencyList}
                        onCurrencyChange={(value) => {
                            const newCurrency = value?.currencyCode || "Select Currency";
                            setSnackbarCurrency(newCurrency);
                            applyChangesToSelected(newCurrency, snackbarUnit);
                        }}
                    />
                    <SnackbarUnitDropdown 
                        currencyUnit={snackbarUnit}
                        onCurrencyUnitChange={(value) => {
                            const newUnit = value?.label || "Select Unit";
                            setSnackbarUnit(newUnit);
                            applyChangesToSelected(snackbarCurrency, newUnit);
                        }}
                    />
                </div>
                <div className="w-px h-5 bg-neutral-20"></div>
                <button
                    onClick={handleSnackbarClose}
                    className="text-neutral-60 hover:text-neutral-80"
                    aria-label="Close"
                >
                    <FaTimes className="w-4 h-4 text-neutral-60" />
                </button>
            </div>
        );
    }, [showSnackbar, selectedCompanies, snackbarCurrency, snackbarUnit, currencyList, handleSelectAll, applyChangesToSelected, activeTabId, tabData, tabs]);

    // Excel download function using the utility (dynamic)
    const handleExcelDownload = () => {
        const excelTabData = tabs.map(tab => {
            const tabSection = tabData[tab.moduleName] || { periods: [], data: [] };
            return {
                sheetName: tab.name,
                isCompanyLevel: tab.isCompanyLevel,
                currency: currency,
                unit: currencyUnit,
                periods: tabSection.periods,
                data: tabSection.data
            };
        });
        generateExcelDownload(excelTabData, inputfundName);
    };

    return (
        <div className="pt-6 bg-white min-h-screen h-screen overflow-y-auto">
            <div
                className="flex justify-between items-center mb-4 px-3 py-2 rounded-lg border bg-primary-35 border-neutral-5"

            >
                <FundCard
                    fundName={fundDetails.fundName}
                    companyCount={fundDetails.companyCount}
                    currency={currency}
                    currencyUnit={currencyUnit}
                    currencyList={currencyList}
                    onCurrencyChange={(value) => {
                        // Always pass the selected currency object
                        if (value && value.currencyCode) {
                            setCurrency(value.currencyCode);
                            setHasChanges(true);
                        }
                    }}
                    onCurrencyUnitChange={(value) => {
                        if (value && value.label) {
                            setCurrencyUnit(value.label);
                            setHasChanges(true);
                        }
                    }}
                />

                {/* Action Buttons */}
                <div className="flex items-center space-x-3">
                    <ButtonIcon
                        className="border"
                        data-testid="history-button"
                        intent="secondary"
                        data-tooltip-id="tool-tip-setting-top"
                        disabled
                    >
                        <FiSettings className="h-4 w-4" />
                        <ToolTip place="left" text="Setting" toolTipId="tool-tip-setting-top" />
                    </ButtonIcon>
                    <Button
                        onClick={handleSaveAllChanges}
                        // disabled={!hasChanges}
                        data-testid="save-draft-button"
                        intent="secondary"
                    >
                        Save
                    </Button>
                    <ButtonIcon
                        className="border"
                        data-testid="export-button"
                        intent="secondary"
                        data-tooltip-id="tool-tip-download-top"
                        onClick={handleExcelDownload}
                        disabled={!isSaved}
                    >
                        <BsDownload className="h-4 w-4" />
                        <ToolTip place="left" text="Click on Save before downloding the excel." toolTipId="tool-tip-download-top" />
                    </ButtonIcon>
                    <Button
                        data-testid="publish-button"
                        className={"publish-button flex-row w-fit"}
                        data-tooltip-id="tool-tip-download-publish"
                        onClick={handlePublish}
                        size={"medium"}
                        disabled={false}
                    >
                        <ToolTip place="left" text="Click on Save is mandatory before publish." toolTipId="tool-tip-download-publish" />
                        {isPublishLoading ? (
                            <span className="flex w-fit items-center gap-2">
                                <CgSpinner
                                    strokeWidth={1}
                                    className="size-4 animate-spin text-white"
                                />
                                Publish
                            </span>
                        ) : (
                            <>Publish</>
                        )}
                    </Button>
                </div>            </div>            {/* Tab Navigation */}
            <div className="mb-0 border rounded-lg border-b-0" style={{ borderBottomLeftRadius: '0px', borderBottomRightRadius: '0px' }}>
                <div className="flex justify-between items-center">
                    <Tabs value={tabs.findIndex(tab => tab.id === activeTabId)}
                        onChange={(event, newValue) => {
                            const selectedTab = tabs[newValue];
                            setActiveTabId(selectedTab.id);
                            setSelectedCompanies([]);
                            setShowSnackbar(false);
                        }}
                        variant="scrollable"
                        scrollButtons="auto"
                        sx={{ minHeight: 0, borderBottom: 1, borderColor: 'divider', flex: 1, borderBottom: 'none' }}
                    >
                        {tabs.map((tab, idx) => (
                            <Tab
                                key={tab.id}
                                label={
                                    <span className="body-r">{tab.name}</span>
                                }
                                sx={{
                                    minHeight: 0,
                                    px: 2,
                                    py: 1,
                                    borderTopLeftRadius: '8px',
                                    borderTopRightRadius: '8px',
                                    borderBottom: activeTabId === tab.id ? '2px solid #1976d2' : 'none',
                                }}
                            />
                        ))}
                    </Tabs>

                    {/* Status Buttons */}
                    <CircularStatusButtons
                        statusData={{
                            mapped: getStatusCounts(activeTabId).mapped,
                            matchFound: getStatusCounts(activeTabId).matchFound,
                            unmapped: getStatusCounts(activeTabId).unmapped,
                            noMatchFound: getStatusCounts(activeTabId).noMatchFound,
                        }}
                    />
                </div>
            </div>

            <div className="border shadow-sm">
                {/* Table-based Multi-column header component */}
                <MultiColumnHeaderTable
                    periods={activeTabConfig.periods}
                    setPeriods={activeTabConfig.setPeriods}
                    kpiOptions={activeTabConfig.kpiOptions}
                    data={activeTabConfig.data}
                    setCurrentTableData={activeTabConfig.setData}
                    onKpiChange={handleKpiChange}
                    onCellValueChange={handleCellValueChange}
                    kpiConfig={kpiConfig}
                    selectedTabId={tabs.find(tab => tab.id === activeTabId)?.tabId}
                    activeTabId={activeTabId} // <-- Add this line
                    onCompanySelection={handleCompanySelection}
                    selectedCompanies={selectedCompanies}
                    files={fileData}
                    isDeleted={isDeleted}
                    setIsDeleted={setIsDeleted}
                    extractionType="SpecificKpi"
                />
            </div>
            {companySelectionSnackbar}
        </div>
    );
};

export default SpecificKpiPanel;