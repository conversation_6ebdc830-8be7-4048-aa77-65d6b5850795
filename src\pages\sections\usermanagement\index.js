import React from "react";
import { UserManagement } from "@brui/user-management";
import { UserManager } from "oidc-client-ts";
import { getAppConfig } from "../../../utils/authHelper";

const UserManagementContainer = () => {
  const config = getAppConfig();
  const User = new UserManager(config.oidcConfig);

  const umConfig = {
    title: "user-management",
    userManager: User,
    accessType: "role",
    roles: [],
    applicationName: "bh", // bh, fs, cp, rfp
    environment: "dev", // dev, test, uat, perf1, perf2, prod
    client: "pod", // pod or clientcode
    roleSeparator: "-", // '-' or '|'
    environmentSeparator: "-" // '-' or ''
  };
  return <UserManagement {...umConfig} />;
};

export default UserManagementContainer;
