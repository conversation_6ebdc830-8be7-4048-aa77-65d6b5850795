import React from "react";
import { screen, waitFor } from "@testing-library/react";
import App from "./App";
import { renderWithProviders } from "./utils/test-utils";

describe("App component", () => {
  test("renders correctly with default route", async () => {
    renderWithProviders(<App />);
    
    // Wait for the app to load
    await waitFor(() => {
      expect(screen.getByTestId("main-container")).toBeInTheDocument();
    });
  });
  
  test("renders error boundary when error occurs", async () => {
    // Mock an error in the App component
    jest.spyOn(console, "error").mockImplementation(() => {});
    
    const OriginalApp = App;
    
    // Replace App with a component that throws an error
    const ErrorApp = () => {
      throw new Error("Test error");
      return <OriginalApp />;
    };
    
    renderWithProviders(<ErrorApp />);
    
    // Error boundary should catch the error
    await waitFor(() => {
      expect(console.error).toHaveBeenCalled();
    });
    
    // Restore original console.error
    console.error.mockRestore();
  });
});
