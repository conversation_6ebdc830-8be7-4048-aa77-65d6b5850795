import React from "react";
import PropTypes from "prop-types";
import { FaTimes, FaSearch } from "react-icons/fa";
import { Tooltip } from "@progress/kendo-react-tooltip";
import useDocumentViewerStore from "../document-viewer.store";

const DocumentViewerHTMLToolBar = ({
  searchQuery,
  readOnly,
  setSearchQuery
}) => {
  const { setPreviewClose } = useDocumentViewerStore((state) => state);
  return (
    <div className="flex h-12 justify-between border-b">
      <div className="flex h-12 w-full justify-start px-4 py-2.5">
        <div className="flex items-center text-primary-78">
          <FaSearch className="size-3" />
        </div>
        <input
          value={searchQuery}
          data-testid="document-viewer-html-toolbar-search-input"
          onChange={(e) => setSearchQuery(e.target.value)}
          placeholder="Search here..."
          className="body-r w-full items-center border-none text-neutral-80 placeholder:text-neutral-30 focus:ring-0"
        ></input>
        {searchQuery && (
          <button
            onClick={() => setSearchQuery("")}
            className="flex items-center text-neutral-80"
          >
            <FaTimes className="size-3" />
          </button>
        )}
      </div>
      {!readOnly && (
        <Tooltip openDelay={100} position="left" anchorElement="target">
          <button
            title="Close preview"
            data-testid="document-viewer-html-toolbar-close-button"
            onClick={setPreviewClose}
            className="flex h-full w-10 cursor-pointer items-center justify-end border-l text-center text-neutral-60 hover:bg-primary-40 active:bg-primary-50"
          >
            <div
              title="Close preview"
              className="flex w-10 items-center justify-center"
            >
              <FaTimes
                title="Close preview"
                className="justify-center"
                onClick={setPreviewClose}
                data-testid="document-viewer-html-toolbar-close-button-icon"
              />
            </div>
          </button>
        </Tooltip>
      )}
    </div>
  );
};

export default DocumentViewerHTMLToolBar;

DocumentViewerHTMLToolBar.propTypes = {
  searchQuery: PropTypes.string.isRequired,
  setSearchQuery: PropTypes.func.isRequired,
  readOnly: PropTypes.bool
};
