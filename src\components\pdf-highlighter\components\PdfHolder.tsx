import { GlobalWorkerOptions, getDocument } from "pdfjs-dist";
import type { PDFDocumentProxy } from "pdfjs-dist";
import React, { useEffect, useRef, useState } from "react";

interface Props {
  workerSrc: string;
  url: string;
  beforeLoad: JSX.Element;
  errorMessage?: JSX.Element;
  base64data?: any;
  onError?: (error: Error) => void;
  cMapUrl?: string;
  cMapPacked?: boolean;
  pdfDocument: PDFDocumentProxy | null;
  setPdfDocument: (pdfDocument: PDFDocumentProxy | null) => void;
  children?: (pdfDocument: PDFDocumentProxy) => React.ReactNode;
}

export const PdfHolder: React.FC<Props> = ({
  workerSrc = new URL(
    "pdfjs-dist/build/pdf.worker.min.mjs",
    import.meta.url
  ).toString(),
  url,
  base64data,
  beforeLoad,
  errorMessage,
  onError,
  cMapUrl,
  cMapPacked,
  pdfDocument,
  setPdfDocument,
  children
}) => {
  const [error, setError] = useState<Error | null>(null);
  const documentRef = useRef<HTMLSpanElement>(null);

  const handleError = (error: Error) => {
    onError?.(error);
    setPdfDocument(null);
    setError(error);
  };

  const load = async () => {
    const ownerDocument = documentRef.current?.ownerDocument || document;
    setPdfDocument(null);
    setError(null);

    if (typeof workerSrc === "string") {
      GlobalWorkerOptions.workerSrc = workerSrc;
    }
    try {
      if (pdfDocument) {
        await pdfDocument.destroy();
      }

      if (!url) {
        return;
      }

      if (base64data) {
        // Decode base64 string to Uint8Array
        const decodedData = atob(base64data);
        const buffer = new Uint8Array(decodedData.length);
        for (let i = 0; i < decodedData.length; ++i) {
          buffer[i] = decodedData.charCodeAt(i);
        }
        const LoadingPDF = getDocument({ data: buffer });
        const newPdfDocument = await LoadingPDF.promise;
        setPdfDocument(newPdfDocument);
      } else {
        const LoadingPDF = getDocument(url);
        const newPdfDocument = await LoadingPDF.promise;
        setPdfDocument(newPdfDocument);
      }
    } catch (e) {
      handleError(e as Error);
    }
  };

  useEffect(() => {
    load();
    return () => {
      pdfDocument?.destroy();
    };
  }, [url]);

  const renderError = () => {
    if (errorMessage) {
      return React.cloneElement(errorMessage, { error });
    }
    return null;
  };
  return (
    <>
      <span ref={documentRef} />
      {error && renderError()}
      {!error && (!pdfDocument || !children)
        ? beforeLoad
        : typeof children === 'function' && pdfDocument ? children(pdfDocument) : children}
    </>
  );
};
