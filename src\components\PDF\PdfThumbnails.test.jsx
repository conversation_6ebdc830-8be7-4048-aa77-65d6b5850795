/**
 * @jest-environment jsdom
 */
import React from 'react';
import { render, screen, fireEvent, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import PdfThumbnailList from './PdfThumbnails';

// Mock the PDF.js library functionality
const mockGetPage = jest.fn().mockImplementation((pageNum) => {
  return Promise.resolve({
    getViewport: () => ({ height: 100, width: 75 }),
    render: () => ({ promise: Promise.resolve() })
  });
});

const mockPdfDoc = {
  getPage: mockGetPage
};

// Mock canvas functionality
HTMLCanvasElement.prototype.getContext = jest.fn(() => ({
  drawImage: jest.fn(),
  fillRect: jest.fn(),
  clearRect: jest.fn(),
  save: jest.fn(),
  restore: jest.fn()
}));

// Mock Kendo UI Checkbox component
jest.mock('@progress/kendo-react-inputs', () => ({
  Checkbox: ({ value, checked, onChange }) => (
    <input
      type="checkbox"
      checked={checked}
      onChange={(e) => onChange({ value: e.target.checked })}
      data-testid="kendo-checkbox"
    />
  )
}));

describe('PdfThumbnailList Component', () => {
  const defaultProps = {
    pdfDoc: mockPdfDoc,
    totalPages: 5,
    currentPage: 1,
    onSelectPage: jest.fn(),
    onPagesSelected: jest.fn(),
    onRegisterSelectionCallback: jest.fn(),
    initialSelectedPages: []
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders thumbnails for all pages', () => {
    render(<PdfThumbnailList {...defaultProps} />);
    
    // Check if selection counter is rendered
    expect(screen.getByText('0 of 5 page(s) selected')).toBeInTheDocument();
    
    // Check if all page numbers are rendered
    for (let i = 1; i <= defaultProps.totalPages; i++) {
      expect(screen.getByText(`${i}.`)).toBeInTheDocument();
    }
    
    // Check if getPage was called for each thumbnail
    expect(mockGetPage).toHaveBeenCalledTimes(defaultProps.totalPages);
  });

  test('highlights current page', () => {
    render(<PdfThumbnailList {...defaultProps} />);
    
    // The current page number should have the blue text color
    const currentPageText = screen.getByText('1.');
    expect(currentPageText).toHaveClass('text-blue-800');
    
    // Other page numbers should have the gray text color
    const otherPageText = screen.getByText('2.');
    expect(otherPageText).toHaveClass('text-gray-800');
  });

  test('selects a page when clicked', () => {
    render(<PdfThumbnailList {...defaultProps} />);
    
    // Initially no pages are selected
    expect(screen.getByText('0 of 5 page(s) selected')).toBeInTheDocument();
    
    // Click on page 2
    fireEvent.click(screen.getByText('2.'));
    
    // Check if onSelectPage was called with correct page number
    expect(defaultProps.onSelectPage).toHaveBeenCalledWith(2);
    
    // Selection should be updated
    expect(defaultProps.onPagesSelected).toHaveBeenCalledWith([2]);
    expect(screen.getByText('1 of 5 page(s) selected')).toBeInTheDocument();
  });

  test('toggles page selection with checkbox', () => {
    render(<PdfThumbnailList {...defaultProps} />);
    
    // Click on page 3 to select it first
    fireEvent.click(screen.getByText('3.'));
    
    // Now find the checkbox for page 3 and uncheck it
    const checkboxes = screen.getAllByTestId('kendo-checkbox');
    fireEvent.click(checkboxes[2]); // 0-indexed, so 2 is the third checkbox
    
    // Page 3 should be unselected
    expect(defaultProps.onPagesSelected).toHaveBeenCalledWith([]);
    expect(screen.getByText('0 of 5 page(s) selected')).toBeInTheDocument();
  });

  test('initializes with selected pages', () => {
    const props = {
      ...defaultProps,
      initialSelectedPages: [2, 4]
    };
    
    render(<PdfThumbnailList {...props} />);
    
    // Check if selection counter shows correct number
    expect(screen.getByText('2 of 5 page(s) selected')).toBeInTheDocument();
  });

  test('updates selection when parent changes initialSelectedPages', () => {
    const { rerender } = render(<PdfThumbnailList {...defaultProps} />);
    
    // Initially no pages selected
    expect(screen.getByText('0 of 5 page(s) selected')).toBeInTheDocument();
    
    // Update props with selected pages
    rerender(<PdfThumbnailList {...defaultProps} initialSelectedPages={[1, 3, 5]} />);
    
    // Selection should be updated
    expect(screen.getByText('3 of 5 page(s) selected')).toBeInTheDocument();
  });

  test('registers selection callback function', () => {
    render(<PdfThumbnailList {...defaultProps} />);
    
    // Check if onRegisterSelectionCallback was called
    expect(defaultProps.onRegisterSelectionCallback).toHaveBeenCalled();
    
    // Extract the callback function that was passed
    const updateSelectionCallback = defaultProps.onRegisterSelectionCallback.mock.calls[0][0];
    
    // Use the callback to update selection
    act(() => {
      updateSelectionCallback([1, 2, 3]);
    });
    
    // Selection should be updated
    expect(screen.getByText('3 of 5 page(s) selected')).toBeInTheDocument();
  });

  test('maintains selection order', () => {
    render(<PdfThumbnailList {...defaultProps} />);
    
    // Click pages in a non-sequential order
    fireEvent.click(screen.getByText('4.'));
    fireEvent.click(screen.getByText('2.'));
    fireEvent.click(screen.getByText('5.'));
    
    // Selection should be sorted in ascending order
    expect(defaultProps.onPagesSelected).toHaveBeenCalledWith([2, 4, 5]);
  });

  // Skip this test as it's not properly set up to test the closest method
  test.skip('ignores checkbox area when clicking on thumbnail', () => {
    // Create a mock element for the closest function
    const mockClosest = jest.fn().mockImplementation((selector) => {
      return selector === '.checkbox-container' ? {} : null;
    });
    
    // Mock event with target that has closest method
    const mockEvent = {
      target: {
        closest: mockClosest
      }
    };
    
    render(<PdfThumbnailList {...defaultProps} />);
    
    // Get the component instance
    const thumbnailElement = screen.getByText('3.').closest('div');
    
    // Create a spy on the handleThumbnailClick method
    const originalClick = React.createElement;
    React.createElement = jest.fn(originalClick);
    
    // Manually trigger the click with our mock event
    fireEvent.click(thumbnailElement, mockEvent);
    
    // If the click was on a checkbox container, onSelectPage should not be called
    expect(defaultProps.onSelectPage).not.toHaveBeenCalled();
    
    // Restore original createElement
    React.createElement = originalClick;
  });
}); 