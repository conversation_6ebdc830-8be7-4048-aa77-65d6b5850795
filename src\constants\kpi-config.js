/**
 * KPI configuration options for different modules
 */

/**
 * Trading KPI configuration options
 */
export const TRADING_KPI_CONFIG = [
  {
    "moduleName": "TradingRecords",
    "moduleId": 1,
    "pageConfigAliasName": "Trading KPI",
    "subSectionFields": [
      {
        "valueTypId": 4,
        "name": "Actual",
        "aliasName": "Actual",
        "chartValue": "Monthly,Quarterly,Annual"
      },
      {
        "valueTypId": 5,
        "name": "Budget",
        "aliasName": "Budget",
        "chartValue": "Monthly,Quarterly,Annual"
      },
      {
        "valueTypId": 6,
        "name": "Forecast",
        "aliasName": "Forecast",
        "chartValue": "Monthly,Quarterly,Annual"
      },
      {
        "valueTypId": 3,
        "name": "IC",
        "aliasName": "IC",
        "chartValue": "Monthly,Quarterly,Annual"
      },
      {
        "valueTypId": 16,
        "name": "Actual LTM",
        "aliasName": "Actual LTM",
        "chartValue": "Monthly,Quarterly"
      },
      {
        "valueTypId": 17,
        "name": "Budget LTM",
        "aliasName": "Budget LTM",
        "chartValue": "Monthly,Quarterly"
      },
      {
        "valueTypId": 18,
        "name": "Forecast LTM",
        "aliasName": "Forecast LTM",
        "chartValue": "Monthly,Quarterly"
      },
      {
        "valueTypId": 14,
        "name": "Actual YTD",
        "aliasName": "Actual YTD",
        "chartValue": "Monthly,Quarterly"
      },
      {
        "valueTypId": 11,
        "name": "Budget YTD",
        "aliasName": "Budget YTD",
        "chartValue": "Monthly,Quarterly"
      },
      {
        "valueTypId": 13,
        "name": "Forecast YTD",
        "aliasName": "Forecast YTD",
        "chartValue": "Monthly,Quarterly"
      }
    ]
  }
];

export default {
  TRADING_KPI_CONFIG
};
