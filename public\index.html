<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/BEAT-favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description"
          content="Web site created using create-react-app" />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->
    <meta http-equiv="Cache-Control"
          content="no-cache, no-store, must-revalidate, pre-check=0, post-check=0, max-age=0, s-maxage=0" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <!-- <meta
      http-equiv="Content-Security-Policy"
      content="default-src * 'self';connect-src 'self' localhost:* *.beatapps.net *.acuitykp-AcuityOneSS.com analytics.beat-services.net 'sha256-Du12GMF4ycv+l+g45Cwkso7O2vAnEBS/ht+UX4dsvMY='; img-src 'self' data:; script-src 'self' 'unsafe-eval'  'wasm-unsafe-eval' localhost:* *.beatapps.net *.acuitykp-AcuityOneSS.com fast.fonts.net content.beatapps.net analytics.beat-services.net 'sha256-Du12GMF4ycv+l+g45Cwkso7O2vAnEBS/ht+UX4dsvMY='; script-src-elem 'unsafe-inline' 'self' localhost:* *.beatapps.net *.acuitykp-AcuityOneSS.com fast.fonts.net content.beatapps.net analytics.beat-services.net 'sha256-Du12GMF4ycv+l+g45Cwkso7O2vAnEBS/ht+UX4dsvMY='; style-src 'unsafe-inline' 'self' fast.fonts.net content.beatapps.net; style-src-elem 'unsafe-inline' 'self' fast.fonts.net content.beatapps.net; font-src 'self' data: fast.fonts.net ;"
    /> -->
    <meta name="referrer" content="origin" />
    <meta name="bingbot" content="noindex" />
    <meta name="googlebot" content="noindex" />
    <meta name="robots" content="noindex" />
    <title>AcuityOneSS</title>
    <meta name="msapplication-TileColor" content="#da532c" />
    <meta name="theme-color" content="#ffffff" />
    <meta name="application-name" content="AcuityOneSS" />
    <meta name="robots" content="noodp" />
    <!-- Enable cross-origin communication for iframe messaging -->
    <meta http-equiv="Cross-Origin-Opener-Policy" content="same-origin-allow-popups" />
    <link type="text/css"
          rel="stylesheet"
          href="https://fast.fonts.net/lt/1.css?apiType=css&amp;c=4a98d197-ca32-4925-b17b-27dcad2dbb8d&amp;fontids=5664067,5664070,5664077,5664081,5664085,5664089,5664093,5664098,5664103,5664107,5664111,5664115,5664119,5664121,5664128,5664150" />
    <script src="%PUBLIC_URL%/configurations.js"
            type="text/javascript"></script>
    <script>
        (function (apiKey) {
            (function (p, e, n, d, o) {
                var v, w, x, y, z;
                o = p[d] = p[d] || {};
                o._q = o._q || [];

                v = ["initialize", "identify", "updateOptions", "pageLoad", "track"];
                for (w = 0, x = v.length; w < x; ++w)
                    (function (m) {
                        o[m] =
                            o[m] ||
                            function () {
                                o._q[m === v[0] ? "unshift" : "push"](
                                    [m].concat([].slice.call(arguments, 0))
                                );
                            };
                    })(v[w]);

                y = e.createElement(n);
                y.async = !0;
                y.src =
                    "https://cdn.eu.pendo.io/agent/static/" + apiKey + "/pendo.js";

                z = e.getElementsByTagName(n)[0];
                z.parentNode.insertBefore(y, z);
            })(window, document, "script", "pendo");
        })("baadb5a6-c7fa-4574-41d2-1bca2c935ee9");
    </script>
    
    <!-- Message relay script to help with cross-window/iframe communication -->
    <script>
      // Set up a global message handler to ensure messages reach their destination
      window.addEventListener('message', function(event) {
        // Log all incoming messages
        console.log('Global message received:', event.data);
        
        // Store in sessionStorage as a fallback mechanism
        if (event.data && event.data.pdfModule) {
          try {
            sessionStorage.setItem('pdfModuleData', JSON.stringify(event.data));
            console.log('Stored PDF module data in sessionStorage');
          } catch (err) {
            console.error('Failed to store data in sessionStorage:', err);
          }
        }
        
        // Re-broadcast the message to ensure it reaches all components
        try {
          window.postMessage(event.data, '*');
          console.log('Re-broadcast message to window');
        } catch (err) {
          console.error('Error re-broadcasting message:', err);
        }
      });
    </script>
</head>
  <body>
    <style type="text/css">
      @import url("https://fast.fonts.net/lt/1.css?apiType=css&c=4a98d197-ca32-4925-b17b-27dcad2dbb8d&fontids=5664067,5664070,5664077,5664081,5664085,5664089,5664093,5664098,5664103,5664107,5664111,5664115,5664119,5664121,5664128,5664150");

      @font-face {
        font-family: "Helvetica Neue LT W05_36 Th It";
        src: url("Fonts/5664067/2a7e8f89-c0b2-4334-9c34-7a2078d2b959.woff2")
            format("woff2"),
          url("Fonts/5664067/32aad9d8-5fec-4b9d-ad53-4cf7a5b53698.woff")
            format("woff");
      }

      @font-face {
        font-family: "Helvetica Neue LT W05 25 Ult Lt";
        src: url("Fonts/5664070/ec6281a0-c9c4-4477-a360-156acd53093f.woff2")
            format("woff2"),
          url("Fonts/5664070/11066b40-10f7-4123-ba58-d9cbf5e89ceb.woff")
            format("woff");
      }

      @font-face {
        font-family: "Helvetica Neue LT W05 26UltLtIt";
        src: url("Fonts/5664077/2707a251-2d32-4bb6-a3c4-87114ba2365f.woff2")
            format("woff2"),
          url("Fonts/5664077/40f50724-486b-4e7b-9366-237e06eabfc8.woff")
            format("woff");
      }

      @font-face {
        font-family: "Helvetica Neue LT W05 35 Thin";
        src: url("Fonts/5664081/7d63ccf8-e0ae-4dee-ad4d-bbc798aa5803.woff2")
            format("woff2"),
          url("Fonts/5664081/b2c1327f-ab3d-4230-93d7-eee8596e1498.woff")
            format("woff");
      }

      @font-face {
        font-family: "Helvetica Neue LT W05 45 Light";
        src: url("Fonts/5664085/f9c5199e-a996-4c08-9042-1eb845bb7495.woff2")
            format("woff2"),
          url("Fonts/5664085/2a34f1f8-d701-4949-b12d-133c1c2636eb.woff")
            format("woff");
      }

      @font-face {
        font-family: "Helvetica Neue LT W05 46 Lt It";
        src: url("Fonts/5664089/5e4f385b-17ff-4d27-a63a-9ee28546c9a8.woff2")
            format("woff2"),
          url("Fonts/5664089/116cde47-4a07-44a5-9fac-cbdcc1f14f79.woff")
            format("woff");
      }

      @font-face {
        font-family: "Helvetica Neue LT W05 55 Roman";
        src: url("Fonts/5664093/08b57253-2e0d-4c12-9c57-107f6c67bc49.woff2")
            format("woff2"),
          url("Fonts/5664093/08edde9d-c27b-4731-a27f-d6cd9b01cd06.woff")
            format("woff");
      }

      @font-face {
        font-family: "Helvetica Neue LT W05 56 Italic";
        src: url("Fonts/5664098/4bd56f95-e7ab-4a32-91fd-b8704cbd38bc.woff2")
            format("woff2"),
          url("Fonts/5664098/4fe1c328-1f21-434a-8f0d-5e0cf6c70dfb.woff")
            format("woff");
      }

      @font-face {
        font-family: "Helvetica Neue LT W05 65 Medium";
        src: url("Fonts/5664103/240c57a0-fdce-440d-9ce3-85e0cb56f470.woff2")
            format("woff2"),
          url("Fonts/5664103/7802e576-2ffa-4f22-a409-534355fbea79.woff")
            format("woff");
      }

      @font-face {
        font-family: "Helvetica Neue LT W05 66 Md It";
        src: url("Fonts/5664107/de68be2a-5d0e-4b8d-b3eb-940f75503e2a.woff2")
            format("woff2"),
          url("Fonts/5664107/31029e78-79a0-4940-b82d-2e3c238e1355.woff")
            format("woff");
      }

      @font-face {
        font-family: "Helvetica Neue LT W05 76 Bd It";
        src: url("Fonts/5664111/13ab58b4-b5ba-4c95-afde-ab2608fbbbd9.woff2")
            format("woff2"),
          url("Fonts/5664111/5018b5b5-c821-4653-bc74-d0b11d735f1a.woff")
            format("woff");
      }

      @font-face {
        font-family: "Helvetica Neue LT W05 85 Heavy";
        src: url("Fonts/5664115/7e42a406-9133-48c0-a705-4264ac520b43.woff2")
            format("woff2"),
          url("Fonts/5664115/837750e9-3227-455d-a04e-dc76764aefcf.woff")
            format("woff");
      }

      @font-face {
        font-family: "Helvetica Neue LT W05 86 Hv It";
        src: url("Fonts/5664119/0acba88f-0de4-4d43-81fd-920d7427f665.woff2")
            format("woff2"),
          url("Fonts/5664119/713c9c40-9cbd-4276-819e-d0efaf5d3923.woff")
            format("woff");
      }

      @font-face {
        font-family: "Helvetica Neue LT W05 95 Black";
        src: url("Fonts/5664121/fc4fb6ca-f981-4115-b882-c78e9f08be52.woff2")
            format("woff2"),
          url("Fonts/5664121/6ed03453-f512-45ba-84bf-fe4ea45d5e6a.woff")
            format("woff");
      }

      @font-face {
        font-family: "Helvetica Neue LT W05 96 Blk It";
        src: url("Fonts/5664128/995add04-59cc-41ea-abd2-4712eaddf2a8.woff2")
            format("woff2"),
          url("Fonts/5664128/7090e465-f6bf-4664-8b5a-d877a6915d87.woff")
            format("woff");
      }

      @font-face {
        font-family: "Helvetica Neue LT W05 75 Bold";
        src: url("Fonts/5664150/800da3b0-675f-465f-892d-d76cecbdd5b1.woff2")
            format("woff2"),
          url("Fonts/5664150/7b415a05-784a-4a4c-8c94-67e9288312f5.woff")
            format("woff");
      }
    </style>

    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="app"></div>
  </body>
</html>
