import React from "react";
import PropTypes from "prop-types";
import { Tooltip } from "@progress/kendo-react-tooltip";
const ProgressBar = ({ percentage }) => {
  return (
    <Tooltip
      className="body-r"
      openDelay={100}
      position="bottom"
      anchorElement="target"
    >
      <div
        title={"Relevance- " + percentage + "%"}
        className="items-left flex h-2 w-20 items-center gap-2 rounded-lg border-2 border-neutral-5 bg-neutral-5"
      >
        <div
          title={"Relevance- " + percentage + "%"}
          data-testid={"progress-bar"}
          style={{
            width: percentage + "%"
          }}
          className={`h-1 rounded bg-primary-78`}
        ></div>
      </div>
    </Tooltip>
  );
};

export default ProgressBar;

ProgressBar.propTypes = {
  percentage: PropTypes.number
};
