import { post } from "../../../general/fetcher";


export const UPLOAD_API_URL = 'api/ai/feedback/spread/mapping';
const As_Reported = "As Reported";
export const Extract = ({
  job_id,
  country,
  client_id,
  company_id,
  fs_type,
  industry,
  currency,
  mappedData,
  extractionType,
  client_env
}) => {
  // Normalize the extraction type for more robust comparison
  const normalizedExtractionType = extractionType?.trim()?.toLowerCase();
  const normalizedAsReported = As_Reported.toLowerCase();

  const body = {
    version: "0.1.0",
    job_id: job_id,
    country: country,
    client_id: client_env,
    company_id: company_id,
    fs_type: fs_type,
    industry: industry,
    currency: currency,
    template_id:"",
    data: mappedData
  };

  return post(UPLOAD_API_URL, body);
};
