import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";
import PublicFillings from "./public-filings";
import { FILINGS_API } from "../../../infra/api/filings/get-filing-service";

jest.mock("../../../infra/api/filings/get-filing-service", () => ({
  FILINGS_API: jest.fn()
}));

jest.mock("../../dropdown", () => () => <div>Dropdown Component</div>);
jest.mock(
  "../../../partials/molecules/tabs-filled-group/tabs-filled-group",
  () => () => <div>TabsFilledGroup Component</div>
);
jest.mock("../../chip", () => ({ text, onClick, isSelected }) => (
  <div onClick={onClick} data-selected={isSelected}>
    {text}
  </div>
));

describe("PublicFillings Component", () => {
  const mockFilingsData = {
    data: {
      data: [
        {
          acuity_id: "USPU24904409",
          filing_date: "2024-07-30",
          doc: { url: "url1", type: "type1" },
          filing_type: "10-K"
        },
        {
          acuity_id: "USPU24904409",
          filing_date: "2025-01-29",
          doc: { url: "url2", type: "type2" },
          filing_type: "10-Q"
        }
      ]
    }
  };

  beforeEach(() => {
    FILINGS_API.mockReturnValue(mockFilingsData);
  });

  test("renders PublicFillings component", () => {
    render(
      <PublicFillings
        selectedChipsCount={5}
        selectedCompanyId="USPU24904409"
        onSelectedChipsChange={jest.fn()}
      />
    );
    expect(screen.getByText("Public Filling(s)")).toBeInTheDocument();
    expect(screen.getByText("Dropdown Component")).toBeInTheDocument();
    expect(screen.getByText("TabsFilledGroup Component")).toBeInTheDocument();
  });

  test("toggles collapse state", async () => {
    render(
      <PublicFillings
        selectedChipsCount={5}
        selectedCompanyId="USPU24904409"
        onSelectedChipsChange={jest.fn()}
      />
    );
    const toggleButton = screen.getByRole("button");
    fireEvent.click(toggleButton);
    await waitFor(() =>
      expect(screen.getByTestId("expand-more-icon")).toBeInTheDocument()
    );

    fireEvent.click(toggleButton);
    await waitFor(() =>
      expect(screen.getByTestId("expand-less-icon")).toBeInTheDocument()
    );
  });

  test("displays selected chips count", () => {
    render(
      <PublicFillings
        selectedChipsCount={5}
        selectedCompanyId="USPU24904409"
        onSelectedChipsChange={jest.fn()}
      />
    );
    expect(
      screen.getByText((content, element) => content.includes("5 selected"))
    ).toBeInTheDocument();
  });

  test("handles chip selection and deselection", () => {
    const handleSelectedChipsChange = jest.fn();
    render(
      <PublicFillings
        selectedChipsCount={5}
        selectedCompanyId="USPU24904409"
        onSelectedChipsChange={handleSelectedChipsChange}
      />
    );
    const chip = screen.getByText("30 Jul 2024");
    fireEvent.click(chip);
    expect(handleSelectedChipsChange).toHaveBeenCalledWith(1);
    fireEvent.click(chip);
    expect(handleSelectedChipsChange).toHaveBeenCalledWith(0);
  });

  test("displays no data found state", () => {
    FILINGS_API.mockReturnValueOnce({ data: { data: [] } });
    render(
      <PublicFillings
        selectedChipsCount={5}
        selectedCompanyId="USPU24904409"
        onSelectedChipsChange={jest.fn()}
      />
    );
    expect(screen.getByText("No data found!")).toBeInTheDocument();
  });
});
