import React from "react";
import { render, fireEvent } from "@testing-library/react";
import DocumentViewerPDFToolBar from "./document-viewer-pdf-toolbar";

describe("DocumentViewerPDFToolBar", () => {
  const mockSetSearchQuery = jest.fn();
  const mockSetScrollToPage = jest.fn();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders the component with required props", () => {
    const { getByPlaceholderText, getByText } = render(
      <DocumentViewerPDFToolBar
        searchQuery=""
        readOnly={false}
        setSearchQuery={mockSetSearchQuery}
        pageCount={10}
        currentPage={1}
        setScrollToPage={mockSetScrollToPage}
      />
    );

    expect(getByPlaceholderText("Search here...")).toBeInTheDocument();
    expect(getByText("Page 1 of 10")).toBeInTheDocument();
  });

  it("handles search input changes", () => {
    const { getByPlaceholderText } = render(
      <DocumentViewerPDFToolBar
        searchQuery=""
        readOnly={false}
        setSearchQuery={mockSetSearchQuery}
        pageCount={10}
        currentPage={1}
        setScrollToPage={mockSetScrollToPage}
      />
    );

    const searchInput = getByPlaceholderText("Search here...");
    fireEvent.change(searchInput, { target: { value: "test" } });

    expect(mockSetSearchQuery).toHaveBeenCalledWith("test");
  });

  it("handles search clear button click", () => {
    const { getByTestId } = render(
      <DocumentViewerPDFToolBar
        searchQuery="test"
        readOnly={false}
        setSearchQuery={mockSetSearchQuery}
        pageCount={10}
        currentPage={1}
        setScrollToPage={mockSetScrollToPage}
      />
    );

    const clearButton = getByTestId("document-viewer-pdf-toolbar-search-close");
    fireEvent.click(clearButton);

    expect(mockSetSearchQuery).toHaveBeenCalledWith("");
  });

  it("navigates to the first page", () => {
    const { getByTestId } = render(
      <DocumentViewerPDFToolBar
        searchQuery=""
        readOnly={false}
        setSearchQuery={mockSetSearchQuery}
        pageCount={10}
        currentPage={2}
        setScrollToPage={mockSetScrollToPage}
      />
    );

    const firstPageButton = getByTestId(
      "document-viewer-pdf-toolbar-first-page"
    );
    fireEvent.click(firstPageButton);

    expect(mockSetScrollToPage).toHaveBeenCalledWith(1);
  });

  it("navigates to the previous page", () => {
    const { getByTestId } = render(
      <DocumentViewerPDFToolBar
        searchQuery=""
        readOnly={false}
        setSearchQuery={mockSetSearchQuery}
        pageCount={10}
        currentPage={2}
        setScrollToPage={mockSetScrollToPage}
      />
    );

    const previousPageButton = getByTestId(
      "document-viewer-pdf-toolbar-previous-page"
    );
    fireEvent.click(previousPageButton);

    expect(mockSetScrollToPage).toHaveBeenCalledWith(1);
  });

  it("navigates to the next page", () => {
    const { getByTestId } = render(
      <DocumentViewerPDFToolBar
        searchQuery=""
        readOnly={false}
        setSearchQuery={mockSetSearchQuery}
        pageCount={10}
        currentPage={1}
        setScrollToPage={mockSetScrollToPage}
      />
    );

    const nextPageButton = getByTestId("document-viewer-pdf-toolbar-next-page");
    fireEvent.click(nextPageButton);

    expect(mockSetScrollToPage).toHaveBeenCalledWith(2);
  });

  it("navigates to the last page", () => {
    const { getByTestId } = render(
      <DocumentViewerPDFToolBar
        searchQuery=""
        readOnly={false}
        setSearchQuery={mockSetSearchQuery}
        pageCount={10}
        currentPage={1}
        setScrollToPage={mockSetScrollToPage}
      />
    );

    const lastPageButton = getByTestId("document-viewer-pdf-toolbar-last-page");
    fireEvent.click(lastPageButton);

    expect(mockSetScrollToPage).toHaveBeenCalledWith(10);
  });
});
