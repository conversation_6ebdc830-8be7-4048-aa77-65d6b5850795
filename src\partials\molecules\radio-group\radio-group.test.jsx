import { render, screen, fireEvent } from "@testing-library/react";
import RadioGroup from "./radio-group";
import React from "react";

describe("RadioGroup component", () => {
  it("should render RadioGroup component", () => {
    render(<RadioGroup />);
    expect(screen.getByTestId("radio-group-button")).toBeInTheDocument();
    //click on button
    fireEvent.click(screen.getByTestId("radio-group-button"));

    //get the list
    const dropdown = screen.getByTestId("radio-group-list");
    //focus the dropdown
    dropdown.focus();
    expect(dropdown.onfocus);
    dropdown.blur();
    expect(dropdown.onfocus).toBeFalsy();

    //keydown
    fireEvent.click(screen.getByTestId("radio-group-button"));
    fireEvent.keyDown(screen.getByTestId("radio-group-button"));
  });
});
