import React, { cloneElement, useState } from "react";
import PropTypes from "prop-types";
import { ComboBox } from "@progress/kendo-react-dropdowns";
import { filterBy } from "@progress/kendo-data-query";
import { Button } from "../../partials/atoms/button";
import { FiPlus, FiPercent, FiType } from "react-icons/fi";
import { Bs123, BsCash, BsAsterisk } from "react-icons/bs";
import { KPI_INFO_CURRENCY, KPI_INFO_MULTIPLE, KPI_INFO_NUMBER, KPI_INFO_PERCENTAGE, KPI_INFO_TEXT } from "../../constants/kpi-info";

/**
 * Gets the KPI indicator icon based on the KPI type
 * @param {string} kpiInfo - The KPI information
 * @returns {React.ReactElement|null} - The KPI indicator icon or null if no match
 */
const getKpiIndicator = (kpiInfo) => {
  if (!kpiInfo) return null;
  
  switch (kpiInfo) {
    case KPI_INFO_PERCENTAGE:
      return <FiPercent title="Percentage"/>;
    case KPI_INFO_TEXT:
      return <FiType title="Text"/>;
    case KPI_INFO_NUMBER:
      return <Bs123 title="Number"/>;
    case KPI_INFO_CURRENCY:
      return <BsCash title="Currency"/>;
    case KPI_INFO_MULTIPLE:
      return <BsAsterisk title="Multiple"/>;
    default:
      return null;
  }
};

const StatementDropdown = ({
  isMapped,
  highlightText,
  dropdownData,
  comboProps,
  handleStatementChange,
  searchQuery,
  selectedTab
}) => {
  const [data, setData] = useState(dropdownData);
  let initialValue = dropdownData.find((x) => x.mappingId === comboProps.dataItem.mappingId);
  if(!initialValue || initialValue === undefined){
    initialValue = dropdownData.find((x) => x.stext === comboProps.dataItem.status);
  }
  const [value, setValue] = useState(initialValue);
  const [inputValue, setInputValue] = useState(initialValue?.stext || "");
  // When the dropdown loses focus
  const handleBlur = () => {
    // Check if the current input value matches any item in the dropdown
    const matchingItem = dropdownData.find(item => item.stext === inputValue);
    
    if (!matchingItem) {
      // If no match found, restore the previous valid value
      setValue(value);
      setInputValue(value?.stext || "");
    }
  };

  return (
    <div className="flex items-center w-full relative">
    <ComboBox
      disabled={isMapped}
      clearButton={false}      
      filterable={true}
      onFilterChange={(e) => {
        setData(filterBy(dropdownData.slice(), e.filter));
        setInputValue(e.filter);
      }}
      placeholder={"Select the line item to map"}
      data={data}
      textField="stext"
      groupField="parent_text"
      value={value}
      onBlur={handleBlur}
      className="statementDropdown w-full"
      popupSettings={{
        className:
          "box-border flex flex-col pt-[8px] pb-[8px] px-0 bg-white border border-[#e6e6e6] shadow-md rounded-[8px] max-h-[300px] overflow-y-auto"
      }}      
	  valueRender={(element, text) => {        
        if (searchQuery && value && value.stext && value.stext?.trim() !== "") {
          const itemChildren = (
            <span className="w-full overflow-hidden text-ellipsis whitespace-nowrap pr-8">
              {highlightText(value.stext, searchQuery)}
            </span>
          );
          return itemChildren;
        }
        return <span className="w-full overflow-hidden text-ellipsis whitespace-nowrap pr-8">{element}</span>;
      }}
      itemRender={(li, liProps) => {
        const isSelected = liProps.selected;
        const itemChildren =
          liProps.dataItem && liProps.dataItem !== "" ? (
            <div
              className={`box-border flex flex-row cursor-pointer items-center px-4 py-2 gap-2 w-full h-8 border-[#e6e6e6] flex-none self-stretch z-[1] ${
                isSelected ? "bg-[#4061c7]" : " bg-white hover:bg-[#F5F9FF]"
              }`}
            >
              <span
                className={`w-full h-4 font-normal text-[12px] leading-4 flex-none whitespace-nowrap overflow-hidden text-ellipsis text-left ${
                  isSelected ? "text-white" : "hover:text-[#1a1a1a]"
                }`}
                title={liProps.dataItem.stext} // Add tooltip on hover
              >
                {liProps.dataItem.parentId > 0 
                  ? '\u00A0\u00A0\u00A0\u00A0' + liProps.dataItem.stext 
                  : liProps.dataItem.stext}
              </span>
            </div>
          ) : null;
        return cloneElement(li, li.props, itemChildren);
      }}      
	  onChange={(e) => {
        if (e.target.value) {
          setValue(e.target.value);
          setInputValue(e.target.value.stext || "");
          handleStatementChange(e, comboProps, selectedTab);
        } else if (e.nativeEvent) {
          // This is a user typing event, update only the input value
          setInputValue(e.target.value || "");
        } else {
          // Handle clearing the combo, restore previous value
          setValue(value);
          setInputValue(value?.stext || "");
        }
      }}
    />    
    <span className="absolute left-[14rem] top-1/2 transform -translate-y-1/2 z-10">      
      {value && (() => {
        const kpiInfo = value.kpiInfo || "#";
        const kpiIndicator = getKpiIndicator(kpiInfo);
        return (
          kpiIndicator && (
            <span className="text-neutral-60 flex-shrink-0">
              {kpiIndicator}
            </span>
          )
        );
      })()}
    </span>
    </div>
  );
};

export default StatementDropdown;

StatementDropdown.propTypes = {
  isMapped: PropTypes.bool,
  highlightText: PropTypes.func,
  dropdownData: PropTypes.array,
  comboProps: PropTypes.object,
  handleStatementChange: PropTypes.func,
  searchQuery: PropTypes.string,
  selectedTab: PropTypes.number
};