.ao-custom-upload-area {
  border: none !important;
}

.ao-custom-upload-area .k-dropzone {
  border: 0.125rem dotted #4061c7;
  border-radius: 0.5rem;
  direction: rtl;
  padding: 1.25rem;
  background-color: #ffffff;
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}

.ao-custom-upload-preview-box .k-file {
  padding: 0 !important;
}

.ao-custom-upload-file .k-upload-files {
  border-bottom: 1px solid #e6e6e6 !important;
  margin-top: 0.5rem;
}

.ao-custom-select-file .k-upload-button-wrap {
  border: 1px solid #93b0ed;
  border-radius: 0.25rem;
}

.ao-custom-select-file .k-upload-button-wrap:active {
  border-color: #152b7a;
  border-radius: 0.25rem;
}

.ao-custom-button-background .k-button {
  background-color: #ffffff;
  border: none !important;
}

.ao-custom-button-background .k-button:active {
  background: #c4d9ff;
}
.ao-custom-button-background .k-button:hover {
  background: #ebf3ff;
}

.ao-custom-button-text-background .k-button-text {
  color: #4061c7;
}

.ao-custom-button-text-background .k-button-text:active {
  color: #152b7a;
}

.ao-custom-icon .k-icon {
  display: none;
}

.ao-custom-icon .k-icon {
  display: none;
}

.k-disabled.ao-disabled-datepicker {
  background-color: #f2f2f2 !important;
}

/* Target the inner elements of the disabled DatePicker */
.k-disabled.ao-disabled-datepicker .k-dateinput-wrap,
.k-disabled.ao-disabled-datepicker .k-dateinput {
  background-color: #f2f2f2 !important;
}

/* Ensure placeholder is hidden in all cases */
.k-disabled.ao-disabled-datepicker input::placeholder {
  color: transparent !important;
}

.k-disabled.ao-disabled-datepicker
  .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-input-button,
.k-disabled
  .k-button.k-button-md.k-button-solid.k-button-solid-base.k-icon-button.k-input-button {
  background-color: #f2f2f2 !important;
  border-color: #cccccc !important;
}

/* Target the icon inside the disabled button to ensure it has proper contrast */
.k-disabled.ao-disabled-datepicker .k-button.k-input-button .k-icon,
.k-disabled .k-button.k-input-button .k-icon {
  color: #666666 !important;
}

/* Remove any hover effects on the disabled button */
.k-disabled.ao-disabled-datepicker .k-button.k-input-button:hover,
.k-disabled .k-button.k-input-button:hover {
  background-color: #f2f2f2 !important;
  border-color: #cccccc !important;
}

.k-link:hover {
  color: #fff;
}

.k-calendar .k-calendar-td:hover .k-link {
  background-color: #EBF3FF;
}

.k-calendar .k-calendar-td.k-selected .k-link {
  background-color: #4061C7;
}
.k-calendar .k-calendar-td.k-selected:hover .k-link {
  background-color: #4061C7;
}

.k-calendar .k-calendar-view .k-today {
  color: #4061C7;
}

.k-calendar .k-calendar-navigation .k-reset:hover {
  color: #1A1A1A;
}

.k-calendar .k-calendar-navigation li:hover,
.k-calendar .k-calendar-navigation li.k-hover {
  background-color: #EBF3FF !important;
  color: #1A1A1A;
}

.k-calendar .k-button-text {
  color: #4061C7;
}

/* Ensure hover states maintain the proper color */
.k-calendar .k-button:hover .k-button-text {
  color: #4061C7;
}

/* For selected/active state */
.k-calendar .k-button.k-state-selected .k-button-text,
.k-calendar .k-button:active .k-button-text {
  color: #4061C7;
}

.file-selection-disabled .k-upload-button {
  pointer-events: none;
  opacity: 0.5;
  cursor: not-allowed;
}

.file-selection-disabled .k-dropzone {
  pointer-events: none;
  opacity: 0.5;
  background-color: rgba(0, 0, 0, 0.05);
}

/* Keep the file list items interactive */
.file-selection-disabled .k-file {
  pointer-events: auto;
  opacity: 1;
}

/* Dynamic z-index utility with arbitrary values */
[class^="z-"], [class*=" z-"] {
  z-index: var(--z-index);
}

.z-10 { --z-index: 10; }
.z-20 { --z-index: 20; }
.z-30 { --z-index: 30; }
.z-40 { --z-index: 40; }
.z-50 { --z-index: 50; }
.z-60 { --z-index: 60; }
.z-70 { --z-index: 70; }
.z-80 { --z-index: 80; }
.z-90 { --z-index: 90; }
.z-99 { --z-index: 99; }
.z-100 { --z-index: 100; }
.z-auto { z-index: auto; }
.z-0 { --z-index: 0; }
.z-negative-1 { --z-index: -1; }

.currency-radius{
  border-radius: 0.375rem 0 0 0.375rem;
}
.currency-unit-radius{
  border-radius: 0 0.375rem 0.375rem 0;
}