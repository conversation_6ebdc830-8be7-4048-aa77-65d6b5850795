import React from "react";
import PropTypes from "prop-types";
import EntityLogo from "../../partials/molecules/entity-logo/entity-logo";
import StatusBadge from "./StatusBadge";
import { FiCopy } from "react-icons/fi";
import { notify } from "../../partials/molecules/toaster";

const CompanyCard = ({ company, isSelected, onClick }) => {
  const statusClass =
    company.status === "Failed" || company.status === "In Progress"
      ? "bg-zinc-50"
      : "bg-white hover:bg-primary-35";
  //   const selectedClass = isSelected && "bg-[#E3EEFF]";
  const formatDate = (timestamp) => {
    const date = new Date(timestamp);
    const userTimezoneOffset = date.getTimezoneOffset() * 60000;
    const localDate = new Date(date.getTime() - userTimezoneOffset);

    const options = {
      month: "short",
      day: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true
    };

    const formatter = new Intl.DateTimeFormat("en-US", options);
    const parts = formatter.formatToParts(localDate);

    const month = parts.find((part) => part.type === "month").value;
    const day = parts.find((part) => part.type === "day").value;
    const year = parts.find((part) => part.type === "year").value;
    const hour = parts.find((part) => part.type === "hour").value;
    const minute = parts.find((part) => part.type === "minute").value;
    const dayPeriod = parts.find((part) => part.type === "dayPeriod").value;

    return `${month} ${day}, ${year} | ${hour}:${minute} ${dayPeriod}`;
  };

  return (
    <div
      key={company.processId}
      className={`h-30 border border-neutral-10 rounded-lg p-5 m-4 relative mx-20 ${statusClass}`}
      onClick={() => onClick(company.processId)}
    >
      <div className="flex h-7 justify-start gap-2">
        <div
          className="body-m h-7 inline-flex w-fit rounded-full border px-3 py-1 text-neutral-80 bg-white"
          title={`${company.jobId}`}
          onClick={(e) => {
            e.stopPropagation();
            navigator.clipboard.writeText(company.jobId)
              .then(() => {
                notify.success("Job ID copied successfully.");
              })
              .catch(() => {
                notify.error("Failed to copy Job ID. Please try again.");
              });
          }}
        >
          <span className="mr-[8px]">ID</span>
          <div className="flex items-center gap-1">
            <FiCopy
              className="cursor-pointer"
              // title="Copy Job ID"
              size={14}
            />
          </div>
        </div>
        <div className="body-m h-7 w-fit rounded-full border px-3 py-1 text-neutral-80 bg-white">
          {company.extractionType}
        </div>
      </div>
      <div className="flex items-center mt-5">
        <div className="flex items-center">
          <div className="pr-3">
            <EntityLogo
              acuityID={company.acuityId}
              companyName={company.name}
              logoSize="medium"
            />
          </div>
          {company?.ticker && company.ticker.trim() !== "" && (
            <>
              <p className="heading-2-m text-neutral-80">{company.ticker}</p>
              <div className="border-l border-neutral-10 h-6 mx-4 border-dashed"></div>
            </>
          )}
          <h2 className="heading-2-m text-neutral-80">{company.name}</h2>
        </div>
      </div>
      <div className="absolute right-4 top-4 flex items-center gap-2">
        <p className="caption-i text-neutral-60">
          Created on: {formatDate(company.createdOn)}
        </p>
        <StatusBadge status={company.status} />
      </div>
    </div>
  );
};

CompanyCard.propTypes = {
  company: PropTypes.shape({
    processId: PropTypes.string.isRequired,
    jobId: PropTypes.string.isRequired,
    extractionType: PropTypes.string.isRequired,
    acuityId: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    ticker: PropTypes.string.isRequired,
    createdOn: PropTypes.string.isRequired,
    status: PropTypes.string.isRequired
  }).isRequired,
  isSelected: PropTypes.bool.isRequired,
  onClick: PropTypes.func.isRequired
};

export default CompanyCard;
