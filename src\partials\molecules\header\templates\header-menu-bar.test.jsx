import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { BrowserRouter as Router } from "react-router-dom";
import HeaderMenuBar from "./header-menu-bar";

jest.mock("../../../atoms/nav-link", () => ({
  NavLinkComponent: ({ text, to }) => <div>{text}</div>
}));

jest.mock("../../drop-down", () => ({
  Dropdown: ({ children, dropdownOpen }) =>
    dropdownOpen ? <div>{children}</div> : null
}));

describe("HeaderMenuBar", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders without crashing", () => {
    render(
      <Router>
        <HeaderMenuBar />
      </Router>
    );
    expect(screen.getByText("My Workspace")).toBeInTheDocument();
    expect(screen.getByText("Screener")).toBeInTheDocument();
    expect(screen.getByText("AI Playground")).toBeInTheDocument();
    expect(screen.getByText("More")).toBeInTheDocument();
  });

  it('opens dropdown when "More" is clicked', () => {
    render(
      <Router>
        <HeaderMenuBar />
      </Router>
    );
    fireEvent.click(screen.getByText("More"));
    expect(screen.getByText("My Highlights")).toBeInTheDocument();
    expect(screen.getByText("Manage Alerts")).toBeInTheDocument();
  });

  it("closes dropdown when a dropdown item is clicked", () => {
    render(
      <Router>
        <HeaderMenuBar />
      </Router>
    );
    fireEvent.click(screen.getByText("More"));
    fireEvent.click(screen.getByText("My Highlights"));
    expect(screen.queryByText("My Highlights")).not.toBeInTheDocument();
    expect(screen.queryByText("Manage Alerts")).not.toBeInTheDocument();
  });

  it("handles keyboard events to open dropdown", () => {
    render(
      <Router>
        <HeaderMenuBar />
      </Router>
    );
    fireEvent.keyDown(screen.getByText("More"), {
      key: "Enter",
      code: "Enter"
    });
    expect(screen.getByText("My Highlights")).toBeInTheDocument();
    fireEvent.keyDown(screen.getByText("More"), { key: " ", code: "Space" });
    expect(screen.getByText("My Highlights")).toBeInTheDocument();
  });

  it("does not open dropdown when other menu items are clicked", () => {
    render(
      <Router>
        <HeaderMenuBar />
      </Router>
    );
    fireEvent.click(screen.getByText("My Workspace"));
    expect(screen.queryByText("My Highlights")).not.toBeInTheDocument();
    fireEvent.click(screen.getByText("Screener"));
    expect(screen.queryByText("My Highlights")).not.toBeInTheDocument();
    fireEvent.click(screen.getByText("AI Playground"));
    expect(screen.queryByText("My Highlights")).not.toBeInTheDocument();
  });

  it("renders correctly with different props", () => {
    const { rerender } = render(
      <Router>
        <HeaderMenuBar someProp="value1" />
      </Router>
    );
    expect(screen.getByText("My Workspace")).toBeInTheDocument();

    rerender(
      <Router>
        <HeaderMenuBar someProp="value2" />
      </Router>
    );
    expect(screen.getByText("My Workspace")).toBeInTheDocument();
  });
});
