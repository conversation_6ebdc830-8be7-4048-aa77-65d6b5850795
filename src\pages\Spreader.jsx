import React, { useState, useRef } from "react";
import SearchCompany from "../components/Search/search-company";
import { EvidencePanel } from "../components/evidence-panel/evidence-panel";
import EmptyCompanyData from "../resources/images/Illustrations.svg";
import FinancialDocument from "../components/cards/financial-document-source/financial-document";
import SpreaderHistory from "./spreader-history/spreader-history";
import { notify } from "../partials/molecules/toaster";
import Template from "../components/Template/template-selection";
import { DropdownItemsEnum } from "../components/Template/dropdown-list";
import { GET_SPREADER_HISTORY_API } from "../infra/api/SpreaderHistory/spreader-history-service";
import { v4 as uuidv4 } from "uuid";
import { UploadFileToAlexanderia } from "../infra/api/alexanderia/upload-service";
import { ADD_SPREAD_DETAILS_SERVICE } from "../infra/api/company/add-spread-details-service";
import { ADD_JOB_DETAILS_SERVICE } from "../infra/api/company/add-job-details-service";
import { Extract } from "../infra/api/alexanderia/extract-service";
import HeaderAction from "../Header/header-action";

const Spreader = () => {
  const [showExtract, setShowExtract] = useState(false);
  const [showHistory, setShowHistory] = useState(false);
  const [isHistoryLoading, setIsHistoryLoading] = useState(false);
  const [showEvidence, setShowEvidence] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState(null);
  const [companyID, setCompanyID] = useState(null);
  const [processID, setProcessID] = useState(null);
  const [showTest, setShowTest] = useState(false);
  const [hideHeader, setHideHeader] = useState(false);
  const [removePadding, setRemovePadding] = useState(false);
  const [removeTopMargin, setRemoveTopMargin] = useState(false);
  const [selectedTab, setSelectedTab] = useState(0);
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedFilingsCount, setSelectedFilingsCount] = useState(0);
  const [selectedTemplateType, setSelectedTemplateType] = useState(
    DropdownItemsEnum.CHOOSE_TEMPLATES
  );
  const [selectedTemplateOption, setSelectedTemplateOption] = useState(
    DropdownItemsEnum.TEMPLATE_BASED
  );
  const [spreaderHistoryData, setSpreaderHistoryData] = useState(null);
  const [selectedProcessId, setSelectedProcessId] = useState(null);
  const [isExtracting, setIsExtracting] = useState(false);
  const [extractionType, setExtractionType] = useState("");

  const templateRef = useRef(null);
  const financialcompanyref = useRef(null);
  const publicfilingsref = useRef(null);
  const uploadFilesref = useRef(null);
  const [fileDetailsForAlexanderiaUpload, setFileDetailsForAlexanderiaUpload] =
    useState(() => []);
  const [uploadDocumentDetails, setUploadDocumentDetails] = useState(() => []);

  const handleCompanySelect = (company) => {
    setCompanyID(uuidv4());
    setProcessID(uuidv4());
    if (selectedCompany?.acuity_id !== company.acuity_id) {
      setShowExtract(false);
      setShowHistory(false);
      if (templateRef.current) {
        templateRef.current.resetTemplateState();
      }
      if (financialcompanyref.current) {
        financialcompanyref.current.resetfinancialcompanyState();
      }
      if (publicfilingsref.current) {
        publicfilingsref.current.resetpublicfilingsrefState();
      }
      if (uploadFilesref.current) {
        uploadFilesref.current.resetuploadFilesrefState();
      }
    }
    setSelectedCompany(company);
  };

  const prepareAlexanderiaUploadDetails = async () => {
    try {
      setIsExtracting(true); // Set loading state at the start
      let files = [];
      let filteredData = fileDetailsForAlexanderiaUpload.filter(
        (file) => file.isSelected
      );
      let detailedData = filteredData.map((file) => ({
        file_name: `${file.generatedDocId}.pdf`,
        source: "upload",
        s3_path: file.s3Url
      }));

      files = [...detailedData];
      const uploadDetails = uploadDocumentDetails.map((file) => ({
        file_name: `${file.documentId}.pdf`,
        source: "upload",
        s3_path: file.url
      }));

      if (uploadDetails.length > 0) {
        files = [...files, ...uploadDetails];
      }

      // Upload files to Alexanderia
      const uploadResponse = await UploadFileToAlexanderia({ files });

      if (!uploadResponse?.files) {
        throw new Error("Upload failed");
      }

      // Extract data
      const extractResponse = await Extract({
        files: uploadResponse.files,
        isAsReported: selectedTemplateOption === DropdownItemsEnum.AS_REPORTED,
        isTemplate: selectedTemplateOption === DropdownItemsEnum.TEMPLATE_BASED,
        company_ticker: selectedCompany?.primary_ticker || "",
        company_id: selectedCompany?.acuity_id,
        company_name: selectedCompany?.name
      });

      if (extractResponse && !extractResponse?.isError) {
        // Add spread and job details
        await Promise.all([
          ADD_SPREAD_DETAILS_SERVICE({
            processId: processID,
            jobId: extractResponse.job_id,
            acuityId: selectedCompany?.acuity_id,
            companyID,
            companyName: selectedCompany?.name,
            country: selectedCompany?.headquarter?.country?.[0]?.value || "NA",
            sector: selectedCompany?.sector?.[0]?.value || "NA",
            isPublic: selectedCompany?.is_public,
            primaryExchange: selectedCompany?.primary_exchange,
            primaryTicker: selectedCompany?.primary_ticker,
            isTemplate:
              selectedTemplateOption === DropdownItemsEnum.TEMPLATE_BASED
          }),
          ADD_JOB_DETAILS_SERVICE({
            companyID,
            jobId: extractResponse.job_id,
            processId: processID
          })
        ]);
        await handleHistoryVisibility(true);
      } else {
        throw new Error("Extraction failed");
      }
    } catch (error) {
      notify.error("Extraction process failed");
      setShowHistory(false);
    } finally {
      setIsExtracting(false); // Reset loading state
      setSelectedCompany(null);
    }
  };

  const handleExtractVisibility = async () => {
    await prepareAlexanderiaUploadDetails();
  };

  const handleHistoryVisibility = async (banner = false) => {
    try {
      const response = await GET_SPREADER_HISTORY_API(0);
      if (response) {
        setSpreaderHistoryData(response);
        setShowHistory(true);
        setShowExtract(false);
        if (banner)
          notify.info(
            "Extraction queued! Return to this page later to see the status of your request."
          );
      }
    } catch (error) {
      notify.error("Failed to fetch history data");
    }
  };

  const handleBackButton = (value) => {
    setShowHistory(false);
    setShowExtract(false);
    setSelectedCompany(null);
    if (value) {
      // Additional logic if needed
    }
  };
  const handleBackfunc = () => {
    setShowHistory(true);
    setShowExtract(false);
    setShowTest(false);
    setHideHeader(false);
    setRemovePadding(false);
    setRemoveTopMargin(false);
    setShowEvidence(false);
    setSelectedTab(0);
    setSearchQuery("");
  };
  const handleTestClick = () => {
    setShowTest(true);
    setShowExtract(false);
    setShowHistory(false);
  };

  const handleHistoryCardSelect = (company) => {
    setShowEvidence(true);
    setShowExtract(true);
    setRemovePadding(true);
    setRemoveTopMargin(true);
    setSearchQuery("");
    // Pass processId to EvidencePanel through props
    setSelectedProcessId(company.processId);
    setExtractionType(company.extractionType);
    setSelectedCompany({
      name: company.name,
      ticker: company.ticker,
      acuityid: company.acuityId
    });
  };

  const handleOptionChange = (option) => {
    setSelectedTemplateOption(option);
  };

  return (
    <div className="w-full">
      {/* Header with border below */}
      <div className="border-b border-[#EDEDF2]">
        {!showTest && !hideHeader && !showEvidence && (
          <HeaderAction
            showHistory={showHistory}
            handleBackButton={handleBackButton}
            handleHistoryVisibility={handleHistoryVisibility}
            handleExtractVisibility={handleExtractVisibility}
            handleTestClick={handleTestClick}
            selectedCompany={selectedCompany}
            selectedFilingsCount={selectedFilingsCount}
            selectedTemplateOption={selectedTemplateOption}
            selectedTemplateType={selectedTemplateType}
            isHistoryLoading={isHistoryLoading}
            isExtracting={isExtracting}
          />
        )}
      </div>

      {showHistory && !showEvidence && (
        <div className="h-[calc(95vh-7rem)] overflow-y-auto">
          <SpreaderHistory
            onCardSelect={(company) => handleHistoryCardSelect(company)}
            historyData={spreaderHistoryData}
          />
        </div>
      )}

      {(showExtract || showEvidence) && (
        <div
          className={`${removePadding ? "pb-6 pl-4" : "p-6"} ${
            removeTopMargin ? "mt-0" : ""
          }`}
        >
          <EvidencePanel
            handleBackfunc={handleBackfunc}
            processId={selectedProcessId}
            companyName={selectedCompany?.name}
            companyTicker={selectedCompany?.ticker}
            extractionType={extractionType}
          />
        </div>
      )}
      {showTest && (
        <EvidencePanel
          handleBackfunc={handleBackfunc}
          processId={selectedProcessId}
          companyName={selectedCompany?.name}
          companyTicker={selectedCompany?.ticker}
          extractionType={extractionType}
        />
      )}
      {!showExtract && !showEvidence && !showTest && !showHistory && (
        <div className="px-6 xl:px-14 custom-padding-1366 custom-padding-body-1440 custom-padding-body-1920">
          <SearchCompany onCompanySelect={handleCompanySelect} />
          {selectedCompany ? (
            <div className="grid grid-cols-2 gap-6 py-4 ">
              <div className="space-y-4">
                <FinancialDocument
                  ref={financialcompanyref}
                  publicfilingsref={publicfilingsref}
                  uploadFilesref={uploadFilesref}
                  selectedCompanyDetails={selectedCompany}
                  onCompanySelect={handleCompanySelect}
                  companyID={companyID}
                  processID={processID}
                  alexnaderiaUploadDetails={setFileDetailsForAlexanderiaUpload}
                  fileDetailsForAlexanderiaUpload={
                    fileDetailsForAlexanderiaUpload
                  }
                  uploadDocumentDetails={uploadDocumentDetails}
                  setUploadDocumentDetails={setUploadDocumentDetails}
                  setSelectedFilingsCount={setSelectedFilingsCount}
                />
              </div>
              <div className="border rounded-lg p-5 bg-white shadow-sm max-h-max">
                <h2 className="font-medium text-neutral-60 text-sm font-display-2-m leading-4 pb-4">
                  Choose Template
                  <span className="text-negative-100">*</span>
                </h2>
                <div className="space-y-4">
                  <Template
                    ref={templateRef}
                    setSelectedTemplateType={setSelectedTemplateType}
                    setSelectedTemplateOption={setSelectedTemplateOption}
                    onOptionChange={handleOptionChange}
                  />
                </div>
              </div>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center position-relative mt-40">
              <img src={EmptyCompanyData} alt="No data found" />
              <label className="body-r text-gray-600">
                Select Company to start the extraction process
              </label>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default Spreader;
