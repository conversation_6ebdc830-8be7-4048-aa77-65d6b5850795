import React, { useState, useEffect } from "react";
import { Combobox, ComboboxInput, ComboboxButton } from "@headlessui/react";
import { HiPlus } from "react-icons/hi";
import { ButtonIcon } from "../../atoms/button";
import { FaTimes } from "react-icons/fa";
import useAiPlaygroundFilterStore from "../../../pages/ai-playground/templates/ai-playground-header/ai-playground-filter/ai-playgroud-filter.store";
import { FiLink, FiTrash2 } from "react-icons/fi";
import { notify } from "../toaster";
import PropTypes from "prop-types";
import { isValidURL } from "../../../general/utils";

const WebLinkComponent = ({ setWebLinks, webLinks, index }) => {
  const { selectedWebLinks, setSelectedWebLinks } = useAiPlaygroundFilterStore(
    (state) => state
  );
  const [query, setQuery] = useState(selectedWebLinks[index - 1] || "");
  const [isError, setIsError] = useState(false);

  useEffect(() => {
    if (isValidURL(query)) {
      setIsError(false);
      if (!selectedWebLinks.includes(query)) {
        setSelectedWebLinks([...selectedWebLinks, query]);
      }
    } else {
      setIsError(true);
    }
  }, [query]);

  const onClickAddWebLink = () => {
    webLinks.length < 10
      ? setWebLinks([...webLinks, index + 1])
      : notify.info("Maximum (10) link limit reached.");
  };

  const onClickDeleteWebLink = () => {
    const updatedWeblinks = webLinks.filter((item) => item !== index);
    setWebLinks(updatedWeblinks);
    const updatedselectedWeblinks = selectedWebLinks.filter(
      (item) => item !== query
    );
    setSelectedWebLinks(updatedselectedWeblinks);
  };

  return (
    <div className={"flex w-full items-center gap-2"}>
      <Combobox
        immediate={true}
        as={"div"}
        value={query}
        className={"flex h-10 w-full items-center"}
      >
        <ComboboxInput
          data-testid="ai-playground-search-input"
          value={query}
          maxLength={200}
          className={`body-r relative h-8 w-full rounded border px-9 py-1.5 pl-9 text-neutral-80 placeholder-neutral-30 ${isError ? "border-negative-100 focus:border-negative-100" : "border-neutral-10 hover:border-primary-78 focus:border-primary-90"} focus:outline-none focus:ring-0`}
          placeholder={"Enter here"}
          autoComplete="off"
          onKeyUp={(e) => e?.key === "Enter" && onClickEnter()}
          onChange={(e) => {
            setQuery(e.target.value);
          }}
        ></ComboboxInput>

        <FiLink
          className="absolute left-3 top-3 z-60 size-4 cursor-pointer text-neutral-60"
          strokeWidth={1}
        />
        <ComboboxButton className="absolute right-14 top-2 z-10 h-3 w-3">
          {query.length > 0 && (
            <FaTimes
              data-testid="ai-playground-search-cross"
              onClick={() => setQuery("")}
              className="absolute h-3 w-3 cursor-pointer text-neutral-60"
              strokeWidth={1}
            />
          )}
        </ComboboxButton>
      </Combobox>
      {webLinks[webLinks.length - 1] === index ? (
        <ButtonIcon
          disabled={isError}
          intent={"secondary"}
          onClick={() => {
            onClickAddWebLink();
          }}
        >
          <HiPlus />
        </ButtonIcon>
      ) : (
        <button
          onClick={() => {
            onClickDeleteWebLink();
          }}
          className={
            "body-r flex size-8 items-center justify-center rounded text-negative-100 transition duration-300 hover:bg-negative-60 disabled:cursor-not-allowed"
          }
        >
          <FiTrash2 />
        </button>
      )}
    </div>
  );
};

WebLinkComponent.propTypes = {
  setWebLinks: PropTypes.func.isRequired,
  webLinks: PropTypes.array.isRequired,
  index: PropTypes.number.isRequired
};

export default WebLinkComponent;
