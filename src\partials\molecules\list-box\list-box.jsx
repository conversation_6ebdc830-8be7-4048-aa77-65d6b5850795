import React from "react";
import {
  Listbox,
  ListboxButton,
  ListboxOptions,
  ListboxOption
} from "@headlessui/react";
import { HiChevronDown } from "react-icons/hi";
import PropTypes from "prop-types";

const ListBox = ({ list, selectedList, setSelectedList, setOpen, query }) => {
  return (
    <Listbox
      value={selectedList}
      onChange={(e) => {
        setSelectedList(e);
        if (query.length > 1) setOpen(true);
      }}
    >
      <ListboxButton
        onClick={() => setOpen(false)}
        className={`body-r group relative inline-flex h-8 w-32 items-center rounded px-3 py-2 text-primary-78 hover:bg-primary-35 data-[open]:bg-primary-50 data-[open]:text-primary-90`}
      >
        {selectedList}
        <HiChevronDown
          className="group pointer-events-none absolute right-2.5 top-2 size-4 text-primary-90"
          aria-hidden="true"
        />
      </ListboxButton>
      <ListboxOptions
        anchor="bottom"
        transition
        className={`body-r group mt-1 w-28 items-center rounded border bg-white py-0.5 text-primary-78 shadow-lg`}
      >
        {list.map((item) => (
          <ListboxOption
            key={item}
            value={item}
            className={`body-r data-[selected]:body-m h-10 cursor-pointer items-center px-3 py-2 text-neutral-80 hover:bg-primary-35 hover:text-primary-78 focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 data-[selected]:bg-primary-40 data-[selected]:text-primary-78`}
          >
            <span>{item}</span>
          </ListboxOption>
        ))}
      </ListboxOptions>
    </Listbox>
  );
};

ListBox.propTypes = {
  list: PropTypes.arrayOf(PropTypes.string).isRequired,
  selectedList: PropTypes.string.isRequired,
  setSelectedList: PropTypes.func.isRequired,
  setOpen: PropTypes.func.isRequired,
  query: PropTypes.string.isRequired
};
export default ListBox;
