import { render, screen } from "@testing-library/react";
import DocumentViewer from "./document-viewer";
import TestAppRenderer from "../../../infra/test-utils/test-app-renderer";

jest.mock("pdfjs-dist", () => ({
  GlobalWorkerOptions: {
    workerSrc: ""
  },
  getDocument: jest.fn().mockReturnValue({
    promise: Promise.resolve({
      pdf: "JVBjz9MNCjc5NSAwIG9iag08PC9MaW5lYXJpemVkIDEvTCAyMDkxMzg1L08gNzk3L0UgMTM0NjU0L04gNDcvVCAyMDkwMzgyL0ggWyA1MzcgMTA5MV0"
    })
  })
}));

jest.mock("pdfjs-dist/web/pdf_viewer.mjs", () => ({
  PDFViewer: jest.fn(),
  EventBus: jest.fn().mockImplementation(() => ({
    on: jest.fn()
  })),
  PDFLinkService: jest.fn(),
  PDFFindController: jest.fn()
}));

describe("Component DocumentViewer render", () => {
  it("should render DocumentViewer without value", () => {
    render(
      <TestAppRenderer>
        <DocumentViewer />
      </TestAppRenderer>
    );
    expect(
      screen.getByTestId("document-viewer-error-preview-open-link")
    ).toBeInTheDocument();
  });
  it("should render DocumentViewer with docid", () => {
    render(
      <TestAppRenderer>
        <DocumentViewer docId={"test"} />
      </TestAppRenderer>
    );
    expect(screen.getByTestId("document-viewer-loading")).toBeInTheDocument();
  });
});
