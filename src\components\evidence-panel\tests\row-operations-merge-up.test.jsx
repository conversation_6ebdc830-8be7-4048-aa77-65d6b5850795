import { mergeRowUp } from '../row-operations-merge-up';
import { notify } from '../../../partials/molecules/toaster';
import { NOTIFICATION_MESSAGES } from '../../.././constants';

// Mock the notification functions
jest.mock('../../../partials/molecules/toaster', () => ({
  notify: {
    warning: jest.fn(),
    info: jest.fn(),
    error: jest.fn(),
    success: jest.fn()
  }
}));

describe('mergeRowUp', () => {
  // Common test setup
  let selectedRowIds;
  let checkedItems;
  let currentTableData;
  let setCurrentTableData;
  let setTableDataByTab;
  let selectedTab;
  let setCheckedItems;
  let setShowPopup;
  
  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();
    
    // Setup test data
    currentTableData = [
      { id: 'row1', label: 'Row 1', value: 'Value 1', status: 'active' },
      { id: 'row2', label: 'Row 2', value: '', status: 'active' },
      { id: 'row3', label: 'Row 3', value: 'Value 3', status: 'active' }
    ];
    
    // Mock function implementations
    setCurrentTableData = jest.fn();
    setTableDataByTab = jest.fn();
    setCheckedItems = jest.fn();
    setShowPopup = jest.fn();
    selectedTab = 'tab1';
    checkedItems = {};
  });
  
  test('should show info message when first row is selected', () => {
    // Arrange
    selectedRowIds = ['row1'];
    
    // Act
    mergeRowUp(
      selectedRowIds, 
      checkedItems, 
      currentTableData, 
      setCurrentTableData, 
      setTableDataByTab, 
      selectedTab, 
      setCheckedItems, 
      setShowPopup
    );
    
    // Assert
    expect(notify.info).toHaveBeenCalledWith(NOTIFICATION_MESSAGES.CANNOT_MERGE_FIRST_ROW);
    expect(setCurrentTableData).not.toHaveBeenCalled();
  });
  
  test('should show error when selected row has values', () => {
    // Arrange
    selectedRowIds = ['row3'];
    
    // Act
    mergeRowUp(
      selectedRowIds, 
      checkedItems, 
      currentTableData, 
      setCurrentTableData, 
      setTableDataByTab, 
      selectedTab, 
      setCheckedItems, 
      setShowPopup
    );
    
    // Assert
    expect(notify.error).toHaveBeenCalledWith(NOTIFICATION_MESSAGES.ROW_HAS_VALUES);
    expect(setCurrentTableData).not.toHaveBeenCalled();
  });
  
  test('should successfully merge row with the row above', () => {
    // Arrange
    selectedRowIds = ['row2'];
    
    // Act
    mergeRowUp(
      selectedRowIds, 
      checkedItems, 
      currentTableData, 
      setCurrentTableData, 
      setTableDataByTab, 
      selectedTab, 
      setCheckedItems, 
      setShowPopup
    );
    
    // Assert
    // Check that the data was updated correctly
    const expectedNewData = [
      { id: 'row1', label: 'Row 1 Row 2', value: 'Value 1', status: 'active' },
      { id: 'row3', label: 'Row 3', value: 'Value 3', status: 'active' }
    ];
    expect(setCurrentTableData).toHaveBeenCalledWith(expect.arrayContaining([
      expect.objectContaining({ label: 'Row 1 Row 2' }),
      expect.objectContaining({ id: 'row3' })
    ]));
    
    // Check that tableDataByTab was updated
    expect(setTableDataByTab).toHaveBeenCalled();
    const updateFunction = setTableDataByTab.mock.calls[0][0];
    const prevState = { tab1: [] };
    const newState = updateFunction(prevState);
    expect(newState).toEqual({ tab1: expect.any(Array) });
    
    // Check other side effects
    expect(setCheckedItems).toHaveBeenCalledWith({});
    expect(setShowPopup).toHaveBeenCalledWith(false);
    expect(notify.success).toHaveBeenCalledWith(NOTIFICATION_MESSAGES.MERGE_SUCCESS);
  });
  
  test('should handle empty labels correctly', () => {
    // Arrange
    currentTableData = [
      { id: 'row1', label: '', value: 'Value 1', status: 'active' },
      { id: 'row2', label: 'Row 2', value: '', status: 'active' }
    ];
    selectedRowIds = ['row2'];
    
    // Act
    mergeRowUp(
      selectedRowIds, 
      checkedItems, 
      currentTableData, 
      setCurrentTableData, 
      setTableDataByTab, 
      selectedTab, 
      setCheckedItems, 
      setShowPopup
    );
    
    // Assert
    expect(setCurrentTableData).toHaveBeenCalled();
    const updatedData = setCurrentTableData.mock.calls[0][0];
    expect(updatedData[0].label).toBe('Row 2');
  });
});