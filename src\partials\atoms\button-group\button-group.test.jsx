import React, { act } from "react";
import { render } from "@testing-library/react";
import ButtonGroup from "./button-group";

const data = [
  { id: 1, name: "Button 1" },
  { id: 2, name: "Button 2" },
  { id: 3, name: "Button 3" }
];

const active = { id: 1, name: "Button 1" };
const onClick = jest.fn();

describe("ButtonGroup", () => {
  it("should render a list of buttons", () => {
    const { getByText } = render(
      <ButtonGroup onClick={onClick} active={active} data={data} />
    );

    expect(getByText("Button 1")).toBeInTheDocument();
    expect(getByText("Button 2")).toBeInTheDocument();
    expect(getByText("Button 3")).toBeInTheDocument();
  });

  it("should call the onClick handler when a button is clicked", () => {
    const { getByText } = render(
      <ButtonGroup onClick={onClick} active={active} data={data} />
    );
    act(() => {
      getByText("Button 1").click();
    });

    expect(onClick).toHaveBeenCalledWith({ id: 1, name: "Button 1" });
  });

  it("should render a NotFound component if the data is empty", () => {
    const { getByText } = render(<ButtonGroup onClick={onClick} data={[]} />);

    expect(getByText("Not Found")).toBeInTheDocument();
  });
});
