import { post } from "../../../general/fetcher";

const CONTROLLER = "aiplatform";

export const UPLOAD_API_URL = `${CONTROLLER}/extract`;

export const Extract = ({
  files,
  isAsReported,
  company_id,
  company_name,
  isTemplate,
  company_ticker
}) => {
  const body = {
    client_id: "acuity-one",
    session_id: "",
    job_type: "spread",
    notes_extraction: "false",
    is_llm_agent: "false",
    job_engine: "acuity",
    company_id: company_id,
    company_name: company_name,
    template_id: isTemplate ? "123e4567-e89b-12d3-a456-426614174000" : "",
    template: isTemplate,
    as_reported: isAsReported,
    ticker: company_ticker,
    kpi_json: false,
    files: files
  };
  return post(UPLOAD_API_URL, body);
};
