import React from "react";
import { render, screen } from "@testing-library/react";
import NoDataFound from "./no-data-found";

describe("NoDataFound Component", () => {
  test("renders without crashing", () => {
    render(<NoDataFound />);
    expect(screen.getByTestId("grid-no-data")).toBeInTheDocument();
  });

  test("renders the no data text", () => {
    render(<NoDataFound />);
    expect(screen.getByTestId("no-data-text")).toHaveTextContent(
      "No Data found!"
    );
  });

  test("matches snapshot", () => {
    const { asFragment } = render(<NoDataFound />);
    expect(asFragment()).toMatchSnapshot();
  });
});
