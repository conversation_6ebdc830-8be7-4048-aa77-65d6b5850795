import React from "react";

function EmptyScreenerImage() {
  return (
    <svg
      width="206"
      height="122"
      viewBox="0 0 206 122"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M102.25 34.3315C102.25 34.3315 70.0502 9.10408 64.4152 8.22208C58.7802 7.34008 53.8722 5.4638 43.0402 7.2338C32.2082 9.0038 23.0728 20.2845 21.6408 26.6205C20.2088 32.9565 18.1746 35.7927 20.6916 47.6127C23.2086 59.4327 27.6975 65.0775 27.6975 65.0775L52.8772 102.595C52.8772 102.595 59.4916 112.49 66.2775 114.802C73.0635 117.114 83.4579 111.056 88.0539 109.165C92.6499 107.274 108.871 89.7413 116.33 90.2143C123.789 90.6873 149.798 94.5502 149.798 94.5502C149.798 94.5502 180.226 107.731 185.054 91.8627C189.882 75.9947 182.867 52.5149 173.349 39.3979C163.831 26.2809 149.369 40.7299 146.988 39.3979C144.607 38.0659 102.25 34.3315 102.25 34.3315Z"
        fill="url(#paint0_linear_9790_14680)"
      />
      <g filter="url(#filter0_d_9790_14680)">
        <path
          d="M158.829 70.8398H46.7383C45.6337 70.8398 44.7383 71.7353 44.7383 72.8398V94.8078C44.7383 95.9124 45.6337 96.8078 46.7383 96.8078H158.829C159.934 96.8078 160.829 95.9124 160.829 94.8078V72.8398C160.829 71.7353 159.934 70.8398 158.829 70.8398Z"
          fill="white"
        />
        <path
          d="M158.829 71.3398H46.7383C45.9099 71.3398 45.2383 72.0114 45.2383 72.8398V94.8078C45.2383 95.6363 45.9099 96.3078 46.7383 96.3078H158.829C159.658 96.3078 160.329 95.6363 160.329 94.8078V72.8398C160.329 72.0114 159.658 71.3398 158.829 71.3398Z"
          stroke="#E6E6E6"
        />
      </g>
      <path
        d="M59.2492 89.9339C62.6236 89.9339 65.3592 87.1983 65.3592 83.8239C65.3592 80.4494 62.6236 77.7139 59.2492 77.7139C55.8747 77.7139 53.1392 80.4494 53.1392 83.8239C53.1392 87.1983 55.8747 89.9339 59.2492 89.9339Z"
        fill="#E6E6E6"
      />
      <path
        d="M83.3082 80.0049H74.9072C74.2743 80.0049 73.7612 80.5177 73.7612 81.1504C73.7612 81.783 74.2743 82.2959 74.9072 82.2959H83.3082C83.9411 82.2959 84.4542 81.783 84.4542 81.1504C84.4542 80.5177 83.9411 80.0049 83.3082 80.0049Z"
        fill="#E6E6E6"
      />
      <path
        d="M103.165 80.0049H88.6543C88.0214 80.0049 87.5083 80.5177 87.5083 81.1504C87.5083 81.783 88.0214 82.2959 88.6543 82.2959H103.165C103.798 82.2959 104.311 81.783 104.311 81.1504C104.311 80.5177 103.798 80.0049 103.165 80.0049Z"
        fill="#E6E6E6"
      />
      <path
        d="M108.511 85.3506H74.1431C73.5102 85.3506 72.9971 85.8634 72.9971 86.4961C72.9971 87.1287 73.5102 87.6416 74.1431 87.6416H108.511C109.144 87.6416 109.657 87.1287 109.657 86.4961C109.657 85.8634 109.144 85.3506 108.511 85.3506Z"
        fill="#E6E6E6"
      />
      <path
        d="M150.663 77.7139H122.113C121.561 77.7139 121.113 78.1616 121.113 78.7139V88.9339C121.113 89.4862 121.561 89.9339 122.113 89.9339H150.663C151.216 89.9339 151.663 89.4862 151.663 88.9339V78.7139C151.663 78.1616 151.216 77.7139 150.663 77.7139Z"
        fill="#E6E6E6"
      />
      <g filter="url(#filter1_d_9790_14680)">
        <path
          d="M165.702 47.9268H53.6113C52.5068 47.9268 51.6113 48.8222 51.6113 49.9268V71.8948C51.6113 72.9993 52.5068 73.8948 53.6113 73.8948H165.702C166.807 73.8948 167.702 72.9993 167.702 71.8948V49.9268C167.702 48.8222 166.807 47.9268 165.702 47.9268Z"
          fill="white"
        />
        <path
          d="M165.702 48.4268H53.6113C52.7829 48.4268 52.1113 49.0983 52.1113 49.9268V71.8948C52.1113 72.7232 52.7829 73.3948 53.6113 73.3948H165.702C166.531 73.3948 167.202 72.7232 167.202 71.8948V49.9268C167.202 49.0983 166.531 48.4268 165.702 48.4268Z"
          stroke="#B3B3B3"
        />
      </g>
      <path
        d="M66.1222 67.0208C69.4967 67.0208 72.2322 64.2852 72.2322 60.9108C72.2322 57.5363 69.4967 54.8008 66.1222 54.8008C62.7477 54.8008 60.0122 57.5363 60.0122 60.9108C60.0122 64.2852 62.7477 67.0208 66.1222 67.0208Z"
        fill="#E6E6E6"
      />
      <path
        d="M90.1813 57.0918H81.7803C81.1474 57.0918 80.6343 57.6047 80.6343 58.2373C80.6343 58.8699 81.1474 59.3828 81.7803 59.3828H90.1813C90.8142 59.3828 91.3273 58.8699 91.3273 58.2373C91.3273 57.6047 90.8142 57.0918 90.1813 57.0918Z"
        fill="#E6E6E6"
      />
      <path
        d="M110.038 57.0918H95.5273C94.8944 57.0918 94.3813 57.6047 94.3813 58.2373C94.3813 58.8699 94.8944 59.3828 95.5273 59.3828H110.038C110.671 59.3828 111.184 58.8699 111.184 58.2373C111.184 57.6047 110.671 57.0918 110.038 57.0918Z"
        fill="#E6E6E6"
      />
      <path
        d="M115.384 62.4375H81.0161C80.3832 62.4375 79.8701 62.9504 79.8701 63.583C79.8701 64.2156 80.3832 64.7285 81.0161 64.7285H115.384C116.017 64.7285 116.53 64.2156 116.53 63.583C116.53 62.9504 116.017 62.4375 115.384 62.4375Z"
        fill="#E6E6E6"
      />
      <path
        d="M157.536 54.8008H128.986C128.434 54.8008 127.986 55.2485 127.986 55.8008V66.0208C127.986 66.5731 128.434 67.0208 128.986 67.0208H157.536C158.089 67.0208 158.536 66.5731 158.536 66.0208V55.8008C158.536 55.2485 158.089 54.8008 157.536 54.8008Z"
        fill="#E6E6E6"
      />
      <g filter="url(#filter2_d_9790_14680)">
        <path
          d="M156.829 25.0146H48.7383C46.5291 25.0146 44.7383 26.8055 44.7383 29.0146V46.9826C44.7383 49.1918 46.5291 50.9826 48.7383 50.9826H156.829C159.038 50.9826 160.829 49.1918 160.829 46.9826V29.0146C160.829 26.8055 159.038 25.0146 156.829 25.0146Z"
          fill="white"
        />
        <path
          d="M156.829 25.7646H48.7383C46.9434 25.7646 45.4883 27.2197 45.4883 29.0146V46.9826C45.4883 48.7776 46.9434 50.2326 48.7383 50.2326H156.829C158.624 50.2326 160.079 48.7776 160.079 46.9826V29.0146C160.079 27.2197 158.624 25.7646 156.829 25.7646Z"
          stroke="#93B0ED"
          stroke-width="1.5"
        />
      </g>
      <path
        d="M59.2492 44.1087C62.6236 44.1087 65.3592 41.3731 65.3592 37.9987C65.3592 34.6242 62.6236 31.8887 59.2492 31.8887C55.8747 31.8887 53.1392 34.6242 53.1392 37.9987C53.1392 41.3731 55.8747 44.1087 59.2492 44.1087Z"
        fill="url(#paint1_linear_9790_14680)"
      />
      <path
        d="M83.3082 34.1797H74.9072C74.2743 34.1797 73.7612 34.6925 73.7612 35.3252C73.7612 35.9578 74.2743 36.4707 74.9072 36.4707H83.3082C83.9411 36.4707 84.4542 35.9578 84.4542 35.3252C84.4542 34.6925 83.9411 34.1797 83.3082 34.1797Z"
        fill="#3562CE"
      />
      <path
        d="M103.165 34.1797H88.6543C88.0214 34.1797 87.5083 34.6925 87.5083 35.3252C87.5083 35.9578 88.0214 36.4707 88.6543 36.4707H103.165C103.798 36.4707 104.311 35.9578 104.311 35.3252C104.311 34.6925 103.798 34.1797 103.165 34.1797Z"
        fill="#3562CE"
      />
      <path
        d="M108.511 39.5254H74.1431C73.5102 39.5254 72.9971 40.0382 72.9971 40.6709C72.9971 41.3035 73.5102 41.8164 74.1431 41.8164H108.511C109.144 41.8164 109.657 41.3035 109.657 40.6709C109.657 40.0382 109.144 39.5254 108.511 39.5254Z"
        fill="#3562CE"
      />
      <path
        d="M150.663 31.8887H122.113C121.561 31.8887 121.113 32.3364 121.113 32.8887V43.1087C121.113 43.661 121.561 44.1087 122.113 44.1087H150.663C151.216 44.1087 151.663 43.661 151.663 43.1087V32.8887C151.663 32.3364 151.216 31.8887 150.663 31.8887Z"
        fill="url(#paint2_linear_9790_14680)"
      />
      <defs>
        <filter
          id="filter0_d_9790_14680"
          x="38.7383"
          y="65.8398"
          width="128.091"
          height="37.9678"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="1" />
          <feGaussianBlur stdDeviation="3" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.18 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_9790_14680"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_9790_14680"
            result="shape"
          />
        </filter>
        <filter
          id="filter1_d_9790_14680"
          x="45.6113"
          y="42.9268"
          width="128.091"
          height="37.9678"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="1" />
          <feGaussianBlur stdDeviation="3" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.18 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_9790_14680"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_9790_14680"
            result="shape"
          />
        </filter>
        <filter
          id="filter2_d_9790_14680"
          x="38.7383"
          y="20.0146"
          width="128.091"
          height="37.9678"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset dy="1" />
          <feGaussianBlur stdDeviation="3" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.18 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_9790_14680"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_9790_14680"
            result="shape"
          />
        </filter>
        <linearGradient
          id="paint0_linear_9790_14680"
          x1="103.06"
          y1="6.5"
          x2="103.06"
          y2="115.321"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#FFF0FC" />
          <stop offset="1" stop-color="#CED4F9" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_9790_14680"
          x1="54.5567"
          y1="41.1026"
          x2="66.6423"
          y2="32.7441"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#BE439D" />
          <stop offset="1" stop-color="#E5B471" />
        </linearGradient>
        <linearGradient
          id="paint2_linear_9790_14680"
          x1="124.657"
          y1="41.1026"
          x2="135.853"
          y2="21.7447"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#BE439D" />
          <stop offset="1" stop-color="#E5B471" />
        </linearGradient>
      </defs>
    </svg>
  );
}
export default React.memo(EmptyScreenerImage);
