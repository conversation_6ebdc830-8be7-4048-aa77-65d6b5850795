import { render, screen } from "@testing-library/react";
import TextArea from "./text-area";

describe("Component TextArea render", () => {
  it("should render TextArea ", () => {
    render(<TextArea />);
    expect(screen.getByTestId("text-area")).toBeInTheDocument();
  });
  it("should render TextArea with disabled", () => {
    render(<TextArea disabled={true} />);
    expect(screen.getByTestId("text-area")).toBeInTheDocument();
  });
});
