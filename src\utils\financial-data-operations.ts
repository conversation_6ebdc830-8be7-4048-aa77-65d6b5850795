import {
  PdfPageHeight,
  PdfPageWidth
} from "../constants";
/**
 * Utility functions for financial data operations
 * These functions handle common row operations like addition and deletion
 */

/**
 * Updates financial data after adding a new row
 * @param financialData The original financial data object
 * @param selectedTab The currently selected tab index
 * @param selectedIndex The index where the new row should be inserted
 * @param newRow The new row data
 * @param setStatusFinancialData Function to update the financial data state
 * @returns The updated financial data or null if update fails
 */
export const updateFinancialDataAfterRowAddition = (
  financialData: any, 
  selectedTab: any, 
  selectedIndex: any,
  newRow: any,
  setStatusFinancialData: any
) => {
    if (!financialData?.tableGroups?.[selectedTab]?.tables?.[0]?.rows) {
      console.warn(`No rows found for tab ${selectedTab}`);
      return null;
    }
    
    // Create new row structure for financialsData format
    const newFinancialRow = {
      label: {
        id: newRow.id,
        text: newRow.label || "",
        style: newRow.style === "header" ? "header" : "lineitem",
        mapping: newRow.status || "",
        mappingId: newRow.mappingId || 0,
        mappingScore: newRow.mappingScore || null
      },
      cells: Object.keys(newRow)
        .filter(key => key.startsWith("value_"))
        .map(key => ({
          columnKey: key,
          value: newRow[key] || null
        }))
    };
    const updatedRows = [...financialData.tableGroups[selectedTab].tables[0].rows];
    updatedRows.splice(selectedIndex, 0, newFinancialRow);
    const updatedFinancialData = {
      ...financialData,
      tableGroups: financialData.tableGroups.map((group: any, index: any) => {
        if (index === selectedTab) {
          return {
            ...group,
            tables: group.tables.map((table: any, tableIndex: any) => {
              if (tableIndex === 0) {
                return {
                  ...table,
                  rows: updatedRows
                };
              }
              return table;
            })
          };
        }
        return group;
      })
    };
    setStatusFinancialData(updatedFinancialData);
    return updatedFinancialData;
};

/**
 * Updates financial data after deleting rows
 * @param financialData The original financial data object
 * @param selectedTab The currently selected tab index
 * @param selectedRowIds Array of row IDs to delete
 * @param setStatusFinancialData Function to update the financial data state
 * @returns The updated financial data or undefined if update fails
 */
export const updateFinancialDataAfterRowDeletion = (
  financialData: any, 
  selectedTab: any, 
  selectedRowIds: any,
  setStatusFinancialData: any
) => {
  if (!financialData?.tableGroups?.[selectedTab]?.tables?.[0]?.rows) {
    return; 
  }
  
  // Filter out the deleted rows
  const updatedRows = financialData.tableGroups[selectedTab].tables[0].rows.filter(
    (row: any) => !selectedRowIds.includes(row.label.id)
  );
  const updatedFinancialData = {
    ...financialData,
    tableGroups: financialData.tableGroups.map((group: any, index: any) => {
      if (index === selectedTab) {
        return {
          ...group,
          tables: group.tables.map((table: any, tableIndex: any) => {
            if (tableIndex === 0) {
              return {
                ...table,
                rows: updatedRows
              };
            }
            return table;
          })
        };
      }
      return group;
    })
  };  
  // Update the state with the new data
  setStatusFinancialData(updatedFinancialData);
  // Return the updated data in case you need it elsewhere
  return updatedFinancialData;
};
export const updateCircularStatusButtonDataMapping = ( financialData: any,
  dataItem: any, 
  mappingValue: any,
  selectedTab: number) => {
  if (financialData.tableGroups[selectedTab]) {
    financialData.tableGroups[selectedTab].tables[0].rows =
      financialData.tableGroups[selectedTab].tables[0].rows.map((item:any) => {
        if (item.label.id === dataItem.id) {
          item.label.mapping = mappingValue.stext;
          item.label.mappingId = mappingValue.mappingId || mappingValue.mapping_id;
          item.label.mappingScore = 1; 
        }
        return item;
      });
  }
};
export  const convertPositionToPercentage = (position:any) => {
    if (!position?.boundingRect) {
      console.error("Invalid position object:", position);
      return { bounds: [0, 0, 0, 0], pageNumber: 1 };
    }

    // Get PDF page dimensions from the boundingRect
    const pageWidth = position.boundingRect.width || PdfPageWidth;
    const pageHeight = position.boundingRect.height || PdfPageHeight;

    // Extract coordinates correctly using the x1, y1 properties
    const x1 = position.boundingRect.x1;
    const y1 = position.boundingRect.y1;
    const x2 = position.boundingRect.x2;
    const y2 = position.boundingRect.y2;

    // Calculate percentage bounds
    const bounds = [
      x1 / pageWidth,
      y1 / pageHeight,
      (x2 - x1) / pageWidth, // width as percentage
      (y2 - y1) / pageHeight // height as percentage
    ];

    return {
      bounds,
      pageNumber: position.pageNumber
    };
  };
  export const createPdfHighlight = (pdfHighlights: any, columnKey: string) => {
  if (!pdfHighlights?.[columnKey]) {
    return null;
  }

  return {
    text: pdfHighlights[columnKey].text,
    bounds: pdfHighlights[columnKey].bounds,
    pageNumber: pdfHighlights[columnKey].pageNumber ?? 1,
    fileKey: pdfHighlights[columnKey].fileKey
  };
};
export const convertBoundingRectToPercentage=(position:any)=> {
   const { boundingRect, pageNumber } = position;
  if (!boundingRect) {
    console.error("Invalid position object:", position);
    return { bounds: [0, 0, 0, 0], pageNumber: 1 };
  }
  return {
    bounds: [
      boundingRect.x1 / boundingRect.width,
      boundingRect.y1 / boundingRect.height,
      boundingRect.x2 / boundingRect.width,
      boundingRect.y2 / boundingRect.height
    ],
    pageNumber
  };
}
export const SpecificKpiY2Add = 0.01; // Specific KPI Y2 adjustment value