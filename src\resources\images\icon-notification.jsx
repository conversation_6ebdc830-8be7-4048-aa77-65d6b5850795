import * as React from "react";
const IconNotification = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={28}
    height={28}
    viewBox="0 0 28 28"
    {...props}
  >
    <g
      id="Notifications_icon"
      data-name="Notifications icon"
      transform="translate(-1145 -16)"
    >
      <g
        id="icon_bg"
        data-name="icon bg"
        transform="translate(1145 16)"
        fill="#fff"
        stroke="#e6e6e6"
        strokeWidth={1}
      >
        <circle cx={14} cy={14} r={14} stroke="none" />
        <circle cx={14} cy={14} r={13.5} fill="none" />
      </g>
      <path
        id="Icon_awesome-bell"
        data-name="Icon awesome-bell"
        d="M5.665,12.949A1.618,1.618,0,0,0,7.283,11.33H4.047A1.618,1.618,0,0,0,5.665,12.949Zm5.447-3.786c-.489-.525-1.4-1.315-1.4-3.9A3.994,3.994,0,0,0,6.474,1.336V.809a.809.809,0,1,0-1.618,0v.527A3.994,3.994,0,0,0,1.621,5.26c0,2.587-.914,3.377-1.4,3.9A.79.79,0,0,0,0,9.712a.81.81,0,0,0,.812.809h9.706a.81.81,0,0,0,.812-.809A.79.79,0,0,0,11.112,9.162Z"
        transform="translate(1153 24)"
        fill="#4061c7"
      />
    </g>
  </svg>
);
export default React.memo(IconNotification);
