import React from 'react';
import { screen, waitFor } from '@testing-library/react';
import RoutesConfig from './routes';
import { renderWithProviders } from '../utils/test-utils';

// Mock the lazy-loaded components
jest.mock('./pdf-preview/PdfPage.jsx', () => () => <div data-testid="pdf-page">PDF Page</div>);
jest.mock('../components/evidence-panel/evidence-panel.tsx', () => () => <div data-testid="evidence-panel">Evidence Panel</div>);
jest.mock('./specific-kpi/specific-kpi.jsx', () => () => <div data-testid="specific-kpi">Specific KPI</div>);
jest.mock('../partials/atoms/not-found', () => ({
  NotFound: () => <div data-testid="not-found">Not Found</div>
}));
jest.mock('../partials/molecules/idle-timeout/idle-timeout.jsx', () => () => <div>Idle Timeout</div>);
jest.mock('../partials/molecules/not-support', () => () => <div data-testid="screen-not-support">Screen Not Supported</div>);

// Mock the window.innerWidth
const originalInnerWidth = window.innerWidth;

describe('RoutesConfig component', () => {
  beforeEach(() => {
    // Reset window.innerWidth
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 1024
    });
    
    // Mock resize event listener
    window.addEventListener = jest.fn().mockImplementation((event, callback) => {
      if (event === 'resize') {
        callback();
      }
    });
  });
  
  afterEach(() => {
    // Restore window.innerWidth
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: originalInnerWidth
    });
    
    jest.clearAllMocks();
  });

  test('renders the main container', async () => {
    renderWithProviders(<RoutesConfig />);
    
    expect(screen.getByTestId('main-container')).toBeInTheDocument();
  });
  
  test('redirects to NotFound for unknown routes', async () => {
    renderWithProviders(<RoutesConfig />, { route: '/unknown-route' });
    
    await waitFor(() => {
      expect(screen.getByTestId('not-found')).toBeInTheDocument();
    });
  });
  
  test('renders the PDF preview page on /preview route', async () => {
    renderWithProviders(<RoutesConfig />, { route: '/preview' });
    
    await waitFor(() => {
      expect(screen.getByTestId('pdf-page')).toBeInTheDocument();
    });
  });
  
  test('renders the Evidence Panel on /panel route', async () => {
    renderWithProviders(<RoutesConfig />, { route: '/panel' });
    
    await waitFor(() => {
      expect(screen.getByTestId('evidence-panel')).toBeInTheDocument();
    });
  });
  
  test('renders the Specific KPI panel on /specific-kpi route', async () => {
    renderWithProviders(<RoutesConfig />, { route: '/specific-kpi' });
    
    await waitFor(() => {
      expect(screen.getByTestId('specific-kpi')).toBeInTheDocument();
    });
  });
  
  test('renders ScreenNotSupport for small screens', async () => {
    // Set window.innerWidth to a small value
    Object.defineProperty(window, 'innerWidth', {
      writable: true,
      configurable: true,
      value: 500
    });
    
    renderWithProviders(<RoutesConfig />);
    
    expect(screen.getByTestId('screen-not-support')).toBeInTheDocument();
  });
}); 