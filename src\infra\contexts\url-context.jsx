import React, { useEffect } from "react";
import PropTypes from "prop-types";
import { useLocation } from "react-router-dom";

/**
 * Provides the URL context for the application.
 * When navigating to other URLs/pages, update or reset the Zustand store here.
 * Primarily reset the store for components that are reused.
 */
const UrlContextProvider = ({ children }) => {
  const location = useLocation();

  return <React.Fragment>{children}</React.Fragment>;
};

UrlContextProvider.propTypes = {
  children: PropTypes.element
};

export default UrlContextProvider;
