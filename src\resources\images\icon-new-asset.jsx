import * as React from "react";

const IconNewAsset = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlnsXlink="http://www.w3.org/1999/xlink"
    width={175.117}
    height={144.375}
    {...props}
  >
    <defs>
      <linearGradient
        id="b"
        x1={0.5}
        x2={0.5}
        y2={1}
        gradientUnits="objectBoundingBox"
      >
        <stop offset={0} stopColor="#fff0fc" />
        <stop offset={1} stopColor="#ced4f9" />
      </linearGradient>
      <linearGradient
        id="a"
        x1={0.116}
        x2={1.168}
        y1={0.754}
        y2={-0.169}
        gradientUnits="objectBoundingBox"
      >
        <stop offset={0} stopColor="#edcf28" />
        <stop offset={1} stopColor="#dea509" />
      </linearGradient>
      <linearGradient
        xlinkHref="#a"
        id="d"
        x1={0.251}
        x2={1.03}
        y1={0.378}
        y2={0.87}
      />
      <filter
        id="c"
        width={165.149}
        height={135.719}
        x={3.539}
        y={3.124}
        filterUnits="userSpaceOnUse"
      >
        <feOffset dy={1} />
        <feGaussianBlur result="blur" stdDeviation={3} />
        <feFlood floodOpacity={0.18} />
        <feComposite in2="blur" operator="in" />
        <feComposite in="SourceGraphic" />
      </filter>
      <filter
        id="e"
        width={79.262}
        height={79.262}
        x={79.524}
        y={56.813}
        filterUnits="userSpaceOnUse"
      >
        <feOffset dy={1} />
        <feGaussianBlur result="blur-2" stdDeviation={3} />
        <feFlood floodOpacity={0.18} />
        <feComposite in2="blur-2" operator="in" />
        <feComposite in="SourceGraphic" />
      </filter>
    </defs>
    <g transform="translate(-1141.461 -110.396)">
      <path
        fill="url(#b)"
        stroke="rgba(0,0,0,0)"
        d="M-10413.607-19761.535s-41-19.672-48.206-20.8-31.214 25.811-31.214 25.811-13.961 13.438-10.912 44.988 32.084 14.451 32.084 14.451 20.443 28.469 40.709 35.162 25.278-9.379 45.552-15.2 16.847-3.2 35.541-8.072 24.653-27.764 15.165-41.842 16.994-65.643-19.5-75.42-59.219 40.922-59.219 40.922Z"
        data-name="Path 32907"
        transform="translate(11646.344 19914.617)"
      />
      <g filter="url(#c)" transform="translate(1141.46 110.4)">
        <g
          fill="#fff"
          stroke="#b3b3b3"
          data-name="Rectangle 2323"
          transform="translate(12.54 11.12)"
        >
          <rect width={147.149} height={117.719} stroke="none" rx={4} />
          <rect
            width={146.149}
            height={116.719}
            x={0.5}
            y={0.5}
            fill="none"
            rx={3.5}
          />
        </g>
      </g>
      <circle
        cx={1.839}
        cy={1.839}
        r={1.839}
        fill="#4061c7"
        data-name="Ellipse 167"
        transform="translate(1161.357 127.039)"
      />
      <circle
        cx={1.839}
        cy={1.839}
        r={1.839}
        fill="#4061c7"
        data-name="Ellipse 168"
        transform="translate(1168.715 127.039)"
      />
      <circle
        cx={1.839}
        cy={1.839}
        r={1.839}
        fill="#4061c7"
        data-name="Ellipse 169"
        transform="translate(1176.072 127.039)"
      />
      <rect
        width={109.325}
        height={11.036}
        fill="#ececec"
        data-name="Rectangle 2326"
        rx={2}
        transform="translate(1183.948 141.427)"
      />
      <rect
        width={27.59}
        height={16.554}
        fill="#e6e6e6"
        data-name="Rectangle 2327"
        rx={1}
        transform="translate(1170.554 162.66)"
      />
      <rect
        width={34.948}
        height={16.554}
        fill="url(#a)"
        data-name="Rectangle 2328"
        rx={1}
        transform="translate(1179.751 187.737)"
      />
      <rect
        width={35.984}
        height={16.554}
        fill="#e6e6e6"
        data-name="Rectangle 2329"
        rx={1}
        transform="translate(1199.984 209.81)"
      />
      <rect
        width={40.466}
        height={1.839}
        fill="#e6e6e6"
        data-name="Rectangle 2330"
        rx={0.92}
        transform="translate(1202.743 164.5)"
      />
      <rect
        width={40.466}
        height={1.839}
        fill="#4061c7"
        data-name="Rectangle 2335"
        rx={0.92}
        transform="translate(1220.217 191.416)"
      />
      <rect
        width={40.466}
        height={1.839}
        fill="#e6e6e6"
        data-name="Rectangle 2338"
        rx={0.92}
        transform="translate(1240.647 213.488)"
      />
      <rect
        width={40.466}
        height={1.839}
        fill="#e6e6e6"
        data-name="Rectangle 2331"
        rx={0.92}
        transform="translate(1202.743 168.178)"
      />
      <rect
        width={40.466}
        height={1.839}
        fill="#4061c7"
        data-name="Rectangle 2334"
        rx={0.92}
        transform="translate(1220.217 195.095)"
      />
      <rect
        width={40.466}
        height={1.839}
        fill="#e6e6e6"
        data-name="Rectangle 2337"
        rx={0.92}
        transform="translate(1240.647 217.167)"
      />
      <rect
        width={18.394}
        height={1.839}
        fill="#e6e6e6"
        data-name="Rectangle 2332"
        rx={0.92}
        transform="translate(1202.743 171.857)"
      />
      <rect
        width={18.394}
        height={1.839}
        fill="#4061c7"
        data-name="Rectangle 2333"
        rx={0.92}
        transform="translate(1220.217 198.774)"
      />
      <rect
        width={18.394}
        height={1.839}
        fill="#e6e6e6"
        data-name="Rectangle 2336"
        rx={0.92}
        transform="translate(1240.647 220.846)"
      />
      <g data-name="Group 9" transform="rotate(2 -4402.832 35379.71)">
        <path
          fill="url(#d)"
          d="m967.795 317.777-25.059-24.571-4.35 4.351 24.97 24.688a3.6 3.6 0 0 0 4.321-.151l.26-.253a3.737 3.737 0 0 0-.142-4.064Z"
          data-name="Path 16"
          transform="translate(-892.074 -247.277)"
        />
        <path
          fill="#4061c7"
          d="m45.763 52.393 5.027 5.028a.657.657 0 0 0 .93 0l5.7-5.7a.657.657 0 0 0 0-.93l-5.027-5.027Z"
          data-name="Path 17"
        />
        <g filter="url(#e)" transform="translate(-92.8 -61.61)">
          <g fill="#fff" data-name="Path 20">
            <path d="M118.141 124.54a29.018 29.018 0 0 1-20.278-9.24 29.019 29.019 0 0 1-7.804-20.874A29.019 29.019 0 0 1 99.3 74.148a29.018 29.018 0 0 1 20.873-7.804 29.02 29.02 0 0 1 20.277 9.24 29.019 29.019 0 0 1 7.805 20.874 29.018 29.018 0 0 1-9.241 20.278 29.02 29.02 0 0 1-20.873 7.803Zm1.854-53.079a23.915 23.915 0 0 0-17.202 6.432 23.916 23.916 0 0 0-7.617 16.711c-.113 3.238.412 6.4 1.56 9.4a23.916 23.916 0 0 0 4.872 7.803 23.914 23.914 0 0 0 16.712 7.616c3.237.113 6.4-.412 9.4-1.56a23.914 23.914 0 0 0 7.802-4.872 23.916 23.916 0 0 0 7.616-16.712 23.916 23.916 0 0 0-6.432-17.202 23.915 23.915 0 0 0-16.711-7.616Z" />
            <path
              fill="#b3b3b3"
              d="M120.156 66.844a28.52 28.52 0 0 0-20.515 7.67 28.521 28.521 0 0 0-9.082 19.93 28.521 28.521 0 0 0 7.67 20.514 28.521 28.521 0 0 0 19.93 9.082 28.522 28.522 0 0 0 20.514-7.67 28.52 28.52 0 0 0 9.082-19.93 28.52 28.52 0 0 0-7.67-20.515 28.522 28.522 0 0 0-19.93-9.081m-1.853 53.078a24.412 24.412 0 0 1-17.06-7.775 24.411 24.411 0 0 1-6.565-17.56 24.411 24.411 0 0 1 7.775-17.06 24.413 24.413 0 0 1 17.56-6.566 24.413 24.413 0 0 1 17.06 7.775 24.411 24.411 0 0 1 6.566 17.56 24.411 24.411 0 0 1-7.775 17.06 24.412 24.412 0 0 1-17.56 6.566m1.888-54.078c16.346.571 29.134 14.285 28.564 30.631-.571 16.347-14.285 29.135-30.631 28.564-16.347-.57-29.135-14.284-28.564-30.63.57-16.347 14.284-29.135 30.63-28.565Zm-1.854 53.079c12.968.453 23.848-9.693 24.301-22.661.453-12.968-9.693-23.848-22.66-24.301-12.969-.453-23.849 9.693-24.302 22.66-.453 12.969 9.693 23.849 22.661 24.302Z"
            />
          </g>
        </g>
        <circle
          cx={23.495}
          cy={23.495}
          r={23.495}
          fill="#f0f0f0"
          data-name="Ellipse 4"
          opacity={0.8}
          transform="translate(6.12 6.12)"
        />
        <path
          fill="#fff"
          d="M14.023 14.023c-3.4 3.4-5.441 6.866-4.561 7.745s4.346-1.163 7.745-4.562 5.441-6.866 4.562-7.745-4.346 1.162-7.746 4.562Z"
          data-name="Path 21"
        />
      </g>
      <path
        fill="none"
        stroke="#e5e5e5"
        d="M1155 135.235h145.688"
        data-name="Line 6"
      />
      <path
        fill="none"
        stroke="#e5e5e5"
        d="M1177 135.235v104.302"
        data-name="Line 7"
      />
    </g>
  </svg>
);
export default React.memo(IconNewAsset);
