import { oidcConfig, config, viewerUrl, baseUrl } from "./config";

describe("oidcConfig", () => {
  it("should call oidcConfig", () => {
    let result = oidcConfig;

    expect(result).not.toBeNull();
  });
  it("should call oidcConfig", () => {
    let result = config;

    expect(result).not.toBeNull();
  });
  it("should call oidcConfig", () => {
    let result = viewerUrl;

    expect(result).not.toBeNull();
  });
  it("should call oidcConfig", () => {
    let result = oidcConfig;

    expect(result).not.toBeNull();
  });
  it("should call oidcConfig", () => {
    let result = baseUrl;

    expect(result).not.toBeNull();
  });
});
