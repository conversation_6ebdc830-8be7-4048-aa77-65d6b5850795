import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import IconLogoPlaceholder from "../../../resources/images/icon-logo-placeholder";
import { ENTITY_LOGO_API } from "../../../infra/api/company/get-company-service";

const EntityLogo = ({ acuityID, companyName, logoSize }) => {
  const getPresignedURL = ENTITY_LOGO_API(acuityID);
  const url = getPresignedURL?.data?.data?.[0]?.logo_url || "";

  const companyInitials = companyName
    ?.split(/[^a-zA-Z]+/)
    ?.filter(Boolean)
    ?.splice(0, 2)
    ?.map((word) => word.charAt(0).toUpperCase())
    ?.join("");

  const [error, setError] = useState(!url || url.length === 0);

  useEffect(() => {
    setError(!url || url.length === 0);
  }, [url]);

  const handleImageError = () => {
    setError(true);
  };

  const sizeMap = {
    small: {
      sizeClassLogo: "size-4 flex items-center justify-center rounded-[1rem]",
      typographyClass: "body-r"
    },
    medium: {
      sizeClassLogo: "size-6 flex items-center justify-center rounded-[1rem]",
      typographyClass: "caption-m"
    },
    large: {
      sizeClassLogo: "size-8 flex items-center justify-center rounded-[1rem]",
      typographyClass: "heading-2-b"
    }
  };

  const { sizeClassLogo, typographyClass } =
    sizeMap[logoSize] || sizeMap.medium;

  if (getPresignedURL?.isLoading) {
    return (
      <div className={`${sizeClassLogo} border border-neutral-5`}>
        <span
          className={`${sizeClassLogo} ${typographyClass} ${
            companyName ? "bg-noticeable-100 text-white" : "bg-white"
          }`}
        >
          {companyInitials}
        </span>
      </div>
    );
  }

  return error ? (
    <div className={`${sizeClassLogo} border border-neutral-5`}>
      {url ? (
        <IconLogoPlaceholder data-testid="icon-logo-placeholder" />
      ) : (
        <span
          className={`${sizeClassLogo} ${typographyClass} ${
            companyName ? "bg-noticeable-100 text-white" : "bg-white"
          }`}
        >
          {companyInitials}
        </span>
      )}
    </div>
  ) : (
    <div className={`${sizeClassLogo} border border-neutral-5`}>
      <img
        className={`object-contain ${sizeClassLogo} bg-white`}
        src={url}
        alt="entity-logo"
        onError={handleImageError}
      />
    </div>
  );
};

EntityLogo.propTypes = {
  acuityID: PropTypes.string,
  companyName: PropTypes.string,
  logoSize: PropTypes.oneOf(["small", "medium", "large"])
};
export default EntityLogo;
