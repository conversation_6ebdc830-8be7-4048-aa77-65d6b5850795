import React from "react";
import PropTypes from "prop-types";
import "./radio.css";

const RadioButton = ({ options, name, selectedValue, onChange }) => {
  return (
    <div className="radio-button-container">
      {options.map((option, index) => (
        <label key={option.value} className="radio-button-label gap-3">
          <input
            data-testid={"radio-button-input"}
            type="radio"
            name={name}
            value={option.value}
            checked={selectedValue === option.value}
            onChange={onChange}
            className="radio-button-input"
          />
          <span className="radio-button-outer">
            <span
              className={`radio-button-inner ${selectedValue === option.value ? "" : "hidden"}`}
            ></span>
          </span>
          <span className="radio-button-text">{option.label}</span>
        </label>
      ))}
    </div>
  );
};

RadioButton.propTypes = {
  options: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      value: PropTypes.string.isRequired
    })
  ).isRequired,
  name: PropTypes.string.isRequired,
  selectedValue: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired
};

export default RadioButton;
