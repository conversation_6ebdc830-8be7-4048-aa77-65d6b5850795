import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import { DatePicker } from "@progress/kendo-react-dateinputs";
import {
  FILING_TYPES,
  DOCUMENT_STATUS_TYPES
} from "../../constants/column-edit-constants";
import Dropdown from "../dropdown";
import { PopUp } from "../../partials/molecules/pop-up";

const EditPopup = ({
  showEditColumnPopup,
  setShowEditColumnPopup,
  inputValues,
  onSave,
  onCancel,
  editingColumnIds,
  selectedTab,
  actionType,
  columnDetails
}) => {
  // Add state to track if form has been modified
  const [formModified, setFormModified] = useState(false);

  // Initialize state first
  const [values, setValues] = useState(() => {
    if (actionType === "insertLeft" || actionType === "insertRight") {
      return {
        Filing_Type: inputValues.Filing_Type || "",
        Filing_Version: inputValues.Filing_Version || "",
        Filing_Date: inputValues.Filing_Date || null
      };
    } else {
      return {
        Filing_Type: columnDetails.Filing_Type || "",
        Filing_Version: columnDetails.Filing_Version || "",
        Filing_Date: null
      };
    }
  });

  // Original values for comparison - also respect the action type
  const [originalValues, setOriginalValues] = useState(() => {
    if (actionType === "insertLeft" || actionType === "insertRight") {
      return {
        Filing_Type: "",
        Filing_Version: "",
        Filing_Date: null
      };
    } else {
      return {
        Filing_Type: columnDetails.Filing_Type || "",
        Filing_Version: columnDetails.Filing_Version || "",
        Filing_Date: null
      };
    }
  });

  // Handle date conversion in useEffect to properly debug and handle date conversion
  useEffect(() => {
    // Only set date from columnDetails if we're editing, not adding new columns
    if (columnDetails.Filing_Date && actionType === "edit") {
      try {
        let dateValue = null;

        // Handle different possible formats
        if (columnDetails.Filing_Date instanceof Date) {
          dateValue = columnDetails.Filing_Date;
        }
        // If it's a timestamp number
        else if (!isNaN(Number(columnDetails.Filing_Date))) {
          dateValue = new Date(Number(columnDetails.Filing_Date));
        }
        // If it's an ISO string or any other string format
        else if (typeof columnDetails.Filing_Date === "string") {
          // Try direct conversion first
          dateValue = new Date(columnDetails.Filing_Date);

          // If that fails, try parsing date parts (assuming MM/DD/YYYY or similar)
          if (isNaN(dateValue.getTime())) {
            const parts = columnDetails.Filing_Date.split(/[-\/]/);
            if (parts.length === 3) {
              // Try different date part orders
              dateValue = new Date(parts[2], parts[1] - 1, parts[0]); // DD/MM/YYYY
              if (isNaN(dateValue.getTime())) {
                dateValue = new Date(parts[2], parts[0] - 1, parts[1]); // MM/DD/YYYY
              }
            }
          }
        }

        // Validate the date is valid
        if (dateValue && !isNaN(dateValue.getTime())) {
          setValues((prev) => ({ ...prev, Filing_Date: dateValue }));
          setOriginalValues((prev) => ({ ...prev, Filing_Date: dateValue }));
        } else {
          console.warn(
            "Could not convert to valid date:",
            columnDetails.Filing_Date
          );
        }
      } catch (e) {
        console.error("Error parsing date:", e);
      }
    }
  }, [columnDetails.Filing_Date, actionType]);

  // Check if the form values have changed from original
  useEffect(() => {
    const isModified =
      values.Filing_Type !== originalValues.Filing_Type ||
      values.Filing_Version !== originalValues.Filing_Version ||
      (values.Filing_Date &&
        originalValues.Filing_Date &&
        values.Filing_Date.getTime() !==
          originalValues.Filing_Date.getTime()) ||
      (values.Filing_Date && !originalValues.Filing_Date) ||
      (!values.Filing_Date && originalValues.Filing_Date);

    setFormModified(isModified);
  }, [values, originalValues]);

  // Extract filing types and status types for dropdown items
  const filingTypeItems = FILING_TYPES.map((option) => option.label);
  const statusTypeItems = DOCUMENT_STATUS_TYPES.map((option) => option.label);

  // Determine heading text based on action type
  const getHeadingText = () => {
    if (actionType === "insertLeft" || actionType === "insertRight") {
      return "Add new column";
    }
    return "Edit column details"; // Default is edit
  };

  const handleFilingTypeChange = (selectedValue) => {
    // Find the corresponding value for the selected label
    const option = FILING_TYPES.find((opt) => opt.label === selectedValue);
    setValues((prevValues) => ({
      ...prevValues,
      Filing_Type: option ? option.value : ""
    }));
  };

  const handleStatusChange = (selectedValue) => {
    // Find the corresponding value for the selected label
    const option = DOCUMENT_STATUS_TYPES.find(
      (opt) => opt.label === selectedValue
    );
    setValues((prevValues) => ({
      ...prevValues,
      Filing_Version: option ? option.value : ""
    }));
  };

  const handleDateChange = (e) => {
    setValues((prevValues) => ({
      ...prevValues,
      Filing_Date: e.value
    }));
  };

  const handleSave = () => {
    // Make sure all three values are included in the saved data
    const columnKey =
      editingColumnIds && editingColumnIds.length === 1
        ? editingColumnIds[0]
        : null;

    // Pass all three field values along with column key and tab index
    onSave({
      Filing_Type: values.Filing_Type,
      Filing_Version: values.Filing_Version,
      Filing_Date: values.Filing_Date,
      columnKey,
      tabIndex: selectedTab
    });
  };

  // Find the label for the selected value
  const getSelectedFilingTypeLabel = () => {
    const option = FILING_TYPES.find((opt) => opt.value === values.Filing_Type);
    return option ? option.label : "Select filing type";
  };

  const getSelectedStatusLabel = () => {
    const option = DOCUMENT_STATUS_TYPES.find(
      (opt) => opt.value === values.Filing_Version
    );
    return option ? option.label : "Select status";
  };

  return (
    <PopUp
      colGrid={"grid-cols-12"}
      header={getHeadingText()}
      showPopUp={showEditColumnPopup}
      setShowPopUp={onCancel}
      cancelButtonText={"Cancel"}
      submitButtonText={"Save"}
      disabled={!formModified}
      onSubmit={handleSave}
      cols={"col-span-4 col-start-5"}
    >
      <div className="p-6 py-4">
        <div className="mb-4">
          <label className="xs-m font-medium text-neutral-60">
            Filing Type
          </label>
          <div className="mt-1">
            <Dropdown
              items={filingTypeItems}
              placeholder="Select filing type"
              selectedValue={getSelectedFilingTypeLabel()}
              setSelectedValue={handleFilingTypeChange}
            />
          </div>
        </div>
        <div className="mb-4"></div>
        <div className="mb-4">
          <label className="xs-m font-medium text-neutral-60">
            Original/Restated
          </label>
          <div className="mt-1">
            <Dropdown
              items={statusTypeItems}
              placeholder="Select status"
              selectedValue={getSelectedStatusLabel()}
              setSelectedValue={handleStatusChange}
            />
          </div>
        </div>
        <div className="mb-4">
          <label className="xs-m font-medium text-neutral-60">
            Filing date
          </label>
          <div className="mt-1 relative edit-column-date-picker">
            <DatePicker
              placeholder="Select filing date"
              name="Filing_Date"
              max={new Date()}
              format={"dd-MM-yyyy"}
              onChange={handleDateChange}
              value={values.Filing_Date}
              className="ao-custom-button-background"
            />
          </div>
        </div>
      </div>
    </PopUp>
  );
};

EditPopup.propTypes = {
  showEditColumnPopup: PropTypes.bool,
  setShowEditColumnPopup: PropTypes.func,
  inputValues: PropTypes.shape({
    Filing_Type: PropTypes.string,
    Filing_Version: PropTypes.string,
    Filing_Date: PropTypes.instanceOf(Date)
  }).isRequired,
  onSave: PropTypes.func.isRequired,
  onCancel: PropTypes.func.isRequired,
  editingColumnIds: PropTypes.array,
  selectedTab: PropTypes.number,
  actionType: PropTypes.string,
  columnDetails: PropTypes.object
};

export default EditPopup;
