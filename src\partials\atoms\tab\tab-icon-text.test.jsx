import { render, screen } from "@testing-library/react";
import TabIconText from "./tab-icon-text";

describe("Component TabIconText render", () => {
  it("should render TabIconText without active", () => {
    render(
      <TabIconText
        onClick={() => {}}
        icon={""}
        label={"test"}
        testId="tab-icon"
        active={false}
      />
    );
    expect(screen.getByTestId("tab-icon")).toBeInTheDocument();
  });
  it("should render TabIconText with active", () => {
    render(
      <TabIconText
        onClick={() => {}}
        icon={""}
        label={"test"}
        testId="tab-icon"
        active={true}
      />
    );
    expect(screen.getByTestId("tab-icon")).toBeInTheDocument();
  });
});
