/* Overlay */
.custom-drawer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  transition: opacity 0.3s ease-in-out;
}

/* Drawer */
.custom-drawer {
  position: fixed;
  background: #fff;
  z-index: 1000;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  transition: all 0.3s ease-in-out;
  overflow: auto;
}

.custom-drawer-left,
.custom-drawer-right {
  top: 0;
  bottom: 0;
}

.custom-drawer-top,
.custom-drawer-bottom {
  left: 0;
  right: 0;
}

