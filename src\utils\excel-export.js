import ExcelJS from "exceljs";
import { saveAs } from "file-saver";

/**
 * Generate and download an Excel file with data from all tabs
 * 
 * @param {Object} tabs - Array of tab objects with name property
 * @param {Object} tabData - Object containing data for all tabs
 * @param {Object} tabData.consolidated - Consolidated tab data and periods
 * @param {Object} tabData.financial - Financial tab data and periods
 * @param {Object} tabData.masterData - Master Data tab data and periods
 * @param {Object} tabData.investmentKpi - Investment KPI tab data and periods
 * @param {string} fundName - Name of the fund to use in the filename
 */
export const generateExcelDownload = (tabData,
  fundName = "Growth_Equity_Fund"
) => {
  // Create a new workbook
  const workbook = new ExcelJS.Workbook();
  workbook.creator = 'Foliosure';
  workbook.lastModifiedBy = 'Foliosure';
  workbook.created = new Date(); 
  console.log("Generating sheet for:", tabData);
  // Process each tab and add as sheet
  tabData.forEach(({ sheetName,isCompanyLevel, currency, unit, periods, data }) => {
    // Create a new worksheet
    
    const worksheet = workbook.addWorksheet(sheetName, {
      properties: {
        tabColor: { argb: '4061C7' }
      }
    });

    // Calculate total columns needed
    let totalColumns = 1; // First column for row labels
    periods.forEach(period => {
      totalColumns += period.columns || 1;
    });

    // Define styles
    const titleStyle = {
      font: { bold: true, size: 14 },
      alignment: { horizontal: 'center', vertical: 'middle' }
    };

    const labelStyle = {
      font: { bold: true },
      alignment: { horizontal: 'left', vertical: 'middle' }
    };

    const valueStyle = {
      font: { italic: true },
      alignment: { horizontal: 'left', vertical: 'middle' }
    };

    const headerPeriodStyle = {
      font: { bold: true },
      alignment: { horizontal: 'center', vertical: 'middle' },
      fill: {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'E6F2FF' }
      },
      border: {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      }
    };

    const headerKpiStyle = {
      font: { bold: true },
      alignment: { horizontal: 'center', vertical: 'middle' },
      fill: {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'F2F9FF' }
      },
      border: {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      }
    };

    const rowLabelStyle = {
      font: { bold: true },
      alignment: { horizontal: 'left', vertical: 'middle' },
      fill: {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'F9F9F9' }
      },
      border: {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      }
    };

    const dataCellStyle = {
      alignment: { horizontal: 'right', vertical: 'middle' },
      border: {
        top: { style: 'thin' },
        left: { style: 'thin' },
        bottom: { style: 'thin' },
        right: { style: 'thin' }
      }
    };

    // Set column widths
    worksheet.columns = [
      { header: '', width: 15 }, // A - Empty column
      { header: '', width: 35 }, // B - Row label column
      ...Array(totalColumns).fill({ width: 15 }) // Data columns
    ];

    // Add title row
    const titleRow = worksheet.addRow(['', sheetName]);
    titleRow.height = 30;
    const titleCell = titleRow.getCell(2);
    titleCell.style = titleStyle;
    // Merge title cells
    worksheet.mergeCells(`B1:${worksheet.getColumn(totalColumns + 2).letter}1`);

    // Add fund info
    const fundRow = worksheet.addRow(['', 'Fund', fundName]);
    fundRow.getCell(2).style = labelStyle;
    fundRow.getCell(3).style = valueStyle;

    // Add currency info
    const currencyRow = worksheet.addRow(['', 'Currency', currency]);
    currencyRow.getCell(2).style = labelStyle;
    currencyRow.getCell(3).style = valueStyle;

    // Add unit info
    const unitRow = worksheet.addRow(['', 'Unit', unit]);
    unitRow.getCell(2).style = labelStyle;
    unitRow.getCell(3).style = valueStyle;

    // Add empty row for spacing
    worksheet.addRow([]);

    // Create header arrays for all three rows
    let headerRow1 = [];
    let headerRow2 = [];
    let headerRow3 = [];
     // Track column merges for period headers
    let currentColIndex = 3; // Start from column C (index 2 in ExcelJS)
    let currentRowIndex = 7; // Start from row 7 (index 6 in ExcelJS)
    if( isCompanyLevel) {
      headerRow1 = ["","Time Period:", "Company Currency", "Company Unit"];
      headerRow2 = ["","Standard KPI", "", ""];
      headerRow3 = ["","Document KPI", "", ""];
      currentColIndex += 2; // Adjust for Company Currency and Unit
    } else {
      headerRow1 = ["","Time Period:"];   
      headerRow2 = ["","Standard KPI"];
      headerRow3 = ["","Document KPI"];
    }

   
    const periodMerges = [];

    // Build headers
    periods.forEach((period, periodIndex) => {
      const colSpan = period.columns || 1;
      const periodLabel = period.label || "(No Period)";
      headerRow1.push(periodLabel);
      
      // Add empty cells for merge if needed
      for (let i = 1; i < colSpan; i++) {
        headerRow1.push('');
      }
      
      // Record merge range if span > 1
      if (colSpan > 1) {
        const startCol = currentColIndex;
        const endCol = currentColIndex + colSpan - 1;
        periodMerges.push(
          `${worksheet.getColumn(startCol).letter + currentRowIndex}:` +
          `${worksheet.getColumn(endCol).letter + currentRowIndex}`
        );
      }
      
      // Add Standard KPI names in second row
      for (let colIndex = 0; colIndex < colSpan; colIndex++) {
        const kpiName = period.selectedKpis?.[colIndex]?.text || `Column ${colIndex + 1}`;
        headerRow2.push(kpiName);
      }
      
      // Add Document KPI names in third row
      for (let colIndex = 0; colIndex < colSpan; colIndex++) {
        // Use document KPIs if available, otherwise use placeholder
        if (period.documentKpis && period.documentKpis[colIndex]) {
          headerRow3.push(period.documentKpis[colIndex].text || `Doc KPI ${colIndex + 1}`);
        } else {
          headerRow3.push(`Doc KPI ${colIndex + 1}`);
        }
      }
      
      currentColIndex += colSpan;
    });

    // Add header rows to worksheet
    const row1 = worksheet.addRow(headerRow1);
    const row2 = worksheet.addRow(headerRow2);
    const row3 = worksheet.addRow(headerRow3);

    if (isCompanyLevel) {
      // Merge cells for rowspan (after adding header rows)
    worksheet.mergeCells('C7:C9'); // Company Currency
    worksheet.mergeCells('D7:D9'); // Company Unit
    }



    // Apply header row styles to ALL cells including column F (index 6)
    for (let i = 1; i <= headerRow1.length; i++) {
      // Skip the first empty cell
      if (i >= 2) {
        row1.getCell(i).style = headerPeriodStyle;
        row2.getCell(i).style = headerKpiStyle;
        row3.getCell(i).style = headerKpiStyle;
      }
    }

    // Apply period header cell merges
    periodMerges.forEach(range => {
      worksheet.mergeCells(range);
    });

    // Add data rows
data.forEach(row => {
  // Add values for the first two columns: Company Currency and Company Unit
  const rowData = ["",
     row.label                 // Row label (if you want to keep this as a third column)
  ];
  if( isCompanyLevel) {
    rowData.push(row.currencyCode || ''); // Company Currency
    rowData.push(row.unit || ''); // Company Unit
  }

  // Add all values for period columns
  periods.forEach((period, periodIndex) => {
        for (let colIndex = 0; colIndex < (period.columns || 1); colIndex++) {
          // For compatibility with both array-based and object-based data structures
          let cellValue;
          
          if (Array.isArray(row.values)) {
            // If using array-based structure: row.values[periodIndex][colIndex]
            cellValue = row.values[periodIndex]?.[colIndex]?.value;
          } else {
            // If using object-based structure with periodId_kpiId keys
            const periodId = period.periodId;
            let kpiId = period.documentKpis?.[colIndex]?.kpiId;
            if(kpiId=== undefined && period.selectedKpis && period.selectedKpis[colIndex]) {
              kpiId = period.selectedKpis?.[colIndex]?.kpiId;
            }
            if (periodId && kpiId) {
              cellValue = row.values[`${periodId}_${kpiId}`]?.value;
            }
          }
          
          // Handle undefined or null values
          rowData.push(cellValue !== undefined && cellValue !== null ? cellValue : '');
        }
      });

  // Add row to worksheet
  const excelRow = worksheet.addRow(rowData);
  let j=2;
  if (isCompanyLevel) {
  // Apply styles to row
  excelRow.getCell(1).style = rowLabelStyle; // Company Currency
  excelRow.getCell(2).style = rowLabelStyle; // Company Unit
  excelRow.getCell(3).style = rowLabelStyle; // Row label (if present)
  j=4;
  }else {
    // Apply styles to row
    excelRow.getCell(2).style = rowLabelStyle; // Row label
  }

  // Apply data cell styles to ALL cells (including column F)
  for (let i = j; i <= rowData.length; i++) {
    const cell = excelRow.getCell(i);
    cell.style = dataCellStyle;
  }
});
    

    // Specifically ensure column F has styles applied
    // This is a safeguard to ensure column F definitely gets styled
    // worksheet.getColumn(6).eachCell({ includeEmpty: false }, (cell, rowNumber) => {
    //   // Skip header rows (1-5)
    //   if (rowNumber <= 5) return;
      
    //   // Apply header styles to rows 6-8 (the three header rows)
    //   if (rowNumber >= 6 && rowNumber <= 8) {
    //     if (rowNumber === 6) {
    //       cell.style = headerPeriodStyle;
    //     } else {
    //       cell.style = headerKpiStyle;
    //     }
    //   } 
    //   // Apply data styles to all other rows
    //   else if (rowNumber > 8) {
    //     cell.style = dataCellStyle;
        
    //     // Format numbers
    //     const value = cell.value;
    //     if (typeof value === 'number' || (typeof value === 'string' && !isNaN(Number(value)))) {
    //       cell.numFmt = '#,##0.00';
    //       if (typeof value === 'string') {
    //         cell.value = Number(value);
    //       }
    //     }
    //   }
    // });

    // Freeze panes to keep headers visible when scrolling
    worksheet.views = [
      { state: 'frozen', xSplit: 2, ySplit: 9, activeCell: 'C10' }
    ];
  });

  // Generate Excel file and trigger download
  workbook.xlsx.writeBuffer().then(buffer => {    
    const timestamp = getTimestamp(); // or use new Date().toISOString() for a readable format
    const fileName = `${fundName?.replace(/\s+/g, '_')}_${timestamp}.xlsx`;
    const blob = new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    saveAs(blob, fileName);
  });
  const getTimestamp= () =>{
    const now = new Date();
    const pad = (n, z = 2) => n.toString().padStart(z, '0');
    const date = `${now.getFullYear()}-${pad(now.getMonth() + 1)}-${pad(now.getDate())}`;
    const time = `${pad(now.getHours())}-${pad(now.getMinutes())}-${pad(now.getSeconds())}`;   
    return `${date}_${time}`;
}
};
