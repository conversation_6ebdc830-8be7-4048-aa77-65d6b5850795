import { ExcelExportColumn } from "@progress/kendo-react-excel-export";
import {
  FinancialStatements,
  EXPORT_EXCEL_MESSAGES,
  MARK_AS_HEADER_MESSAGES
} from "../.././constants";

export const handleExport = (
  exportGridOneRef,
  exportGridTwoRef,
  exportGridThreeRef,
  exportGridFourRef,
  companyName,
  notify,
  currency,
  currencyUnit,
  kpiConfig
) => {
  const optionsGridOne = exportGridOneRef.current.workbookOptions();
  const optionsGridTwo = exportGridTwoRef.current.workbookOptions();
  const optionsGridThree = exportGridThreeRef.current.workbookOptions();

  // Add companyName to each sheet's header
  const headerRow1 = {
    height: 40,
    cells: [{ value: `${companyName}`, colSpan: 2, bold: true }]
  };

  const headerRow2 = {
    height: 20,
    cells: [
      { value: "", colSpan: 2 },
      { value: "Currency", colSpan: 1, bold: true },
      { value: currency, colSpan: 1 }
    ]
  };

  const headerRow3 = {
    height: 20,
    cells: [
      { value: "", colSpan: 2 },
      { value: "Units", colSpan: 1, bold: true },
      { value: currencyUnit, colSpan: 1 }
    ]
  };

  const emptyRow = {
    height: 20,
    cells: []
  };
  // Income Statement sheet
  optionsGridOne.sheets[0].frozenRows = 4;
  optionsGridOne.sheets[0].rows.unshift(emptyRow);
  optionsGridOne.sheets[0].rows.unshift(headerRow3);
  optionsGridOne.sheets[0].rows.unshift(headerRow2);
  optionsGridOne.sheets[0].rows.unshift(headerRow1);
  optionsGridOne.sheets[0].title = kpiConfig.find(x => x.moduleName === "ProfitAndLoss")?.pageConfigAliasName || FinancialStatements[0];

  // Balance Sheet sheet
  optionsGridTwo.sheets[0].frozenRows = 4;
  optionsGridTwo.sheets[0].rows.unshift(emptyRow);
  optionsGridTwo.sheets[0].rows.unshift(headerRow3);
  optionsGridTwo.sheets[0].rows.unshift(headerRow2);
  optionsGridTwo.sheets[0].rows.unshift(headerRow1);
  optionsGridTwo.sheets[0].title = kpiConfig.find(x => x.moduleName === "BalanceSheet")?.pageConfigAliasName || FinancialStatements[1];

  // Cashflow sheet
  optionsGridThree.sheets[0].frozenRows = 4;
  optionsGridThree.sheets[0].rows.unshift(emptyRow);
  optionsGridThree.sheets[0].rows.unshift(headerRow3);
  optionsGridThree.sheets[0].rows.unshift(headerRow2);
  optionsGridThree.sheets[0].rows.unshift(headerRow1);
  optionsGridThree.sheets[0].title = kpiConfig.find(x => x.moduleName === "CashFlow")?.pageConfigAliasName || FinancialStatements[2];


  // Combine sheets into one workbook
  optionsGridOne.sheets[1] = optionsGridTwo.sheets[0];
  optionsGridOne.sheets[2] = optionsGridThree.sheets[0];

  exportGridOneRef.current.save(optionsGridOne);

  setTimeout(() => {
  notify.success(EXPORT_EXCEL_MESSAGES.EXPORT_SUCCESS);
  }, 2000);
};

export const renderExcelExportColumns = (
  columnNames,
  isMapped,
  isNotesTable = false,
  columnHeaderInfo = {}
) => {
  const columns = columnNames
    .map((columnName, index) => {
      if (/^\w{2}-\d{4}\|\w*\|\d{2}-\d{2}-\d{4}\|\w*$/.test(columnName.field)) {
        const parts = columnName.field.split("|");
        const dateParts = parts[2].split("-");
        const date = new Date(
          `${dateParts[2]}-${dateParts[1]}-${dateParts[0]}`
        );
        if (isNaN(date.getTime())) {
          console.error("Invalid date:", parts[2]);
          return null;
        }

        const formattedDate = `${dateParts[0]}-${dateParts[1]}-${dateParts[2]}`;
        //const fullColumnName = formattedDate + " | Original | ANL";
        const fullColumnName = formattedDate;
        return (
          <ExcelExportColumn
            key={columnName.field}
            field={columnName.field}
            title={fullColumnName}
            width={180}
          />
        );      } else if (
        columnName.field === "" ||
        columnName.field === "null|null|null|null"
      ) {
        // Use the title from columnName if available, otherwise use empty string
        const newColumnName = columnName.title || "";
        return (
          <ExcelExportColumn
            key={columnName.field}
            field={columnName.field}
            title={newColumnName}
            width={180}
          />
        );
      }
      return (
        <ExcelExportColumn
          key={columnName.field}
          field={columnName.field}
          title={columnName.title}
          width={180}
        />
      );
    });
  return columns;
};

/**
 * Converts a row into a header row by changing its style and clearing all value cells.
 * Returns an object with the result status, message, and updated data.
 * 
 * @param {Array} currentTableData - Current table data array
 * @param {string} selectedRowId - ID of the row to be converted to header
 * @returns {Object} Result object with success status, message and updated data
 */
export const makeHeaderRow = (currentTableData, selectedRowId) => {
  // Find the selected row
  const selectedRow = currentTableData.find(row => row.id === selectedRowId);
  if (!selectedRow) return { success: false, message: "Selected row not found", updatedData: currentTableData };

  // List of fields to ignore when checking for values
  const ignoredFields = ['id', 'label', 'definedName', 'style', 'cellIds', 'pdfHighlights', 'periodDates', 'columnIds']; // Removed 'status' from ignored fields

  // Check if row has any values in any columns except the ignored ones
  const hasValues = Object.keys(selectedRow)
    .filter(key => !ignoredFields.includes(key) && key !== 'status') // Exclude the ignored fields, but consider 'status' separately
    .some(key => {
      const value = selectedRow[key];
      // Check if the value is non-empty (not null, undefined, empty string, or just whitespace)
      return value !== null &&
        value !== undefined &&
        value !== "" &&
        !(typeof value === 'string' && value.trim() === "");
    });

  // Only proceed if the row doesn't have any values
  if (!hasValues) {
    // Create updated row with style 'header' and all values cleared
    const updatedData = currentTableData.map(row => {
      if (row.id === selectedRowId) {
        // Create a new object for the updated row
        const updatedRow = {
          ...row,
          style: 'header',
          status: '' // Explicitly clear the status field for headers
        };

        // Explicitly set all value fields to null
        Object.keys(row)
          .filter(key => !ignoredFields.includes(key) && key !== 'status') // Exclude status as we've already handled it
          .forEach(key => {
            // Ensure we're setting to null, not empty string
            updatedRow[key] = null;
          });

        return updatedRow;
      }
      return row;
    });

    return {
      success: true,
      message: MARK_AS_HEADER_MESSAGES.EXPORT_SUCCESS,
      updatedData
    };
  } else {
    return {
      success: false,
      message: MARK_AS_HEADER_MESSAGES.EXPORT_ERROR,
      updatedData: currentTableData
    };
  }
};

export const formatDateWithHyphens = (date) => {
  if (!date) return "";
  
  // If the date is already in the desired format like "actual jan 2025", return it as is
  if (typeof date === 'string' && date.includes(' ')) {
    return date;
  }

  // Otherwise, parse the date and format it as "DD-MM-YYYY"
  try {
    const d = new Date(date);
    if (isNaN(d.getTime())) return date; // Return original if not a valid date
    
    const day = String(d.getDate()).padStart(2, '0');
    const month = String(d.getMonth() + 1).padStart(2, '0'); // Months are 0-based
    const year = d.getFullYear();
    return `${day}-${month}-${year}`;
  } catch (e) {
    console.error("Error formatting date:", e);
    return date; // Return original in case of error
  }
};