import React, { useRef, useCallback, useEffect } from "react";

interface Props {
  onMoveAway: () => void;
  paddingX: number;
  paddingY: number;
  children: JSX.Element;
}

export const MouseMonitor: React.FC<Props> = ({
  onMoveAway,
  paddingX,
  paddingY,
  children,
  ...restProps
}) => {
  const containerRef = useRef<HTMLDivElement | null>(null);

  const onMouseMove = useCallback(
    (event: MouseEvent) => {
      if (!containerRef.current) {
        return;
      }

      const { clientX, clientY } = event;
      const { left, top, width, height } =
        containerRef.current.getBoundingClientRect();

      const inBoundsX =
        clientX > left - paddingX && clientX < left + width + paddingX;
      const inBoundsY =
        clientY > top - paddingY && clientY < top + height + paddingY;

      const isNear = inBoundsX && inBoundsY;

      if (!isNear) {
        onMoveAway();
      }
    },
    [onMoveAway, paddingX, paddingY]
  );

  useEffect(() => {
    const currentContainer = containerRef.current;
    if (currentContainer) {
      const doc = currentContainer.ownerDocument;
      doc.addEventListener("mousemove", onMouseMove);
      return () => {
        doc.removeEventListener("mousemove", onMouseMove);
      };
    }
  }, [onMouseMove]);

  return (
    <div ref={containerRef}>{React.cloneElement(children, restProps)}</div>
  );
};
