import { useQuery } from "@tanstack/react-query";
import { post } from "../../../general/fetcher";
import { minutesToMilliseconds, minutesToHours } from "date-fns";

const CONTROLLER = "datalake";

export const SEARCH_COMPANY_API_URL = `${CONTROLLER}/entity`;
export const ENTITY_LOGO_URL = `${CONTROLLER}/entity/logo`;
export const COMPANY_RESPONSE ={
  "data": [
      {
          "name": "Esval S.A.",
          "acuity_id": "CLPU372473655",
          "acuity_security_id": "CLPU372473655",
          "is_public": true,
          "is_primary": true,
          "security_ticker": "ESVAL-C",
          "security_exchange": null,
          "search_result_from": [
              "name",
              "is_primary"
          ],
          "logo_url": [
              {
                  "source": "usearch",
                  "value": "https://res.cloudinary.com/zoominfo-images/image/upload/w_120,h_120,c_fit/esval.cl"
              }
          ],
          "primary_exchange": "SNSE",
          "headquarter": {
              "country": [
                  {
                      "source": "capeq",
                      "value": "Chile"
                  }
              ]
          },
          "sector": null,
          "primary_ticker": "ESVAL-C"
      },
      {
          "name": "Phoenix Power Company Saog",
          "acuity_id": "OMPU399354380",
          "acuity_security_id": "OMPU399354380",
          "is_public": true,
          "is_primary": true,
          "security_ticker": "PHPC",
          "security_exchange": null,
          "search_result_from": [
              "name",
              "is_primary"
          ],
          "logo_url": [
              {
                  "source": "usearch",
                  "value": "https://res.cloudinary.com/zoominfo-images/image/upload/w_120,h_120,c_fit/phoenixpoweroman.com"
              }
          ],
          "primary_exchange": "MSM",
          "headquarter": {
              "country": [
                  {
                      "source": "capeq",
                      "value": "Oman"
                  }
              ]
          },
          "sector": null,
          "primary_ticker": "PHPC"
      },
      {
          "name": "Dhofar Generating Company Saog",
          "acuity_id": "OMPU368343734",
          "acuity_security_id": "OMPU368343734",
          "is_public": true,
          "is_primary": true,
          "security_ticker": "DGEN",
          "security_exchange": null,
          "search_result_from": [
              "name",
              "is_primary"
          ],
          "logo_url": [
              {
                  "source": "usearch",
                  "value": "https://res.cloudinary.com/zoominfo-images/image/upload/w_120,h_120,c_fit/dgcoman.com"
              }
          ],
          "primary_exchange": "MSM",
          "headquarter": {
              "country": [
                  {
                      "source": "capeq",
                      "value": "Oman"
                  }
              ]
          },
          "sector": null,
          "primary_ticker": "DGEN"
      },
      {
          "name": "Dhofar Food & Investment Saog",
          "acuity_id": "OMPU258186109",
          "acuity_security_id": "OMPU258186109",
          "is_public": true,
          "is_primary": true,
          "security_ticker": "DFIN",
          "security_exchange": null,
          "search_result_from": [
              "name",
              "is_primary"
          ],
          "logo_url": null,
          "primary_exchange": "MSM",
          "headquarter": {
              "country": [
                  {
                      "source": "capeq",
                      "value": "Oman"
                  }
              ]
          },
          "sector": null,
          "primary_ticker": "DFIN"
      },
      {
          "name": "One Great Studio Company Limited",
          "acuity_id": "JMPU358672865",
          "acuity_security_id": "JMPU358672865",
          "is_public": true,
          "is_primary": true,
          "security_ticker": "1GS",
          "security_exchange": null,
          "search_result_from": [
              "name",
              "is_primary"
          ],
          "logo_url": [
              {
                  "source": "usearch",
                  "value": "https://res.cloudinary.com/zoominfo-images/image/upload/w_120,h_120,c_fit/onegreatstudio.com"
              }
          ],
          "primary_exchange": "JMSE",
          "headquarter": {
              "country": [
                  {
                      "source": "capeq",
                      "value": "Jamaica"
                  }
              ]
          },
          "sector": null,
          "primary_ticker": "1GS"
      },
      {
          "name": "Uni\u00f3n El Golf S.A.",
          "acuity_id": "CLPU137827017",
          "acuity_security_id": "CLPU137827017",
          "is_public": true,
          "is_primary": true,
          "security_ticker": "UNION GOLF",
          "security_exchange": null,
          "search_result_from": [
              "name",
              "is_primary"
          ],
          "logo_url": null,
          "primary_exchange": "SNSE",
          "headquarter": {
              "country": [
                  {
                      "source": "capeq",
                      "value": "Chile"
                  }
              ]
          },
          "sector": null,
          "primary_ticker": "UNION GOLF"
      },
      {
          "name": "Semantix, Inc.",
          "acuity_id": "BRPU409510077",
          "acuity_security_id": "BRPU409510077",
          "is_public": true,
          "is_primary": true,
          "security_ticker": "STIX.F",
          "security_exchange": null,
          "search_result_from": [
              "name",
              "security_ticker",
              "is_primary"
          ],
          "logo_url": [
              {
                  "source": "usearch",
                  "value": "https://res.cloudinary.com/zoominfo-images/image/upload/w_120,h_120,c_fit/semantix.ai"
              }
          ],
          "primary_exchange": "OTCPK",
          "headquarter": {
              "country": [
                  {
                      "source": "capeq",
                      "value": "Brazil"
                  }
              ]
          },
          "sector": [
              {
                  "source": "eod",
                  "value": [
                      "Technology"
                  ]
              }
          ],
          "primary_ticker": "STIX.F"
      },
      {
          "name": "Next Geosolutions Europe Spa",
          "acuity_id": "ITPU23840652",
          "acuity_security_id": "ITPU23840652",
          "is_public": true,
          "is_primary": true,
          "security_ticker": "NXT",
          "security_exchange": null,
          "search_result_from": [
              "name",
              "is_primary"
          ],
          "logo_url": null,
          "primary_exchange": "BIT",
          "headquarter": {
              "country": [
                  {
                      "source": "capeq",
                      "value": "Italy"
                  }
              ]
          },
          "sector": null,
          "primary_ticker": "NXT"
      },
      {
          "name": "La Foresti\u00e8re Equatoriale Sa",
          "acuity_id": "FRPU61257785",
          "acuity_security_id": "FRPU61257785",
          "is_public": true,
          "is_primary": true,
          "security_ticker": "FORE",
          "security_exchange": null,
          "search_result_from": [
              "name",
              "is_primary"
          ],
          "logo_url": null,
          "primary_exchange": "ENXTPA",
          "headquarter": {
              "country": [
                  {
                      "source": "capeq",
                      "value": "France"
                  }
              ]
          },
          "sector": null,
          "primary_ticker": "FORE"
      },
      {
          "name": "Palingeo S.P.A.",
          "acuity_id": "ITPU197161403",
          "acuity_security_id": "ITPU197161403",
          "is_public": true,
          "is_primary": true,
          "security_ticker": "PAL",
          "security_exchange": null,
          "search_result_from": [
              "name",
              "is_primary"
          ],
          "logo_url": null,
          "primary_exchange": "BIT",
          "headquarter": {
              "country": [
                  {
                      "source": "capeq",
                      "value": "Italy"
                  }
              ]
          },
          "sector": null,
          "primary_ticker": "PAL"
      },
      {
          "name": "Bigrep Se",
          "acuity_id": "DEPU368086208",
          "acuity_security_id": "DEPU368086208",
          "is_public": true,
          "is_primary": true,
          "security_ticker": "B1GR",
          "security_exchange": null,
          "search_result_from": [
              "name",
              "is_primary"
          ],
          "logo_url": [
              {
                  "source": "usearch",
                  "value": "https://res.cloudinary.com/zoominfo-images/image/upload/w_120,h_120,c_fit/bigrep.com"
              }
          ],
          "primary_exchange": "XTRA",
          "headquarter": {
              "country": [
                  {
                      "source": "capeq",
                      "value": "Germany"
                  }
              ]
          },
          "sector": null,
          "primary_ticker": "B1GR"
      },
      {
          "name": "E-Globe S.P.A.",
          "acuity_id": "ITPU14721990",
          "acuity_security_id": "ITPU14721990",
          "is_public": true,
          "is_primary": true,
          "security_ticker": "EGB",
          "security_exchange": null,
          "search_result_from": [
              "name",
              "is_primary"
          ],
          "logo_url": null,
          "primary_exchange": "BIT",
          "headquarter": {
              "country": [
                  {
                      "source": "capeq",
                      "value": "Italy"
                  }
              ]
          },
          "sector": null,
          "primary_ticker": "EGB"
      },
      {
          "name": "La Sia S.P.A.",
          "acuity_id": "ITPU110223392",
          "acuity_security_id": "ITPU110223392",
          "is_public": true,
          "is_primary": true,
          "security_ticker": "LASIA",
          "security_exchange": null,
          "search_result_from": [
              "name",
              "is_primary"
          ],
          "logo_url": null,
          "primary_exchange": "BIT",
          "headquarter": {
              "country": [
                  {
                      "source": "capeq",
                      "value": "Italy"
                  }
              ]
          },
          "sector": null,
          "primary_ticker": "LASIA"
      },
      {
          "name": "Fabrity Holding S.A.",
          "acuity_id": "PLPU157885075",
          "acuity_security_id": "PLPU157885075",
          "is_public": true,
          "is_primary": true,
          "security_ticker": "FAB",
          "security_exchange": null,
          "search_result_from": [
              "name",
              "is_primary"
          ],
          "logo_url": null,
          "primary_exchange": "WSE",
          "headquarter": {
              "country": [
                  {
                      "source": "capeq",
                      "value": "Poland"
                  }
              ]
          },
          "sector": null,
          "primary_ticker": "FAB"
      },
      {
          "name": "Finance For Food S.P.A.",
          "acuity_id": "ITPU12310192",
          "acuity_security_id": "ITPU12310192",
          "is_public": true,
          "is_primary": true,
          "security_ticker": "FFF",
          "security_exchange": null,
          "search_result_from": [
              "name",
              "is_primary"
          ],
          "logo_url": null,
          "primary_exchange": "BIT",
          "headquarter": {
              "country": [
                  {
                      "source": "capeq",
                      "value": "Italy"
                  }
              ]
          },
          "sector": null,
          "primary_ticker": "FFF"
      },
      {
          "name": "Frendy Energy S.P.A.",
          "acuity_id": "ITPU12772557",
          "acuity_security_id": "ITPU12772557",
          "is_public": true,
          "is_primary": true,
          "security_ticker": "FRE",
          "security_exchange": null,
          "search_result_from": [
              "name",
              "is_primary"
          ],
          "logo_url": null,
          "primary_exchange": "BIT",
          "headquarter": {
              "country": [
                  {
                      "source": "capeq",
                      "value": "Italy"
                  }
              ]
          },
          "sector": null,
          "primary_ticker": "FRE"
      },
      {
          "name": "Litix S.P.A.",
          "acuity_id": "ITPU207392294",
          "acuity_security_id": "ITPU207392294",
          "is_public": true,
          "is_primary": true,
          "security_ticker": "LTX",
          "security_exchange": null,
          "search_result_from": [
              "name",
              "is_primary"
          ],
          "logo_url": null,
          "primary_exchange": "BIT",
          "headquarter": {
              "country": [
                  {
                      "source": "capeq",
                      "value": "Italy"
                  }
              ]
          },
          "sector": null,
          "primary_ticker": "LTX"
      },
      {
          "name": "Xenia H\u00f4tellerie Solution S.P.A. Societ\u00e0 Benefit",
          "acuity_id": "ITPU88961827",
          "acuity_security_id": "ITPU88961827",
          "is_public": true,
          "is_primary": true,
          "security_ticker": "XHS",
          "security_exchange": null,
          "search_result_from": [
              "name",
              "is_primary"
          ],
          "logo_url": null,
          "primary_exchange": "BIT",
          "headquarter": {
              "country": [
                  {
                      "source": "capeq",
                      "value": "Italy"
                  }
              ]
          },
          "sector": null,
          "primary_ticker": "XHS"
      },
      {
          "name": "Iflex Flexible Packaging, S.A.",
          "acuity_id": "ESPU131152711",
          "acuity_security_id": "ESPU131152711",
          "is_public": true,
          "is_primary": true,
          "security_ticker": "IFLEX",
          "security_exchange": null,
          "search_result_from": [
              "name",
              "is_primary"
          ],
          "logo_url": null,
          "primary_exchange": "BME",
          "headquarter": {
              "country": [
                  {
                      "source": "capeq",
                      "value": "Spain"
                  }
              ]
          },
          "sector": null,
          "primary_ticker": "IFLEX"
      },
      {
          "name": "Beewize S.P.A.",
          "acuity_id": "ITPU171596369",
          "acuity_security_id": "ITPU171596369",
          "is_public": true,
          "is_primary": true,
          "security_ticker": "BWZ",
          "security_exchange": null,
          "search_result_from": [
              "name",
              "is_primary"
          ],
          "logo_url": null,
          "primary_exchange": "BIT",
          "headquarter": {
              "country": [
                  {
                      "source": "capeq",
                      "value": "Italy"
                  }
              ]
          },
          "sector": null,
          "primary_ticker": "BWZ"
      }
  ],
  "isError": false,
  "message": "success",
  "meta": {
      "total_count": {
          "all": 969215,
          "public": null,
          "private": null
      },
      "page_size": 10,
      "page": 0,
      "from_index": 0,
      "request_id": "d6df39e3-d75c-45a4-ab4e-039b40f90713",
      "aggregate_entities": null,
      "timestamp": "2025-03-17T08:25:43.692262"
  }
}
export const ENTITY_API = (query, searchIn) => {
  const body = {
    app_name: "AcuityOne",
    search: query,
    search_in: searchIn,
    filter: {
      is_primary: [true, "NA"],
      company_type: ["public", "private"]
    },
    projection: [
      "headquarter_country",
      "sector",
      "primary_exchange",
      "primary_ticker"
    ],
    sort: {
      is_public: "desc"
    },
    all_data_sources: false,
    page_size: 20,
    page: 0
  };
  return useQuery({
    queryKey: [SEARCH_COMPANY_API_URL, query, searchIn],
    queryFn: async ({ signal }) => {
     // let response = await post(SEARCH_COMPANY_API_URL, body, signal);
     let response = COMPANY_RESPONSE;
      return response;
    },
    enabled: query?.length > 0,
    staleTime: minutesToMilliseconds(5),
    cacheTime: minutesToMilliseconds(30),
    retry: 1,
    refetchOnWindowFocus: false,
    refetchOnMount: true
  });
};

export const ENTITY_LOGO_API = (acuityId) => {
  const body = {
    app_name: "AcuityOne",
    filter: {
      acuity_ids: [acuityId]
    }
  };
  return useQuery({
    queryKey: [ENTITY_LOGO_URL, acuityId],
    queryFn: async () => {
      let response = await post(ENTITY_LOGO_URL, body);
      return response;
    },
    enabled: acuityId?.length > 0,
    staleTime: minutesToMilliseconds(5),
    cacheTime: minutesToMilliseconds(30),
    retry: 1,
    refetchOnWindowFocus: false,
    refetchOnMount: true
  });
};
