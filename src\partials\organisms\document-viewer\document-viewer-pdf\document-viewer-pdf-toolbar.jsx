import React from "react";
import PropTypes from "prop-types";
import { FaTimes, FaSearch } from "react-icons/fa";
import { Tooltip } from "@progress/kendo-react-tooltip";
import {
  FiChevronLeft,
  FiChevronRight,
  FiChevronsLeft,
  FiChevronsRight
} from "react-icons/fi";
import useDocumentViewerStore from "../document-viewer.store";

const DocumentViewerPDFToolBar = ({
  searchQuery,
  readOnly,
  setSearchQuery,
  pageCount,
  currentPage,
  setScrollToPage
}) => {
  const { setPreviewClose } = useDocumentViewerStore((state) => state);

  return (
    <div className="flex h-12 w-full justify-between border-b">
      <div className="flex h-12 flex-grow justify-start border-neutral-10 px-4 py-2.5">
        <div className="flex items-center text-primary-78">
          <FaSearch className="size-3" />
        </div>
        <input
          value={searchQuery}
          data-testid="document-viewer-pdf-toolbar-search"
          onChange={(e) => {
            setSearchQuery(e.target.value);
          }}
          placeholder="Search here..."
          className="body-r w-full items-center border-none text-neutral-80 placeholder:text-neutral-30 focus:ring-0"
        ></input>
        {searchQuery && (
          <button
            data-testid="document-viewer-pdf-toolbar-search-close"
            onClick={() => setSearchQuery("")}
            className="flex items-center text-neutral-80"
          >
            <FaTimes className="size-3" />
          </button>
        )}
      </div>
      <div className="flex justify-end border-l">
        <div className="flex border-collapse justify-between">
          <div className="flex w-44 items-center justify-start px-2.5 py-2.5 text-center">
            <span className="caption-r text-neutral-30">
              Page {currentPage} of {pageCount}
            </span>
          </div>
          <div className="flex justify-end">
            <div className="flex justify-between">
              <div className="flex items-center justify-start">
                <button
                  disabled={currentPage === 1}
                  data-testid="document-viewer-pdf-toolbar-first-page"
                  onClick={() => {
                    setScrollToPage(1);
                  }}
                  className="flex size-12 place-items-center items-center border-l border-neutral-5 text-center align-middle enabled:text-neutral-70 enabled:hover:bg-primary-40 disabled:text-neutral-30"
                >
                  <FiChevronsLeft className="m-auto p-0" />
                </button>
                <button
                  disabled={currentPage === 1}
                  data-testid="document-viewer-pdf-toolbar-previous-page"
                  onClick={() => {
                    setScrollToPage(currentPage - 1);
                  }}
                  className="flex size-12 place-items-center items-center border-l border-neutral-5 text-center align-middle enabled:text-neutral-70 enabled:hover:bg-primary-40 disabled:pointer-events-none disabled:text-neutral-30"
                >
                  <FiChevronLeft className="m-auto p-0" />
                </button>
              </div>
              <div className="flex size-12 justify-center border-l border-neutral-5 text-center">
                <span className="body-r m-auto flex size-10 items-center justify-center">
                  {currentPage}
                </span>
              </div>
              <div className="flex justify-end">
                <div className="flex items-center justify-start">
                  <button
                    disabled={currentPage === pageCount}
                    data-testid="document-viewer-pdf-toolbar-next-page"
                    onClick={() => {
                      setScrollToPage(currentPage + 1);
                    }}
                    className="flex size-12 place-items-center items-center border-l border-neutral-5 text-center align-middle enabled:text-neutral-70 hover:enabled:bg-primary-40 disabled:text-neutral-30"
                  >
                    <FiChevronRight className="m-auto p-0" />
                  </button>
                  <button
                    disabled={currentPage === pageCount}
                    data-testid="document-viewer-pdf-toolbar-last-page"
                    onClick={() => {
                      setScrollToPage(pageCount);
                    }}
                    className="flex size-12 place-items-center items-center border-l border-neutral-5 text-center align-middle enabled:text-neutral-70 enabled:hover:bg-primary-40 disabled:text-neutral-30"
                  >
                    <FiChevronsRight className="m-auto p-0" />
                  </button>
                </div>
              </div>
            </div>
          </div>
          {!readOnly && (
            <Tooltip openDelay={100} position="left" anchorElement="target">
              <button
                title="Close preview"
                data-testid="document-viewer-pdf-toolbar-close-button"
                onClick={setPreviewClose}
                className="flex size-12 h-full cursor-pointer items-center justify-end border-l text-center text-neutral-60 hover:bg-primary-40 active:bg-primary-50"
              >
                <div
                  title="Close  preview"
                  className="flex w-12 items-center justify-center"
                >
                  <FaTimes
                    data-testid="document-viewer-pdf-toolbar-close-button-icon"
                    title="Close preview"
                    className="justify-center"
                    onClick={setPreviewClose}
                  />
                </div>
              </button>
            </Tooltip>
          )}
        </div>
      </div>
    </div>
  );
};

export default DocumentViewerPDFToolBar;

DocumentViewerPDFToolBar.propTypes = {
  searchQuery: PropTypes.string.isRequired,
  setSearchQuery: PropTypes.func.isRequired,
  pageCount: PropTypes.number,
  currentPage: PropTypes.number,
  setScrollToPage: PropTypes.func,
  readOnly: PropTypes.bool
};
