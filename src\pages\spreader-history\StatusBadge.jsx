import React from "react";
import PropTypes from "prop-types";
import { FaCheckCircle } from "react-icons/fa";
import { BsExclamationCircleFill } from "react-icons/bs";
import { CgSpinner } from "react-icons/cg";

const StatusBadge = ({ status }) => {
  switch (status) {
    case "In Progress":
      return (
        <div className="body-m flex w-fit items-center gap-2 rounded-full border border-noticeable-60 bg-noticeable-50 px-3 py-1 text-neutral-80">
          <CgSpinner
            strokeWidth={1}
            className="size-4 animate-spin text-noticeable-90"
          />
          In Progress
        </div>
      );
    case "Completed":
      return (
        <div className="body-m flex w-fit items-center gap-2 rounded-full border border-positive-60 bg-[#ECFFF1] px-3 py-1 text-neutral-80">
          <FaCheckCircle data-testid="fa-check-circle" color="#1B873A" />
          Extraction completed
        </div>
      );
    case "Failed":
      return (
        <div className="body-m flex w-fit items-center gap-2 rounded-full border border-negative-60 bg-[#FFEFF0] px-3 py-1 text-neutral-80">
          <BsExclamationCircleFill
            data-testid="bs-exclamation-circle-fill"
            color="#DE3139"
          />
          Failed
        </div>
      );
    default:
      return (
        <div className="body-m flex w-fit items-center gap-2 rounded-full border border-positive-60 bg-[#ECFFF1] px-3 py-1 text-neutral-80">
          <FaCheckCircle data-testid="fa-check-circle" color="#1B873A" />
          Extraction completed
        </div>
      );
  }
};

StatusBadge.propTypes = {
  status: PropTypes.string.isRequired
};

export default StatusBadge;
