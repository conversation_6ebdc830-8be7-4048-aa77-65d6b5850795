import React from "react";
import PropTypes from "prop-types";

const Chip = ({ children }) => {
  return (
    <div className="flex flex-row justify-center items-center text-neutral-80 px-3 py-0.5 gap-2 h-6 bg-white border border-[#e6e6e6] rounded-full flex-none caption-r leading-[18px]  whitespace-nowrap">
      {children}
    </div>
  );
};

Chip.propTypes = {
  children: PropTypes.node.isRequired
};

export default Chip;
