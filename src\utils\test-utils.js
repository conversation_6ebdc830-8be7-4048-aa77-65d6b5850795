import React from 'react';
import { render } from '@testing-library/react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MemoryRouter } from 'react-router-dom';
import { AuthProvider } from 'react-oidc-context';
import { QueryClientProvider, QueryClient } from '@tanstack/react-query';
import { ErrorContextProvider } from '../infra/contexts/error-context';
import QueueContextProvider from '../infra/contexts/queue-context';

// Create a custom render function that includes providers
export function renderWithProviders(
  ui,
  {
    route = '/',
    queryClient = new QueryClient({
      defaultOptions: {
        queries: {
          retry: false,
        },
      },
    }),
    ...renderOptions
  } = {}
) {
  function Wrapper({ children }) {
    return (
      <ErrorContextProvider>
        <QueryClientProvider client={queryClient}>
          <QueueContextProvider>
            <MemoryRouter initialEntries={[route]}>
              {children}
            </MemoryRouter>
          </QueueContextProvider>
        </QueryClientProvider>
      </ErrorContextProvider>
    );
  }
  
  return render(ui, { wrapper: Wrapper, ...renderOptions });
}

// Create a custom render function that includes auth provider
export function renderWithAuth(
  ui,
  {
    route = '/',
    ...renderOptions
  } = {}
) {
  function Wrapper({ children }) {
    return (
      <ErrorContextProvider>
        <AuthProvider>
          <MemoryRouter initialEntries={[route]}>
            {children}
          </MemoryRouter>
        </AuthProvider>
      </ErrorContextProvider>
    );
  }
  
  return render(ui, { wrapper: Wrapper, ...renderOptions });
}

// Mock data for common tests
export const mockData = {
  // Add mock data here as needed for your tests
};

// Common test IDs for querying elements
export const testIds = {
  // Add test IDs here as needed for your tests
}; 