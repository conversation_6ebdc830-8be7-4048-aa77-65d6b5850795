/* var.css  */
/* Based on UX Stlyle Guide 2023 */
/* https://xd.adobe.com/view/045b203f-9fa6-4de9-832b-c0e9505a6437-5108/specs/. */

html {
  /* Page1 */
  /* Colors: */
  /* Primary Colors */
  --primary-blue-35: #f5f9ff;
  --primary-blue-40: #ebf3ff;
  --primary-blue-50: #c4d9ff;
  --primary-blue-60: #93b0ed;
  --primary-blue-70: #5d7bc7;
  --primary-blue-78: #4061c7;
  --primary-blue-90: #152b7a;
  --primary-blue-100: #021155;
  --primary-blue-110: #00072e;
  /* secondary Colors */
  --secondary-pink-60: #f9ebff;
  --secondary-pink-70: #eaabff;
  --secondary-pink-80: #e283fc;
  --secondary-pink-100: #9c27b0;
  --secondary-pink-110: #bd00d6;
  --secondary-pink-130: #3a003d;
  /* negative Colors */
  --negative-red-60: #ffd1da;
  --negative-red-70: #ffabb7;
  --negative-red-90: #ff5e6c;
  --negative-red-100: #de3139;
  --negative-red-110: #b80d10;
  --negative-red-120: #910000;
  /* noticeable Colors */
  --noticeable-orange-50: #ffebe3;
  --noticeable-orange-60: #ffd3bd;
  --noticeable-orange-70: #ffbd96;
  --noticeable-orange-90: #ff984a;
  --noticeable-orange-100: #f68523;
  --noticeable-orange-110: #cf6700;
  /* positive Colors */
  --positive-green-60: #ccffd4;
  --positive-green-70: #a2fab2;
  --positive-green-80: #6ad481;
  --positive-green-100: #1b873a;
  --positive-green-110: #056122;
  --positive-green-120: #003b15;
  /* informational Colors */
  --informational-blue-50: #d9edff;
  --informational-blue-60: #b3d7ff;
  --informational-blue-70: #8cc0ff;
  --informational-blue-90: #4089ff;
  --informational-blue-100: #1a6aff;
  --informational-blue-110: #0045d9;
  /* neutral Colors */
  --neutral-gray-00: #fff;
  --neutral-gray-02: #fafafa;
  --neutral-gray-05: #f2f2f2;
  --neutral-gray-10: #e6e6e6;
  --neutral-gray-20: #ccc;
  --neutral-gray-30: #b3b3b3;
  --neutral-gray-60: #666;
  --neutral-gray-70: #4d4d4d;
  --neutral-gray-80: #333;
  --neutral-gray-90: #1a1a1a;
  --neutral-gray-100: #000;
  /* Brand -gradient */
  --brand-gradient100: transparent
    linear-gradient(90deg, var(--primary-blue-100) 0%, #9c27b0 100%) 0% 0%
    no-repeat padding-box;
  /* -font-/text values */
  --brand-font-family: "Helvetica Neue LT W05 55 Roman";
  --brand-font-style-normal: normal;
  --brand-font-weight-medium: medium;
  --brand-font-weight-normal: normal;
  --brand-font-size-14: 0.875rem;
  --brand-character-spacing-0: 0px;
  --brand-line-spacing-20: 1.25rem;
  --w05-36-th-it: "Helvetica Neue LT W05_36 Th It";
  --w05-25-ult-lt: "Helvetica Neue LT W05 25 Ult Lt";
  --w05-26ultltit: "Helvetica Neue LT W05 26UltLtIt";
  --w05-35-thin: "Helvetica Neue LT W05 35 Thin";
  --w05-45-light: "Helvetica Neue LT W05 45 Light";
  --w05-46-lt-it: "Helvetica Neue LT W05 46 Lt It";
  --w05-55-roman: "Helvetica Neue LT W05 55 Roman";
  --w05-56-italic: "Helvetica Neue LT W05 56 Italic";
  --w05-65-medium: "Helvetica Neue LT W05 65 Medium";
  --w05-66-md-it: "Helvetica Neue LT W05 66 Md It";
  --w05-76-bd-it: "Helvetica Neue LT W05 76 Bd It";
  --w05-85-heavy: "Helvetica Neue LT W05 85 Heavy";
  --w05-86-hv-it: "Helvetica Neue LT W05 86 Hv It";
  --w05-95-black: "Helvetica Neue LT W05 95 Black";
  --w05-96-blk-it: "Helvetica Neue LT W05 96 Blk It";
  --w05-75-bold: "Helvetica Neue LT W05 75 Bold";
  /* unnamed-color */
  --unnamed-color-33333380: #333;
  --unnamed-color-eaf3ff: #eaf3ff;
  --unnamed-color-fafafb: #fafafb;
  --unnamed-color-333333: #333;
}

.typo-xxl-r {
  font: var(--brand-font-style-normal) normal var(--brand-font-weight-normal)
    1.875rem/ 2.625rem var(--brand-font-family);
  letter-spacing: var(--brand-character-spacing-0);
  color: var(--neutral-gray-80);
  text-align: left;
  opacity: 1;
}

.typo-xl-m {
  font: var(--brand-font-style-normal) normal var(--brand-font-weight-medium)
    1.5rem/2rem var(--brand-font-family);
  letter-spacing: var(--brand-character-spacing-0);
  color: var(--neutral-gray-80);
  text-align: left;
  opacity: 1;
}

.typo-xl-r {
  font: var(--brand-font-style-normal) normal var(--brand-font-weight-normal)
    1.5rem/2rem var(--brand-font-family);
  letter-spacing: var(--brand-character-spacing-0);
  color: var(--neutral-gray-80);
  text-align: left;
  opacity: 1;
}

.typo-l-b {
  font: var(--brand-font-style-normal) normal bold 1.25rem/1.75rem
    var(--brand-font-family);
  letter-spacing: var(--brand-character-spacing-0);
  color: var(--neutral-gray-80);
  text-align: left;
  opacity: 1;
}

.typo-l-m {
  font: var(--brand-font-style-normal) normal var(--brand-font-weight-medium)
    1.25rem/1.75rem var(--brand-font-family);
  letter-spacing: var(--brand-character-spacing-0);
  color: var(--neutral-gray-80);
  text-align: left;
  opacity: 1;
}

.typo-m-m {
  font: var(--brand-font-style-normal) normal var(--brand-font-weight-medium)
    1rem/1.5rem var(--brand-font-family);
  letter-spacing: var(--brand-character-spacing-0);
  color: var(--neutral-gray-80);
  text-align: left;
  opacity: 1;
}

.typo-m-r {
  font: var(--brand-font-style-normal) normal var(--brand-font-weight-normal)
    1rem/1.5rem var(--brand-font-family);
  letter-spacing: var(--brand-character-spacing-0);
  color: var(--neutral-gray-80);
  text-align: left;
  opacity: 1;
}

.typo-s-b {
  font: var(--brand-font-style-normal) normal bold var(--brand-font-size-14) /
    var(--brand-line-spacing-20) var(--brand-font-family);
  letter-spacing: var(--brand-character-spacing-0);
  color: var(--neutral-gray-80);
  text-align: left;
  opacity: 1;
}

.typo-s-m {
  font: var(--brand-font-style-normal) normal var(--brand-font-weight-medium)
    var(--brand-font-size-14) / var(--brand-line-spacing-20)
    var(--brand-font-family);
  letter-spacing: var(--brand-character-spacing-0);
  color: var(--neutral-gray-80);
  text-align: left;
  opacity: 1;
}

.typo-s-r {
  font: var(--brand-font-style-normal) normal var(--brand-font-weight-normal)
    var(--brand-font-size-14) / var(--brand-line-spacing-20)
    var(--brand-font-family);
  letter-spacing: var(--brand-character-spacing-0);
  color: var(--neutral-gray-80);
  text-align: left;
  opacity: 1;
}

.typo-s-i {
  font: italic normal var(--brand-font-weight-normal) 0.875rem/1.25rem
    var(--brand-font-family);
  letter-spacing: var(--brand-character-spacing-0);
  color: var(--neutral-gray-80);
  text-align: left;
  opacity: 1;
}

.typo-xs-m {
  font: var(--brand-font-style-normal) normal var(--brand-font-weight-medium)
    0.75rem/1.125rem var(--brand-font-family);
  letter-spacing: var(--brand-character-spacing-0);
  color: var(--neutral-gray-80);
  text-align: left;
  opacity: 1;
}

.typo-xs-r {
  font: var(--brand-font-style-normal) normal var(--brand-font-weight-normal)
    0.75rem/1.125rem var(--brand-font-family);
  letter-spacing: var(--brand-character-spacing-0);
  color: var(--neutral-gray-80);
  text-align: left;
  opacity: 1;
}

/* -character- -style-s */
.s-m {
  -font-family: var(--brand-font-family);
  -font-style-: var(--brand-font-style-normal);
  -font-weight-: var(--brand-font-weight-medium);
  -font--size-: var(--brand-font-size-14);
  -line--height: var(--brand-line-spacing-20);
  letter-spacing: var(--brand-character-spacing-0);
  color: var(--neutral-gray-80);
}

.s-r {
  -font-family: var(--brand-font-family);
  -font-style-: var(--brand-font-style-normal);
  -font-weight-: var(--brand-font-weight-normal);
  -font--size-: var(--brand-font-size-14);
  -line--height: var(--brand-line-spacing-20);
  letter-spacing: var(--brand-character-spacing-0);
}

/* Button */
.beat-button-primary,
.beat-icon-primary {
  background: var(--primary-blue-78) 0% 0% no-repeat padding-box;
  border-radius: 0.25rem;
  opacity: 1;
}

.beat-button-primary:hover,
.beat-icon-primary:hover {
  background: var(--primary-blue-90) 0% 0% no-repeat padding-box;
  border-radius: 0.25rem;
  opacity: 1;
}

.beat-button-primary:active,
.beat-icon-primary:active {
  background: var(--primary-blue-100) 0% 0% no-repeat padding-box;
  border-radius: 0.25rem;
  opacity: 1;
}

.beat-button-primary:disabled,
.beat-icon-primary:disabled {
  background: var(--primary-blue-60) 0% 0% no-repeat padding-box;
  border-radius: 0.25rem;
  opacity: 1;
}

.beat-button-secondary,
.beat-icon-secondary {
  background: var(--unnamed-color-ffffff) 0% 0% no-repeat padding-box;
  border: 0.0625rem solid var(--primary-blue-78);
  border-radius: 0.25rem;
  opacity: 1;
}

.beat-button-secondary:hover,
.beat-icon-secondary:hover {
  border: 0.0625rem solid var(--primary-blue-78);
  background: var(--primary-blue-40) 0% 0% no-repeat padding-box;
  border-radius: 0.25rem;
  opacity: 1;
}

.beat-button-secondary:active,
.beat-icon-secondary:active {
  background: var(--unnamed-color-ffffff) 0% 0% no-repeat padding-box;
  border: 0.0625rem solid #182d75;
  border-radius: 0.25rem;
  opacity: 1;
}

.beat-button-secondary:disabled,
.beat-icon-secondary:disabled {
  background: var(--unnamed-color-ffffff) 0% 0% no-repeat padding-box;
  border: 0.0625rem solid var(--primary-blue-60);
  border-radius: 0.25rem;
  opacity: 1;
}

.beat-icon {
  background: var(--unnamed-color-ffffff) 0% 0% no-repeat padding-box;
  opacity: 0;
}

.beat-icon:hover {
  background: var(--unnamed-color-ffffff) 0% 0% no-repeat padding-box;
  opacity: 0;
}

.beat-icon-white {
  background: var(--unnamed-color-ffffff) 0% 0% no-repeat padding-box;
  opacity: 1;
}

.beat-icon-white:hover {
  background: var(--primary-blue-78) 0% 0% no-repeat padding-box;
  opacity: 1;
}

.beat-icon-blue {
  background: var(--primary-blue-78) 0% 0% no-repeat padding-box;
  opacity: 1;
}

.beat-icon-blue:hover {
  background: var(--primary-blue-78) 0% 0% no-repeat padding-box;
  opacity: 1;
}

.beat-alert-information {
  background: var(--informational-blue-100) 0% 0% no-repeat padding-box;
  border: 0.0625rem solid var(--informational-blue-100);
  border-radius: 0.25rem;
  opacity: 1;
}

.beat-alert-warning {
  background: var(--noticeable-orange-110) 0% 0% no-repeat padding-box;
  border: 0.0625rem solid var(--noticeable-orange-100);
  border-radius: 0.25rem;
  opacity: 1;
}

.beat-alert-error {
  background: var(--negative-red-100) 0% 0% no-repeat padding-box;
  border: 0.0625rem solid var(--negative-red-110);
  border-radius: 0.25rem;
  opacity: 1;
}

.beat-alert-success {
  background: var(--negative-red-100) 0% 0% no-repeat padding-box;
  border: 0.0625rem solid var(--negative-red-110);
  border-radius: 0.25rem;
  opacity: 1;
}

.beat-input .beat-input-label {
  font: var(--unnamed-font-style-normal) normal
    var(--unnamed-font-weight-medium) 0.75rem/1.125rem
    var(--unnamed-font-family-helvetica-neue);
  letter-spacing: var(--unnamed-character-spacing-0);
  text-align: left;
  color: var(--neutral-gray-60);
  opacity: 1;
}

.beat-input {
  background: var(--unnamed-color-ffffff) 0% 0% no-repeat padding-box;
  background: var(--neutral-gray-00) 0% 0% no-repeat padding-box;
  border: 0.0625rem solid var(--neutral-gray-60);
  border-radius: 0.25rem;
  opacity: 1;
}

.beat-input:disabled .beat-input-label {
  font: var(--unnamed-font-style-normal) normal
    var(--unnamed-font-weight-medium) 0.75rem/1.125rem
    var(--unnamed-font-family-helvetica-neue);
  letter-spacing: var(--unnamed-character-spacing-0);
  text-align: left;
  color: var(--neutral-gray-30);
  opacity: 1;
}

.beat-input:disabled {
  background: #f2f2f2 0% 0% no-repeat padding-box;
  border: 0.0625rem solid var(--neutral-gray-20);
  border-radius: 0.25rem;
  opacity: 1;
}

.beat-input:focus .beat-input-label {
  font: var(--unnamed-font-style-normal) normal
    var(--unnamed-font-weight-medium) 0.75rem/1.125rem
    var(--unnamed-font-family-helvetica-neue);
  letter-spacing: var(--unnamed-character-spacing-0);
  text-align: left;
  color: var(--neutral-gray-60);
  opacity: 1;
}

.beat-input:focus {
  background: var(--unnamed-color-ffffff) 0% 0% no-repeat padding-box;
  border: 0.0625rem solid var(--primary-blue-78);
  border-radius: 0.25rem;
  opacity: 1;
}

.search-input {
  border: 0.0625rem solid gold;
  padding-left: 1.25rem;
  border-radius: 1.5625rem;
}

.search-btn {
  position: relative;
  left: -2.5rem;
}

.nav-item {
  text-transform: lowercase;
}

a,
button {
  text-transform: none !important;
}

.admin-menu:hover {
  cursor: pointer;
}

.no-border .nep-button-secondary {
  border: none !important;
  padding: 0;
  color: var(--neutral-gray-60);
}

.no-border .nep-button-secondary:hover,
.no-border .nep-button-secondary:focus,
.no-border .nep-button-secondary:active {
  background: none !important;
}

.no-border .nep-dropdown-right-bottom .nep-dropdown-button:after {
  display: none !important;
}

.no-border .nep-popover-content {
  border-radius: unset;
}
.no-border * {
  font-size: 0.875rem !important;
  cursor: pointer;
}

a:hover {
  color: var(--informational-blue-100) !important;
}

.menu-font-family {
  font-family: var(--brand-font-family) sans-serif !important;
  font-size: 0.875rem !important;
  padding: 0.75rem 1rem !important;
}

.menu-font-family:hover {
  background-color: var(--primary-blue-35) !important;
}
.header-bg-color{
  background-color: var(--primary-blue-35);
}
.menu-font-family:active {
  background-color: var(--primary-blue-40) !important;
}

.header-menu-dropdown {
  box-shadow: 0px 0px 0.75rem #00000029 !important;
}

.profile {
  display: flex;
  justify-content: center;
  background: transparent linear-gradient(180deg, #021155 0%, #9c27b0 100%) 0%
    0% no-repeat padding-box !important;
  box-shadow: 0px 0px 0.1875rem #00000029 !important;
  color: #fff !important;
  font-size: 0.5625rem !important;
  line-height: 0.9375rem !important;
  width: 1.5rem !important;
  height: 1.5rem !important;
  align-items: center !important;
  border-radius: 50%;
  cursor: pointer;
}

.config-side-menu-item .nep-menu-item:hover,
.config-side-menu-item a:hover {
  background: var(--primary-blue-35) 0% 0% no-repeat padding-box !important;
  color: #434655 !important;
}

.config-side-menu-item .nep-menu-active,
.config-side-menu-item .nep-menu-active .nep-menu-title {
  background: var(--primary-blue-40) 0% 0% no-repeat padding-box !important;
  color: var(--primary-blue-78) !important;
}

.navigation-header-menu a {
  height: 3rem !important;
  padding-top: 0.4rem;
}

.nav-menu-header-item .nep-tabs-tab > div {
  height: 3.75rem;
  padding: 0.5rem 0;
  display: flex;
  align-items: center;
}

.nav-menu-header-item .nep-tabs-active {
  padding-bottom: 0 !important;
}
