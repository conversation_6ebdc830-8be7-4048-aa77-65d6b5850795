import React, { useState, useRef, useEffect } from "react";
import { Transition } from "@headlessui/react";
import PropTypes from "prop-types";

// eslint-disable-next-line react/prop-types
const RadioGroup = ({ children, align, icon, ...rest }) => {
  const [dropdownOpen, setDropdownOpen] = useState(false);

  const trigger = useRef(null);
  const dropdown = useRef(null);

  // close on click outside
  useEffect(() => {
    const clickHandler = ({ target }) => {
      if (!dropdown.current) return;
      if (
        !dropdownOpen ||
        dropdown.current.contains(target) ||
        trigger.current.contains(target)
      )
        return;
      setDropdownOpen(false);
    };
    document.addEventListener("click", clickHandler);
    return () => document.removeEventListener("click", clickHandler);
  });

  // close if the esc key is pressed
  useEffect(() => {
    const keyHandler = ({ keyCode }) => {
      if (!dropdownOpen || keyCode !== 27) return;
      setDropdownOpen(false);
    };
    document.addEventListener("keydown", keyHandler);
    return () => document.removeEventListener("keydown", keyHandler);
  });

  return (
    <button
      data-testid={"radio-group-button"}
      {...rest}
      ref={trigger}
      onClick={() => setDropdownOpen(!dropdownOpen)}
      aria-haspopup="true"
      aria-expanded={dropdownOpen}
    >
      {icon}

      <Transition
        show={dropdownOpen}
        tag="div"
        className={`absolute top-full z-10 mt-1 w-48 origin-top-right justify-between rounded bg-white shadow-lg ${
          align === "right" ? "right-0" : "left-0"
        }`}
        enter="transition ease-out duration-300 transform"
        enterStart="opacity-0 -translate-y-2"
        enterEnd="opacity-100 translate-y-0"
        leave="transition ease-out duration-300"
        leaveStart="opacity-100"
        leaveEnd="opacity-0"
      >
        <ul ref={dropdown} data-testid="radio-group-list">
          {children}
        </ul>
      </Transition>
    </button>
  );
};

export default RadioGroup;

RadioGroup.propTypes = {
  children: PropTypes.node,
  align: PropTypes.string,
  icon: PropTypes.node
};
