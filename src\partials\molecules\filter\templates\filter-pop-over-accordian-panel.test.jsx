import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import FilterAccordianPanel from "./filter-pop-over-accordian-panel";
const mockData = [
  {
    value: "1",
    label: "Category 1",
    data: [
      { value: "1-1", label: "Option 1", selected: false },
      { value: "1-2", label: "Option 2", selected: true }
    ],
    selected: 1
  },
  {
    value: "2",
    label: "Category 2",
    data: [
      { value: "2-1", label: "Option A", selected: false },
      { value: "2-2", label: "Option B", selected: false }
    ],
    selected: 0
  }
];

const mockOnSelect = jest.fn();

describe("FilterAccordianPanel Component", () => {
  test("renders search input", () => {
    render(
      <FilterAccordianPanel
        data={mockData}
        onSelect={mockOnSelect}
        selectedFilterId="1"
        clearAll={false}
      />
    );
    expect(
      screen.getByTestId("filter-pop-over-accordian-panel-search-input")
    ).toBeInTheDocument();
  });

  test("renders filter categories", () => {
    render(
      <FilterAccordianPanel
        data={mockData}
        onSelect={mockOnSelect}
        selectedFilterId="1"
        clearAll={false}
      />
    );
    expect(screen.getByText("Category 1")).toBeInTheDocument();
    expect(screen.getByText("Category 2")).toBeInTheDocument();
  });

  test("selects all options in a category", () => {
    render(
      <FilterAccordianPanel
        data={mockData}
        onSelect={mockOnSelect}
        selectedFilterId="1"
        clearAll={false}
      />
    );
    fireEvent.click(
      screen.getByTestId("filter-pop-over-accordian-panel-select-all-check-box")
    );
    const checkboxes = screen.getAllByTestId(
      "filter-pop-over-accordian-panel-check-box"
    )[0];
    fireEvent.click(checkboxes);
  });

  test("filters options based on search query", () => {
    render(
      <FilterAccordianPanel
        data={mockData}
        onSelect={mockOnSelect}
        selectedFilterId="1"
        clearAll={false}
      />
    );
    fireEvent.change(
      screen.getByTestId("filter-pop-over-accordian-panel-search-input"),
      {
        target: { value: "Option 1" }
      }
    );
  });
});
