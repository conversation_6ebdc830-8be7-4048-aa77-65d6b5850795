/**
 * @jest-environment jsdom
 */
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import CustomDrawer from './CustomDrawer';

// Add Jest TypeScript declarations
declare const describe: any;
declare const test: any;
declare const expect: any;
declare const jest: any;

describe('CustomDrawer Component', () => {
  // Test rendering when closed
  test('does not render content when closed', () => {
    const onClose = jest.fn();
    
    render(
      <CustomDrawer isOpen={false} onClose={onClose}>
        <div data-testid="drawer-content">Test Content</div>
      </CustomDrawer>
    );
    
    // The drawer content should be in the document but not visible
    // since the drawer is positioned off-screen when closed
    const content = screen.queryByTestId('drawer-content');
    expect(content).toBeInTheDocument();
    
    // The overlay should not be in the document
    const overlay = document.querySelector('.custom-drawer-overlay');
    expect(overlay).not.toBeInTheDocument();
  });
  
  // Test rendering when open
  test('renders content when open', () => {
    const onClose = jest.fn();
    
    render(
      <CustomDrawer isOpen={true} onClose={onClose}>
        <div data-testid="drawer-content">Test Content</div>
      </CustomDrawer>
    );
    
    // Content should be visible
    expect(screen.getByText('Test Content')).toBeInTheDocument();
    
    // The overlay should be in the document
    const overlay = document.querySelector('.custom-drawer-overlay');
    expect(overlay).toBeInTheDocument();
  });
  
  // Test click on overlay closes drawer
  test('calls onClose when overlay is clicked', () => {
    const onClose = jest.fn();
    
    render(
      <CustomDrawer isOpen={true} onClose={onClose}>
        <div data-testid="drawer-content">Test Content</div>
      </CustomDrawer>
    );
    
    // Click the overlay
    const overlay = document.querySelector('.custom-drawer-overlay');
    fireEvent.click(overlay!);
    
    // onClose should be called
    expect(onClose).toHaveBeenCalledTimes(1);
  });
  
  // Test positioning - right (default)
  test('renders with correct position styles - right (default)', () => {
    const onClose = jest.fn();
    
    render(
      <CustomDrawer isOpen={true} onClose={onClose}>
        <div>Content</div>
      </CustomDrawer>
    );
    
    const drawer = document.querySelector('.custom-drawer');
    expect(drawer).toHaveClass('custom-drawer-right');
    expect(drawer).toHaveStyle('right: 0');
  });
  
  // Test positioning - left
  test('renders with correct position styles - left', () => {
    const onClose = jest.fn();
    
    render(
      <CustomDrawer isOpen={true} onClose={onClose} position="left">
        <div>Content</div>
      </CustomDrawer>
    );
    
    const drawer = document.querySelector('.custom-drawer');
    expect(drawer).toHaveClass('custom-drawer-left');
    expect(drawer).toHaveStyle('left: 0');
  });
  
  // Test positioning - top
  test('renders with correct position styles - top', () => {
    const onClose = jest.fn();
    
    render(
      <CustomDrawer isOpen={true} onClose={onClose} position="top">
        <div>Content</div>
      </CustomDrawer>
    );
    
    const drawer = document.querySelector('.custom-drawer');
    expect(drawer).toHaveClass('custom-drawer-top');
    expect(drawer).toHaveStyle('top: 0');
  });
  
  // Test positioning - bottom
  test('renders with correct position styles - bottom', () => {
    const onClose = jest.fn();
    
    render(
      <CustomDrawer isOpen={true} onClose={onClose} position="bottom">
        <div>Content</div>
      </CustomDrawer>
    );
    
    const drawer = document.querySelector('.custom-drawer');
    expect(drawer).toHaveClass('custom-drawer-bottom');
    expect(drawer).toHaveStyle('bottom: 0');
  });
  
  // Test custom width
  test('applies custom width', () => {
    const onClose = jest.fn();
    
    render(
      <CustomDrawer isOpen={true} onClose={onClose} width="500px">
        <div>Content</div>
      </CustomDrawer>
    );
    
    const drawer = document.querySelector('.custom-drawer');
    expect(drawer).toHaveStyle('width: 500px');
  });
  
  // Test custom height
  test('applies custom height', () => {
    const onClose = jest.fn();
    
    render(
      <CustomDrawer isOpen={true} onClose={onClose} height="50%">
        <div>Content</div>
      </CustomDrawer>
    );
    
    const drawer = document.querySelector('.custom-drawer');
    expect(drawer).toHaveStyle('height: 50%');
  });
  
  // Test closed drawer positioning
  test('positions drawer off-screen when closed', () => {
    const onClose = jest.fn();
    
    render(
      <CustomDrawer isOpen={false} onClose={onClose} width="400px">
        <div>Content</div>
      </CustomDrawer>
    );
    
    const drawer = document.querySelector('.custom-drawer');
    expect(drawer).toHaveStyle('right: -400px');
  });
}); 