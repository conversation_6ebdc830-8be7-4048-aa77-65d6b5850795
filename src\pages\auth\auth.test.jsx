import React from "react";
import { render } from "@testing-library/react";
import { useAuth } from "react-oidc-context";
import Login from "./auth";
import TestAppRenderer from "../../infra/test-utils/test-app-renderer";

jest.mock("react-oidc-context");

describe("Testing auth component", () => {
  afterEach(() => {
    jest.resetAllMocks();
  });

  it("render login componenet ", () => {
    useAuth.mockReturnValue({ isAuthenticated: true });
    render(
      <TestAppRenderer>
        <Login />
      </TestAppRenderer>
    );
  });

  it("not render login componenet ", () => {
    useAuth.mockReturnValue({ isAuthenticated: false });
    render(
      <TestAppRenderer>
        <Login />
      </TestAppRenderer>
    );
  });
});
