import { render, fireEvent, screen } from "@testing-library/react";
import { <PERSON>rows<PERSON>Router as Router } from "react-router-dom";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { RolesContext } from "../../../../general/context";
import HeaderUserProfile from "./header-user-profile";

jest.mock("react-oidc-context");

describe("HeaderUserProfile", () => {
  const roleConfig = {
    USER_MANAGEMENT_POLICY: true
  };

  const queryClient = new QueryClient();

  beforeEach(() => {
    render(
      <RolesContext.Provider value={roleConfig}>
        <QueryClientProvider client={queryClient}>
          <Router>
            <HeaderUserProfile />
          </Router>
        </QueryClientProvider>
      </RolesContext.Provider>
    );
  });

  it("renders without crashing", () => {
    expect(screen.getByTestId("header-user-profile")).toBeInTheDocument();
  });

  it("opens and closes the dropdown", () => {
    const button = screen.getByTestId("header-user-profile");
    fireEvent.click(button);
    expect(screen.getByTestId("dropdown-logout")).toBeInTheDocument();
  });

  it("opens the sign out model", () => {
    const button = screen.getByTestId("header-user-profile");
    fireEvent.click(button);
    const logoutButton = screen.getByTestId("user-profile-dropdown-logout");
    fireEvent.click(logoutButton);
    expect(
      screen.getByTestId("user-profile-dropdown-logout")
    ).toBeInTheDocument();
  });

  it("navigates to the admin console", () => {
    const button = screen.getByTestId("header-user-profile");
    fireEvent.click(button);
    const adminConsoleButton = screen.getByTestId(
      "user-profile-dropdown-admin-console"
    );
    fireEvent.click(adminConsoleButton);
    expect(window.location.pathname).toBe("/user-management");
  });
});
