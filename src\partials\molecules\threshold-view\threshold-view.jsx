import React from "react";
import PropTypes from "prop-types";

const ThresholdView = ({ value, icon }) => {
  return (
    <ul className="w-52 rounded border-[0.25px] bg-white  p-4 text-neutral-90 shadow-xs ">
      <li className="xs-r mb-3 flex justify-between">
        <span className="xs-r flex cursor-pointer items-center justify-start ">
          Environmental Score
        </span>
        <div className="flex">
          {icon}
          <span className="xs-m flex items-center justify-end">{value}</span>
        </div>
      </li>
      <li className="xs-r mb-3 flex justify-between">
        <span className="xs-r flex cursor-pointer items-center justify-start ">
          Social Score
        </span>
        <div className="flex">
          {icon}
          <span className="xs-m flex items-center justify-end">{value}</span>
        </div>
      </li>
      <li className="xs-r flex justify-between">
        <span className="xs-r flex cursor-pointer items-center justify-start ">
          Governance Score
        </span>
        <div className="flex">
          {icon}
          <span className="xs-m flex items-center justify-end">{value}</span>
        </div>
      </li>
    </ul>
  );
};

ThresholdView.propTypes = {
  value: PropTypes.any,
  icon: PropTypes.object
};

export default ThresholdView;
