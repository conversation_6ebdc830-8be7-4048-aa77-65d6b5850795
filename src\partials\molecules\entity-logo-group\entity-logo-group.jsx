import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
import LogoPlaceholder from "../../../resources/images/icon-logo-placeholder";
import { GET_WATCH_LIST_ENTITY_API } from "../../../infra/api/watch-list-service";
import { ENTITY_LOGO_API } from "../../../infra/api/data-lake-service";

const EntityLogo = ({ acuityId }) => {
  const getPresignedURL = ENTITY_LOGO_API(acuityId);
  const url = getPresignedURL?.data?.data?.[0]?.logo_url || "";
  const [error, setError] = useState(!url || url.length === 0);

  useEffect(() => {
    setError(!url || url.length === 0);
  }, [url]);

  const handleImageError = () => {
    setError(true);
  };

  return error ? (
    <LogoPlaceholder />
  ) : (
    <img
      className="size-4"
      src={url}
      alt="entity-logo-group"
      onError={handleImageError}
    />
  );
};

EntityLogo.propTypes = {
  acuityId: PropTypes.array
};

const EntityLogoGroup = ({ watchlists }) => {
  const [entities, setEntities] = useState([]);
  const watchlistArray = Array.isArray(watchlists) ? watchlists : [watchlists];
  const responses = watchlistArray.map((watchlist) =>
    GET_WATCH_LIST_ENTITY_API(watchlist)
  );
  const allEntities = responses.map(
    (response) => response?.data?.entities || []
  );

  useEffect(() => {
    setEntities(allEntities);
  }, [watchlists]);

  return (
    <div>
      {entities.map((entityArray, index) => {
        const limitedEntities = Array.isArray(entityArray)
          ? entityArray.slice(0, 4)
          : [];
        const filledEntities = [
          ...limitedEntities,
          ...Array(4 - limitedEntities.length).fill(null)
        ];
        return (
          <div data-testid="entity-logo-group" key={index}>
            <div className="col grid border-collapse grid-cols-2">
              {filledEntities.map((item, idx) => (
                <div
                  key={idx}
                  className={`col-span-1 flex size-5 items-center justify-center ${getBorderClass(idx)}`}
                >
                  {item ? <EntityLogo acuityId={item.acuityId} /> : null}
                </div>
              ))}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export const getBorderClass = (index) => {
  switch (index) {
    case 0:
      return "rounded-ss border-l border-t border-neutral-5";
    case 1:
      return "rounded-se border-l border-r border-t border-neutral-5";
    case 2:
      return "rounded-es border border-r-0 border-neutral-5";
    case 3:
      return "rounded-ee border border-r border-t border-neutral-5";
    default:
      return "";
  }
};

EntityLogoGroup.propTypes = {
  watchlists: PropTypes.array
};

export default EntityLogoGroup;
