import React from "react";
import { render, screen } from "@testing-library/react";
import Entity<PERSON>ogo from "./entity-logo";
import IconLogoPlaceholder from "../../../resources/images/icon-logo-placeholder";
import { ENTITY_LOGO_API } from "../../../infra/api/data-lake-service";

jest.mock("../../../infra/api/data-lake-service", () => ({
  ENTITY_LOGO_API: jest.fn()
}));

jest.mock("../../../resources/images/icon-logo-placeholder", () => () => (
  <div data-testid="icon-logo-placeholder">IconLogoPlaceholder</div>
));

describe("EntityLogo", () => {
  const mockAcuityID = "12345";
  const mockCompanyName = "Test Company";
  const mockLogoSize = "medium";

  beforeEach(() => {
    ENTITY_LOGO_API.mockReturnValue({
      data: {
        data: [{ logo_url: "http://example.com/logo.png" }]
      },
      isLoading: false
    });
  });

  test("renders loading state", () => {
    ENTITY_LOGO_API.mockReturnValueOnce({ isLoading: true });
    render(
      <EntityLogo
        acuityID={mockAcuityID}
        companyName={mockCompanyName}
        logoSize={mockLogoSize}
      />
    );
    expect(screen.getByText("TC")).toBeInTheDocument();
  });

  test("renders error state with empty url", () => {
    ENTITY_LOGO_API.mockReturnValueOnce({
      data: {
        data: [{ logo_url: "" }]
      },
      isLoading: false
    });
    render(
      <EntityLogo
        acuityID={mockAcuityID}
        companyName={mockCompanyName}
        logoSize={mockLogoSize}
      />
    );
    expect(screen.getByText("TC")).toBeInTheDocument();
  });

  test("renders with valid url", () => {
    render(
      <EntityLogo
        acuityID={mockAcuityID}
        companyName={mockCompanyName}
        logoSize={mockLogoSize}
      />
    );
    expect(screen.getByAltText("entity-logo")).toHaveAttribute(
      "src",
      "http://example.com/logo.png"
    );
  });

  test("handles image error", () => {
    render(
      <EntityLogo
        acuityID={mockAcuityID}
        companyName={mockCompanyName}
        logoSize={mockLogoSize}
      />
    );
    const img = screen.getByAltText("entity-logo");
    expect(img).toHaveAttribute("src", "http://example.com/logo.png");
  });
});
