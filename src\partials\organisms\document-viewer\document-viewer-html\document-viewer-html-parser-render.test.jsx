import React from "react";
import { render, act } from "@testing-library/react";
import DocumentViewerHTMLRender from "./document-viewer-html-parser-render";
import useDocumentViewerStore from "../document-viewer.store";
import TestAppRenderer from "../../../../infra/test-utils/test-app-renderer";
import parse from "html-react-parser";

// // Mock dependencies
// jest.mock('react-frame-component', () => ({
//   __esModule: true,
//   default: jest
//     .fn()
//     .mockImplementation(({ children, ...props }) => <iframe {...props}>{children}</iframe>)
// }));

describe("DocumentViewerHTMLRender", () => {
  let store;

  beforeEach(() => {
    store = useDocumentViewerStore.getState();
    act(() => {
      store.setShowHighLightPopOver(true);
    });
  });
  const mockMetaData = {
    category: "text",
    data: {
      metaData: [{ highlightedText: "highlighted text", index: 0 }],
      beforeText: "before ",
      afterText: " after",
      totalTextNodes: 1
    }
  };

  const mockParsedHtml = parse(
    "<div>before <mark>highlighted text</mark> after</div>"
  );

  test("renders the component with parsed HTML", () => {
    render(
      <TestAppRenderer>
        <DocumentViewerHTMLRender
          parsedHtml={mockParsedHtml}
          metaData={mockMetaData}
          searchQuery={"before"}
          readOnly={true}
        ></DocumentViewerHTMLRender>
      </TestAppRenderer>
    );
  });

  // test('renders the component with parsed HTML', () => {
  //   const component = (
  //     <TestAppRenderer>
  //       <DocumentViewerHTMLRender
  //         parsedHtml={mockParsedHtml}
  //         metaData={mockMetaData}
  //         searchQuery={'before'}
  //         readOnly={true}
  //       ></DocumentViewerHTMLRender>
  //     </TestAppRenderer>
  //   );
  //   const instance = component.getInstance();
  //   const mockEvent = {
  //     prevProps: {
  //       URL: 'aa.htm'
  //     }
  //   };
  //   instance.componentDidUpdate(mockEvent);
  //   expect(instance != null).toBeTruthy();
  // });
});
