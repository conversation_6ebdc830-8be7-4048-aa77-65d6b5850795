// Format period date utility functions
/**
 * Month names array used for formatting period dates
 */
const MONTH_NAMES = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

/**
 * Get formatted month name from a month number
 * 
 * @param monthNumber - Month number (1-12 for formatPeriodType, 0-11 for formatPeriodTypeWithDataType)
 * @param isZeroBased - Whether the month index is zero-based (0-11) or one-based (1-12)
 * @returns Formatted month name (e.g., 'Jan', 'Feb')
 */
const getMonthName = (monthNumber: number, isZeroBased: boolean = false): string => {
  const index = isZeroBased ? monthNumber : monthNumber - 1;
  return MONTH_NAMES[index];
};

/**
 * Format base period string without data type
 * 
 * @param periodType - Type of period ('monthly', 'quarterly', or undefined)
 * @param periodMonth - Month number
 * @param periodQuarter - Quarter value (Q1, Q2, Q3, Q4)
 * @param periodYear - Year of the period
 * @param isMonthZeroBased - Whether the month index is zero-based (0-11) or one-based (1-12)
 * @returns Formatted period string
 */
const formatBasePeriod = (
  periodType: string | undefined,
  periodMonth: number | undefined,
  periodQuarter: string | number | undefined,
  periodYear: number | string | undefined,
  isMonthZeroBased: boolean = false
): string => {
  if (periodType === 'monthly' && periodMonth !== undefined && periodMonth !== null) {
    return `${getMonthName(periodMonth, isMonthZeroBased)} ${periodYear}`;
  } else if (periodType === 'quarterly' && periodQuarter !== undefined && periodQuarter !== null) {
    return `${periodQuarter} ${periodYear}`;
  } else if (periodYear !== undefined && periodYear !== null) {
    return `${periodYear}`;
  }
  return '';
};

/**
 * Formats period date based on provided period type, month, quarter and year
 * 
 * @param periodType - Type of period ('monthly', 'quarterly', or undefined)
 * @param periodMonth - Month number (1-12) for monthly period
 * @param periodQuarter - Quarter value (Q1, Q2, Q3, Q4) for quarterly period
 * @param periodYear - Year of the period
 * @returns Formatted period date string
 */
// export const formatPeriodType = (
//   periodType: string | undefined,
//   periodMonth: number | undefined,
//   periodQuarter: string | number | undefined,
//   periodYear: number | string | undefined
// ): string => {
//   return formatBasePeriod(periodType, periodMonth, periodQuarter, periodYear, false);
// };

/**
 * Formats period date with data type included
 * 
 * @param periodType - Type of period ('monthly', 'quarterly', or undefined)
 * @param periodMonth - Month number (0-11) for monthly period
 * @param periodQuarter - Quarter value (Q1, Q2, Q3, Q4) for quarterly period
 * @param periodYear - Year of the period
 * @param dataType - Type of data (actual, budget, etc.)
 * @returns Formatted period date string with data type
 */
export const formatPeriodTypeWithDataType = (
  periodType: string | undefined,
  periodMonth: number | undefined,
  periodQuarter: string | number | undefined, 
  periodYear: number | string | undefined,
  dataType: string | undefined
): string => {
  if (!dataType) return '';
  
  const periodString = formatBasePeriod(periodType, periodMonth, periodQuarter, periodYear, true);
  return periodString ? `(${dataType}) ${periodString}` : '';
};


