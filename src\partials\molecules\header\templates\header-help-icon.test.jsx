import { render, screen } from "@testing-library/react";
import HeaderHelpIcon from "./header-help-icon";

describe("HeaderHelpIcon", () => {
  it("renders without crashing", () => {
    render(<HeaderHelpIcon />);
  });

  it("renders a bell icon", () => {
    render(<HeaderHelpIcon />);
    const bellIcon = screen.getByTestId("header-help-icon-dropdown");
    expect(bellIcon).toBeInTheDocument();
  });

  it("renders a dropdown with help text", () => {
    render(<HeaderHelpIcon />);
    const dropdownButton = screen.getByTestId("header-help-icon-dropdown");
    expect(dropdownButton).toBeInTheDocument();
    expect(dropdownButton).toHaveTextContent("Help");
  });
});
