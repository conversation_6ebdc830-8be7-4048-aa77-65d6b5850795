import { fetcher } from "../../../general/fetcher";
import { serviceUrl } from "../../../constants/config";

const GET_KPI_CONFIG_API_URL = serviceUrl + "api/data-ingestion/kpi-config";
const GET_PAGE_CONFIG_DATA_URL = serviceUrl + "api/data-ingestion/page-config";

export const GET_KPI_CONFIG_SERVICE = async () => {
  try {
    const response = await fetcher(GET_KPI_CONFIG_API_URL);
    if (response?.length > 0) {
      return await response;
    } else {
      console.error("Error fetching KPI config:", response?.statusText);
    }
  } catch (error) {
    console.error("Error fetching KPI config:", error);
    throw error;
  }
};

export const GET_PAGE_CONFIG_DATA = async () => {
  try {
    const response = await fetcher(GET_PAGE_CONFIG_DATA_URL);
    if (response?.length > 0) {
      return await response;
    } else {
      console.error("Error fetching Page config:", response?.statusText);
    }
  } catch (error) {
    console.error("Error fetching Page config:", error);
    throw error;
  }
};

export const GET_KPI_MAPPING_SERVICE = async (portfolioCompanyId) => {
  try {
    const url = `${serviceUrl}api/data-ingestion/kpi-mapping?portfolioCompanyId=${portfolioCompanyId}`;
    const response = await fetcher(url);
    if (response?.length > 0) {
      return await response;
    } else {
      console.error("Error fetching KPI config:", response?.statusText);
    }
  } catch (error) {
    console.error("Error fetching KPI mapping:", error);
    throw error;
  }
};
