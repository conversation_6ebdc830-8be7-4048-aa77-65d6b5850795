/* Custom styling for PDF components */

/* Circular checkbox styles */
.thumbnail-section .k-checkbox-md {
  border-radius: 50% !important;
  width: 18px !important;
  height: 18px !important;
}

.k-checkbox:checked {
  background-color: #4061C7 !important;
  border-color: #4061C7 !important;
}

/* Custom checkmark to make it circular */
.k-checkbox:checked::before {
  content: "";
  position: absolute;
  width: 8px;
  height: 8px;
  background-color: white;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* Circular checkbox focus styles */
.k-checkbox:focus-visible {
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.3) !important;
  border-radius: 50% !important;
}

/* Selected pages counter */
.pdf-selection-counter {
  padding: 4px 12px;
}
.footer-s{
  width: 100%;
  border-top-width: 0.87px;
}
.preview-shadow{
  box-shadow: 0px 3.07px 3.07px 0px #00000040;
}