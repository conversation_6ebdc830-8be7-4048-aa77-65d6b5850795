import React from 'react';
import { render } from '@testing-library/react';
import { highlightText, formatWithCommas } from './text-utils';

describe('Text Utils', () => {
  describe('highlightText', () => {
    test('should return text unchanged when no query is provided', () => {
      const text = 'Hello World';
      const result = highlightText(text, '');
      expect(result).toBe(text);
    });

    test('should return text unchanged when query is null or undefined', () => {
      const text = 'Hello World';
      expect(highlightText(text, null)).toBe(text);
      expect(highlightText(text, undefined)).toBe(text);
    });

    test('should handle null or undefined text input', () => {
      expect(highlightText(null, 'query')).toBe(null);
      expect(highlightText(undefined, 'query')).toBe(undefined);
    });

    test('should highlight matching text with yellow background', () => {
      const text = 'Hello World';
      const query = 'World';
      const result = highlightText(text, query);
      
      // Render the result to check if it contains the highlighted span
      const { container } = render(<div>{result}</div>);
      const highlightedElement = container.querySelector('span.bg-\\[\\#F8FF36\\]');
      
      expect(highlightedElement).toBeTruthy();
      expect(highlightedElement.textContent).toBe('World');
    });

    test('should be case insensitive when highlighting', () => {
      const text = 'Hello WORLD';
      const query = 'world';
      const result = highlightText(text, query);
      
      const { container } = render(<div>{result}</div>);
      const highlightedElement = container.querySelector('span.bg-\\[\\#F8FF36\\]');
      
      expect(highlightedElement).toBeTruthy();
      expect(highlightedElement.textContent).toBe('WORLD');
    });

    test('should handle numbers with commas and highlight correctly', () => {
      const text = '1234567';
      const query = '1234567';
      const result = highlightText(text, query);
      
      const { container } = render(<div>{result}</div>);
      const highlightedElement = container.querySelector('span.bg-\\[\\#F8FF36\\]');
      
      expect(highlightedElement).toBeTruthy();
      // Should format number with commas
      expect(container.textContent).toContain('1,234,567');
    });

    test('should handle partial matches in numeric values', () => {
      const text = '1000000';
      const query = '1000';
      const result = highlightText(text, query);
      
      const { container } = render(<div>{result}</div>);
      const highlightedElement = container.querySelector('span.bg-\\[\\#F8FF36\\]');
      
      expect(highlightedElement).toBeTruthy();
    });

    test('should handle text that does not match query', () => {
      const text = 'Hello World';
      const query = 'xyz';
      const result = highlightText(text, query);
      
      expect(result).toBe(text);
    });

    test('should handle special regex characters in query', () => {
      const text = 'Price: $100.50 (USD)';
      const query = '$100.50';
      const result = highlightText(text, query);
      
      // Should not throw error and should handle special characters
      expect(typeof result).toBe('string');
    });
  });

  describe('formatWithCommas', () => {
    test('should add commas to large numbers', () => {
      expect(formatWithCommas('1000')).toBe('1,000');
      expect(formatWithCommas('1000000')).toBe('1,000,000');
      expect(formatWithCommas('1234567890')).toBe('1,234,567,890');
    });

    test('should handle numbers as strings', () => {
      expect(formatWithCommas('123456')).toBe('123,456');
    });

    test('should handle numbers passed as numbers', () => {
      expect(formatWithCommas(123456)).toBe('123,456');
    });

    test('should not add commas to small numbers', () => {
      expect(formatWithCommas('123')).toBe('123');
      expect(formatWithCommas('99')).toBe('99');
    });

    test('should handle decimal numbers', () => {
      expect(formatWithCommas('1234.56')).toBe('1,234.56');
      expect(formatWithCommas('1000000.789')).toBe('1,000,000.789');
    });

    test('should handle negative numbers', () => {
      expect(formatWithCommas('-1234567')).toBe('-1,234,567');
      expect(formatWithCommas('-1000.50')).toBe('-1,000.50');
    });

    test('should return null/undefined/empty values unchanged', () => {
      expect(formatWithCommas(null)).toBe(null);
      expect(formatWithCommas(undefined)).toBe(undefined);
      expect(formatWithCommas('')).toBe('');
    });

    test('should handle zero', () => {
      expect(formatWithCommas('0')).toBe('0');
      expect(formatWithCommas(0)).toBe('0');
    });

    test('should handle non-numeric strings', () => {
      expect(formatWithCommas('abc')).toBe('abc');
      expect(formatWithCommas('hello123world')).toBe('hello123world');
    });
  });
});