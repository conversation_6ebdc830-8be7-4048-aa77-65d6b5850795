/**
 * @jest-environment jsdom
 */
import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import TableCellKPI from './TableCellKPI';

// We don't need to manually mock the SVG anymore since it's handled by Jest config

describe('TableCellKPI Component', () => {
  const defaultProps = {
    isLabel: 'A',
    kpiName: 'Revenue'
  };

  test('renders the KPI label', () => {
    render(<TableCellKPI {...defaultProps} />);
    
    expect(screen.getByText('A')).toBeInTheDocument();
  });
  
  test('renders the KPI name', () => {
    render(<TableCellKPI {...defaultProps} />);
    
    expect(screen.getByText('Revenue')).toBeInTheDocument();
  });
  
  test('truncates long KPI names with ellipsis', () => {
    // This test checks that the truncate class is applied - the actual truncation
    // is a CSS feature that can't be directly tested in JSDOM
    const longKpiName = 'Very Long KPI Name That Should Be Truncated';
    render(<TableCellKPI isLabel="B" kpiName={longKpiName} />);
    
    const kpiNameElement = screen.getByText(longKpiName);
    expect(kpiNameElement).toHaveClass('truncate');
    expect(kpiNameElement).toHaveAttribute('title', longKpiName);
  });
  
  test('renders the AI icon', () => {
    render(<TableCellKPI {...defaultProps} />);
    
    const aiIcon = screen.getByAltText('AI Suggestion');
    expect(aiIcon).toBeInTheDocument();
    expect(aiIcon).toHaveAttribute('src', 'test-file-stub');
  });
  
  test('renders the info icon', () => {
    render(<TableCellKPI {...defaultProps} />);
    
    // The FiInfo icon is rendered as an SVG, so we need to check for its presence
    // in a different way - looking for any svg within the component
    const svgElement = document.querySelector('svg');
    expect(svgElement).toBeInTheDocument();
    expect(svgElement).toHaveClass('text-[#808080]');
  });
}); 