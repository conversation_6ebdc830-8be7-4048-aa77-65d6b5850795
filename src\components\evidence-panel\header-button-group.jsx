import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import { BsDownload } from "react-icons/bs";
import { FiSettings } from "react-icons/fi";
import { RiResetLeftLine } from "react-icons/ri";
import ButtonIcon from "../../partials/atoms/button/button-icon";
import { ToolTip } from "../../partials/atoms/tool-tip";
import { Button } from "../../partials/atoms/button";
import ExcelExportComponents from "./excel-export-components";
import { Extract } from "../../infra/api/alexanderia/feedback-service";
import {UPDATE_FINANCIALS_SERVICE,UPDATE_FINANCIALS_API_SERVICE} from "../../infra/api/financial/update-financials-service"
import { notify } from "../../partials/molecules/toaster";
import { CgSpinner } from "react-icons/cg";
import { updateFinancialData } from "./utilities/update-financialdata";
import { GET_KPI_MAPPING_SERVICE} from "../../infra/api/company/kpi-service";
import { SUBMIT_KPI_DATA_SERVICE, UPDATE_JOB_STATUS_SERVICE } from "../../infra/api/company/kpi-submit-service";
import { KpiModule } from "../../constants/kpi-module";
import { formatKpiValue } from "../../constants/commonFunctions";

const ButtonGroup = ({
  setShowDownloadConfirmationPopup,
  exportGridOneRef,
  exportGridTwoRef,
  exportGridThreeRef,
  exportGridFourRef,
  incomeStatementData,
  balanceSheetData,
  cashFlowData,
  notesData,
  columnNamesForIncomeStatementData,
  columnNamesForBalanceSheetData,
  columnNamesForCashFlowData,
  columnNamesForNotesData,
  ticker,
  job_id,
  country,
  client_id,
  company_id,
  fs_type,
  industry,
  currency,
  mappedData,
  incomeStatementMappedData,
  balanceSheetMappedData,
  cashFlowMappedData,
  isMapped,  setIsMapped,
  handleExcelExport,
  companyName,
  financialsData,
  handleResetPublish,
  extractionType,
  areAllDropdownsSelected, // New prop for validation
  disableButton,
  disableSaveButton,
  hasChanges,
  client_env,
  kpiConfig,
  acuityid,
  companyDetails,
  selectedCurrencyData,
  selectedCurrencyUnitData
}) => {
  const [isPublishLoading, setIsPublishLoading] = useState(false);
  const [localHasChanges, setLocalHasChanges] = useState(false);
  useEffect(() => {
    if (selectedCurrencyData || selectedCurrencyUnitData) {
      setLocalHasChanges(true);
    } else {
      // Otherwise, use the prop value
      setLocalHasChanges(false);
    }
  }, [selectedCurrencyData, selectedCurrencyUnitData]);
  const handleSave = async () => {
    try {
      if (!isMapped) {
        handleResetPublish();
        setIsPublishLoading(true);
        financialsData.currencyUnit = selectedCurrencyUnitData?.label || financialsData.currencyUnit || "Absolute";
        financialsData.currencyCode = selectedCurrencyData?.currencyCode || financialsData.currencyCode || "";
        financialsData.currencyId = selectedCurrencyData?.currencyID || financialsData.currencyId || 0;
        const statements = [
          { index: 0, data: incomeStatementData, name: 'Income Statement' },
          { index: 1, data: balanceSheetData, name: 'Balance Sheet' },
          { index: 2, data: cashFlowData, name: 'Cash Flow' }
        ];

        const updatedFinancialData = updateFinancialData(
          financialsData,
          statements,
          notesData
        );
        const isValid = validateColumnHeaders(updatedFinancialData.tableGroups);
        if (isValid) {
          //Make parallel calls to Extract API for all statement types
          const extractionPromises = [];
          if (incomeStatementMappedData && incomeStatementMappedData.length > 0) {
            extractionPromises.push(
              Extract({
                job_id,
                country,
                client_id,
                company_id,
                fs_type: "IS",
                industry,
                currency,
                mappedData: incomeStatementMappedData,
                extractionType,
                client_env
              })
            );
          }          
          if (balanceSheetMappedData && balanceSheetMappedData.length > 0) {
            extractionPromises.push(
              Extract({
                job_id,
                country,
                client_id,
                company_id,
                fs_type: "BS",
                industry,
                currency,
                mappedData: balanceSheetMappedData,
                extractionType,
                client_env
              })
            );
          }          
          if (cashFlowMappedData && cashFlowMappedData.length > 0) {
            extractionPromises.push(
              Extract({
                job_id,
                country,
                client_id,
                company_id,
                fs_type: "CF",
                industry,
                currency,
                mappedData: cashFlowMappedData,
                extractionType,
                client_env
              })
            );
          }         
          // Only execute Promise.all if there are promises to await
          if (extractionPromises.length > 0) {
            await Promise.all(extractionPromises);
          }
          const result = await UPDATE_FINANCIALS_API_SERVICE(updatedFinancialData.processID, updatedFinancialData);
          if (result) {
            await submitKpiData(updatedFinancialData);
          }
        }
      }
      // setIsMapped(!isMapped);
    } catch (error) {
      console.error("Failed to save:", error);
      notify.error("Publish failed");
    }
    setIsPublishLoading(false);
  };   
  
  const validateColumnHeaders = (tableGroups) => {
    if (!tableGroups || tableGroups.length === 0) return true; // No validation needed if no table groups
    let notAllowedColumns = [];
    let notAllowedRows = [];
    for (const group of tableGroups) {
      if (group.tables.length > 0) {
        const table = group.tables[0];

        const columns = table.columns || [];
        const rows = table.rows || [];
        let invaliadColumns = [];
        let invaliadRows = [];
        for (const column of columns) {
          // Skip if no periodDate
          if (!column.periodDate) continue;
          // Check if periodDate matches the required format:
          // (Any text) followed by either:
          // - Year only: (Text) 2025
          // - Month and year: (Text) Jan 2025
          // - Quarter and year: (Text) Q1 2025
          const validFormat = /^\([^)]+\)\s+((?:Jan|Feb|Mar|Apr|May|Jun|Jul|Aug|Sep|Oct|Nov|Dec|Q[1-4])?\s+\d{4}|\d{4})$/;

          if (!validFormat.test(column.periodDate)) {
            invaliadColumns.push(column.periodDate);
          }
        }
        // Only show error if invalid columns found
        if (invaliadColumns.length > 0) {
          notAllowedColumns.push(group.alias);
        }
        for (const row of rows) {
          // Skip if no label or header row
          if (!row.label?.text || row.label?.style === "header") continue;
          if ((row.label.mapping == null || row.label.mapping == undefined || row.label.mapping == "") &&
            (row.label.mappingId == null || row.label.mappingId == undefined || row.label.mappingId == "")) {
            invaliadRows.push(row.label.text);
          }
        }
        if (invaliadRows.length > 0) {
          notAllowedRows.push(group.alias);
        }
      }
    }
    if (notAllowedColumns.length > 0 || notAllowedRows.length > 0) {
      if (notAllowedColumns.length > 0) {
        notify.error(`Invalid column headers found in: ${notAllowedColumns.join(", ")}`);
      }
      if (notAllowedRows.length > 0) {
        notify.error(`Unmapped Standard KPI found in: ${notAllowedRows.join(", ")}`);
      }
      return false;
    }
    else {
      return true;
    }
  };
  const handleSaveBackend = async () => {
    try {
        financialsData.currencyUnit = selectedCurrencyUnitData?.label || financialsData.currencyUnit || "Absolute";
        financialsData.currencyCode = selectedCurrencyData?.currencyCode || financialsData.currencyCode || "";
        financialsData.currencyId = selectedCurrencyData?.currencyID || financialsData.currencyId || 0;
      const statements = [
        { index: 0, data: incomeStatementData, name: 'Income Statement' },
        { index: 1, data: balanceSheetData, name: 'Balance Sheet' },
        { index: 2, data: cashFlowData, name: 'Cash Flow' }
      ];
  
      const updatedFinancialData = updateFinancialData(
        financialsData,
        statements,
        notesData
      );        
      await UPDATE_FINANCIALS_API_SERVICE(updatedFinancialData.processID, updatedFinancialData);
      
      // Show a success message with instructions
      notify.success("Saved successfully.");
      
      // Wait a moment for the notification to be visible
      setTimeout(() => {
        // Force a full page refresh - the most reliable way to ensure Excel export updates
        window.location.reload();
      }, 100);
    } catch (error) {
      console.error("Failed to save:", error);
      notify.error("Save failed");
    }
  };
  
  /**
   * Submits KPI data to the backend
   * @returns {Promise<boolean>} - Whether the submission was successful
   */
  const submitKpiData = async (updatedFinancialData) => {
    try {
      const kpiPayload = await createKpiPayload(updatedFinancialData);
      if (!kpiPayload) {
        notify.error("Unable to create KPI payload. Please check your data.");
        return false;
      }

      // Call the API service to submit the data
      const response = await SUBMIT_KPI_DATA_SERVICE(kpiPayload,updatedFinancialData.processID);
      if(response && response.success){
        let status = "";
        if (response && response.success) {
          status = "Completed";
          notify.success(response?.message);
        }
        else {
          status = "Failed";
          const errorMessage = response?.message || "Unknown error occurred";
          notify.error(`Failed to update status: ${errorMessage}`);
        }
        const statusRequest = {
          processId: updatedFinancialData.processID,
          status: status
        }
        const statusResponse = await UPDATE_JOB_STATUS_SERVICE(statusRequest);
        if (statusResponse && statusResponse.isSuccess) {
          notify.success(statusResponse?.message);
          return true;
        } else {
          const errorMessage = statusResponse?.message || "Unknown error occurred";
          notify.error(`Failed to update status: ${errorMessage}`);
          return false;
        }
      }
      
    } catch (error) {
      console.error("Error submitting KPI data:", error);
      return false;
    }
  };

  /**
   * Creates a KPI payload for unlocked columns
   * Returns a payload object formatted for the API
   */
  const createKpiPayload = async (updatedFinancialData) => {
    // Skip if no data
    if (!updatedFinancialData) {
      return null;
    }
   var files = Array.isArray(updatedFinancialData.files) ? updatedFinancialData.files : [];
  const fileDetails = files
  .filter(file => file && file.fileName && file.url)
  .map(file => ({
    fileId: file.fileName,
    filePath: file.url.replace(/^.*(Documents\/)/, "Documents/"),
    fileName: file.fileName
  }));
    let payload = [];

    if ((updatedFinancialData?.tableGroups ?? []).length > 0) {
      // Using for...of instead of forEach to properly handle async operations
      for (const currentGroup of updatedFinancialData.tableGroups) {
        // Process current table group's data
        let kpiList = [];
        const moduleId = getModuleId(currentGroup?.label);
        if (!currentGroup || !currentGroup.tables || !currentGroup.tables[0]) {
          continue; // Skip this iteration instead of returning null
        }
        let result  = await GET_KPI_MAPPING_SERVICE(acuityid);
        const mappingData  = result?.find(x => x.moduleId == moduleId)?.kpis || [];
        const currentTable = currentGroup.tables[0];
        const columns = currentTable.columns || [];
        const rows = currentTable.rows || [];

        // Skip if no columns or rows
        if (columns.length === 0 || rows.length === 0) {
          continue; // Skip this iteration instead of returning null
        }

        const onlyKeys = columns.map((key) => key.columnKey);

        // Process each row
        for (const row of rows) {
          // Skip rows with empty labels or header rows
          if (!row.label?.text || row.label?.style === "header") {
            continue;
          }

          // Find the KPI mapping for this row's label
          const kpiMapping = mappingData?.find(
            (mapping) => mapping.mappingId === row.label.mappingId
          );

          // Skip if no matching KPI mapping
          if (!kpiMapping) {
            continue;
          }

          const mappingId = kpiMapping.mappingId;
          const kpiId = kpiMapping.kpiId;

          const matchedCells = row?.cells?.filter((x) => onlyKeys.includes(x.columnKey));

          // Process each cell in the row that matches a column key
          for (const cell of matchedCells) {
            // Extract period information from column header
            const columnInfo = columns.find(x => x.columnKey === cell?.columnKey);
            const periodInfo = extractPeriodInfo(columnInfo, moduleId);

            // Skip if we couldn't extract period info
            if (!periodInfo) {
              continue;
            }

            let formatedValue = formatKpiValue(kpiMapping.kpiInfo, cell.value);

            // Add the KPI to the list
            kpiList.push({
              kpiId: kpiId,
              mappingId: mappingId,
              kpiValue: formatedValue.toString(),
              valueType: periodInfo.valueType || "",
              valueTypeId: periodInfo.valueTypeId || 0,
              month: periodInfo.month || null,
              quarter: periodInfo.quarter || null,
              year: periodInfo.year || null,
              moduleId: moduleId,
              kpiInfo: kpiMapping?.kpiInfo,
              methodologyId: kpiMapping?.methodologyID
            });
          }
        }
        // Construct the full payload item
        payload.push({
          kpiList: kpiList,
          companyId: companyDetails?.portfolioCompanyID,
          moduleId: moduleId,
          currencyId: updatedFinancialData?.currencyId || 0,
          fundCurrencyId: 0,
          connectionString: "string",
          userId: 0,
          isFinancial: true,
          unitCurrency: updatedFinancialData?.currencyUnit || "Thousands",
          documentDetails: fileDetails,
        });
      }
    }

    return payload;
  };

  /**
   * Extract period information from column header info
   */
  const extractPeriodInfo = (columnInfo, moduleId) => {
    if (!columnInfo || !columnInfo.periodDate) {
      return null;
    }

    // Handle formats like "(Actual) Q1 2020" or "(Actual YTD) Q1 2020"
    const parenPeriodPattern = /\(([^)]+)\)\s*(Q[1-4])?\s*(\d{4})/i;
    const match = columnInfo.periodDate.match(parenPeriodPattern);

    if (match) {
      // We have a pattern like "(Actual) Q1 2020" or "(Actual YTD) Q1 2020" or "(Actual) 2023"
      const valueType = match[1].trim(); // "Actual" or "Actual YTD"
      const quarterStr = match[2] || ""; // "Q1" or ""
      const year = parseInt(match[3], 10) || 0; // 2023 or 2020

      // Find valueTypeId from kpiConfig
      const valueTypeConfig = kpiConfig?.find(config =>
        config.moduleId === moduleId
      )?.subSectionFields.find(field =>
        field.aliasName.toLowerCase() === valueType.toLowerCase()
      );

      const valueTypeId = valueTypeConfig?.valueTypId || 4; // Default to 4 if not found

      // Extract quarter if present
      let quarter = '';
      if (quarterStr && quarterStr.toLowerCase().startsWith('q')) {
        quarter = quarterStr;
      }

      return {
        valueType: valueType.charAt(0).toUpperCase() + valueType.slice(1),
        valueTypeId,
        month: null, // No month specified in this format
        quarter, // Will be 0 if no quarter was specified
        year,
      };
    }

    // Handle the standard format (e.g. "actual jan 2020" or "actual q1 2020")
    // First check if it has parentheses that might need special handling
    if (columnInfo.periodDate.includes('(') && columnInfo.periodDate.includes(')')) {
      // There are parentheses but they didn't match our first pattern
      // Extract the part in parentheses as valueType, and the rest for further processing
      const parenMatch = columnInfo.periodDate.match(/\(([^)]+)\)\s*(.*)/);
      if (parenMatch) {
        const valueType = parenMatch[1].trim();
        const remainingText = parenMatch[2].trim();
        const remainingParts = remainingText.split(' ');

        // Find valueTypeId from kpiConfig
        const valueTypeConfig = kpiConfig?.find(config =>
          config.moduleId === moduleId
        )?.subSectionFields.find(field =>
          field.aliasName.toLowerCase() === valueType.toLowerCase()
        );

        const valueTypeId = valueTypeConfig?.valueTypId || 4;

        let month = 0;
        let quarter = null;
        let year = 0;

        // Extract quarter/month and year from remaining parts
        if (remainingParts.length >= 1) {
          // Try to get the year from the last part
          year = parseInt(remainingParts[remainingParts.length - 1], 10) || 0;

          // If we have at least two parts, check for quarter/month
          if (remainingParts.length >= 2) {
            const periodPart = remainingParts[0].toLowerCase();
            if (periodPart.startsWith('q')) {
              quarter = remainingParts[0] || "";
            } else {
              // Check if it's a month
              const monthNames = ["jan", "feb", "mar", "apr", "may", "jun", "jul", "aug", "sep", "oct", "nov", "dec"];
              month = monthNames.findIndex(m => periodPart.startsWith(m)) + 1;
            }
          }
        }

        return {
          valueType: valueType.charAt(0).toUpperCase() + valueType.slice(1),
          valueTypeId,
          month,
          quarter,
          year
        };
      }
    }

    // Regular split-based approach for simple formats
    const parts = columnInfo.periodDate.split(' ');
    if (parts.length < 3) {
      return null;
    }

    const valueType = parts[0]; // e.g., "actual", "budget"
    let month = 0;
    let quarter = 0;
    let year = parseInt(parts[2], 10) || 0;

    // Handle different period formats
    if (parts[1].startsWith('q')) {
      quarter = parseInt(parts[1].substring(1), 10) || 0;
    } else {
      // Convert month name to number
      const monthNames = ["jan", "feb", "mar", "apr", "may", "jun", "jul", "aug", "sep", "oct", "nov", "dec"];
      month = monthNames.findIndex(m => parts[1].toLowerCase().startsWith(m)) + 1;
    }

    // Find valueTypeId from kpiConfig
    const valueTypeConfig = kpiConfig?.find(config =>
      config.moduleId === moduleId
    )?.subSectionFields.find(field =>
      field.aliasName.toLowerCase() === valueType
    );

    const valueTypeId = valueTypeConfig?.valueTypId || 4; // Default to 4 if not found

    return {
      valueType: valueType.charAt(0).toUpperCase() + valueType.slice(1),
      valueTypeId,
      month,
      quarter,
      year
    };
  };

  const getModuleId = (aliasName) => {
    switch (aliasName) {
      case "income statement": return KpiModule.ProfitAndLoss; // 7
      case "balance sheet": return KpiModule.BalanceSheet; // 8
      case "cash flow": return KpiModule.CashFlow; // 9
      default: return KpiModule.ProfitAndLoss;
    }
  };

  const downloadExcel = () => {
    isMapped ? handleExcelExport() : setShowDownloadConfirmationPopup(true);
  };
  return (
    <div className="flex items-center gap-2 ml-auto">
      <ButtonIcon
        className="border border-primary-78"
        data-testid="export-button"
        intent="teritory"
        data-tooltip-id="tool-tip-download-top"
        onClick={downloadExcel}
      >
        <BsDownload className="h-4 w-4" />
        <ToolTip place="left" text="Click on Save before downloding the excel." toolTipId="tool-tip-download-top" />
      </ButtonIcon>
      <ExcelExportComponents
        exportGridOneRef={exportGridOneRef}
        exportGridTwoRef={exportGridTwoRef}
        exportGridThreeRef={exportGridThreeRef}
        exportGridFourRef={exportGridFourRef}
        incomeStatementData={incomeStatementData}
        balanceSheetData={balanceSheetData}
        cashFlowData={cashFlowData}
        notesData={notesData}
        columnNamesForIncomeStatementData={columnNamesForIncomeStatementData}
        columnNamesForBalanceSheetData={columnNamesForBalanceSheetData}
        columnNamesForCashFlowData={columnNamesForCashFlowData}
        columnNamesForNotesData={columnNamesForNotesData}
        ticker={ticker}
        isMapped={isMapped}
        companyName={companyName}
        columnHeaderInfo={financialsData?.columnHeaderInfo}
      />      {(
        <Button
          disabled={!(hasChanges || localHasChanges)}
          data-testid="save-draft-button"
          intent="secondary"
          onClick={handleSaveBackend}
        >
          Save
        </Button>
      )}
      <Button
        data-testid="publish-button"
        className={"publish-button flex-row w-fit"}
        data-tooltip-id="tool-tip-download-publish"
        onClick={handleSave}
        size={"medium"}
        disabled={isPublishLoading || disableButton}
      >
        <ToolTip place="left" text="Click on Save is mandatory before publish." toolTipId="tool-tip-download-publish" />
        {isPublishLoading ? (
          <span className="flex w-fit items-center gap-2">
            <CgSpinner
              strokeWidth={1}
              className="size-4 animate-spin text-white"
            />
            {isMapped ? "Rework" : "Publish"}
          </span>
        ) : (
          <>{isMapped ? "Rework" : "Publish"}</>
        )}
      </Button>
    </div>
  );
};

ButtonGroup.propTypes = {
  setShowDownloadConfirmationPopup: PropTypes.func.isRequired,
  exportGridOneRef: PropTypes.object.isRequired,
  exportGridTwoRef: PropTypes.object.isRequired,
  exportGridThreeRef: PropTypes.object.isRequired,
  exportGridFourRef: PropTypes.object.isRequired,
  incomeStatementData: PropTypes.array.isRequired,
  balanceSheetData: PropTypes.array.isRequired,
  cashFlowData: PropTypes.array.isRequired,
  notesData: PropTypes.array.isRequired,
  columnNamesForIncomeStatementData: PropTypes.array.isRequired,
  columnNamesForBalanceSheetData: PropTypes.array.isRequired,
  columnNamesForCashFlowData: PropTypes.array.isRequired,
  columnNamesForNotesData: PropTypes.array.isRequired,
  ticker: PropTypes.string.isRequired,
  job_id: PropTypes.string.isRequired,
  country: PropTypes.string.isRequired,
  client_id: PropTypes.string.isRequired,
  company_id: PropTypes.string.isRequired,
  fs_type: PropTypes.oneOf(["IS", "CF", "BS"]).isRequired,
  industry: PropTypes.string.isRequired,
  currency: PropTypes.string.isRequired,
  mappedData: PropTypes.arrayOf(
    PropTypes.shape({
      rtext: PropTypes.string.isRequired,
      stext: PropTypes.string.isRequired,
      mapping_id: PropTypes.number.isRequired,
      order: PropTypes.number.isRequired
    })
  ).isRequired,
  incomeStatementMappedData: PropTypes.array.isRequired,
  balanceSheetMappedData: PropTypes.array.isRequired,
  cashFlowMappedData: PropTypes.array.isRequired,
  handleExcelExport: PropTypes.func.isRequired,
  isMapped: PropTypes.bool.isRequired,
  setIsMapped: PropTypes.func.isRequired,
  companyName: PropTypes.string.isRequired,
  financialsData: PropTypes.any.isRequired,
  handleResetPublish: PropTypes.func.isRequired,  
  extractionType: PropTypes.string.isRequired,
  areAllDropdownsSelected: PropTypes.bool.isRequired, // New prop for validation
  kpiConfig: PropTypes.object.isRequired,
  acuityid: PropTypes.string.isRequired,
  companyDetails: PropTypes.object.isRequired,
  selectedCurrencyData: PropTypes.object.isRequired,
  selectedCurrencyUnitData: PropTypes.object.isRequired
};

export default ButtonGroup;
