.nep-modal-mask {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

#loader<PERSON>ontainer {
    background: rgba(0, 0, 0, 0.25) !important;
}

#loaderContainer .loader-body {
    position: relative;  
    top: 35%;
}

#loaderContainer .lds-ellipsis {
    display: inline-block;
    position: relative;
    width: 80px;
}

#loaderContainer .lds-ellipsis div {
    position: absolute;
    top: 33px;
    width: 13px;
    height: 13px;
    border-radius: 50%;
    background: #4061C7;
    animation-timing-function: cubic-bezier(0, 1, 1, 0);
}

#loaderContainer .lds-ellipsis div:nth-child(1) {
    left: 8px;
    animation: lds-ellipsis1 0.6s infinite;
}

#loaderContainer .lds-ellipsis div:nth-child(2) {
    left: 8px;
    animation: lds-ellipsis2 0.6s infinite;
}

#loaderContainer .lds-ellipsis div:nth-child(3) {
    left: 32px;
    animation: lds-ellipsis2 0.6s infinite;
}

#loaderContainer .lds-ellipsis div:nth-child(4) {
    left: 56px;
    animation: lds-ellipsis3 0.6s infinite;
}

@keyframes lds-ellipsis1 {
    0% {
        transform: scale(0);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes lds-ellipsis3 {
    0% {
        transform: scale(1);
    }
    100% {
        transform: scale(0);
    }
}

@keyframes lds-ellipsis2 {
    0% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(24px, 0);
    }
}

#loaderContainer .nep-spin-ring {
    margin: auto;
    border-style: solid;
    border-color: #f5f5f7;
    -webkit-animation: frames 1s infinite linear;
    animation: frames 1s infinite linear;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    border-width: 5.4px;
    border-top-color: #4061C7;
}

@keyframes frames {
    0% {
        -webkit-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(359deg);
        transform: rotate(359deg);
    }
}

.nep-modal {
    position: fixed;
    z-index: 1050;
    top: 0;
    left: 0;
    overflow: auto;
    width: 100%;
    height: 100%;
    text-align: center;
    opacity: 0;
    transition: opacity 0.2s linear;
}

.nep-modal-show {
    opacity: 1;
}

.nep-modal-show .nep-modal-panel {
    transform: translate(0);
}
