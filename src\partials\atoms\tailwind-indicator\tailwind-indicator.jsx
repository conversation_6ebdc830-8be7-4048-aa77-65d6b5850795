import React, { useState, useEffect, Suspense } from "react";
import { isDevelopment } from "../../../general/utils";

export const TailwindIndicator = () => {
  const [showDevtools, setShowDevtools] = useState(isDevelopment);

  useEffect(() => {
    window.toggleTailwind = () => setShowDevtools((old) => !old);
  }, []);

  return (
    <>
      {showDevtools && (
        <Suspense fallback={null}>
          <TailwindDevTools />
        </Suspense>
      )}
    </>
  );
};

export const TailwindDevTools = () => {
  const [screenSize, setScreenSize] = useState(window.innerWidth);

  window.addEventListener("resize", () => {
    setScreenSize(window.innerWidth);
  });

  return (
    <div className="body-m fixed right-1 top-1 z-30 flex h-6 items-center justify-center rounded-full bg-neutral-10 px-2">
      <div className="block sm:hidden">xs</div>
      <div className="hidden sm:block md:hidden">sm</div>
      <div className="hidden md:block lg:hidden">md</div>
      <div className="hidden lg:block xl:hidden">lg</div>
      <div className="hidden xl:block 2xl:hidden">xl</div>
      <div className="hidden 2xl:block 3xl:hidden">2xl</div>
      <div className="hidden 3xl:block">3xl</div>
      <div>:{screenSize}</div>
    </div>
  );
};
