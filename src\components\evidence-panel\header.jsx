import React, { useState, useEffect, useRef } from "react";
import PropTypes from "prop-types";
import { BsArrowLeft } from "react-icons/bs";
import ButtonIcon from "../../partials/atoms/button/button-icon";
import { notify } from "../../partials/molecules/toaster";
import DownloadConfirmationPopup from "./download-confirmation-popup";
import HeaderInfo from "./header-info";
import ButtonGroup from "./header-button-group";
import { handleExport } from "./utils";

const Header = ({
  className = "flex flex-row w-full h-[60px]",
  handleBackButton,
  currentTableData,
  incomeStatementData,
  balanceSheetData,
  cashFlowData,
  companyName,
  generateColumns,
  deserializeData,
  tableGroups,
  companyTicker,
  acuityid,
  currencyUnit,
  currency,
  financialsData,
  notesData,
  job_id,
  country,
  client_id,
  company_id,
  fs_type,
  industry,
  mappedData,
  incomeStatementMappedData,
  balanceSheetMappedData,
  cashFlowMappedData,
  isMapped,
  setIsMapped,
  standardLineItemsForIncomeStatement,
  standardLineItemsForBalanceSheet,
  standardLineItemsForCashFlow,
  selectedTab,
  handleResetPublish,
  extractionType,
  currencyDetails,
  onCurrencyChange,
  onCurrencyUnitChange,
  kpiConfig,
  areAllDropdownsSelected,
  disableButton,
  disableSaveButton,
  hasChanges,
  client_env,
  companyDetails
}) => {
  const [showDownloadConfirmationPopup, setShowDownloadConfirmationPopup] =
    useState(false);
  const [
    columnNamesForIncomeStatementData,
    setColumnNamesForIncomeStatementData
  ] = useState([]);
  const [columnNamesForBalanceSheetData, setColumnNamesForBalanceSheetData] =
    useState([]);
  const [columnNamesForCashFlowData, setColumnNamesForCashFlowData] = useState(
    []
  );
  const [columnNamesForNotesData, setColumnNamesForNotesData] = useState([]);
  const [updatedIncomeStatementData, setUpdatedIncomeStatementData] = useState(
    []
  );
  const [updatedBalanceSheetData, setUpdatedBalanceSheetData] = useState([]);
  const [updatedCashFlowData, setUpdatedCashFlowData] = useState([]);
  const exportGridOneRef = useRef(null);
  const exportGridTwoRef = useRef(null);
  const exportGridThreeRef = useRef(null);
  const exportGridFourRef = useRef(null);
  const [selectedCurrencyData, setSelectedCurrencyData] = useState(null);
  const [selectedCurrencyUnitData, setSelectedCurrencyUnitData] = useState(null);

  // Handler for currency changes from HeaderInfo
  const handleHeaderInfoCurrencyChange = (currencyData) => {
    // Store the selected currency data in state
    setSelectedCurrencyData(currencyData);
    
    // Also call the original onCurrencyChange if provided
    if (onCurrencyChange) {
      onCurrencyChange(currencyData);
    }
  };
  // Handler for currency unit changes from HeaderInfo
  const handleHeaderInfoCurrencyUnitChange = (currencyUnitData) => {
    // Store the selected currency data in state
    setSelectedCurrencyUnitData(currencyUnitData);

    // Also call the original onCurrencyUnitChange if provided
    if (onCurrencyUnitChange) {
      onCurrencyUnitChange(currencyUnitData);
    }
  };

  const handleExcelExport = () => {
    handleExport(
      exportGridOneRef,
      exportGridTwoRef,
      exportGridThreeRef,
      exportGridFourRef,
      companyName,
      notify,
      currency,
      currencyUnit,
      kpiConfig
    );
    setShowDownloadConfirmationPopup(false);
  };
  const generateColumnNamesForNotes = (data) => {
    const columns = [
      {
        field: "status",
        title: "Notes",
        width: "325px",
        className: "body-r text-neutral-80",
        headerClassName: "body-m text-neutral-60"
      },
      {
        field: "label",
        title: "Heading",
        width: "204px",
        className: "text-body-r font-body-r text-neutral-80 cursor-pointer",
        headerClassName: "body-m text-neutral-60"
      }
    ];
    if (data.length > 0) {
      const sampleItem = data[0];
      Object.keys(sampleItem.cellIds).forEach((key) => {
        columns.push({
          field: key,
          width: "160px",
          className: "body-r text-neutral-80 text-right cursor-pointer",
          headerClassName: "!p-0"
        });
      });
    }

    return columns;
  };

  const generateColumnNames = (data) => {
    const columns = [
      {
        field: "status",
        title: "Standard Line Item",
        alias:"Standarad KPI",
        width: "325px",
        className: "body-r text-neutral-80",
        headerClassName: "body-m text-neutral-60"
      },
      {
        field: "label",
        title: "Document Line Item",
        alias:"Document KPI",
        width: "204px",
        className: "text-body-r font-body-r text-neutral-80 cursor-pointer",
        headerClassName: "body-m text-neutral-60"
      }
    ];
    if (data.length > 0) {
      const sampleItem = data[0];

      // Check if periodDates exists in the sampleItem
      if (sampleItem.cellIds && sampleItem.periodDates) {
        Object.keys(sampleItem.cellIds).forEach((key) => {
          // Get the corresponding period date for this cell ID
          const periodDate = sampleItem.periodDates[key] || key; // Fallback to the key if no date found
          columns.push({
            field: key, // Keep the original field as the key for data binding
            title: periodDate, // Display the period date as the column title
            width: "160px",
            className: "body-r text-neutral-80 text-right cursor-pointer",
            headerClassName: "!p-0",
            // Store the original mapping for reference
            periodDateMapping: {
              cellId: key,
              periodDate: periodDate
            }
          });
        });
      } else {
        // Fallback to original behavior if periodDates isn't available
        Object.keys(sampleItem.cellIds).forEach((key) => {
          columns.push({
            field: key,
            width: "160px",
            className: "body-r text-neutral-80 text-right cursor-pointer",
            headerClassName: "!p-0"
          });
        });
      }
    }
    return columns;
  };

  useEffect(() => {
    if (financialsData?.tableGroups) {
      const updatedIncomeStatementData = incomeStatementData.map((item) => {
        const matchedItem = standardLineItemsForIncomeStatement.find(
          (standardItem) => standardItem.rtext === item.label
        );
        return matchedItem ? { ...item, status: matchedItem.stext } : item;
      });
      const updatedBalanceSheetData = balanceSheetData.map((item) => {
        const matchedItem = standardLineItemsForBalanceSheet.find(
          (standardItem) => standardItem.rtext === item.label
        );
        return matchedItem ? { ...item, status: matchedItem.stext } : item;
      });
      const updatedCashFlowData = cashFlowData.map((item) => {
        const matchedItem = standardLineItemsForCashFlow.find(
          (standardItem) => standardItem.rtext === item.label
        );
        return matchedItem ? { ...item, status: matchedItem.stext } : item;
      });
      setUpdatedIncomeStatementData(updatedIncomeStatementData);
      setUpdatedBalanceSheetData(updatedBalanceSheetData);
      setUpdatedCashFlowData(updatedCashFlowData);
    }
  }, [
    financialsData,
    incomeStatementData,
    balanceSheetData,
    cashFlowData,
    notesData,
    standardLineItemsForIncomeStatement,
    standardLineItemsForBalanceSheet,
    standardLineItemsForCashFlow
  ]);

  useEffect(() => {
    const incomeStatementColumns = generateColumnNames(incomeStatementData);
    setColumnNamesForIncomeStatementData(incomeStatementColumns);

    const balanceSheetColumns = generateColumnNames(balanceSheetData);
    setColumnNamesForBalanceSheetData(balanceSheetColumns);

    const cashFlowColumns = generateColumnNames(cashFlowData);
    setColumnNamesForCashFlowData(cashFlowColumns);

    const notesColumns = notesData.map((notesTableData) =>
      generateColumnNamesForNotes(notesTableData)
    );
    setColumnNamesForNotesData(notesColumns);
  }, [
    incomeStatementData,
    balanceSheetData,
    cashFlowData,
    notesData,
    generateColumns
  ]);
  return (
    <header
      className={`${className} border-t border-b border-[#ededf2] header-bg-color pr-4 py-[0.875rem] flex items-center border-l border-r border-l-[#EDEDF2] border-r-[#EDEDF2] rounded-[8px]`}
    >
      <div className="flex flex-row items-center w-full">
        <HeaderInfo
          companyName={companyName}
          companyTicker={companyTicker}
          acuityid={acuityid}
          currencyUnit={currencyUnit}
          currency={currency}
          currencyList={currencyDetails}
          onCurrencyChange={handleHeaderInfoCurrencyChange}
          onCurrencyUnitChange={handleHeaderInfoCurrencyUnitChange}
        />
        <div className="ml-2 bg-white">
          <ButtonGroup
            setShowDownloadConfirmationPopup={setShowDownloadConfirmationPopup}
            exportGridOneRef={exportGridOneRef}
            exportGridTwoRef={exportGridTwoRef}
            exportGridThreeRef={exportGridThreeRef}
            exportGridFourRef={exportGridFourRef}
            incomeStatementData={updatedIncomeStatementData}
            balanceSheetData={updatedBalanceSheetData}
            cashFlowData={updatedCashFlowData}
            notesData={notesData}
            columnNamesForIncomeStatementData={columnNamesForIncomeStatementData}
            columnNamesForBalanceSheetData={columnNamesForBalanceSheetData}
            columnNamesForCashFlowData={columnNamesForCashFlowData}
            columnNamesForNotesData={columnNamesForNotesData}
            ticker={companyTicker}
            job_id={job_id}
            country={country}
            client_id={client_id}
            company_id={company_id}
            fs_type={fs_type}
            industry={industry}
            currency={currencyUnit}
            mappedData={mappedData}
            incomeStatementMappedData={incomeStatementMappedData}
            balanceSheetMappedData={balanceSheetMappedData}
            cashFlowMappedData={cashFlowMappedData}
            isMapped={isMapped}
            setIsMapped={setIsMapped}
            handleExcelExport={handleExcelExport}
            companyName={companyName}
            financialsData={financialsData}
            handleResetPublish={handleResetPublish}
            extractionType={extractionType}
            areAllDropdownsSelected={areAllDropdownsSelected}
            disableButton={disableButton}
            disableSaveButton={disableSaveButton}
            hasChanges={hasChanges}
            client_env={client_env}
            kpiConfig={kpiConfig}
            acuityid={acuityid}
            companyDetails={companyDetails}
            selectedCurrencyData={selectedCurrencyData}
            selectedCurrencyUnitData={selectedCurrencyUnitData}
          />
        </div>
      </div>
      {showDownloadConfirmationPopup && (
        <DownloadConfirmationPopup
          showDownloadConfirmationPopup={showDownloadConfirmationPopup}
          setShowDownloadConfirmationPopup={setShowDownloadConfirmationPopup}
          handleExcelExport={handleExcelExport}
        />
      )}
    </header>
  );
};

Header.propTypes = {
  className: PropTypes.string,
  handleBackButton: PropTypes.func.isRequired,
  currentTableData: PropTypes.array.isRequired,
  incomeStatementData: PropTypes.array.isRequired,
  balanceSheetData: PropTypes.array.isRequired,
  cashFlowData: PropTypes.array.isRequired,
  companyName: PropTypes.string.isRequired,
  generateColumns: PropTypes.func.isRequired,
  deserializeData: PropTypes.object,
  tableGroups: PropTypes.array,
  notesData: PropTypes.array.isRequired,
  companyTicker: PropTypes.string.isRequired,
  acuityid: PropTypes.string.isRequired,
  job_id: PropTypes.string.isRequired,
  country: PropTypes.string.isRequired,
  client_id: PropTypes.string.isRequired,
  company_id: PropTypes.string.isRequired,
  fs_type: PropTypes.oneOf(["IS", "CF", "BS"]).isRequired,
  industry: PropTypes.string.isRequired,
  currency: PropTypes.string.isRequired,
  currencyUnit: PropTypes.string.isRequired,
  financialsData: PropTypes.object.isRequired,
  mappedData: PropTypes.arrayOf(
    PropTypes.shape({
      rtext: PropTypes.string.isRequired,
      stext: PropTypes.string.isRequired,
      mapping_id: PropTypes.number.isRequired,
      order: PropTypes.number.isRequired
    })
  ).isRequired,
  incomeStatementMappedData: PropTypes.array.isRequired,
  balanceSheetMappedData: PropTypes.array.isRequired,
  cashFlowMappedData: PropTypes.array.isRequired,
  standardLineItemsForIncomeStatement: PropTypes.array.isRequired,
  standardLineItemsForBalanceSheet: PropTypes.array.isRequired,
  standardLineItemsForCashFlow: PropTypes.array.isRequired,
  selectedTab: PropTypes.number.isRequired,
  isMapped: PropTypes.bool.isRequired,
  setIsMapped: PropTypes.func.isRequired,
  handleResetPublish: PropTypes.func.isRequired,
  extractionType: PropTypes.string.isRequired,
  onCurrencyChange: PropTypes.func,
  onCurrencyUnitChange: PropTypes.func,
  kpiConfig: PropTypes.object.isRequired,
  areAllDropdownsSelected: PropTypes.bool.isRequired,
  companyDetails: PropTypes.object.isRequired
};

export default Header;
