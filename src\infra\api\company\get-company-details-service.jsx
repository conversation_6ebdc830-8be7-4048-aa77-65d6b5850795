import { fetcher } from "../../../general/fetcher";
import { baseUrl, serviceUrl } from "../../../constants/config";


export const GET_COMPANY_DETAILS = async (companyId) => {
  try {
    return await fetcher(`${serviceUrl}api/portfolio-company/ingestion/${companyId}`);
  } catch (error) {
    console.error("Error fetching curriencies:", error);
    throw error;
  }
};

export const GET_FUND_DETAILS = async (fundId) => {
  try {
    return await fetcher(`${serviceUrl}api/data-ingestion/fund-details/${fundId}`);
  } catch (error) {
    console.error("Error fetching curriencies:", error);
    throw error;
  }
};

export const GET_SPECIFIC_KPI_DETAILS = async (processId) => {
  try {
    return await fetcher(`${baseUrl}extraction/api/specific-kpi-document/${processId}`)
  } catch (error) {
    console.error("Error fetching curriencies:", error);
    throw error;
  }
};