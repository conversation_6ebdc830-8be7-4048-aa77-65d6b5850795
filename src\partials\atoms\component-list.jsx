import React from "react";
import { HiTrash } from "react-icons/hi2";
import { FaSave } from "react-icons/fa";
import { FiDownload } from "react-icons/fi";
import { Button } from "./button";
import { Badge } from "./badge";
import ButtonIcon from "./button/button-icon";
import { ToolTip } from "./tool-tip";
import Toggle from "./toggle/toggle";
import InputBox from "./input/input-box";
import { notify } from "../molecules/toaster/toaster";
import ButtonIconText from "./button/button-icon-text";
import CheckBox from "./check-box";
import Filter from "../molecules/filter";
import RadioButton from "./radio-button/radio";
import { ProgressBar } from "./progress-bar";
import TabsGroup from "../molecules/tabs-group";
import TabsFilledGroup from "../molecules/tabs-filled-group";
import PropTypes from "prop-types";
import WatchListTabsTablePager from "../../pages/watch-list/templates/watch-list-tabs/templates/watch-list-tabs-table/watch-list-tabs-table-pager";
const ComponentList = () => {
  return (
    <div
      data-testid="component-list"
      className="relative my-8 h-screen space-y-8 overflow-auto"
    >
      {/* Button */}
      <h2 className="text-2xl font-bold text-slate-800">Button</h2>
      <div className="flex gap-2">
        <div>
          <Button>Submit</Button>
        </div>
        <div>
          <Button intent={"secondary"}>Cancel</Button>
        </div>
        <div>
          <Button intent={"teritory"}>Cancel</Button>
        </div>
        <div>
          <Button disabled>Submit</Button>
        </div>
        <div>
          <Button intent={"secondary"} disabled>
            Cancel
          </Button>
        </div>
        <div>
          <Button intent={"teritory"} disabled>
            Cancel
          </Button>
        </div>
      </div>

      {/* Pagination */}
      <div
        className={`flex h-12 w-full items-center justify-between border-t bg-neutral-2`}
      >
        <WatchListTabsTablePager totalCount={240} />
      </div>

      {/* Bagde */}
      <h2 className="text-2xl font-bold text-slate-800">Badge</h2>
      <div className="flex gap-2">
        <Badge />
        <Badge value={2} />
        <Badge value={10} />
        <Badge value={100} />
      </div>

      {/* Button-Icon */}
      <h2 className="text-2xl font-bold text-slate-800">Button Icon</h2>
      <div className="flex gap-2">
        {/* Button-Icon-Medium */}
        <div>
          <ButtonIcon>
            <HiTrash className="h-4 w-4" />
          </ButtonIcon>
        </div>
        <div>
          <ButtonIcon intent={"secondary"}>
            <HiTrash className="h-4 w-4" />
          </ButtonIcon>
        </div>
        <div>
          <ButtonIcon intent={"teritory"}>
            <HiTrash className="h-4 w-4" />
          </ButtonIcon>
        </div>
        {/* Button-Icon-Disabled */}
        <div>
          <ButtonIcon disabled>
            <HiTrash className="h-4 w-4" />
          </ButtonIcon>
        </div>
        <div>
          <ButtonIcon intent={"secondary"} disabled>
            <HiTrash className="h-4 w-4" />
          </ButtonIcon>
        </div>
        <div>
          <ButtonIcon intent={"teritory"} disabled>
            <HiTrash className="h-4 w-4" />
          </ButtonIcon>
        </div>
        {/* Button-Icon-Small */}
        <div>
          <ButtonIcon size={"small"}>
            <HiTrash className="h-3 w-3" />
          </ButtonIcon>
        </div>
        <div>
          <ButtonIcon size={"small"} intent={"secondary"}>
            <HiTrash className="h-3 w-3" />
          </ButtonIcon>
        </div>
        <div>
          <ButtonIcon size={"small"} intent={"teritory"}>
            <HiTrash className="h-3 w-3" />
          </ButtonIcon>
        </div>

        {/* Button-Icon-Small-Disabled */}
        <div>
          <ButtonIcon size={"small"} disabled>
            <HiTrash className="h-3 w-3" />
          </ButtonIcon>
        </div>
        <div>
          <ButtonIcon size={"small"} intent={"secondary"} disabled>
            <HiTrash className="h-3 w-3" />
          </ButtonIcon>
        </div>
        <div>
          <ButtonIcon size={"small"} intent={"teritory"} disabled>
            <HiTrash className="h-3 w-3" />
          </ButtonIcon>
        </div>
      </div>
      {/* Download-Icon */}
      <h2 className="text-2xl font-bold text-slate-800">Download Icon</h2>
      <div className="flex gap-2">
        {/* Download-Icon-Medium */}
        <div>
          <ButtonIcon>
            <FiDownload className="size-4" />
          </ButtonIcon>
        </div>
        <div>
          <ButtonIcon intent={"secondary"}>
            <FiDownload className="size-4" />
          </ButtonIcon>
        </div>
        <div>
          <ButtonIcon intent={"teritory"}>
            <FiDownload className="size-4" />
          </ButtonIcon>
        </div>
        {/* Button-Icon-Disabled */}
        <div>
          <ButtonIcon disabled>
            <FiDownload className="size-4" />
          </ButtonIcon>
        </div>
        <div>
          <ButtonIcon intent={"secondary"} disabled>
            <FiDownload className="size-4" />
          </ButtonIcon>
        </div>
        <div>
          <ButtonIcon intent={"teritory"} disabled>
            <FiDownload className="size-4" />
          </ButtonIcon>
        </div>
        {/* Download-Icon-Small */}
        <div>
          <ButtonIcon size={"small"}>
            <FiDownload className="size-3" />
          </ButtonIcon>
        </div>
        <div>
          <ButtonIcon size={"small"} intent={"secondary"}>
            <FiDownload className="size-3" />
          </ButtonIcon>
        </div>
        <div>
          <ButtonIcon size={"small"} intent={"teritory"}>
            <FiDownload className="size-3" />
          </ButtonIcon>
        </div>

        {/* Download-Icon-Small-Disabled */}
        <div>
          <ButtonIcon size={"small"} disabled>
            <FiDownload className="size-3" />
          </ButtonIcon>
        </div>
        <div>
          <ButtonIcon size={"small"} intent={"secondary"} disabled>
            <FiDownload className="size-3" />
          </ButtonIcon>
        </div>
        <div>
          <ButtonIcon size={"small"} intent={"teritory"} disabled>
            <FiDownload className="size-3" />
          </ButtonIcon>
        </div>
      </div>

      {/* Button-Text */}
      <h2 className="text-2xl font-bold text-slate-800">
        Button With Text & Icon
      </h2>
      <div className="flex gap-2">
        {/* Button-Icon-Medium */}
        <div>
          <ButtonIconText>
            <FaSave className="h-4 w-4" />
            Submit
          </ButtonIconText>
        </div>
        <div>
          <ButtonIconText intent={"secondary"}>
            <FaSave className="h-4 w-4" />
            Submit
          </ButtonIconText>
        </div>
        <div>
          <ButtonIconText intent={"teritory"}>
            <FaSave className="h-4 w-4" />
            Submit
          </ButtonIconText>
        </div>
        {/* Button-Icon-Disabled */}
        <div>
          <ButtonIconText disabled>
            <FaSave className="h-4 w-4" />
            Submit
          </ButtonIconText>
        </div>
        <div>
          <ButtonIconText intent={"secondary"} disabled>
            <FaSave className="h-4 w-4" />
            Submit
          </ButtonIconText>
        </div>
        <div>
          <ButtonIconText intent={"teritory"} disabled>
            <FaSave className="h-4 w-4" />
            Submit
          </ButtonIconText>
        </div>
      </div>

      {/* Button-Text-Right */}
      <div className="flex gap-2">
        {/* Button-Icon-Medium */}
        <div>
          <ButtonIconText>
            Download
            <FaSave className="h-4 w-4" />
          </ButtonIconText>
        </div>
        <div>
          <ButtonIconText intent={"secondary"}>
            Download
            <FaSave className="h-4 w-4" />
          </ButtonIconText>
        </div>
        <div>
          <ButtonIconText intent={"teritory"}>
            Download
            <FaSave className="h-4 w-4" />
          </ButtonIconText>
        </div>
        {/* Button-Icon-Disabled */}
        <div>
          <ButtonIconText disabled>
            Download
            <FaSave className="h-4 w-4" />
          </ButtonIconText>
        </div>
        <div>
          <ButtonIconText intent={"secondary"} disabled>
            Download
            <FaSave className="h-4 w-4" />
          </ButtonIconText>
        </div>
        <div>
          <ButtonIconText intent={"teritory"} disabled>
            Download
            <FaSave className="h-4 w-4" />
          </ButtonIconText>
        </div>
      </div>
      {/* Radio Button component */}
      <h2 className="text-2xl font-bold text-slate-800">Radio Button</h2>
      <RadioButton
        options={[
          {
            value: "radio",
            label: "Radio Button"
          }
        ]}
      />
      <h2 className="text-2xl font-bold text-slate-800">Filter Component</h2>
      {/* Filter Component */}
      <div className="flex gap-44">
        {/* Filter Component */}
        <div>
          <FilterComponent />
        </div>
        <div>
          <FilterComponent />
        </div>
        <div>
          <FilterComponent />
        </div>
        {/* Button-Icon-Disabled */}
        <div>
          <FilterComponent />
        </div>
        <div>
          <FilterComponent />
        </div>
        <div>
          <FilterComponent />
        </div>
      </div>

      {/* Button-Icon-Tooltip */}
      <h2 className="text-2xl font-bold text-slate-800">Tooltip</h2>
      <div className="flex gap-2">
        {/* Button-Icon-Medium */}
        <div>
          <ButtonIcon data-tooltip-id="tool-tip-delete-top">
            <HiTrash className="h-4 w-4" />
            <ToolTip
              place={"top"}
              text={"This is delete"}
              toolTipId={"tool-tip-delete-top"}
            />
          </ButtonIcon>
        </div>
        <div>
          <ButtonIcon data-tooltip-id="tool-tip-delete-left">
            <HiTrash className="h-4 w-4" />
            <ToolTip
              place={"left"}
              text={"This is delete"}
              toolTipId={"tool-tip-delete-left"}
            />
          </ButtonIcon>
        </div>
        <div>
          <ButtonIcon data-tooltip-id="tool-tip-delete-right">
            <HiTrash className="h-4 w-4" />
            <ToolTip
              place={"right"}
              text={"This is delete"}
              toolTipId={"tool-tip-delete-right"}
            />
          </ButtonIcon>
        </div>
        <div>
          <ButtonIcon data-tooltip-id="tool-tip-delete-bottom">
            <HiTrash className="h-4 w-4" />
            <ToolTip
              place={"bottom"}
              text={"This is delete"}
              toolTipId={"tool-tip-delete-bottom"}
            />
          </ButtonIcon>
        </div>
      </div>

      {/* Toggle Button */}
      <h2 className="text-2xl font-bold text-slate-800">Toggle Button</h2>
      <div className="flex gap-2">
        <div>
          <ToggleComponent checked={false} />
        </div>
        <div>
          <ToggleComponent checked={true} />
        </div>
        <div>
          <ToggleComponent checked={false} disabled={true} />
        </div>
        <div>
          <ToggleComponent checked={true} disabled={true} />
        </div>
      </div>

      {/* Checkbox */}
      <h2 className="text-2xl font-bold text-slate-800">Checkbox</h2>
      <div className="flex gap-8">
        <div>
          <CheckBoxComponent checked={false} />
        </div>
        <div>
          <CheckBoxComponent checked={true} />
        </div>
        <div>
          <CheckBoxComponent checked={true} indeterminate={true} />
        </div>
        <div>
          <CheckBoxComponent checked={false} disabled={true} />
        </div>
        <div>
          <CheckBoxComponent checked={true} disabled={true} />
        </div>
        <div>
          <CheckBoxComponent
            checked={false}
            indeterminate={true}
            disabled={true}
          />
        </div>
      </div>

      {/* Input Box */}
      <h2 className="text-2xl font-bold text-slate-800">Input Closed</h2>
      <div className="grid grid-cols-4 gap-4">
        <div>
          <InputBox placeholder={"Enter Text"} />
        </div>
        <div>
          <InputBox
            disabled
            placeholder={"Enter Text"}
            value={"Disabled Textbox"}
          />
        </div>
        <div>
          <InputBox disabled label={"Name"} />
        </div>
        <div>
          <InputBox
            mandatory={true}
            placeholder={"Enter Text"}
            label={"Name"}
          />
        </div>
        <div>
          <InputBox error={true} placeholder={"Enter Text"} />
        </div>
        <div>
          <InputBox error={true} placeholder={"Enter Text"} label={"Name"} />
        </div>
        <div>
          <InputBox
            error={true}
            placeholder={"Enter Text"}
            label={"Error Label"}
          />
        </div>
        <div>
          <InputBox
            error={true}
            placeholder={"Enter Text"}
            label={"Error Label"}
            message={"This is required"}
          />
        </div>
      </div>

      {/* Input Box - Outlined */}
      <h2 className="text-2xl font-bold text-slate-800">Input Outlined</h2>
      <div className="grid grid-cols-4 gap-8">
        <div>
          <InputBox intent={"outline"} placeholder={"Enter Text"} />
        </div>
        <div>
          <InputBox
            intent={"outline"}
            placeholder={"Enter Text"}
            disabled
            value={"Disabled Textbox"}
          />
        </div>
        <div>
          <InputBox intent={"outline"} disabled label={"Name"} />
        </div>
        <div>
          <InputBox
            intent={"outline"}
            placeholder={"Enter Text"}
            mandatory={true}
            label={"Name"}
          />
        </div>
        <div>
          <InputBox
            intent={"outline"}
            placeholder={"Enter Text"}
            error={true}
          />
        </div>
        <div>
          <InputBox
            intent={"outline"}
            placeholder={"Enter Text"}
            error={true}
            label={"Name"}
          />
        </div>
        <div>
          <InputBox
            intent={"outline"}
            placeholder={"Enter Text"}
            error={true}
            label={"Error Label"}
          />
        </div>
        <div>
          <InputBox
            intent={"outline"}
            placeholder={"Enter Text"}
            error={true}
            message={"This is required"}
            label={"Error Label"}
          />
        </div>
      </div>

      {/* Text Area */}
      {/* <h2 className="text-2xl font-bold text-slate-800">Text Area</h2>
      <div className="grid grid-cols-4 gap-8">
        <div>
          <TextArea placeholder={'Enter Text'} />
        </div>
        <div>
          <TextArea placeholder={'Enter Text'} value={'This is sample text'} />
        </div>
        <div>
          <TextArea placeholder={'Enter Text'} value={'This is sample text'} disabled />
        </div>
        <div>
          <TextArea placeholder={'Enter Text'} value={'This is sample text'} disabled />
        </div>
      </div> */}
      {/* Tab Components */}
      <h2 className="text-2xl font-bold text-slate-800">Progress Bar</h2>
      <div className="flex gap-2">
        <ProgressBar percentage={100} />
        <ProgressBar percentage={50} />
        <ProgressBar percentage={25} />
      </div>

      {/* Tab Components */}
      <h2 className="text-2xl font-bold text-slate-800">Tabs Component</h2>
      <div>
        <TabsComponent />
      </div>

      <h2 className="text-2xl font-bold text-slate-800">
        Tabs Filled Component
      </h2>
      <div>
        <TabsFilledComponent />
      </div>

      {/* Toaster */}
      <h2 className="text-2xl font-bold text-slate-800">Toaster</h2>
      <div className="flex gap-2">
        <Button
          data-testid="component-list-success"
          onClick={() =>
            notify.success("This is a successfull toaster message")
          }
        >
          Success
        </Button>
        <Button
          data-testid="component-list-information"
          onClick={() => notify.info("This is a information toaster message")}
        >
          Information
        </Button>
        <Button
          data-testid="component-list-warning"
          onClick={() => notify.warning("This is a warning toaster message")}
        >
          Warning
        </Button>
        <Button
          data-testid="component-list-error"
          onClick={() => notify.error("This is a error toaster message")}
        >
          Error
        </Button>
      </div>
    </div>
  );
};

const TabsComponent = () => {
  const [activeTab, setActiveTab] = React.useState("tab1");

  const mockTabs = [
    {
      name: "Tab 1",
      key: "tab1",
      component: (
        <div className="flex items-center justify-center">
          Content for Tab 1
        </div>
      )
    },
    {
      name: "Tab 2",
      key: "tab2",
      component: (
        <div className="flex items-center justify-center">
          Content for Tab 2
        </div>
      )
    },
    {
      name: "Tab 3",
      key: "tab3",
      component: (
        <div className="flex items-center justify-center">
          Content for Tab 3
        </div>
      )
    }
  ];

  return (
    <div>
      <TabsGroup
        tabs={mockTabs}
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        className="custom-class"
      />
    </div>
  );
};
const TabsFilledComponent = () => {
  const [activeTab, setActiveTab] = React.useState("tab1");

  const mockTabs = [
    {
      name: "Tab 1",
      key: "tab1",
      component: (
        <div className="flex items-center justify-center">
          Content for Tab 1
        </div>
      )
    },
    {
      name: "Tab 2",
      key: "tab2",
      component: (
        <div className="flex items-center justify-center">
          Content for Tab 2
        </div>
      )
    },
    {
      name: "Tab 3",
      key: "tab3",
      component: (
        <div className="flex items-center justify-center">
          Content for Tab 3
        </div>
      )
    }
  ];

  return (
    <div>
      <TabsFilledGroup
        tabs={mockTabs}
        activeTab={activeTab}
        setActiveTab={setActiveTab}
        className="custom-class"
      />
    </div>
  );
};

const ToggleComponent = ({ checked, disabled }) => {
  const [toggle, setToggle] = React.useState(checked);
  return (
    <div>
      <Toggle
        data-testid="component-list-toggle"
        checked={toggle}
        disabled={disabled}
        onChange={() => setToggle(!toggle)}
      />
    </div>
  );
};
ToggleComponent.propTypes = {
  checked: PropTypes.bool,
  disabled: PropTypes.bool
};

const CheckBoxComponent = ({ checked, disabled, indeterminate }) => {
  const [toggle, setToggle] = React.useState(checked);
  return (
    <div>
      <CheckBox
        data-testid="component-list-check"
        checked={toggle}
        disabled={disabled}
        indeterminate={indeterminate}
        onChange={() => setToggle(!toggle)}
      />
    </div>
  );
};

CheckBoxComponent.propTypes = {
  checked: PropTypes.bool,
  disabled: PropTypes.bool,
  indeterminate: PropTypes.bool
};

const FilterComponent = () => {
  const filterTypes = {
    MULTI_SELECT: "multi-select",
    DATE_RANGE: "date-range",
    ACCORDIAN_PANEL: "accordian-panel"
  };
  const [data, setData] = React.useState([
    {
      value: "1",
      label: "Company",
      data: [
        { value: "1", label: "Apple Inc" },
        { value: "2", label: "Ijj Corp." },
        { value: "3", label: "Appliqate Inc" },
        { value: "4", label: "Asiasoft Corporation Public Company Limited" },
        { value: "5", label: "BDO Unibank Inc" },
        { value: "6", label: "BDCom Online Ltd" },
        { value: "7", label: "Bombardier Inc." },
        { value: "8", label: "BCAL Diagnostics Ltd" },
        {
          value: "9",
          label:
            "Bdbetway, Bdbetwayn, Bdbetwaysports, Bdbetway India, Bdbetway Online"
        },
        { value: "10", label: "Dividend Select 15 Corp" }
      ],
      type: filterTypes.MULTI_SELECT
    },
    {
      value: 2,
      label: "Filing Type",
      data: [
        {
          value: 1,
          label: "Financial Reports",
          data: [
            {
              label: "10-Q",
              value: "10-q"
            },
            {
              label: "10-Q/A",
              value: "10-q/a"
            }
          ]
        },
        {
          value: 2,
          label: "Financial Reports",
          data: [
            {
              label: "10A",
              value: "10A"
            },
            {
              label: "10K",
              value: "10a"
            }
          ]
        },
        {
          value: 3,
          label: "Financial Reports",
          data: [
            {
              label: "10A",
              value: "10A"
            },
            {
              label: "10K",
              value: "10a"
            }
          ]
        },
        {
          value: 4,
          label: "Financial Reports",
          data: [
            {
              label: "10A",
              value: "10A"
            },
            {
              label: "10K",
              value: "10a"
            }
          ]
        },
        {
          value: 5,
          label: "Financial Reports",
          data: [
            {
              label: "10A",
              value: "10A"
            },
            {
              label: "10K",
              value: "10a"
            }
          ]
        },
        {
          value: 6,
          label: "Financial Reports",
          data: [
            {
              label: "10A",
              value: "10A"
            },
            {
              label: "10K",
              value: "10a"
            }
          ]
        }
      ],
      type: filterTypes.ACCORDIAN_PANEL
    },
    {
      value: 3,
      label: "Period",
      data: [],
      type: filterTypes.DATE_RANGE
    },
    {
      value: 4,
      label: "Industry",
      data: [
        { value: 1, label: "test4" },
        { value: 2, label: "test15" }
      ],
      type: filterTypes.MULTI_SELECT
    }
  ]);
  return (
    <div>
      <Filter data={data} onFilter={setData} />
    </div>
  );
};

export default ComponentList;
