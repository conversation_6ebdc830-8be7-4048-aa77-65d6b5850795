import React from "react";
import { render } from "@testing-library/react";
import FullScreenLoader from "./FullScreenLoader";

describe("FullScreenLoader", () => {
  it("should render correctly when isLoading is true", () => {
    const loadingMessage = "Loading...";
    const isLoading = true;
    const { getByText } = render(
      <FullScreenLoader loadingMessage={loadingMessage} isLoading={isLoading} />
    );
    getByText(loadingMessage);
  });

  it("should not render anything when isLoading is false", () => {
    const { queryByText } = render(<FullScreenLoader isLoading={false} />);
    queryByText("Loading...");
  });
});
