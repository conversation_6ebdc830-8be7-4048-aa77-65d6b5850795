/* eslint-disable */
import React from "react";
import { useRegisterSW } from "virtual:pwa-register/react";
import { RxCross1 } from "react-icons/rx";

const ReloadPrompt = () => {
  // replaced dyanmicaly
  const reloadSW = "__RELOAD_SW__";

  const {
    offlineReady: [offlineReady, setOfflineReady],
    needRefresh: [needRefresh, setNeedRefresh],
    updateServiceWorker
  } = useRegisterSW({
    onRegisteredSW(swUrl, r) {
      if (reloadSW === "true") {
        r &&
          setInterval(() => {
            r.update();
          }, 20000 /* 20s for testing purposes */);
      }
    },
    onRegisterError(error) {}
  });

  const close = () => {
    setOfflineReady(false);
    setNeedRefresh(false);
  };

  return (
    <div data-testid={"reload-prompt"} className="z-60">
      {(offlineReady || needRefresh) && (
        <div className="fixed bottom-0 right-0 m-4 space-y-1 rounded border border-solid border-neutral-30 bg-white p-3 text-left shadow-lg">
          <div className="m-m flex justify-between gap-4 text-neutral-80">
            {offlineReady ? (
              <span>App ready to work offline.</span>
            ) : (
              <span>New version is available.</span>
            )}
            <span className="flex h-5 w-5 cursor-pointer place-items-end items-center justify-end p-1 align-middle">
              <RxCross1 onClick={() => close()} />
            </span>
          </div>
          <div className="flex gap-1">
            {needRefresh && (
              <button
                type="button"
                className="s-r flex h-8 items-center rounded-md border border-primary-78 bg-primary-78 px-4 py-2 text-white hover:bg-primary-90"
                onClick={() => updateServiceWorker(true)}
              >
                Update
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default ReloadPrompt;
