import React, { useState, useEffect } from "react";
import { FiChevronDown, FiChevronRight, FiEdit2, <PERSON><PERSON><PERSON><PERSON>, FiX } from "react-icons/fi";
import { Card, CardBody } from "@progress/kendo-react-layout";
import { Fade } from "@progress/kendo-react-animation";
import { Toggle } from "../../partials/atoms/toggle";
import { ButtonIconText } from "../../partials/atoms/button";
import { IoMdAdd } from "react-icons/io";
import { Checkbox } from "@progress/kendo-react-inputs";
import KendoGrid from "../../partials/atoms/grid/kendo-grid";
import NotesDropDown from "./NotesDropDown";
import { FinancialStatements } from "../../constants";
import ButtonIcon from "../../partials/atoms/button/button-icon";

const NotesTab = ({
  notesTables = [],
  isLoading = false,
  searchQuery = "",
  getDocumentLineItems,
  generateColumns,
  isMapped = false,
  showDropdown = false,
  setShowDropdown = () => {},
  setCurrentSelectedState = () => {},
  handleCellClick = () => {},
  updateNoteLabel = () => {}, // Function to update note label
  handleAddNote = () => {}, // Function to handle adding new notes
  onEditingStateChange = () => {} // New prop to notify parent about editing state
}) => {
  // Track if all notes are expanded
  const [expandedNotes, setExpandedNotes] = useState({});
  const [allExpanded, setAllExpanded] = useState(false);

  
  // Add state for editing note labels
  const [editingNoteIndex, setEditingNoteIndex] = useState(null);
  const [editedNoteLabel, setEditedNoteLabel] = useState("");
  // Add state to track updated labels locally
  const [updatedLabels, setUpdatedLabels] = useState({});

  // Toggle expand all notes
  const handleExpandAll = () => {
    const newExpandedState = !allExpanded;
    setAllExpanded(newExpandedState);

    const newExpandedNotes = {};
    notesTables.forEach((note, index) => {
      newExpandedNotes[index] = newExpandedState;
    });

    setExpandedNotes(newExpandedNotes);
  };

  // Toggle a single note expansion
  const handleNoteExpansion = (noteId) => {
    // Don't toggle when editing
    if (editingNoteIndex !== null) return;
    
    const newExpandedNotes = {
      ...expandedNotes,
      [noteId]: !expandedNotes[noteId]
    };

    setExpandedNotes(newExpandedNotes);

    // Check if all notes are now expanded
    const areAllExpanded = notesTables.every(
      (_, index) => newExpandedNotes[index]
    );
    setAllExpanded(areAllExpanded);
  };

  // Start editing a note label
  const startEditingNote = (index, currentLabel) => {
    setEditingNoteIndex(index);
    setEditedNoteLabel(currentLabel);
  };

  // Cancel editing
  const cancelEditing = () => {
    setEditingNoteIndex(null);
    setEditedNoteLabel("");
  };

  // Save the edited label
  const saveNoteLabel = (index) => {
    // Call the updateNoteLabel function passed from parent
    updateNoteLabel(index, editedNoteLabel);
    
    // Store updated label in local state for immediate display
    setUpdatedLabels(prev => ({
      ...prev,
      [index]: editedNoteLabel
    }));
    
    // Reset editing state
    setEditingNoteIndex(null);
    setEditedNoteLabel("");
  };

  // Handle input change for the note label
  const handleNoteLabelChange = (e) => {
    setEditedNoteLabel(e.target.value);
  };

  // Get display label - use updated label if available, otherwise use original
  const getDisplayLabel = (index, originalLabel) => {
    return updatedLabels[index] || originalLabel;
  };

  if (!notesTables || notesTables.length === 0) {
    return (
      <div className="flex items-center justify-center h-full w-full">
        <div className="text-neutral-60 text-lg">Notes is empty</div>
      </div>
    );
  }

  const processTableData = (sections) => {
    if (!sections || !sections[0]?.rows) return [];

    // This would typically call the parent component's processTableData function
    // For now, we'll assume it returns the rows as is
    return (
      sections[0]?.rows?.map((row, index) => ({
        id: row.id || `row-${index}`,
        label: row.label?.text || "",
        ...Object.fromEntries(
          (sections[0]?.columns || []).map((col) => [
            col.columnKey,
            row[col.columnKey]?.text || ""
          ])
        )
      })) || []
    );
  };

  return (
    <div className="p-4 h-full overflow-auto">
      <Card className="mb-4">
        <CardBody>
          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <div className="bg-[#E3EEFF] px-2 py-1 rounded-md text-sm font-bold mr-1">
                {notesTables.length}
              </div>
              <div className="text-lg font-medium mr-2">Notes Extracted</div>
            </div>

            <div className="flex items-center gap-4">
              <div className="flex items-center">
                <Toggle checked={allExpanded} onChange={handleExpandAll} />
                <span className="ml-2 text-[#4061C7]">Expand All</span>
              </div>
              <ButtonIconText intent={"secondary"} onClick={handleAddNote}>
                <IoMdAdd className="text-primary-78" />
                Add New Note
              </ButtonIconText>
            </div>
          </div>
        </CardBody>
      </Card>

      <div className="flex flex-col gap-3">
        {notesTables.map((tableGroup, index) => {
          console.log(`Processing table group ${index}:`, tableGroup);
          const tableResult = processTableData([
            {
              rows: tableGroup.tables[0]?.rows || [],
              columns: tableGroup.tables[0]?.columns || []
            }
          ]);
          console.log(`Table result for group ${index}:`, tableResult);

          // Filter notes data if search query exists
          const filteredTableResult = searchQuery
            ? tableResult.filter(
                (row) =>
                  row.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
                  Object.entries(row).some(
                    ([key, value]) =>
                      key.startsWith("value_") &&
                      value &&
                      String(value)
                        .toLowerCase()
                        .includes(searchQuery.toLowerCase())
                  )
              )
            : tableResult;

          const allCoa = [
            ...new Set([
              ...(getDocumentLineItems
                ? getDocumentLineItems(0, true)?.map((coa) => ({
                    statement: FinancialStatements[0],
                    lineitem: coa
                  }))
                : []), // for IS tab
              ...(getDocumentLineItems
                ? getDocumentLineItems(1, true)?.map((coa) => ({
                    statement: FinancialStatements[1],
                    lineitem: coa
                  }))
                : []), // for BS tab
              ...(getDocumentLineItems
                ? getDocumentLineItems(2, true)?.map((coa) => ({
                    statement: FinancialStatements[2],
                    lineitem: coa
                  }))
                : []) // for CF tab
            ])
          ];

          const isExpanded = expandedNotes[index] || false;
          const isEditing = editingNoteIndex === index;
          // Check if the edited label is empty to disable the save button
          const isLabelEmpty = isEditing && (!editedNoteLabel || editedNoteLabel.trim() === '');

          return (
            <Fade key={index}>
              <div className="border rounded-lg bg-white shadow-sm mb-4">
                {/* Note Header - Always visible */}
                <div
                  className="flex items-center justify-between px-4 py-3 cursor-pointer hover:bg-gray-50 rounded-t-lg border-b border-gray-200"
                  onClick={() => !isEditing && handleNoteExpansion(index)}
                >
                  <div className="flex items-center gap-3 flex-1">
                    <div className="text-gray-500">
                      {isExpanded ? (
                        <FiChevronDown size={20} />
                      ) : (
                        <FiChevronRight size={20} />
                      )}
                    </div>
                    
                    {isEditing ? (
                      <div className="flex items-center gap-2">
                        <input
                          type="text"
                          value={editedNoteLabel}
                          onChange={handleNoteLabelChange}
                          className={`border ${isLabelEmpty ? 'border-red-500' : 'border-gray-300'} rounded-md px-2 py-1 focus:outline-none focus:ring-2 ${isLabelEmpty ? 'focus:ring-red-500' : 'focus:ring-blue-500'}`}
                          onClick={(e) => e.stopPropagation()}
                          autoFocus
                          placeholder="Note label is required"
                        />
                        <ButtonIcon 
                          intent="primary"
                          onClick={(e) => {
                            e.stopPropagation();
                            saveNoteLabel(index);
                          }}
                          disabled={isLabelEmpty}
                          className={`text-white ${isLabelEmpty ? 'bg-gray-400 cursor-not-allowed' : 'bg-green-500 hover:bg-green-600'}`}
                          data-tooltip-id="save-note-label"
                          data-tooltip-content="Save note label"
                        >
                          <FiCheck size={16} />
                        </ButtonIcon>
                        <ButtonIcon
                          intent="secondary"
                          onClick={(e) => {
                            e.stopPropagation();
                            cancelEditing();
                          }}
                          className="text-white bg-red-500 hover:bg-red-600"
                          data-tooltip-id="cancel-edit"
                          data-tooltip-content="Cancel edit"
                        >
                          <FiX size={16} />
                        </ButtonIcon>
                      </div>
                    ) : (
                      <div className="flex items-center gap-2">
                        <div className="font-medium">
                          {getDisplayLabel(index, tableGroup.label)}
                        </div>
                        {!isMapped && (
                          <ButtonIcon
                            intent="secondary"
                            onClick={(e) => {
                              e.stopPropagation();
                              startEditingNote(index, getDisplayLabel(index, tableGroup.label));
                            }}
                            className="ml-2 text-blue-600 hover:text-blue-800"
                            data-tooltip-id="edit-note-label"
                            data-tooltip-content="Edit note label"
                          >
                            <FiEdit2 size={16} />
                          </ButtonIcon>
                        )}
                      </div>
                    )}
                  </div>
                  <div>
                    <NotesDropDown
                      isMapped={isMapped}
                      showDropdown={showDropdown}
                      setShowDropdown={setShowDropdown}
                      allCoa={allCoa}
                      searchQuery={searchQuery}
                    />
                  </div>
                  <div>
                    <ButtonIconText
                      disabled={isMapped}
                      data-testid="map-button"
                      className="flex items-center justify-center"
                    >
                      {/* <img
                        src={ExportIconWhite}
                        alt="Export"
                        className="w-4 h-4"
                      /> */}
                      Map
                    </ButtonIconText>
                  </div>
                </div>

                {/* Note Content - Only visible when expanded */}
                {isExpanded && (
                  <div className="border-t border-gray-200">
                    <div className="flex h-[400px]">
                      <div className="w-3/4 h-full overflow-hidden">
                        <KendoGrid
                          className="w-full h-full"
                          dataId="id"
                          dataResult={filteredTableResult}
                          resultState={{ total: filteredTableResult.length }}
                          setCurrentSelectedState={setCurrentSelectedState}
                          setDataResult={() => {}}
                          columns={generateColumns(tableGroup, index, true)}
                          isNotes={true}
                          pageable={false}
                          isLoading={isLoading}
                          onCellClick={(e) => handleCellClick({ ...e, index })}
                        />
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </Fade>
          );
        })}
      </div>
    </div>
  );
};

export default NotesTab;
