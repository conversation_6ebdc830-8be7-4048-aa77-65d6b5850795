
export const MONTHS = [
  { value: "0", text: "Jan" },
  { value: "1", text: "Feb" },
  { value: "2", text: "<PERSON>" },
  { value: "3", text: "Apr" },
  { value: "4", text: "May" },
  { value: "5", text: "Jun" },
  { value: "6", text: "Jul" },
  { value: "7", text: "Aug" },
  { value: "8", text: "Sep" },
  { value: "9", text: "Oct" },
  { value: "10", text: "Nov" },
  { value: "11", text: "Dec" }
];

/**
 * Get month text from a numeric value (0-11)
 * @param {string|number} monthValue - Month value (0-11)
 * @returns {string} - The month abbreviation or original value if not found
 */
export const getMonthText = (monthValue) => {
  if (monthValue === undefined || monthValue === null) return '';
  
  // Convert to string for comparison
  const strMonthValue = String(monthValue);
  const month = MONTHS.find(x => x.value === strMonthValue);
  
  return month?.text || monthValue;
};

export default {
  MONTHS,
  getMonthText
};
