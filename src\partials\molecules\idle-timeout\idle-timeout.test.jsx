import { cleanup, render } from "@testing-library/react";
import IdleTimeout from "./idle-timeout";
import TestAppRenderer from "../../../infra/test-utils/test-app-renderer";
import { createMocks } from "react-idle-timer";
import { MessageChannel } from "worker_threads";

jest.mock("react-oidc-context", () => ({
  useAuth: jest.fn()
}));

jest.useFakeTimers();

describe("Component IdleTimeout render", () => {
  beforeAll(() => {
    createMocks();
    // @ts-ignore
    global.MessageChannel = MessageChannel;
  });
  afterAll(cleanup);
  it("should render IdleTimeout without value", async () => {
    render(
      <TestAppRenderer>
        <IdleTimeout />
      </TestAppRenderer>
    );
    jest.advanceTimersByTime(500);
  });
});
