// jest-dom adds custom jest matchers for asserting on DOM nodes.
// allows you to do things like:
// expect(element).toHaveTextContent(/react/i)
// learn more: https://github.com/testing-library/jest-dom
import "@testing-library/jest-dom";
import "jest-extended";
//const {defaults} = require('jest-config');

module.exports = {
  testEnvironmet: "jsdom",
  setupFilesAfterEnv: ["./src/setupTests.js"]
  // globals: {
  //    ...defaults.globals,
  // }
};
