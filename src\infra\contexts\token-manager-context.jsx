import { useEffect } from "react";
import PropTypes from "prop-types";
import { useAuth } from "react-oidc-context";
import { tokenURL } from "../api/token-manager-service";
import request from "../../general/request";
import useInterval from "../hooks/useInterval";
import { create } from "zustand";

const tokenManagerStore = (set) => ({
  logOut: false,
  setLogOut: () => set(() => ({ logOut: true }))
});

const useTokenManagerStore = create(tokenManagerStore);

export { useTokenManagerStore };

const TokenManagerContext = ({ children }) => {
  const auth = useAuth();

  const { logOut } = useTokenManagerStore((state) => state);

  useEffect(() => {
    if (logOut) auth.signoutRedirect();
  }, [logOut]);

  const isTokenExist = async () => {
    if (!auth.user?.expires_at) return;

    const expiresAt = auth.user.expires_at * 1000; // Convert to milliseconds
    const currentTime = Date.now();
    const timeToExpire = expiresAt - currentTime;
    const thirtySecondsInMs = 30 * 1000;

    // Skip token check if token is about to expire in less than 30 seconds
    if (timeToExpire <= thirtySecondsInMs) {
      return;
    }
    const { data } = await request.get(tokenURL);
    if (!data) auth.signoutRedirect();
  };

  useInterval(isTokenExist, 20000);

  useEffect(() => {
    const saveToken = async () => {
      if (!auth.user?.access_token) return;
      await request.post(tokenURL);
    };

    saveToken();
  }, [auth.user?.access_token]);

  useEffect(() => {
    const getTabCount = () => {
      return parseInt(localStorage.getItem("tabCount") || "0", 10);
    };

    const incrementTabCount = () => {
      localStorage.setItem("tabCount", getTabCount() + 1);
    };

    const decrementTabCount = () => {
      localStorage.setItem("tabCount", Math.max(getTabCount() - 1, 0));
    };

    if (sessionStorage.getItem("pageAccessedByReload") === "true")
      sessionStorage.removeItem("pageAccessedByReload");
    else incrementTabCount();

    const handleBeforeUnload = () => {
      if (!auth.isAuthenticated) return;

      const navigationEntries = performance.getEntriesByType("navigation");
      const pageAccessedByReload =
        navigationEntries.length > 0 && navigationEntries[0].type === "reload";

      sessionStorage.setItem("pageAccessedByReload", pageAccessedByReload);

      if (!pageAccessedByReload) decrementTabCount();

      if (getTabCount() === 0) {
        localStorage.removeItem("tabCount");
        auth.signoutSilent();
      }
    };

    window.addEventListener("beforeunload", handleBeforeUnload);
    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload);
      //decrementTabCount();
    };
  }, [auth.isAuthenticated]);

  return { ...children };
};

TokenManagerContext.propTypes = {
  children: PropTypes.element
};

export default TokenManagerContext;
