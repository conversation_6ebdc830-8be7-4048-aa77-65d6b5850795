import { create } from "zustand";
import { devtools } from "zustand/middleware";
import { formatDate } from "../../../general/utils";

const documentViewerStore = (set) => ({
  initState: () => {
    set(() => ({
      previewFilingsMetaData: {},
      previewFilingsDocURL: "",
      previewFilingsDocType: "",
      previewFilingsDocId: "",
      selectedText: "",
      fileUrl: "",
      fileExtension: "",
      highlightedMetaData: {},
      showHighLightPopOver: false,
      showTablePopOver: false,
      isPreviewOpen: false,
      activeScreen: ""
    }));
  },
  FILE_TYPES: ["html", "htm", "txt", "xml", "paper", "pdf"],
  NEW_TAB_FILE_TYPES: ["html", "htm", "txt", "xml", "pdf"],
  XML_TYPES: ["html", "htm", "xml", "paper", "text/markdown"],
  MARKDOWN_TYPES: ["md"],
  PDF_TYPES: ["pdf", "application/pdf"],
  PDF_TYPE: "pdf",
  MARKDOWN_TYPE: "md",
  PLAIN_TEXT_TYPE: "txt",
  EXCEL_TYPES: ["xls", "xlsx"],
  PPT_TYPES: ["ppt", "pptx"],
  isPreviewOpen: false,
  setPreviewOpen: () =>
    set(() => ({ isPreviewOpen: true, previewFilingsId: "" })),
  setPreviewClose: () =>
    set(() => ({ isPreviewOpen: false, previewFilingsId: "" })),
  previewFilingsMetaData: {},
  previewFilingsId: "",
  setPreviewFilingsId: (previewFilingsId) => set({ previewFilingsId }),
  previewFilingsDocURL: "",
  previewFilingsDocType: "",
  previewFilingsDocId: "",
  selectedText: "",
  setSelectedText: (selectedText) => set({ selectedText }),
  setPreviewFilingsMetaData: (filingsMetaData) => {
    if (filingsMetaData) {
      const { doc, id, filingType, docId } = filingsMetaData;
      set(() => ({
        previewFilingsMetaData: filingsMetaData,
        previewFilingsDocURL: doc,
        previewFilingsId: id,
        previewFilingsDocType: filingType,
        previewFilingsDocId: docId
      }));
    }
  },
  fileUrl: "",
  fileExtension: "",
  setFileExtension: (fileExtension) => set({ fileExtension }),
  setFileUrl: (fileUrl) => set({ fileUrl }),
  highlightedMetaData: {},
  setHighlightedMetaData: (data) => set({ highlightedMetaData: data }),
  showHighLightPopOver: false,
  setShowHighLightPopOver: (showHighLightPopOver) => {
    if (showHighLightPopOver)
      useDocumentViewerStore.getState().setShowTablePopOver(false);
    set({ showHighLightPopOver });
  },
  showTablePopOver: false,
  setShowTablePopOver: (showTablePopOver) => {
    if (showTablePopOver)
      useDocumentViewerStore.getState().setShowHighLightPopOver(false);
    set({ showTablePopOver });
  },
  showHighLightDeletePopOver: false,
  setShowHighLightDeletePopOver: (showHighLightDeletePopOver) => {
    if (showHighLightDeletePopOver)
      useDocumentViewerStore.getState().setShowHighLightDeletePopOver(false);
    set({ showHighLightDeletePopOver });
  },
  getFileExtension: (fileUrl) => {
    const { PDF_TYPE } = useDocumentViewerStore.getState();
    if (!fileUrl) return "";
    let fileExtension = new URL(fileUrl).host.endsWith("amazonaws.com")
      ? PDF_TYPE
      : fileUrl.split(".").pop().toLowerCase();
    return fileExtension;
  },
  getFileExtensionV2: (url) => {
    if (!url) return "";
    const urlObj = new URL(url);
    if (urlObj.host.endsWith("sec.gov")) {
      return url.split(".").pop().toLowerCase();
    } else {
      const contentType = urlObj.searchParams.get("response-content-type");
      return contentType ? decodeURIComponent(contentType) : null;
    }
  },
  getPreviewMetaDataBody: () => {
    const {
      acuityId,
      acuitySecurityId,
      ticker,
      companyName,
      id,
      doc,
      docId,
      filingType,
      filingDate
    } = useDocumentViewerStore.getState().previewFilingsMetaData;

    const fileDate = formatDate(new Date(filingDate), "yyyy-MM-dd");

    const { highlightedMetaData, selectedText } =
      useDocumentViewerStore.getState();

    const body = {
      acuityId: acuityId,
      acuitySecurityId: acuitySecurityId,
      tickerName: ticker,
      companyName: companyName,
      filingId: id,
      url: doc ? doc : "",
      docId: docId,
      type: filingType,
      filingDate: fileDate,
      text: selectedText,
      metaData: JSON.stringify(highlightedMetaData)
    };

    return body;
  },
  validatePreviewMetaDataBody: (body) => {
    if (!body) return false;
    const requiredFields = [
      "acuityId",
      "acuitySecurityId",
      "tickerName",
      "companyName",
      "filingId",
      "type",
      "filingDate",
      "text",
      "metaData"
    ];

    for (const field of requiredFields) {
      if (
        body[field] === null ||
        body[field] === undefined ||
        body[field] === "" ||
        body[field] === "{}" ||
        body[field] === "[]"
      ) {
        return false;
      }
    }

    if (typeof body.url === null || typeof body.docId === null) {
      return true;
    }

    return true;
  },
  scrollIntoTarget: (container, element) => {
    if (!element) return;
    const getElementPosition = (element) => {
      const rect = element?.getBoundingClientRect();
      const scrollTop = document.documentElement.scrollTop;
      const scrollLeft = document.documentElement.scrollLeft;

      return {
        top: rect.top + scrollTop,
        left: rect.left + scrollLeft,
        bottom: rect.bottom + scrollTop,
        right: rect.right + scrollLeft
      };
    };
    const position = getElementPosition(element);
    container.scroll({
      top: position.top,
      behavior: "smooth"
    });
  },
  previewConfig: {
    watchlist: {
      docHeight: "h-[calc(100vh-8rem)]",
      highlightPopover: true,
      screen: "watchlist"
    },
    myHighlights: {
      docHeight: "h-[calc(100vh-7rem)]",
      highlightPopover: true,
      screen: "my-highlights"
    },
    playGround: {
      docHeight: "h-[calc(100vh-15rem)]",
      highlightPopover: false,
      screen: "play-ground"
    },
    screener: {
      docHeight: "h-[calc(100vh-15rem)]",
      highlightPopover: true,
      screen: "screener"
    }
  },
  activePreviewConfig: {},
  setActivePreviewConfig: (data) => set(() => ({ activePreviewConfig: data }))
});

//Configure additional middlewares(Optional)
const useDocumentViewerStore = create(devtools(documentViewerStore));

export default useDocumentViewerStore;
