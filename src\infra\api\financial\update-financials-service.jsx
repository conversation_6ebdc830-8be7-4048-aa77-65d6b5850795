import { put,post } from "../../../general/fetcher";
import { baseUrl,serviceUrl } from "../../../constants/config";

const CONTROLLER = "financials";
const API_ROUTE = "extraction/api";
const hostUrl = baseUrl.endsWith('/') ? baseUrl : `${baseUrl}/`;
  
export const UPDATE_FINANCIALS = `${baseUrl}/financials/${CONTROLLER}`;
// New endpoint for ExtractController's update-financials
export const UPDATE_FINANCIALS_API = `${hostUrl}${API_ROUTE}/update-financials`;

export const UPDATE_FINANCIALS_SERVICE = async (processId, financialData) => {
    const url = `${UPDATE_FINANCIALS}?Id=${processId}`;
    const response = await put(url, financialData);
    return response;
};
// New service function using the endpoint in ExtractController
export const UPDATE_FINANCIALS_API_SERVICE = async (processId, financialData) => {
    // Make sure processId is not empty
    if (!processId) {
        throw new Error("Process ID cannot be empty");
    }
    
    // Construct final URL with the processId in the path
    const url = `${UPDATE_FINANCIALS_API}/${processId}`;
    
    
    try {
        const response = await post(url, financialData);
        return response;
    } catch (error) {
        console.error("API call failed:", error);
        throw error;
    }
};

// New service function using the endpoint in ExtractController
export const UPDATE_SPECIFIC_KPI_API_SERVICE = async (processId, financialData) => {
    // Make sure processId is not empty
    if (!processId) {
        throw new Error("Process ID cannot be empty");
    }
    
   const url = `${hostUrl}${API_ROUTE}/update-specific-kpi-document/${processId}`;
    try {
        const response = await post(url, financialData);
        return response;
    } catch (error) {
        console.error("API call failed:", error);
        throw error;
    }
};
export const PUBLISH_SPECIFIC_KPI_API_SERVICE = async (processId, financialData) => {
    // Make sure processId is not empty
    if (!processId) {
        throw new Error("Process ID cannot be empty");
    }
    
   const url = `${hostUrl}${API_ROUTE}/update-specific-kpi/${processId}`;
    try {
        const response = await post(url, financialData);
        return response;
    } catch (error) {
        console.error("API call failed:", error);
        throw error;
    }
};