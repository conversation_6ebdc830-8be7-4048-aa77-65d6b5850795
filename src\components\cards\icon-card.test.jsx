/**
 * @jest-environment jsdom
 */
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import IconCard from './icon-card';
import { FiFile } from 'react-icons/fi';

describe('IconCard Component', () => {
  // Test props for React Icon
  const defaultProps = {
    icon: FiFile,
    header: 'Document Title',
    subtitle: 'Document Description',
    onClick: jest.fn()
  };

  // Test props for String Icon (image path)
  const imageIconProps = {
    icon: '/path/to/icon.png',
    header: 'Image Icon',
    subtitle: 'This uses an image as icon',
    onClick: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders with React icon correctly', () => {
    render(<IconCard {...defaultProps} />);
    
    expect(screen.getByText('Document Title')).toBeInTheDocument();
    expect(screen.getByText('Document Description')).toBeInTheDocument();
  });

  test('renders with image icon correctly', () => {
    render(<IconCard {...imageIconProps} />);
    
    expect(screen.getByText('Image Icon')).toBeInTheDocument();
    expect(screen.getByText('This uses an image as icon')).toBeInTheDocument();
    
    const imgElement = screen.getByAltText('icon');
    expect(imgElement).toBeInTheDocument();
    expect(imgElement).toHaveAttribute('src', '/path/to/icon.png');
  });

  test('calls onClick handler when clicked', () => {
    render(<IconCard {...defaultProps} />);
    
    const card = screen.getByText('Document Title').closest('div');
    fireEvent.click(card);
    
    expect(defaultProps.onClick).toHaveBeenCalledTimes(1);
  });

  test('does not call onClick when disabled', () => {
    render(<IconCard {...defaultProps} disabled />);
    
    const card = screen.getByText('Document Title').closest('div');
    fireEvent.click(card);
    
    expect(defaultProps.onClick).not.toHaveBeenCalled();
  });

  test('applies correct styling when enabled', () => {
    render(<IconCard {...defaultProps} />);
    
    const card = screen.getByText('Document Title').closest('div');
    expect(card).toHaveClass('hover:bg-primary-35');
    expect(card).toHaveClass('active:bg-primary-40');
    expect(card).toHaveClass('cursor-pointer');
    expect(card).not.toHaveClass('bg-zinc-50');
    expect(card).not.toHaveClass('cursor-not-allowed');
  });

  test('applies correct styling when disabled', () => {
    render(<IconCard {...defaultProps} disabled />);
    
    const card = screen.getByText('Document Title').closest('div');
    expect(card).toHaveClass('bg-zinc-50');
    expect(card).toHaveClass('cursor-not-allowed');
    expect(card).not.toHaveClass('hover:bg-primary-35');
  });

  test('works without onClick handler', () => {
    // Test that the component works when no onClick handler is provided
    const propsWithoutClick = {
      ...defaultProps,
      onClick: undefined
    };
    
    render(<IconCard {...propsWithoutClick} />);
    
    // This should not throw an error
    const card = screen.getByText('Document Title').closest('div');
    fireEvent.click(card);
    
    // Test passes if no error is thrown
  });

  test('prevents default behavior on click', () => {
    render(<IconCard {...defaultProps} />);
    
    const card = screen.getByText('Document Title').closest('div');
    const mockEvent = {
      preventDefault: jest.fn()
    };
    
    // Manually call the onClick handler of the card with our mock event
    fireEvent.click(card, mockEvent);
    
    // Check that preventDefault was called
    expect(mockEvent.preventDefault).toHaveBeenCalled;
  });
}); 