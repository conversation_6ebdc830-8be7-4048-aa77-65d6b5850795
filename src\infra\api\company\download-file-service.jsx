import { post } from "../../../general/fetcher";

const CONTROLLER = "company";
export const DOWNLOAD_FILE_URL = 'extraction/api/download-file';


export const DOWNLOAD_FILE_SERVICE = ({ fileKey }) => {
  // Extract everything after the bucket name (after the third forward slash)
  // This handles different environment bucket names like s3://beat-foliosure-vault-prod-trial/
  const cleanedKey = fileKey.includes('s3://') 
    ? fileKey.replace(/^s3:\/\/[^/]+\//, '') // Removes everything up to and including first / after bucket name
    : fileKey;

  const body = {
    key: cleanedKey
  };

  return post(DOWNLOAD_FILE_URL, body);
};