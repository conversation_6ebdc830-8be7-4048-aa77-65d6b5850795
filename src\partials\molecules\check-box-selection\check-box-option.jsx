import React from "react";
import CheckBox from "../../atoms/check-box";
import PropTypes from "prop-types";

const CheckBoxOption = ({ label, onChange, testId, checked }) => {
  return (
    <div className="body-r flex h-10 items-center gap-3 border-b px-6 py-2 text-neutral-80">
      <CheckBox
        checked={checked}
        testId={testId}
        onChange={(e) => onChange(e)}
      />
      <span>{label}</span>
    </div>
  );
};

export default CheckBoxOption;

CheckBoxOption.propTypes = {
  label: PropTypes.string.isRequired,
  checked: PropTypes.bool,
  testId: PropTypes.string,
  onChange: PropTypes.func.isRequired
};
