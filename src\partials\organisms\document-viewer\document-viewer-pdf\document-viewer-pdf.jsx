import React, { useState, useEffect, useRef } from "react";
import * as pdfjsLib from "pdfjs-dist";
import PropTypes from "prop-types";
import {
  PDFViewer,
  EventBus,
  PDFLinkService,
  PDFFindController
} from "pdfjs-dist/web/pdf_viewer.mjs";
import "pdfjs-dist/web/pdf_viewer.css";
import "./document-viewer-pdf.css";
import DocumentViewerPDFToolBar from "./document-viewer-pdf-toolbar";
import Mark from "mark.js";
import DocumentViewerPopOverHighLight from "../document-viewer-pop-over-highlight";
import useDocumentViewerStore from "../document-viewer.store";
import DocumentViewerLoading from "../document-viewer-loading";
import DocumentViewerError from "../document-viewer-error";
import DocumentViewerPopOverDeleteHighLight from "../document-viewer-pop-over-delete-highlight";

pdfjsLib.GlobalWorkerOptions.workerSrc = new URL(
  "pdfjs-dist/build/pdf.worker.min.mjs",
  import.meta.url
).toString();

const markJsOptions = {
  element: "mark",
  className: "",
  exclude: [],
  separateWordSearch: false,
  accuracy: "partially",
  diacritics: true,
  synonyms: {},
  iframes: false,
  iframesTimeout: 5000,
  acrossElements: true,
  caseSensitive: false,
  ignoreJoiners: false,
  ignorePunctuation: [],
  wildcards: "disabled",
  each: function (node) {}
};

const DocumentViewerPDF = ({ metaData, readOnly, fileUrl }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pdfData, setPdfData] = useState(null);

  useEffect(() => {
    const loadPDF = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const loadingTask = pdfjsLib.getDocument({
          url: fileUrl,
          cMapUrl: "https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/cmaps/",
          cMapPacked: true
        });

        const pdf = await loadingTask.promise;
        setPdfData(pdf);
        setIsLoading(false);
      } catch (error) {
        console.error("Error loading PDF:", error);
        setError("Failed to load PDF");
        setIsLoading(false);
      }
    };

    if (fileUrl) {
      loadPDF();
    }
  }, [fileUrl]);

  return (
    <div className="relative">
      {isLoading && (
        <div className="h-[calc(100vh-10rem)] w-full">
          <DocumentViewerLoading />
        </div>
      )}
      {!isLoading && pdfData && (
        <DocumentViewerPdfRender
          metaData={metaData}
          readOnly={readOnly}
          pdfData={pdfData}
        />
      )}
      {error && <DocumentViewerError />}
    </div>
  );
};

const DocumentViewerPdfRender = ({ metaData, readOnly, pdfData }) => {
  const {
    setHighlightedMetaData,
    setShowHighLightPopOver,
    showHighLightPopOver,
    setSelectedText,
    setShowHighLightDeletePopOver,
    showHighLightDeletePopOver,
    activePreviewConfig
  } = useDocumentViewerStore((state) => state);

  const { docHeight, highlightPopover } = activePreviewConfig;

  const [pdfDoc, setPdfDoc] = useState(null);
  const [pdfDocViewer, setPdfDocViewer] = useState(null);
  const [isPdfLoading, setIsPdfLoading] = useState(true);
  const [totalNumberOfPages, setTotalNumberOfPages] = useState(0);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState("");
  const [errorReadingPdf, setErrorReadingPdf] = useState(false);

  const container = useRef();
  const viewer = useRef();

  useEffect(() => {
    !showHighLightPopOver && clearHighlights();
  }, [showHighLightPopOver]);

  // (Optionally) enable event handler for pdf.
  const eventBus = new EventBus();

  eventBus.on("pagechanging", (e) => {
    setCurrentPage(e.pageNumber);
  });

  eventBus.on("pagerendered", function (evt) {});

  // (Optionally) enable hyperlinks within PDF files.
  const pdfLinkService = new PDFLinkService({
    eventBus
  });
  // (Optionally) enable find controller.
  const pdfFindController = new PDFFindController({
    eventBus,
    linkService: pdfLinkService,
    highlightMatches: true
  });

  useEffect(() => {
    if (pdfData) {
      loadPdfViewer(pdfData);
    }
  }, [pdfData]);

  //Get the PDF from api and convert it to base64 and set it to the data from pdfjsLib.
  const loadPdfViewer = async (pdf) => {
    setIsPdfLoading(true);
    if (pdfDoc) {
      pdfDoc.destroy();
      setPdfDoc(null);
      setPdfDocViewer(null);
    }
    let pdf64BaseData = atob(pdf);
    if (pdf64BaseData) {
      let loadingTask = pdfjsLib.getDocument({ data: pdf64BaseData });
      await loadingTask.promise.then(
        (pdf) => {
          setPdfDoc(pdf);
          setTotalNumberOfPages(pdf.numPages);
        },
        (reason) => {
          setErrorReadingPdf(true);
          setIsPdfLoading(false);
        }
      );
    }
  };

  useEffect(() => {
    if (pdfDoc) injectPDfToViewer();
  }, [pdfDoc]);

  //Initializes the Pdf data to the viewer and also the required services
  const injectPDfToViewer = () => {
    const pdfViewer = new PDFViewer({
      container: container.current,
      viewer: viewer.current,
      eventBus,
      linkService: pdfLinkService,
      findController: pdfFindController
    });
    pdfLinkService.setViewer(pdfViewer);
    pdfViewer.setDocument(pdfDoc);
    pdfLinkService.setDocument(pdfDoc, null);
    setPdfDocViewer(pdfViewer);

    eventBus.on("pagesloaded", function () {
      pdfViewer.currentScaleValue = "page-width";
      setIsPdfLoading(false);
    });
  };

  //using markjs for highlight
  useEffect(() => {
    let markInstance = new Mark(viewer.current);
    markInstance.unmark(markJsOptions);
    if (searchQuery.length > 0) markInstance.mark(searchQuery, markJsOptions);
    else markInstance.unmark(markJsOptions);
  }, [searchQuery, currentPage]);

  //scroll to the specified pagenumber
  const scrollToPageNumber = (pageNumber) => {
    pdfDocViewer.currentPageNumber = pageNumber;
  };

  const handleMouseUpEvent = () => {
    if (readOnly) return;
    highlightSelection();
  };

  const highlightSelection = () => {
    const selection = window.getSelection();

    clearHighlights();

    const selectedText = selection.toString().trim();
    setSelectedText(selectedText);

    if (!selectedText) {
      clearAllStatus();
      return;
    }

    const pageNumber = pdfDocViewer.currentPageNumber || 1;

    const coords = getHighlightCoords();
    const highlight = {
      text: selectedText,
      page: pageNumber,
      coords: coords
    };

    //dont change the metaData structure without discussion.
    const metaData = {
      version: "1.0",
      type: "pdf",
      category: "highlight",
      data: highlight
    };

    setHighlightedMetaData(metaData);
    setShowHighLightPopOver(true);
  };

  const getHighlightCoords = () => {
    let pageIndex = pdfDocViewer.currentPageNumber;
    let page = pdfDocViewer._pages[pageIndex - 1];
    let pageRect = document
      .querySelector(`[data-page-number="${pageIndex}"]`)
      .getElementsByClassName("canvasWrapper")[0]
      .getClientRects()[0];
    let selectionRects = window.getSelection().getRangeAt(0).getClientRects();
    let viewport = page.viewport;
    let selectionRectsList = Object.values(selectionRects);
    let coords = selectionRectsList.map((r) => {
      return viewport
        .convertToPdfPoint(r.left - pageRect.x, r.top - pageRect.y)
        .concat(
          viewport.convertToPdfPoint(
            r.right - pageRect.x,
            r.bottom - pageRect.y
          )
        );
    });
    return coords;
  };

  useEffect(() => {
    if (isPdfLoading) return;
    if (metaData) {
      scrollToPageNumber(metaData.data.page);
      setTimeout(() => {
        if (metaData.category === "highlight_datalake")
          drawHighlightDataLake(metaData.data);
        else drawHighlight(metaData.data);
      }, 1000);
    }
  }, [isPdfLoading, metaData]);

  const drawHighlight = (highlight) => {
    clearHighlights();
    let page = pdfDocViewer.getPageView(highlight.page - 1);
    let pageElement = document.querySelector(
      `[data-page-number="${highlight.page}"]`
    );
    let viewport = page.viewport;
    let firstEl = null;

    highlight.coords.forEach((rect, index) => {
      let bounds = viewport.convertToViewportRectangle(rect);
      let x1 = Math.min(bounds[0], bounds[2]);
      let y1 = Math.min(bounds[1], bounds[3]);
      let width = Math.abs(bounds[0] - bounds[2]);
      let hight = Math.abs(bounds[1] - bounds[3]);
      let el = createRectDiv([x1, y1, width, hight]);
      if (index === 0) firstEl = el;
      pageElement.appendChild(el);
    });

    if (!firstEl) return;

    const elTopPosition =
      firstEl.getBoundingClientRect().top - window.innerHeight / 3;

    pdfDocViewer.container.scrollBy({
      top: elTopPosition
    });
  };

  const drawHighlightDataLake = (highlight) => {
    clearHighlights();
    let page = pdfDocViewer.getPageView(highlight.page - 1);
    let pageElement = document.querySelector(
      `[data-page-number="${highlight.page}"]`
    );
    let viewport = page.viewport;
    let firstEl = null;

    let bounds = viewport.convertToViewportRectangle(highlight.coords);
    let x1 = Math.min(bounds[0], bounds[2]);
    let y1 = Math.min(bounds[1], bounds[3]);
    let width = Math.abs(bounds[0] - bounds[2]);
    let hight = Math.abs(bounds[1] - bounds[3]);
    let el = createRectDiv([x1, y1, width, hight]);
    pageElement.appendChild(el);
    firstEl = el;

    if (!firstEl) return;

    const elTopPosition =
      firstEl.getBoundingClientRect().top - window.innerHeight / 3;

    pdfDocViewer.container.scrollBy({
      top: elTopPosition
    });
  };

  const createRectDiv = (boundBox) => {
    let el = document.createElement("div");
    el.setAttribute("class", "pdf__highlight");
    el.style.position = "absolute";
    el.style.backgroundColor = "#faf200";
    el.style.opacity = "0.3";
    el.style.left = boundBox[0] + "px";
    el.style.top = boundBox[1] + "px";
    el.style.width = boundBox[2] + "px";
    el.style.height = boundBox[3] + "px";
    return el;
  };

  useEffect(() => {
    setShowHighLightDeletePopOver(readOnly);
  }, [readOnly]);

  const clearAllStatus = () => {
    setShowHighLightPopOver(false);
    clearHighlights();
  };

  const clearHighlights = () => {
    const highlightDivs =
      container?.current?.querySelectorAll(".pdf__highlight");
    if (highlightDivs) {
      highlightDivs.forEach((highlightDiv) => {
        highlightDiv.remove();
      });
    }
  };

  if (errorReadingPdf) return <DocumentViewerError />;

  return (
    <div className="relative">
      {isPdfLoading && (
        <div className="h-[calc(100vh-10rem)] w-full">
          <DocumentViewerLoading />
        </div>
      )}
      {!isPdfLoading && (
        <DocumentViewerPDFToolBar
          setScrollToPage={scrollToPageNumber}
          readOnly={readOnly}
          currentPage={currentPage}
          pageCount={totalNumberOfPages}
          searchQuery={searchQuery}
          setSearchQuery={setSearchQuery}
        />
      )}
      <div className={`relative ${docHeight} w-full`}>
        <div
          ref={container}
          onMouseUp={handleMouseUpEvent}
          id="document-viewer-pdf-container"
          className={`absolute w-full ${docHeight} overflow-auto`}
        >
          <div
            ref={viewer}
            id="document-viewer-pdf-viewer"
            className="pdfViewer w-full"
          />
          Menu
        </div>
        <DocumentViewerPopOverHighLight
          show={showHighLightPopOver}
          setShow={setShowHighLightPopOver}
        />
        {highlightPopover && (
          <DocumentViewerPopOverDeleteHighLight
            show={showHighLightDeletePopOver}
            setShow={setShowHighLightDeletePopOver}
          />
        )}
      </div>
    </div>
  );
};

DocumentViewerPDF.propTypes = {
  metaData: PropTypes.object,
  readOnly: PropTypes.bool,
  fileUrl: PropTypes.string.isRequired
};

DocumentViewerPdfRender.propTypes = {
  metaData: PropTypes.object,
  readOnly: PropTypes.bool,
  pdfData: PropTypes.string
};

export default DocumentViewerPDF;
