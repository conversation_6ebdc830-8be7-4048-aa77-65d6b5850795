/* eslint-disable */
export const PWAConfig = {
  selfDestroying: true,
  registerType: "autoUpdate",
  devOptions: {
    enabled: false
  },
  filename: "beat-acuityone-spreading-ui.js",
  minify: false,
  injectManifest: {
    minify: true,
    sourcemap: false,
    enableWorkboxModulesLogs: false
  },
  workbox: {
    globPatterns: ["**/*.{html,css,js,svg,png,woff,woff2}"],
    maximumFileSizeToCacheInBytes: 5 * 1024 * 1024, // 5 MB
    cleanupOutdatedCaches: true,
    clientsClaim: true,
    skipWaiting: false,
    sourcemap: false,
    runtimeCaching: []
  }
};
