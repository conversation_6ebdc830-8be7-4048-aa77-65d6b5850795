import React, { useEffect } from "react";
import PropTypes from "prop-types";

const CheckBox = ({
  checked,
  testId,
  onChange,
  disabled = false,
  indeterminate = false,
  value = "",
  ...props
}) => {
  const checkboxRef = React.useRef();

  useEffect(() => {
    if (checkboxRef.current) {
      checkboxRef.current.indeterminate = indeterminate;
    }
  }, [indeterminate]);

  return (
    <div className="caption-r flex items-center">
      <input
        data-testid={testId}
        className={`h-4 w-4 appearance-none rounded border-neutral-60 accent-primary-78 transition duration-300 checked:accent-primary-78 indeterminate:accent-primary-78 hover:border-primary-78 focus:outline-none focus:ring-0 focus:ring-offset-0 focus-visible:outline-none enabled:hover:bg-primary-40 enabled:checked:hover:bg-primary-90 enabled:indeterminate:hover:bg-primary-90 disabled:border-neutral-20 disabled:bg-neutral-5 disabled:accent-neutral-30 disabled:checked:border-none disabled:checked:bg-neutral-10 indeterminate:disabled:border-none indeterminate:disabled:bg-neutral-10`}
        type="checkbox"
        value={value ?? ""}
        ref={checkboxRef}
        checked={checked}
        onChange={onChange}
        disabled={disabled ? true : ""}
        {...props}
      />
      <span className="sr-only">Checkbox</span>
    </div>
  );
};

CheckBox.propTypes = {
  checked: PropTypes.bool,
  testId: PropTypes.string,
  onChange: PropTypes.func,
  disabled: PropTypes.bool,
  value: PropTypes.any,
  indeterminate: PropTypes.bool,
  props: PropTypes.any
};

export default CheckBox;
