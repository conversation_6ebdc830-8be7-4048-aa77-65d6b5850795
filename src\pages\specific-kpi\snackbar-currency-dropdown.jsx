import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import { Menu, MenuButton, MenuItem, MenuItems } from "@headlessui/react";
import { HiChevronDown } from "react-icons/hi2";

// Custom upward-opening currency dropdown for the snackbar
const SnackbarCurrencyDropdown = ({ currency, currencyList, onCurrencyChange }) => {
    const [selectedCurrency, setSelectedCurrency] = useState(currency);
    const [searchTerm, setSearchTerm] = useState("");
    
    useEffect(() => {
        // If currency is already a valid currency code, use it directly
        if (currency && typeof currency === 'string' && currency !== "Select Currency") {
            const matchingCurrency = currencyList?.find(
                (opt) => opt.currencyCode === currency
            );
            
            if (matchingCurrency) {
                setSelectedCurrency(currency);
            } else {
                setSelectedCurrency("Select Currency");
            }
        } else {
            setSelectedCurrency("Select Currency");
        }
    }, [currencyList, currency]);
    
    const handleChange = (selectedValue) => {
        // Update local state
        setSelectedCurrency(selectedValue?.currencyCode || "Select Currency");
        setSearchTerm("");
        
        // Call the parent's onChange handler with the selected value
        if (onCurrencyChange) {
            onCurrencyChange(selectedValue);
        }
    };
    
    // Filter currency items based on search term
    const filteredCurrencyItems = searchTerm
        ? currencyList?.filter(item => 
            item?.currencyCode?.toLowerCase().includes(searchTerm.toLowerCase()))
        : currencyList;
    
    return (
        <Menu as="div" className="relative">
            <div>
                <MenuButton
                    className="h-8 w-[135px] body-r items-center px-4 py-1.5 gap-2 border border-neutral-20 currency-radius inline-flex justify-between gap-x-1.5 font-normal ring-gray-300 ring-inset transition-colors text-neutral-80"
                >
                    <span className="truncate max-w-[132px]">{selectedCurrency === "Select Currency" ? "Select Currency" : selectedCurrency}</span>
                    <HiChevronDown aria-hidden="true" className="text-gray-400" />
                </MenuButton>

                <MenuItems
                    transition
                    className="absolute bottom-full mb-2 min-w-[135px] z-99 origin-bottom-right rounded-md bg-white shadow-lg ring-1 ring-black/5 transition focus:outline-none data-[closed]:scale-95 data-[closed]:transform data-[closed]:opacity-0 data-[enter]:duration-100 data-[leave]:duration-75 data-[enter]:ease-out data-[leave]:ease-in max-h-[300px] overflow-y-auto"
                >
                    <div className="sticky top-0 p-2 border-b border-neutral-20 bg-white z-10">
                        <input
                            type="text"
                            placeholder="Search..."
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                            className="w-full px-2 py-1 text-sm border border-neutral-30 rounded focus:outline-none focus:ring-1 focus:ring-primary-60"
                            onClick={(e) => e.stopPropagation()}
                        />
                    </div>
                    <div className="py-1">
                        {filteredCurrencyItems?.length > 0 ? (
                            filteredCurrencyItems.map((item, index) => (
                                <MenuItem key={index}>
                                    {({ focus }) => (
                                        <button
                                            role="menuitem"
                                            className={`block w-[135px] text-left px-4 py-2 text-body-r font-body-r text-neutral-90 ${
                                                focus ? "bg-primary-40 text-gray-900" : ""
                                            } hover:bg-primary-40 hover:text-primary-78`}
                                            onClick={() => handleChange(item)}
                                        >
                                            <div className="flex items-center gap-2">
                                                <span>{item?.currencyCode || "No Currency"}</span>
                                            </div>
                                        </button>
                                    )}
                                </MenuItem>
                            ))
                        ) : (
                            <div className="px-4 py-2 text-sm text-neutral-60 text-center">No matches found</div>
                        )}
                    </div>
                </MenuItems>
            </div>
        </Menu>
    );
};

// Add prop type validation
SnackbarCurrencyDropdown.propTypes = {
    currency: PropTypes.string,
    currencyList: PropTypes.arrayOf(
        PropTypes.shape({
            currencyCode: PropTypes.string
        })
    ),
    onCurrencyChange: PropTypes.func
};

// Default props
SnackbarCurrencyDropdown.defaultProps = {
    currency: "USD",
    currencyList: [],
    onCurrencyChange: () => {}
};

export default SnackbarCurrencyDropdown; 