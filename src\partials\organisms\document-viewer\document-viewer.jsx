import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
import { ErrorBoundary } from "react-error-boundary";
import DocumentViewerHTML from "./document-viewer-html/document-viewer-html";
import DocumentViewerError from "./document-viewer-error";
import DocumentViewerPDF from "./document-viewer-pdf/document-viewer-pdf";
import { GET_FILINGS_VIEW_DOWNLOAD_API } from "../../../infra/api/data-lake-service";
import useDocumentViewerStore from "./document-viewer.store";
import DocumentViewerLoading from "./document-viewer-loading";
import DocumentViewerHTMLParser from "./document-viewer-html/document-viewer-html-parser";

const DocumentViewer = ({ docURL, docId, readOnly, metaData }) => {
  if (docURL) {
    return (
      <DocumentViewerRender
        docURL={docURL}
        readOnly={readOnly}
        metaData={metaData}
      />
    );
  } else if (docId) {
    return (
      <DocumentViewerAPI
        docId={docId}
        readOnly={readOnly}
        metaData={metaData}
      />
    );
  } else if (metaData?.markDown || metaData?.text) {
    return (
      <DocumentViewerRender
        docURL={docURL}
        readOnly={readOnly}
        metaData={metaData}
      />
    );
  } else {
    return <DocumentViewerError docURL={docURL} readOnly={readOnly} />;
  }
};

const DocumentViewerAPI = ({ docId, readOnly, metaData }) => {
  const getFilingsViewDownloadApiQuery = GET_FILINGS_VIEW_DOWNLOAD_API(docId);

  if (getFilingsViewDownloadApiQuery.isLoading)
    return <DocumentViewerLoading />;

  if (!getFilingsViewDownloadApiQuery.isLoading) {
    return (
      <DocumentViewerRender
        docURL={getFilingsViewDownloadApiQuery?.data?.data?.url}
        readOnly={readOnly}
        metaData={metaData}
        fileType={getFilingsViewDownloadApiQuery?.data?.data?.file_type}
      />
    );
  }
};

const DocumentViewerRender = ({
  docURL,
  readOnly,
  metaData = null,
  fileType = null
}) => {
  const {
    setFileUrl,
    setFileExtension,
    XML_TYPES,
    PDF_TYPES,
    MARKDOWN_TYPES,
    PLAIN_TEXT_TYPE,
    getFileExtensionV2
  } = useDocumentViewerStore((state) => state);

  const fileExtension =
    fileType || metaData?.type || getFileExtensionV2(docURL);

  useEffect(() => {
    setFileUrl(docURL);
    setFileExtension(fileExtension);
  }, [docURL]);

  return (
    <ErrorBoundary
      resetKeys={[docURL]}
      fallback={<DocumentViewerError docURL={docURL} readOnly={readOnly} />}
    >
      {(() => {
        if (XML_TYPES.includes(fileExtension)) {
          return (
            <DocumentViewerHTML
              metaData={metaData}
              readOnly={readOnly}
              fileUrl={docURL}
              fileExtension={fileExtension}
            />
          );
        } else if (MARKDOWN_TYPES.includes(fileExtension)) {
          return (
            <DocumentViewerHTMLParser
              metaData={metaData}
              readOnly={readOnly}
              fileData={metaData.markDown}
              fileExtension={fileExtension}
              filingsUrl={"fileUrl"} // temp
            />
          );
        } else if (fileExtension === PLAIN_TEXT_TYPE) {
          if (docURL) {
            return (
              <DocumentViewerHTML
                metaData={metaData}
                readOnly={readOnly}
                fileUrl={docURL}
                fileExtension={fileExtension}
              />
            );
          } else {
            return (
              <DocumentViewerHTMLParser
                metaData={metaData}
                readOnly={readOnly}
                fileData={metaData.text}
                fileExtension={fileExtension}
                filingsUrl={"fileUrl"} // temp
              />
            );
          }
        } else if (PDF_TYPES.includes(fileExtension)) {
          return (
            <DocumentViewerPDF
              metaData={metaData}
              readOnly={readOnly}
              fileUrl={docURL}
            />
          );
        } else {
          return <DocumentViewerError docURL={docURL} readOnly={readOnly} />;
        }
      })()}
    </ErrorBoundary>
  );
};

export default DocumentViewer;

DocumentViewerRender.propTypes = {
  docURL: PropTypes.string,
  readOnly: PropTypes.bool,
  metaData: PropTypes.object
};

DocumentViewerAPI.propTypes = {
  docId: PropTypes.string,
  readOnly: PropTypes.bool,
  metaData: PropTypes.object
};

DocumentViewer.propTypes = {
  docURL: PropTypes.string,
  docId: PropTypes.string,
  readOnly: PropTypes.bool,
  metaData: PropTypes.object
};
