import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import { Tooltip as ReactTooltip } from "react-tooltip";

const DarkToolTip = ({ text, toolTipId, place }) => {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const target = document.querySelector(`[data-tooltip-id="${toolTipId}"]`);

    if (!target) return;

    const handleMouseEnter = () => setIsVisible(true);
    const handleMouseLeave = () => setIsVisible(false);
    const handleClick = () => setIsVisible(false);

    target.addEventListener("mouseenter", handleMouseEnter);
    target.addEventListener("mouseleave", handleMouseLeave);
    target.addEventListener("click", handleClick);
    document.addEventListener("click", handleClick);

    return () => {
      target.removeEventListener("mouseenter", handleMouseEnter);
      target.removeEventListener("mouseleave", handleMouseLeave);
      target.removeEventListener("click", handleClick);
      document.removeEventListener("click", handleClick);
    };
  }, [toolTipId]);

  return (
    <div data-testid="tool-tip" className="caption-r absolute">
      <ReactTooltip
        className="body-r rounded bg-neutral-60 text-white"
        style={{
          paddingInline: "0.5rem",
          paddingTop: "0.125rem",
          paddingBottom: "0.125rem"
        }}
        id={toolTipId}
        place={place}
        content={text}
        delayShow={300}
        isOpen={isVisible}
      />
    </div>
  );
};
DarkToolTip.propTypes = {
  text: PropTypes.string.isRequired,
  toolTipId: PropTypes.string.isRequired,
  place: PropTypes.string
};

export default DarkToolTip;
