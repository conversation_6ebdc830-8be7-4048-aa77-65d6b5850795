import {
  DefaultValues,
  FilingPeriods,
  OtherKPiModuleNames,
  FinancialStatements,
  FinancialStatementsWithName,
  PdfPageHeight,
  PdfPageWidth,
  CommaFormatterRegex,
  QueryWithoutCommaRegex,
  TextWithoutCommaRegex,
  EXCLUDED_MERGE_FIELDS,
  EXCLUDED_FIELD_PREFIXES,
  NOTIFICATION_MESSAGES,
  MERGE_ROW_MESSAGES,
  RowsSuccessMessages,
  ColumnSuccessMessages,
  EXPORT_EXCEL_MESSAGES,
  MARK_AS_HEADER_MESSAGES
} from './constants';

describe('Constants', () => {
  describe('DefaultValues', () => {
    test('should have correct default values', () => {
      expect(DefaultValues.fileName).toBe('');
      expect(DefaultValues.EscapeKey).toBe('Escape');
    });
  });

  describe('FilingPeriods', () => {
    test('should contain all required filing periods', () => {
      expect(FilingPeriods).toEqual(['NA', 'ANL', 'NME', 'HYL', 'QTR']);
      expect(FilingPeriods).toHaveLength(5);
    });
  });

  describe('OtherKPiModuleNames', () => {
    test('should contain 10 KPI module names', () => {
      expect(OtherKPiModuleNames).toHaveLength(10);
      expect(OtherKPiModuleNames[0]).toBe('OtherKPI1');
      expect(OtherKPiModuleNames[9]).toBe('OtherKPI10');
    });

    test('should have sequential KPI names', () => {
      for (let i = 0; i < 10; i++) {
        expect(OtherKPiModuleNames[i]).toBe(`OtherKPI${i + 1}`);
      }
    });
  });

  describe('FinancialStatements', () => {
    test('should contain all required financial statements', () => {
      expect(FinancialStatements).toEqual(['Income Statement', 'Balance Sheet', 'Cash Flow']);
      expect(FinancialStatements).toHaveLength(3);
    });
  });

  describe('FinancialStatementsWithName', () => {
    test('should contain properly structured financial statement objects', () => {
      expect(FinancialStatementsWithName).toHaveLength(3);
      
      expect(FinancialStatementsWithName[0]).toEqual({
        key: 'ProfitAndLoss',
        value: 'Income Statement',
        tabNo: 0
      });
      
      expect(FinancialStatementsWithName[1]).toEqual({
        key: 'BalanceSheet',
        value: 'Balance Sheet',
        tabNo: 1
      });
      
      expect(FinancialStatementsWithName[2]).toEqual({
        key: 'CashFlow',
        value: 'Cash Flow',
        tabNo: 2
      });
    });
  });

  describe('PDF Constants', () => {
    test('should have correct PDF page dimensions', () => {
      expect(PdfPageHeight).toBe(792);
      expect(PdfPageWidth).toBe(612);
    });
  });

  describe('Regular Expressions', () => {
    test('CommaFormatterRegex should work correctly', () => {
      const testNumber = '1234567';
      const result = testNumber.replace(CommaFormatterRegex, ',');
      expect(result).toBe('1,234,567');
    });

    test('TextWithoutCommaRegex should remove commas', () => {
      const testText = '1,234,567';
      const result = testText.replace(TextWithoutCommaRegex, '');
      expect(result).toBe('1234567');
    });

    test('QueryWithoutCommaRegex should be defined', () => {
      expect(QueryWithoutCommaRegex).toBeInstanceOf(RegExp);
    });
  });

  describe('EXCLUDED_MERGE_FIELDS', () => {
    test('should contain all required excluded fields', () => {
      expect(EXCLUDED_MERGE_FIELDS.STATUS).toBe('status');
      expect(EXCLUDED_MERGE_FIELDS.LABEL).toBe('label');
      expect(EXCLUDED_MERGE_FIELDS.ID).toBe('id');
      expect(EXCLUDED_MERGE_FIELDS.DEFINED_NAME).toBe('definedName');
      expect(EXCLUDED_MERGE_FIELDS.STYLE).toBe('style');
      expect(EXCLUDED_MERGE_FIELDS.PERIOD_DATES).toBe('periodDates');
      expect(EXCLUDED_MERGE_FIELDS.COLUMN_IDS).toBe('columnIds');
    });
  });

  describe('EXCLUDED_FIELD_PREFIXES', () => {
    test('should contain correct field prefixes', () => {
      expect(EXCLUDED_FIELD_PREFIXES.CELL_IDS).toBe('cellIds');
      expect(EXCLUDED_FIELD_PREFIXES.PDF_HIGHLIGHTS).toBe('pdfHighlights');
    });
  });

  describe('Message Constants', () => {
    test('NOTIFICATION_MESSAGES should contain correct messages', () => {
      expect(NOTIFICATION_MESSAGES.CANNOT_MERGE_FIRST_ROW).toBe('Cannot merge the first row upward.');
      expect(NOTIFICATION_MESSAGES.ROW_HAS_VALUES).toBe('Cannot merge rows: Selected row contains values');
      expect(NOTIFICATION_MESSAGES.MERGE_SUCCESS).toBe('Rows merged successfully.');
    });

    test('MERGE_ROW_MESSAGES should contain correct messages', () => {
      expect(MERGE_ROW_MESSAGES.SELECT_ONE_ROW).toBe('Please select exactly one row to merge.');
      expect(MERGE_ROW_MESSAGES.CANNOT_MERGE_LAST_ROW).toBe('Cannot merge: This is the last row in the table.');
      expect(MERGE_ROW_MESSAGES.MERGE_SUCCESS).toBe('Row merged successfully.');
    });

    test('RowsSuccessMessages should contain correct messages', () => {
      expect(RowsSuccessMessages.SINGLE_ROW_DELETED).toBe('Row deleted successfully.');
      expect(RowsSuccessMessages.MULTIPLE_ROWS_DELETED).toBe('Rows deleted successfully.');
      expect(RowsSuccessMessages.ROW_ADDED).toBe('Row added successfully.');
    });

    test('ColumnSuccessMessages should contain correct messages', () => {
      expect(ColumnSuccessMessages.SINGLE_COLUMN_DELETED).toBe('Column deleted successfully.');
      expect(ColumnSuccessMessages.MULTIPLE_COLUMNS_DELETED).toBe('Columns deleted successfully.');
      expect(ColumnSuccessMessages.COLUMN_ADDED).toBe('Column added successfully.');
      expect(ColumnSuccessMessages.COLUMN_UPDATED).toBe('Column updated successfully.');
    });

    test('EXPORT_EXCEL_MESSAGES should contain correct message', () => {
      expect(EXPORT_EXCEL_MESSAGES.EXPORT_SUCCESS).toBe('Extraction downloaded successfully.');
    });

    test('MARK_AS_HEADER_MESSAGES should contain correct messages', () => {
      expect(MARK_AS_HEADER_MESSAGES.EXPORT_SUCCESS).toBe('Row converted into header successfully.');
      expect(MARK_AS_HEADER_MESSAGES.EXPORT_ERROR).toBe('Rows with value cells cannot be converted into headers.');
    });
  });
});