import React, { useContext, useEffect } from "react";
import PropTypes from "prop-types";
import { useQueue } from "@uidotdev/usehooks";
import { QueueContext } from "../../general/context";

const QueueContextProvider = ({ children }) => {
  const value = useQueue();

  return (
    <QueueContext.Provider value={value}>
      <QueueContextHandler>{children}</QueueContextHandler>
    </QueueContext.Provider>
  );
};

QueueContextProvider.propTypes = {
  children: PropTypes.element
};

export default QueueContextProvider;

const QueueContextHandler = ({ children }) => {
  const { queue, first } = useContext(QueueContext);
  useEffect(() => {
    if (queue.length > 0) {
      if (typeof first === "function") {
        first();
      }
    }
  }, [queue]);

  return children;
};
