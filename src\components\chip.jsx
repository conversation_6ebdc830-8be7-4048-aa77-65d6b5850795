import React from "react";
import PropTypes from "prop-types";
import { MdClose } from "react-icons/md";

const Chip = ({
  text,
  size,
  themeColor,
  fillMode,
  rounded,
  onClick,
  isSelected,
  className
}) => {
  const baseClasses =
    "px-3 py-1 text-sm font-body-r focus:outline-none flex items-center";
  const sizeClasses = size === "large" ? "text-lg" : "text-sm";
  const fillClasses =
    fillMode === "outline"
      ? `border border-${themeColor} text-${themeColor}`
      : `bg-${themeColor} text-white`;
  const roundedClasses = rounded ? "rounded-full" : "rounded-md";
  const selectedClasses = isSelected ? `bg-${themeColor} text-white` : "";

  const handleClick = (e) => {
    if (onClick) {
      onClick(e);
    }
  };

  return (
    <button
      className={`${baseClasses} ${sizeClasses} ${roundedClasses} ${selectedClasses} ${
        !className.includes("cursor-not-allowed") ? fillClasses : ""
      } ${className}`}
      onClick={handleClick}
      disabled={className.includes("cursor-not-allowed")}
    >
      <span>{text}</span>
      {isSelected && (
        <MdClose
          className="ml-2 cursor-pointer"
          onClick={(e) => {
            e.stopPropagation();
            handleClick(e);
          }}
        />
      )}
    </button>
  );
};

Chip.propTypes = {
  text: PropTypes.string.isRequired,
  size: PropTypes.oneOf(["small", "large"]),
  themeColor: PropTypes.string,
  fillMode: PropTypes.oneOf(["outline", "fill"]),
  rounded: PropTypes.bool,
  onClick: PropTypes.func,
  isSelected: PropTypes.bool,
  className: PropTypes.string
};

Chip.defaultProps = {
  size: "small",
  themeColor: "gray",
  fillMode: "fill",
  rounded: false,
  onClick: () => {},
  isSelected: false,
  className: ""
};

export default Chip;
