import React from "react";
import PropTypes from "prop-types";
import EntityLogo from "../../partials/molecules/entity-logo/entity-logo";
import CurrencyUnitDropdown from "./currency-unit-dropdown";
import CurrencyDropdown from "./currency-dropdown";

const HeaderInfo = ({
  companyName,
  companyTicker,
  acuityid,
  currencyUnit,
  currency,
  currencyList,
  onCurrencyChange,
  onCurrencyUnitChange
}) => {
  const handleCurrencySelection = (selectedCurrencyData) => {
    // Pass the selected currency data up to header.jsx
    if (onCurrencyChange) {
      onCurrencyChange(selectedCurrencyData);
    }
  };
  const handleCurrencyUnitSelection = (selectedCurrencyUnitData) => {
    // Pass the selected currency data up to header.jsx
    if (onCurrencyUnitChange) {
      onCurrencyUnitChange(selectedCurrencyUnitData);
    }
  };
  return (
    <div className="flex items-center justify-between w-full">
      <div className="flex items-center gap-3 ml-3">
        {/* <div className="w-6 h-6 rounded-full bg-[#f68523] flex items-center justify-center">
        <EntityLogo
          acuityID={acuityid}
          companyName={companyName}
          logoSize="medium"
        />
      </div> */}
        <div className="flex items-center gap-2 truncate">
          <span
            title={companyName}
            className="text-heading-2-m text-neutral-80"
          >
            {companyName}
          </span>
        </div>
      </div>
      <div className="flex items-center gap-2 ml-auto bg-white">
        {<CurrencyDropdown
          currency={currency}
          currencyList={currencyList}
          onCurrencyChange={handleCurrencySelection}
        />}
        {<CurrencyUnitDropdown 
          currencyUnit={currencyUnit}
          onCurrencyUnitChange={handleCurrencyUnitSelection}
        />}
      </div>
    </div>
  );
};

HeaderInfo.propTypes = {
  companyName: PropTypes.string.isRequired,
  companyTicker: PropTypes.string.isRequired,
  acuityid: PropTypes.string.isRequired,
  currencyUnit: PropTypes.string.isRequired,
  currency: PropTypes.string.isRequired,
  onCurrencyChange: PropTypes.func,
  onCurrencyUnitChange: PropTypes.func
};

export default HeaderInfo;
