import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import HeaderMenuBar from "./templates/header-menu-bar";
import HeaderHelpIcon from "./templates/header-help-icon";
import HeaderUserProfile from "./templates/header-user-profile";
import logo from "../../../resources/images/logo-brand.svg";

const Header = () => {
  const [dropdownOpen, setDropdownOpen] = useState(false);

  const history = useNavigate();

  const navigateToHome = () => {
    history("/home"); // Change from '..' to '/home'
  };

  return (
    <div className="sticky top-0 z-30 w-full justify-between border-b border-neutral-200 bg-white shadow-md">
      <div className="flex h-16 items-center justify-between">
        <div
          data-testid="header-menu-bar"
          className="flex h-full justify-start"
        >
          <button
            className="flex w-fit h-fit "
            onClick={navigateToHome}
            aria-label="Navigate to Home"
          >
            <img src={logo} alt="ResearchHub" className="h-[65px]" />
          </button>
          <HeaderMenuBar />
        </div>
        <div className="flex justify-end lg:mx-6 xl:mx-6 2xl:mx-16 3xl:mx-24">
          <div className="relative items-center justify-end"></div>
          <div className="flex justify-end">
            <div data-testid="header-help-icon">
              <HeaderHelpIcon />
            </div>
            <div
              data-testid="header-user-profile-dropdown"
              className="flex cursor-pointer items-center"
            >
              <HeaderUserProfile
                dropdownOpen={dropdownOpen}
                setDropdownOpen={setDropdownOpen}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Header;
