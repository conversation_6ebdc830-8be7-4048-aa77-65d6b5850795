import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import ButtonGroup from "../header-button-group";
import { fetcher } from "../../../general/fetcher";

jest.mock("../../../general/fetcher", () => ({
  fetcher: jest.fn()
}));

jest.mock("../../../partials/molecules/toaster", () => ({
  notify: {
    success: jest.fn(),
    error: jest.fn()
  }
}));

describe("ButtonGroup", () => {
  const queryClient = new QueryClient();
  const mockSetShowDownloadConfirmationPopup = jest.fn();
  const mockHandleExcelExport = jest.fn();
  const mockSetIsMapped = jest.fn();
  const mockNotifySuccess = require("../../../partials/molecules/toaster")
    .notify.success;
  const mockNotifyError = require("../../../partials/molecules/toaster").notify
    .error;

  const defaultProps = {
    setShowDownloadConfirmationPopup: mockSetShowDownloadConfirmationPopup,
    exportGridOneRef: { current: null },
    exportGridTwoRef: { current: null },
    exportGridThreeRef: { current: null },
    exportGridFourRef: { current: null },
    incomeStatementData: [],
    balanceSheetData: [],
    cashFlowData: [],
    notesData: [],
    columnNamesForIncomeStatementData: [],
    columnNamesForBalanceSheetData: [],
    columnNamesForCashFlowData: [],
    columnNamesForNotesData: [],
    ticker: "TST",
    job_id: "job123",
    country: "USA",
    client_id: "client123",
    company_id: "company123",
    fs_type: "IS",
    industry: "Tech",
    currency: "USD",
    mappedData: [],
    incomeStatementMappedData: [],
    balanceSheetMappedData: [],
    cashFlowMappedData: [],
    handleExcelExport: mockHandleExcelExport,
    isMapped: false,
    setIsMapped: mockSetIsMapped
  };

  const renderWithQueryClient = (ui) => {
    return render(
      <QueryClientProvider client={queryClient}>{ui}</QueryClientProvider>
    );
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders without crashing", () => {
    renderWithQueryClient(<ButtonGroup {...defaultProps} />);
    expect(screen.getByTestId("history-button")).toBeInTheDocument();
    expect(screen.getByTestId("export-button")).toBeInTheDocument();
    expect(screen.getByTestId("reset-button")).toBeInTheDocument();
    expect(screen.getByTestId("save-draft-button")).toBeInTheDocument();
    expect(screen.getByTestId("publish-button")).toBeInTheDocument();
  });

  it("calls setShowDownloadConfirmationPopup when export button is clicked and isMapped is false", () => {
    renderWithQueryClient(<ButtonGroup {...defaultProps} />);
    fireEvent.click(screen.getByTestId("export-button"));
    expect(mockSetShowDownloadConfirmationPopup).toHaveBeenCalledWith(true);
  });

  it("calls handleExcelExport when export button is clicked and isMapped is true", () => {
    renderWithQueryClient(<ButtonGroup {...defaultProps} isMapped={true} />);
    fireEvent.click(screen.getByTestId("export-button"));
    expect(mockHandleExcelExport).toHaveBeenCalled();
  });

  it("calls handleSave when publish button is clicked", async () => {
    fetcher.mockResolvedValueOnce({ data: {} });

    renderWithQueryClient(<ButtonGroup {...defaultProps} />);
    fireEvent.click(screen.getByTestId("publish-button"));

    await waitFor(() => {
      expect(mockNotifySuccess).toHaveBeenCalledWith("Published successfully");
      expect(mockSetIsMapped).toHaveBeenCalledWith(true);
    });
  });

  it("shows loading spinner when publish button is clicked", () => {
    renderWithQueryClient(<ButtonGroup {...defaultProps} />);
    fireEvent.click(screen.getByTestId("publish-button"));
    expect(screen.getByText("Publish")).toBeInTheDocument();
  });
});
