@import "pdfjs-dist/web/pdf_viewer.css";
@import "./pdf_viewer.css";

.Highlight__popup {
  background-color: #3d464d;
  border: 1px solid rgba(255, 255, 255, 0.25);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 3px;
  max-width: 300px;
  max-height: 100px;
  overflow-y: scroll;
}

.pdfViewer {
  display: block !important;
  position: relative !important;
  margin: 0 !important;
  width: 100% !important;
  height: 100% !important;
}

.pdfViewer .page {
  margin: 0 auto !important;
  border: none !important;
  box-shadow: none !important;
}

/* Override PDF.js styles for better scrolling */
.pdfViewer.scrollHorizontal,
.pdfViewer.scrollWrapped {
  margin: 0 !important;
}

.PdfHighlighter__highlight-layer {
  position: absolute;
  z-index: 3;
  left: 0;
}

.PdfHighlighter__container {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  overflow: auto !important;
}

.page {
  margin-bottom: 10px !important;
}

.PdfHighlighter__container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.PdfHighlighter__container::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.PdfHighlighter__container::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 4px;
}

.PdfHighlighter__container::-webkit-scrollbar-thumb:hover {
  background: #555;
}
