import useModalStore from "./modal.store";

describe("modal.store test", () => {
  let data;
  beforeEach(() => {
    data = {
      type: "CONFIRM",
      open: true,
      title: "Confirm",
      cancelLabel: "Cancel",
      submitLabel: "Submit",
      children: null,
      handleClose: null,
      handleSubmit: null
    };
  });

  test("test setModalOpen", () => {
    const { setModalOpen } = useModalStore.getState();
    setModalOpen(data);
    expect(useModalStore.getState().open).toBe(true);
    expect(useModalStore.getState().title).toBe("Confirm");
    expect(useModalStore.getState().cancelLabel).toBe("Cancel");
    expect(useModalStore.getState().submitLabel).toBe("Submit");
  });

  test("test toggle", () => {
    const { toggle } = useModalStore.getState();
    toggle({ open: !useModalStore.getState().open });

    expect(useModalStore.getState().open).toBe(true);
  });
});
