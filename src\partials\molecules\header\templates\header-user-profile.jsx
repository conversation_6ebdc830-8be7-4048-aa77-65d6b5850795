import { React, useState, useEffect, useRef } from "react";
import { Transition } from "@headlessui/react";
import HeaderLogOutPopUp from "./header-log-out-pop-up";
import { MdLogout } from "../../../atoms/icons";
import { getUserName } from "../../../../infra/store/user-store";
import { useNavigate } from "react-router-dom";
import { FaUsersCog } from "react-icons/fa";
//import { RolesContext } from '../../../../general/context';

const HeaderUserProfile = () => {
  //const roleConfig = useContext(RolesContext);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [showNewUserPopUp, setShowNewUserPopUp] = useState(false);
  const [userData, setUserData] = useState();
  const [userName, setUserName] = useState();

  const dropdown = useRef(null);
  const trigger = useRef(null);
  const history = useNavigate();

  const getCapitalizedInitials = (words) => {
    if (!words) return "";
    const initials = words
      .toString()
      .replace(".com", "")
      .split(".")
      .map((word) => word[0]?.toUpperCase());
    return initials.join("");
  };

  useEffect(() => {
    setUserData(getCapitalizedInitials(getUserName()));
    setUserName(getUserName()?.split("@")[0].replace(".", " "));
  }, []);

  const openSignoutModel = async () => {
    setShowNewUserPopUp(true);
  };

  // close on click outside
  useEffect(() => {
    const clickHandler = ({ target }) => {
      if (
        !dropdownOpen ||
        dropdown.current?.contains(target) ||
        trigger.current?.contains(target)
      )
        return;
      setDropdownOpen(false);
    };
    document.addEventListener("click", clickHandler);
    return () => document.removeEventListener("click", clickHandler);
  });

  // close if the esc key is pressed
  useEffect(() => {
    const keyHandler = ({ keyCode }) => {
      if (!dropdownOpen || keyCode !== 27) return;
      setDropdownOpen(false);
    };
    document.addEventListener("keydown", keyHandler);
    return () => document.removeEventListener("keydown", keyHandler);
  });

  const navigate = () => {
    setDropdownOpen(false);
    history("../user-management");
  };

  return (
    <div className="relative flex cursor-pointer items-center pl-3">
      <button
        id="header-user-profile"
        data-testid="header-user-profile"
        className={`bg-header linear flex size-6 rounded-3xl border-0 bg-transparent bg-gradient-to-r from-primary-100 to-secondary-100 bg-clip-padding hover:bg-gradient-to-l`}
        onClick={() => setDropdownOpen(!dropdownOpen)}
        ref={trigger}
        aria-expanded={dropdownOpen}
      >
        <p className="caption-m size-8 p-1 text-white">{userData}</p>
      </button>
      <Transition
        className={`body-r absolute right-0 top-[1.5rem] mt-1.5 w-[11.25rem] overflow-hidden rounded border border-neutral-10 bg-white shadow-md ${
          dropdownOpen ? "" : "hidden"
        }`}
        id="dropdown-logout"
        data-testid="dropdown-logout"
        show={dropdownOpen}
        enter="transition ease-out duration-200 transform"
        enterFrom="opacity-0 -translate-y-2"
        enterTo="opacity-100 translate-y-0"
        leave="transition ease-out duration-200 transform"
        leaveFrom="opacity-100 translate-y-0"
        leaveTo="opacity-0 -translate-y-2"
      >
        <div
          data-testid={"user-profile-dropdown"}
          ref={dropdown}
          className="items-center py-1"
        >
          <div className="">
            <div
              data-testid="user-profile-name"
              title={userName}
              className="body-r h-10 w-full cursor-default overflow-hidden text-ellipsis whitespace-nowrap break-all border-b border-neutral-10 px-4 py-2.5 text-neutral-90"
            >
              {userName}
            </div>
            {true && (
              <button
                data-testid="user-profile-dropdown-admin-console"
                onClick={navigate}
                className="flex w-full items-center px-4 py-2.5 text-neutral-80 hover:bg-primary-35 focus:bg-primary-40 focus:text-primary-78"
              >
                <FaUsersCog className="flex items-center" />
                <span className="body-r flex items-center pl-3">
                  Admin Console
                </span>
              </button>
            )}
            <button
              data-testid="user-profile-dropdown-logout"
              onClick={() => openSignoutModel()}
              className="flex w-full items-center px-4 py-2.5 text-neutral-80 hover:bg-primary-35 focus:bg-primary-40 focus:text-primary-78"
            >
              <MdLogout className="flex items-center" />
              <span className="body-r flex items-center pl-3">Log out</span>
            </button>
          </div>
        </div>
      </Transition>
      <HeaderLogOutPopUp
        showNewUserPopUp={showNewUserPopUp}
        setShowNewUserPopUp={setShowNewUserPopUp}
      />
    </div>
  );
};
export default HeaderUserProfile;
