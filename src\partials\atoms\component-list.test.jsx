import { fireEvent, render, screen } from "@testing-library/react";
import ComponentList from "./component-list";

describe("Component ComponentList render", () => {
  it("should render ComponentList without value", () => {
    render(<ComponentList />);
    expect(screen.getByTestId("component-list")).toBeInTheDocument();

    fireEvent.click(screen.getByTestId("component-list-success"));
    fireEvent.click(screen.getByTestId("component-list-information"));
    fireEvent.click(screen.getByTestId("component-list-warning"));
    fireEvent.click(screen.getByTestId("component-list-error"));
  });
});
