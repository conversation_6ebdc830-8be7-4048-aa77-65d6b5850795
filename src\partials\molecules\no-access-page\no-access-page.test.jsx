import { render, screen, fireEvent } from "@testing-library/react";
import NoAccessPage from "./no-access-page";
import { useNavigate } from "react-router-dom";

jest.mock("react-router-dom", () => ({
  useNavigate: jest.fn()
}));

describe("NoAcNoAccessPagecess component", () => {
  test("renders the component correctly", () => {
    let navigate = jest.fn();
    useNavigate.mockImplementation(() => navigate);

    render(<NoAccessPage />);

    fireEvent.click(screen.getByTestId("logout-button"));
  });
});
