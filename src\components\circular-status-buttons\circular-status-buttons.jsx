import React, { useState } from "react";
import { FaRegFlag } from "react-icons/fa";

/**
 * CircularStatusButtons Component
 * Displays circular buttons with status indicators
 */
const CircularStatusButtons = ({ statusData = {} }) => {
  // Default status data if not provided
  const defaultStatusCounts = {
    mapped: 12,
    matchFound: 5,
    unmapped: 2,
    noMatchFound: 3
  };

  // Use provided data or defaults
  const counts = {
    ...defaultStatusCounts,
    ...statusData
  };

  // Define the status buttons configuration
  const statusButtons = [
    { label: "NC", value: "Needs Changes", font: "caption-r", bgColor: "bg-noticeable-50", borderColor: "border-noticeable-70" },
    // { label: "Mapped", value: counts.mapped.toString(), font: "body-b", bgColor: "bg-info-50", borderColor: "border-primary-78" },
    // { label: "Match Found", value: counts.matchFound.toString(), font: "body-b", bgColor: "bg-noticeable-50", borderColor: "border-noticeable-90" },
    // { label: "Unmapped", value: counts.unmapped.toString(), font: "body-b", bgColor: "bg-negative-50", borderColor: "border-negative-100" },
    // { label: "No Match Found", value: counts.noMatchFound.toString(), font: "body-b", bgColor: "bg-[#FAF8EE]", borderColor: "border-yellow-pale-100" }
  ];

  // State to track which button is being hovered
  const [hoveredIndex, setHoveredIndex] = useState(null);
  
  return (
    <div className="flex items-center space-x-2 px-2">
      {statusButtons.map((button, index) => (
        <button 
          key={index}
          className={`h-6 px-3 flex items-center justify-center ${button.font} rounded-[14px] text-[#1A1A1A] ${button.bgColor} ${button.borderColor} border`}
          title={button.label === "NC" ? button.label : `${button.label} ${button.value}`}
          onMouseEnter={() => setHoveredIndex(index)}
          onMouseLeave={() => setHoveredIndex(null)}
        >
          {button.label === 'NC' && <FaRegFlag className="pr-2 w-6 text-negative-80"/>}
          {hoveredIndex === index && button.label !== 'NC' ? `${button.value} ${button.label}` : button.value}
        </button>
      ))}
    </div>
  );
};

export default CircularStatusButtons;
