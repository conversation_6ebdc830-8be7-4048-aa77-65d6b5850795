import React, { useState, useEffect, useRef } from "react";
import PropTypes from "prop-types";
import { ComboBox } from "@progress/kendo-react-dropdowns";
import { FaFile, FaRegFlag } from "react-icons/fa";
import { Checkbox } from "@progress/kendo-react-inputs";
import PopupcolumnComponent from "../../components/popup/column-edit-popup";
import { PdfHolder } from "../../components/pdf-highlighter/components/PdfHolder";
import { PdfHighlighter } from "../../components/pdf-highlighter/components/PdfHighlighter";
import { Popup } from "../../components/pdf-highlighter/components/Popup";
import { AreaHighlight } from "../../components/pdf-highlighter/components/AreaHighlight";
import { Highlight } from "../../components/pdf-highlighter/components/TextSelectionHighlight";
import { SpinnerCircle } from "../../partials/atoms/loader";
import EmptySearchImages from "../../resources/images/emptyy_search";
import { MONTHS, getMonthText } from "../../constants/date-utils";
import { TRADING_KPI_CONFIG } from "../../constants/kpi-config";
import { FiPlus, FiPercent, FiType, FiDollarSign, FiX } from "react-icons/fi";
import { Bs123, BsCash } from "react-icons/bs";
import { KPI_INFO_CURRENCY, KPI_INFO_MULTIPLE, KPI_INFO_NUMBER, KPI_INFO_PERCENTAGE, KPI_INFO_TEXT } from "../../constants/kpi-info";
import { set } from "date-fns";
import { validatePeriod } from "../../constants/commonFunctions";
import { convertBoundingRectToPercentage, SpecificKpiY2Add } from "../../utils/financial-data-operations";
import PdfIcon from "../../resources/images/pdf-icon.svg";
import ExcelIcon from "../../resources/images/excel-icon.svg";
import GroupKpiSnackbar from "./GroupKpiSnackbar";
import PeriodHeaderSidebar from "../period-header-sidebar/period-header-sidebar";
import { parseDateString } from "../popup/column-edit-popup";
import { v4 as uuidv4 } from 'uuid';
import { PopUp } from "../../partials/molecules/pop-up";
import { notify } from "../../partials/molecules/toaster";
import ExcelPreview from "../../Excel previewer/excel-preview";
// CSS variables for consistent row heights
const ROW_HEIGHTS = {
  ROW_1: 48, // Time period row height
  ROW_2: 36, // Standard KPI row height
  ROW_3: 36, // Document KPI row height
};

// Total height of header rows
const HEADER_HEIGHT = ROW_HEIGHTS.ROW_1 + ROW_HEIGHTS.ROW_2 + ROW_HEIGHTS.ROW_3;

const getKpiIndicator = (kpiInfo) => {
  if (!kpiInfo) return null;

  switch (kpiInfo) {
    case KPI_INFO_PERCENTAGE:
      return <FiPercent />;
    case KPI_INFO_TEXT:
      return <FiType />;
    case KPI_INFO_NUMBER:
      return <Bs123 />;
    case KPI_INFO_CURRENCY:
      return <FiDollarSign />;
    case KPI_INFO_MULTIPLE:
      return <FiX />;
    default:
      return null;
  }
};

function generateUUID() {
  return uuidv4();
}

const MultiColumnHeaderTable = ({
  periods = [],
  kpiOptions = [],
  onKpiChange = () => { },
  showHeader = true,
  data = [],
  onCellValueChange = () => { },
  // Props for PDF handling
  onCellClick = () => { },
  kpiConfig,
  selectedTabId,
  onCompanySelection = () => { },
  selectedCompanies = [],
  files = [],
  activeTabId, // <-- Add this prop
  setPeriods,
  setCurrentTableData,
  onGroupKpiColumns = () => { },
  onGroupKpiData = () => { },
  isDeleted = false,
  setIsDeleted = () => { },
  extractionType = "SpecificKpi"
}) => {
  const onCancel = () => {
    setShowDeleteConfirmPopUp(false);
    setCheckedColumns({});
    setCheckedDocumentKpis({});
    setCheckedStandardKpis({});
  };
  // Use KPI config from constants file
  const kpiConfigOptions = TRADING_KPI_CONFIG;
  // State for tracking the cell being edited
  const [editCell, setEditCell] = useState({
    rowIndex: null,
    colIndex: null,
    periodIndex: null,
    value: null
  });

  // State to track hovered column (for showing checkboxes)
  const [hoveredColumnIndex, setHoveredColumnIndex] = useState(null);
  // State to track which column checkboxes have been checked
  const [checkedColumns, setCheckedColumns] = useState({});

  // State to track which Document KPI column is being hovered
  const [hoveredDocumentKpiIndex, setHoveredDocumentKpiIndex] = useState(null);

  // State to track which Document KPI column checkboxes have been checked
  const [checkedDocumentKpis, setCheckedDocumentKpis] = useState({});

  // State to track which Standard KPI column is being hovered
  const [hoveredStandardKpiIndex, setHoveredStandardKpiIndex] = useState(null);
  // State to track which Standard KPI column checkboxes have been checked
  const [checkedStandardKpis, setCheckedStandardKpis] = useState({});
  const [checkedindex, setCheckedindex] = useState([]);

  // State for the column popup
  const [isColumnPopupOpen, setIsColumnPopupOpen] = useState(false);
  const [showDeleteConfirmPopUp, setShowDeleteConfirmPopUp] = useState(false);
  const [editingColumnIds, setEditingColumnIds] = useState([]);
  const [columnDetails, setColumnDetails] = useState({
    Filing_Type: "",
    Filing_Version: "",
    Filing_Date: null,
    Period_Type: "",
    Period_Month: "",
    Period_Quarter: "",
    Period_Year: "",
    Data_Type: ""
  });
  // State to track if PDF is shown
  const [showPdfViewer, setShowPdfViewer] = useState(false);
  const [periodId, setPeriodId] = useState(null);
  const [kpiId, setKpiId] = useState(null);

  const [selectedCellInfo, setSelectedCellInfo] = useState(null);
  const [pdfDocument, setPdfDocument] = useState(null);
  const [highlights, setHighlights] = useState([]);
  const pdfHighlighterRef = useRef(null);
  const [originalCellValue, setOriginalCellValue] = useState(null);
  const tableRef = useRef(null);
  // Add pendingHighlight to store highlight that needs to be applied when PDF document is ready
  const [pendingHighlight, setPendingHighlight] = useState(null);
  const [source, setSource] = useState(null);

  // Excel viewer state variables
  const [showExcelViewer, setShowExcelViewer] = useState(false);
  const [selectedCellInfoExcel, setSelectedCellInfoExcel] = useState(null);
  const [excelData, setExcelData] = useState(null);
  const [isExcelLoading, setIsExcelLoading] = useState(false);
  const [selectedViewerFile, setSelectedViewerFile] = useState(null);
  
  // Handle company selection
  const handleCompanyCheckboxChange = (companyId, isChecked) => {
    onCompanySelection(companyId, isChecked);
  };

  // Function to check if a value represents an Excel source
  const isExcelSource = (value) => {
    if (!value || typeof value !== 'object') return false;
    
    // Check fileType property
    if (value.fileType && ['excel', 'xlsx', 'xls'].includes(value.fileType.toLowerCase())) {
      return true;
    }
    
    return false;
  };

  // Handle Excel cell click
  const handleCellClickExcel = (rowIndex, colIndex, periodIndex, value) => {
    console.log("Excel cell clicked:", { rowIndex, colIndex, periodIndex, value });
    console.log("Files array:", files);
    console.log("Value source:", value?.source);
    
    // Extract Excel highlight data
    const excelHighlight = value && typeof value === 'object' && value.excelHighlight;
    
    // Store the cell information with highlight data
    setSelectedCellInfoExcel({
      rowIndex,
      colIndex, 
      periodIndex,
      value,
      excelHighlight
    });
    
    // Find the Excel file associated with this cell
    let cellExcel;
    
    if (value && value.source) {
      cellExcel = files?.find(file => file?.fileId === value.source);
      console.log("Found cellExcel:", cellExcel);
      console.log("CellExcel s3_path:", cellExcel?.s3_path);
    }
    
    // Show Excel viewer if we have an Excel file
    if (cellExcel) {
      setShowExcelViewer(true);
      setSelectedViewerFile(cellExcel);
      setIsExcelLoading(false);
    } else {
      setShowExcelViewer(true);
      setExcelData(null);
      setIsExcelLoading(false);
    }
    
    // Call the callback
    onCellClick(rowIndex, colIndex, periodIndex, value);
  };

  // Handle click on a cell to show PDF  
  // Add missing state for PeriodHeaderSidebar
  const [isPeriodHeaderSidebarOpen, setIsPeriodHeaderSidebarOpen] = useState(false);
  const [sidebarInitialValues, setSidebarInitialValues] = useState({});
  const [sidebarColumnId, setSidebarColumnId] = useState(null);
  // Add state to store grouped column order
  const [groupedKpiOrder, setGroupedKpiOrder] = useState(null);
  const [cellValue, setCellValue] = useState(null);
  
  // Handle click on a cell to show PDF or Excel
  const handleCellClick = (rowIndex, colIndex, periodIndex, value) => {
    setCellValue(value);
    
    // Original PDF handling logic
    if (value) {
      if((typeof value === 'string' && (value.source === undefined || value.source === null))||(typeof value === 'object' && (value.value === undefined || value.value === null || value.value === ''))) {
        // Hide PDF viewer
      setShowPdfViewer(true);
      setShowExcelViewer(true);
      setSelectedCellInfoExcel(null);
      setSelectedCellInfo(null);
      setHighlights([]);
      setPendingHighlight(null);
      setSelectedViewerFile(null);
      setExcelData(null);
      setPdfData(null);
      }else{         
      // Check if it's an Excel source first
    if (isExcelSource(value)) {
      handleCellClickExcel(rowIndex, colIndex, periodIndex, value);
      return;
    }
    if(value.fileType === 'pdf') {
      // First, determine the PDF highlight data
      handleCellClickForPdf(rowIndex, colIndex, periodIndex, value);
    }
      }
     
    } else {
      // Hide PDF viewer
      setShowPdfViewer(true);
      setSelectedCellInfo(null);
      setHighlights([]);
      setPendingHighlight(null);
    }
    // Save the current scroll position
    const scrollPosition = document.querySelector(".k-grid-content")?.scrollTop;
    // Restore scroll position
    setTimeout(() => {
      if (scrollPosition !== undefined) {
        const gridContent = document.querySelector(".k-grid-content");
        if (gridContent) gridContent.scrollTop = scrollPosition;
      }
    }, 0);
  };
  
  const handleCellClickForPdf = (rowIndex, colIndex, periodIndex, value) => {
    // Find the PDF associated with this cell
      let cellPdf;
      
      
        cellPdf = files?.find(pdf => pdf?.fileId === value.source);
  const pdfHighlight = value && typeof value === 'object' && value.pdfHighlight;

  // Store the cell information
  setSelectedCellInfo({
    rowIndex,
    colIndex,
    periodIndex,
    value
  });
  // Only show PDF viewer if we have a PDF file
  if (cellPdf && cellPdf.file) {
    setShowPdfViewer(true);

    // Set the PDF data
    setSelectedViewerFile(cellPdf);

    // Store highlight data to be applied after PDF loads
    if (pdfHighlight && pdfHighlight.bounds && pdfHighlight.bounds.length === 4) {
      console.log("Setting pending highlight from cell click:", pdfHighlight);
      setPendingHighlight(pdfHighlight);
    } else {
      setPendingHighlight(null);
    }
  } else {
    setShowPdfViewer(false);
  }

  // Call the callback
  onCellClick(rowIndex, colIndex, periodIndex, value);
}
  // Function to create a highlight from PDF highlight data
  const handleSubmitHighlight = async (pdfHighlight) => {
    try {
      // Safety check - make sure we have highlight data and PDF document
      if (!pdfHighlight || !pdfDocument) {
        return;
      }

      // Default size if not available
      let pageWidth = 816;
      let pageHeight = 1056;

      // Get actual page dimensions if available
      // Get the page based on the highlight's page number
      const pageNumber = pdfHighlight.pageNumber || 1;

      try {
        const page = await pdfDocument.getPage(pageNumber);
        const viewport = page.getViewport({ scale: 1.0 });
        pageWidth = viewport.width;
        pageHeight = viewport.height;
      } catch (error) {
        console.error("Error getting PDF page dimensions:", error);
      }

      // Calculate bounding rectangle from highlight bounds
      const boundingRect = {
        x1:
          pdfHighlight.bounds[0] < 1
            ? pdfHighlight.bounds[0] * pageWidth
            : pdfHighlight.bounds[0],
        y1:
          pdfHighlight.bounds[1] < 1
            ? pdfHighlight.bounds[1] * pageHeight
            : pdfHighlight.bounds[1],
        x2:
          pdfHighlight.bounds[2] < 1
            ? (pdfHighlight.bounds[2]) * pageWidth
            : pdfHighlight.bounds[2],
        y2:
          pdfHighlight.bounds[3] < 1
            ? (
              pdfHighlight.pageHeight === -1
                ? pdfHighlight.bounds[3]
                : pdfHighlight.bounds[3] + SpecificKpiY2Add
            ) * pageHeight
            : pdfHighlight.bounds[3],
        width: pageWidth,
        height: pageHeight,
        pageNumber: pdfHighlight.pageNumber || 1
      };

      // Ensure dimensions are valid
      console.log("Calculated boundingRect:", boundingRect);

      // Validate dimensions
      if (boundingRect.x2 <= boundingRect.x1 || boundingRect.y2 <= boundingRect.y1) {
        console.error("Invalid highlight dimensions", boundingRect);
        return;
      }

      // Create position with proper structure
      const position = {
        boundingRect,
        rects: [boundingRect],
        pageNumber: pdfHighlight.pageNumber || 1
      };      // Create highlight object with unique ID
      const highlight = {
        id: `highlight-${Date.now()}`, // Unique ID
        content: {
          text: pdfHighlight.text || "Highlighted text"
        },
        position,
        comment: {
          text: pdfHighlight.text || "Highlighted text",
          emoji: "💬"
        }
      };

      console.log("Created highlight:", highlight);

      // Add highlight to state immediately
      if(isValidBoundingRect(boundingRect)){
        
      setHighlights([highlight]);
      }
        
      // Scroll to the highlight after a short delay to ensure PDF is fully rendered
      setTimeout(() => {
        if (pdfHighlighterRef.current?.scrollTo) {
          console.log("Scrolling to highlight:", highlight);
          // Call the scrollTo method that handles both vertical and horizontal scrolling
          pdfHighlighterRef.current.scrollTo(highlight);
        }
      }, 300); // Increased delay to ensure PDF is fully rendered

    } catch (error) {
      console.error("Failed to create highlight:", error);
    }
  };

  const isValidBoundingRect = rect =>
  ['x1', 'y1', 'x2', 'y2'].every(
    key => rect[key] !== undefined && rect[key] !== null && rect[key] > 0
  );

  // Handle cell value change
  const handleCellChange = (e) => {
    onCellValueChange(
      editCell.rowIndex,
      periodId,
      kpiId,
      e.target.value
    );
    setEditCell({
      ...editCell,
      value: e.target.value
    });
  };
  // Handle save on blur or enter key
  const handleCellBlur = () => {
    if (editCell.rowIndex !== null && editCell.colIndex !== null) {
      // Get the period for this edit cell
      const period = periods[editCell.periodIndex];

      // Get the kpiId for this edit cell
      const kpi = period?.selectedKpis?.[editCell.colIndex];

      if (period?.periodId && kpi?.kpiId) {
        // Call the callback with updated value using the new periodId_kpiId format
        onCellValueChange(
          editCell.rowIndex,
          period.periodId,
          kpi.kpiId,
          editCell.value
        );
      } else {
        console.error("Could not find periodId or kpiId for edit cell", { period, kpi, editCell });
      }
    }
  };

  // Handle key press in the cell input
  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleCellBlur();
    } else if (e.key === 'Escape') {
      // Cancel editing
      setEditCell({
        rowIndex: null,
        colIndex: null,
        periodIndex: null,
        value: null
      });
    }
  };

  // Close PDF viewer
  const handleClosePdf = () => {
    setSelectedViewerFile(null);
    setShowPdfViewer(false);
    setSelectedCellInfo(null);
  };

  // Close Excel viewer
  const handleCloseExcel = () => {
    setExcelData(null);
    setShowExcelViewer(false);
    setSelectedCellInfoExcel(null);
    setIsExcelLoading(false);
  };
  
  // Calculate column spans for each period
  const renderColSpan = (period) => {
    return period.columns || 1;
  };

  // Function to flatten columns for second and third rows
  const getFlattenedColumns = () => {
    let flatColumns = [];
    periods.forEach((period) => {
      const colCount = period.columns || 1;
      for (let i = 0; i < colCount; i++) {
        flatColumns.push({
          periodIndex: periods.indexOf(period),
          colIndex: i,
          period: period,
          isEditing: false
        });
      }
    });
    if (groupedKpiOrder && groupedKpiOrder.length === flatColumns.length) {
      // Rearrange columns according to grouped order
      flatColumns = groupedKpiOrder.map((i) => flatColumns[i]);
    }
    return flatColumns;
  };
  const flatColumns = getFlattenedColumns();

  // Handle column popup close or save
  const handleColumnPopupClose = (values) => {
    if (!values) {
      // If no values provided, just close the popup
      setIsColumnPopupOpen(false);
      setEditingColumnIds([]);
      setCheckedColumns({});
      return;
    }

    // Get column index from the editingColumnIds (format is 'period-{index}')
    const columnIndex = editingColumnIds[0]?.split('-')[1];

    if (columnIndex !== undefined) {
      // Create a copy of the periods array
      const updatedPeriods = [...periods];

      // Update the period data with values from the popup
      const periodToUpdate = updatedPeriods[columnIndex];
      if (periodToUpdate) {
        // Format the period label based on the values from the popup
        // This mimics the formatPeriodTypeWithDataType function from the evidence panel
        const { Period_Type, Period_Month, Period_Quarter, Period_Year, Data_Type } = values;

        let formattedLabel = '';
        if (Data_Type) {
          formattedLabel = `(${Data_Type}) `;
          if (Period_Type === 'monthly') {
            formattedLabel += `${getMonthText(Period_Month)} `;
          } else if (Period_Type === 'quarterly' && Period_Quarter) {
            formattedLabel += `${Period_Quarter} `;
          }

          if (Period_Year) {
            formattedLabel += Period_Year;
          }
        }

        // Update the period label
        periodToUpdate.label = formattedLabel.trim() || periodToUpdate.label;
      }

      // Close the popup and reset states
      setIsColumnPopupOpen(false);
      setEditingColumnIds([]);
      setCheckedColumns({});
    }
  };

  const [downloadedPdfs, setDownloadedPdfs] = useState([]);
  const [pdfData, setPdfData] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  // const [data, setCurrentTableData] = useState([]);  
  const [hasChanges, setHasChanges] = useState(false);
  const captureHighlight = (position, content) => {
    if (!selectedCellInfo || !content?.text) return;

    setHighlights([]);

    // Convert position coordinates to percentage values
    const convertedPosition = convertBoundingRectToPercentage(position);

    // First update the cell value
    const rowId = selectedCellInfo.rowIndex;

    onCellValueChange(
      rowId,
      periodId,
      kpiId,
      content.text,
      convertedPosition,      // <-- This is the highlight position
      'pdf', // Default to 'pdf' if not specified
      source || selectedCellInfo.value.source || null
    );
    setHasChanges(true);
    setEditCell({
      ...selectedCellInfo,
      value: content.text
    });
    if (selectedCellInfo.value.pdfHighlight) {
      setSelectedCellInfo({
        ...selectedCellInfo,
        value: {
          ...selectedCellInfo.value,
          fileType: 'pdf',
          pdfHighlight: {
            text: content.text,
            ...selectedCellInfo.value.pdfHighlight,
          },
          value: content.text
        }
      });
    }
    else {
      setSelectedCellInfo({
        ...selectedCellInfo,
        value: {
          value: content.text,
          ...selectedCellInfo.value,
          fileType: 'pdf',
          pdfHighlight: {
            text: content.text,
            bounds: convertedPosition.bounds,
            pageNumber: convertedPosition.pageNumber ?? 1,
            ...selectedCellInfo.value.pdfHighlight,
          }
        }
      });

    }
  };
  useEffect(() => {
    setEditCell({
      rowIndex: null,
      colIndex: null,
      periodIndex: null,
      value: null
    });
    setSelectedCellInfo(null);
    setPdfData(null);
    setShowPdfViewer(false);
    setPendingHighlight(null);
    setHighlights([]);
    setPdfDocument(null);
    
    // Clear Excel state as well
    setSelectedCellInfoExcel(null);
    setExcelData(null);
    setShowExcelViewer(false);
    setIsExcelLoading(false);
    
  }, [activeTabId]);
  useEffect(() => {
    const handleClickOutside = (event) => {
      const pdfOverlay = document.querySelector(".pdf-overlay");
      const isPdfClick = pdfOverlay?.contains(event.target);

      if (
        tableRef.current &&
        !tableRef.current.contains(event.target) &&
        !isPdfClick
      ) {
        setSelectedCellInfo(null);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [tableRef]);


  // Effect to handle PDF document loading and highlight creation
  useEffect(() => {
    // Check if we have a loaded PDF document 
    if (pdfDocument) {
      console.log("PDF Document loaded:", pdfDocument);

      // Apply the pending highlight if available
      if (pendingHighlight) {
        console.log("Creating highlight for:", pendingHighlight);
        // Use setTimeout to ensure the PDF is fully rendered before creating highlight
        setTimeout(() => {
          // Clear any existing highlights first
          setHighlights([]);

          // Now create the new highlight
          handleSubmitHighlight(pendingHighlight);

          // Clear the pending highlight once applied
          setPendingHighlight(null);
        }, 800); // Increased delay to ensure PDF is fully rendered
      } else if (selectedCellInfo && selectedCellInfo.value && selectedCellInfo.value.pdfHighlight) {
        // If no pending highlight but cell has highlight data, apply it
        console.log("Applying highlight from selectedCellInfo:", selectedCellInfo.value.pdfHighlight);
        setTimeout(() => {
          setHighlights([]);
          handleSubmitHighlight(selectedCellInfo.value.pdfHighlight);
        }, 800);
      }
    }
  }, [pdfDocument, pendingHighlight]);
  const getComboBoxValue = (col) => {
    const documentKpi = col.period.documentKpis?.[col.colIndex];
    if (documentKpi && documentKpi.kpiId) {
      return (
        kpiOptions.find((opt) => opt.kpiId === documentKpi.kpiId) || null
      );
    }
    return col.period.selectedKpis?.[col.colIndex] || null;
  };

  // --- Group KPIs by Time Period Snackbar logic encapsulated in a custom hook ---
  function useGroupKpiSnackbar(checkedStandardKpis, setIsPeriodHeaderSidebarOpen, setSidebarInitialValues, setSidebarColumnId, setColumnDetails, flatColumns) {
    const checkedStandardKpiCount = Object.values(checkedStandardKpis || {}).filter(Boolean).length;
    const showGroupSnackbar = checkedStandardKpiCount > 0;
    const groupSnackbarEnabled = checkedStandardKpiCount > 0;
    const handleGroupKpis = () => {
      if (!groupSnackbarEnabled) return;
      // Find the checked columns
      const checkedIndices = Object.keys(checkedStandardKpis).filter((key) => checkedStandardKpis[key]).map(Number);
      // Determine if all checked columns are from the same period
      const checkedPeriodIndices = checkedIndices.map((colIdx) => flatColumns[colIdx]?.periodIndex);
      const allSamePeriod = checkedPeriodIndices.every((val) => val === checkedPeriodIndices[0]);
      let initialLabel = '';
      let parsed = {};
      if (checkedIndices.length > 0 && flatColumns && flatColumns.length > checkedIndices[0] && allSamePeriod) {
        const col = flatColumns[checkedIndices[0]];
        initialLabel = col?.period?.label || '';
        parsed = parseDateString(initialLabel);
      } else {
        // If from different periods, open with empty values
        parsed = {
          Filing_Type: '',
          Filing_Version: '',
          Filing_Date: '',
          Period_Type: '',
          Period_Month: '',
          Period_Quarter: '',
          Period_Year: '',
          Data_Type: ''
        };
        initialLabel = '';
      }
      setSidebarInitialValues(parsed);
      setColumnDetails({
        Filing_Type: '',
        Filing_Version: '',
        Filing_Date: initialLabel,
        Period_Type: parsed.Period_Type,
        Period_Month: parsed.Period_Month,
        Period_Quarter: parsed.Period_Quarter,
        Period_Year: parsed.Period_Year,
        Data_Type: parsed.Data_Type
      });
      setSidebarColumnId("grouped-kpis");
      setIsPeriodHeaderSidebarOpen(true);
    };
    return { checkedStandardKpiCount, showGroupSnackbar, groupSnackbarEnabled, handleGroupKpis };
  }

  // --- Extracted function to handle grouping logic ---
  function groupCheckedKpiColumns({
    checkedStandardKpis,
    sidebarData,
    periods,
    data,
    getFlattenedColumns,
    setPeriods,
    setCurrentTableData,
    setCheckedStandardKpis,
    setGroupedKpiOrder,
    onGroupKpiColumns,
    onGroupKpiData
  }) {
    // Get indices of checked columns (those that are selected by the user)
    const checkedIndices = Object.keys(checkedStandardKpis)
      .filter((key) => checkedStandardKpis[key])
      .map(Number);

    // Flatten the columns structure for easier access to period/column info
    const flatColumns = getFlattenedColumns();

    // Gather the selected KPIs for the checked columns
    const groupedSelectedKpis = checkedIndices.map((colIdx) => {
      const col = flatColumns[colIdx];
      return col.period.selectedKpis[col.colIndex];
    });
    // Gather the document KPIs for the checked columns
    const groupedDocumentKpis = checkedIndices.map((colIdx) => {
      const col = flatColumns[colIdx];
      return col.period.documentKpis ? col.period.documentKpis[col.colIndex] : undefined;
    });

    // Build the new period label based on sidebar data (Data_Type, Period_Type, etc.)
    let newLabel = '';
    if (sidebarData.Data_Type) {
      newLabel = `(${sidebarData.Data_Type}) `;
      if (sidebarData.Period_Type === 'monthly' && sidebarData.Period_Month !== undefined) {
        newLabel += `${getMonthText(sidebarData.Period_Month)} `;
      } else if (sidebarData.Period_Type === 'quarterly' && sidebarData.Period_Quarter) {
        newLabel += `${sidebarData.Period_Quarter} `;
      }
      if (sidebarData.Period_Year) {
        newLabel += sidebarData.Period_Year;
      }
    }
    newLabel = newLabel.trim() || 'Grouped';

    // Check if a period with the same label already exists
    let targetPeriodIndex = periods.findIndex(p => (p.label || '').trim() === newLabel);
    let newPeriods;
    let newPeriodId;
    if (targetPeriodIndex !== -1) {
      // If period exists, merge selected KPIs and document KPIs into it
      newPeriodId = periods[targetPeriodIndex].periodId;
      const mergedSelectedKpis = [
        ...periods[targetPeriodIndex].selectedKpis,
        ...groupedSelectedKpis
      ];
      const mergedDocumentKpis = [
        ...(periods[targetPeriodIndex].documentKpis || []),
        ...groupedDocumentKpis
      ];
      // Update periods: merge into target, remove grouped KPIs from others
      const updatedPeriods = periods.map((period, pIdx) => {
        if (pIdx === targetPeriodIndex) {
          return {
            ...period,
            selectedKpis: mergedSelectedKpis,
            documentKpis: mergedDocumentKpis,
            columns: mergedSelectedKpis.length
          };
        }
        // Remove grouped KPIs from other periods
        const newSelectedKpis = (period.selectedKpis || []).filter((kpi, kIdx) => {
          return !checkedIndices.some((colIdx) => {
            const col = flatColumns[colIdx];
            return col.periodIndex === pIdx && col.colIndex === kIdx;
          });
        });
        const newDocumentKpis = (period.documentKpis || []).filter((kpi, kIdx) => {
          return !checkedIndices.some((colIdx) => {
            const col = flatColumns[colIdx];
            return col.periodIndex === pIdx && col.colIndex === kIdx;
          });
        });
        return { ...period, selectedKpis: newSelectedKpis, documentKpis: newDocumentKpis, columns: newSelectedKpis.length };
      });
      // Remove periods with no KPIs left
      newPeriods = updatedPeriods.filter((p) => (p.selectedKpis || []).length > 0);
    } else {
      // If period does not exist, create a new period with grouped KPIs
      newPeriodId = generateUUID();
      const newPeriod = {
        label: newLabel,
        columns: groupedSelectedKpis.length,
        selectedKpis: groupedSelectedKpis,
        documentKpis: groupedDocumentKpis,
        periodId: newPeriodId,
      };
      // Remove grouped KPIs from their original periods
      const updatedPeriods = periods.map((period, pIdx) => {
        const newSelectedKpis = (period.selectedKpis || []).filter((kpi, kIdx) => {
          return !checkedIndices.some((colIdx) => {
            const col = flatColumns[colIdx];
            return col.periodIndex === pIdx && col.colIndex === kIdx;
          });
        });
        const newDocumentKpis = (period.documentKpis || []).filter((kpi, kIdx) => {
          return !checkedIndices.some((colIdx) => {
            const col = flatColumns[colIdx];
            return col.periodIndex === pIdx && col.colIndex === kIdx;
          });
        });
        return { ...period, selectedKpis: newSelectedKpis, documentKpis: newDocumentKpis, columns: newSelectedKpis.length };
      });
      // Add the new period at the start, keep only periods with KPIs
      const nonEmptyPeriods = updatedPeriods.filter((p) => (p.selectedKpis || []).length > 0);
      newPeriods = [newPeriod, ...nonEmptyPeriods];
    }

    // Update the data rows to move values to the new period
    const updatedData = data.map((row) => {
      const newValues = { ...row.values };
      groupedSelectedKpis.forEach((kpi, idx) => {
        const colIdx = checkedIndices[idx];
        const col = flatColumns[colIdx];
        const oldPeriodId = col.period.periodId;
        const oldKpiId = col.period.selectedKpis[col.colIndex]?.kpiId;
        const oldKey = oldPeriodId && oldKpiId ? `${oldPeriodId}_${oldKpiId}` : null;
        if (oldKey && newValues[oldKey] !== undefined) {
          const newKey = `${newPeriodId}_${kpi.kpiId}`;
          newValues[newKey] = newValues[oldKey];
          delete newValues[oldKey];
        }
      });
      return { ...row, values: newValues };
    });

    // Update periods and data in state or via callback
    if (typeof setPeriods === 'function') {
      setPeriods(newPeriods);
    } else if (typeof onGroupKpiColumns === 'function') {
      onGroupKpiColumns(newPeriods);
    }
    if (typeof setCurrentTableData === 'function') {
      setCurrentTableData(updatedData);
    } else if (typeof onGroupKpiData === 'function') {
      onGroupKpiData(updatedData);
    }
    // Reset checked KPIs and grouped order
    setCheckedStandardKpis({});
    setGroupedKpiOrder(null);
  }
  // --- End of custom hook and grouping function ---

  const [fileDropdownOpen, setFileDropdownOpen] = useState(false);

  const getFileIcon = (fileName) => {
    if (fileName?.toLowerCase().endsWith('.pdf')) return PdfIcon;
    if (fileName?.toLowerCase().endsWith('.xls') || fileName?.toLowerCase().endsWith('.xlsx') || fileName?.toLowerCase().endsWith('.excl') || fileName?.toLowerCase().endsWith('.excl')) return ExcelIcon;
    return PdfIcon;
  };

  const {
    checkedStandardKpiCount,
    showGroupSnackbar,
    groupSnackbarEnabled,
    handleGroupKpis
  } = useGroupKpiSnackbar(
    checkedStandardKpis,
    setIsPeriodHeaderSidebarOpen,
    setSidebarInitialValues,
    setSidebarColumnId,
    setColumnDetails,
    flatColumns
  );
  // --- End of custom hook ---

  // Define this above your return (or as a separate component)
const NoFileFound = (
  <div className="flex flex-col items-center justify-center h-full w-[800px] min-w-[800px] overflow-auto">
    <EmptySearchImages />
    <span className="body-r pt-4 text-neutral-60">
      Value was not found in the documents.
    </span>
    <span className="body-r pt-4 text-neutral-60">
      Please preview the documents and map the value, if present.
    </span>
  </div>
);

  return (
    <div className="flex flex-row w-full relative h-[calc(100vh-148px)]">
      {/* Table Container - Adjusts width based on PDF or Excel visibility */}
      <div
         style={{overflowX: "auto", position: 'relative' }}
          className={`${showPdfViewer || showExcelViewer ? "w-1/2" : "w-full"
            }  transition-width duration-300`}
        >
        {(!data || data.length === 0) ? (
          // Empty state when no data is available
          <div className="h-full w-full flex flex-col items-center justify-center">
            <EmptySearchImages />
            <p className="body-r text-neutral-60 mt-2">No data found!</p>
          </div>
        ) : (          // Table with data
          <table className="w-full border-collapse relative" style={{ minWidth: 'max(1200px, 100%)', }}>
            <thead>              {/* Row 1: Time periods */}
              <tr className="border-b">
                {/* Empty cell for the first column - sticky */}
                <th className="sticky left-0 top-0 z-50 p-2 border-r bg-white text-right align-right"
                    style={{ height: `${ROW_HEIGHTS.ROW_1}px`, width: '300px', minWidth: '300px', maxWidth: '300px' }}>
                  <span className="body-m text-neutral-80">Time- Period :</span>
                </th>

                {/* Time period headers */}
                {periods.map((period, index) => {
                  const isPeriodNull = !period.label || period.label === "null" || period.label === undefined;
                  const isHovered = hoveredColumnIndex === index;

                  return (
                    <th
                      key={`period-${index}`}
                      colSpan={renderColSpan(period)}
                      className="sticky top-0 z-40 border-r px-3 bg-primary-35 text-left text-neutral-80"
                      style={{
                        minWidth: `${renderColSpan(period) * 300}px`,
                        height: `${ROW_HEIGHTS.ROW_1}px`
                      }}
                      onMouseEnter={() => setHoveredColumnIndex(index)}
                      onMouseLeave={() => setHoveredColumnIndex(null)}
                    >
                      <div className="flex flex-row w-full h-full">
                        <div className="flex items-center justify-center w-[64px] pr-2">
                          {/* Flag icon when period is null */}
                          {(validatePeriod(period.label)) &&
                            <div className="flex items-center mr-2">
                              <div className="w-8 h-8 bg-noticeable-50 rounded-full flex items-center justify-center">
                                <FaRegFlag className="w-6 text-negative-80" />
                              </div>
                            </div>
                          }
                          {/* Checkbox appears on hover or when it's checked */}
                          {/* {(isHovered || checkedColumns[index]) &&  */}
                          <Checkbox
                            className="cursor-pointer"
                            checked={!!checkedColumns[index]}
                            onChange={(e) => {
                              // Update checked columns state
                              setCheckedColumns({
                                ...checkedColumns,
                                [index]: e.value
                              });

                              if (e.value) {
                                // When checkbox is checked, open the column popup
                                setEditingColumnIds([`period-${index}`]);

                                // Set column details
                                setColumnDetails({
                                  Filing_Type: "",
                                  Filing_Version: "",
                                  Filing_Date: period.label || null,
                                  Period_Type: "",
                                  Period_Month: "",
                                  Period_Quarter: "",
                                  Period_Year: "",
                                  Data_Type: ""
                                });

                                // Open the popup
                                setIsColumnPopupOpen(true);
                              } else {
                                // When unchecked, close the popup
                                setEditingColumnIds([]);
                                setIsColumnPopupOpen(false);
                              }
                            }}
                          />
                          {/*  } */}
                        </div>

                        <div className="flex flex-col items-start justify-center w-[calc(100% - 64px)] py-2 px-2">
                          <span className="body-m text-neutral-80">
                            {isPeriodNull ? "(No Period)" : period.label}
                          </span>
                        </div>
                      </div>
                    </th>
                  );
                })}
              </tr>
              {/* Row 2: KPI Selection Combo Boxes */}
              <tr className="border-b">
                {/* Label for KPI selectors - sticky */}
                <th className="sticky left-0 top-[48px] z-50 px-3 py-1 border-r bg-neutral-50 text-left align-left"
                    style={{ height: `${ROW_HEIGHTS.ROW_2}px`, width: '300px', minWidth: '300px', maxWidth: '300px' }}>
                  <span className="body-r text-neutral-60">Standard KPI</span>
                </th>
                {/* KPI Selection boxes for each period */}
                {flatColumns.map((col, index) => {
                  const isStandardKpiHovered = hoveredStandardKpiIndex === index;
                  const isStandardKpiChecked = checkedStandardKpis[index];
                  return (
                    <td
                      key={`kpi-selector-${index}`}
                      className="sticky top-[48px] z-30 border-r px-3 py-1 bg-neutral-50"
                      style={{ minWidth: "300px", height: `${ROW_HEIGHTS.ROW_2}px` }}
                      onMouseEnter={() => setHoveredStandardKpiIndex(index)}
                      onMouseLeave={() => setHoveredStandardKpiIndex(null)}
                    >
                      <div className="flex items-center w-full">
                        {/* Checkbox appears on hover or when checked */}
                        {/* {(isStandardKpiHovered || isStandardKpiChecked) && ( */}
                        <div className="mr-2">
                          <Checkbox
                            className="cursor-pointer"
                            checked={!!checkedStandardKpis[index]}
                            disabled={col.period.selectedKpis?.[col.colIndex]?.kpiId === undefined}
                            onChange={(e) => {
                              setCheckedStandardKpis({
                                ...checkedStandardKpis,
                                [index]: e.value
                              });
                              if (e.value)
                                setCheckedindex(prev => [...prev, index]);
                            }}
                          />
                        </div>
                        {/* )} */}
                        <div className="flex-grow">
                          <ComboBox
                            clearButton={false}
                            data={kpiOptions}
                            textField="text"
                            dataItemKey="value"
                            value={col.period.selectedKpis?.[col.colIndex] || null}
                            onChange={(e) => onKpiChange(col.periodIndex, col.colIndex, e.value)}
                            placeholder="Select KPI"
                            className="w-full py-1 px-2 border-0 rounded-md focus:outline-none custom-s-combo-box body-r"
                            filterable={true}
                          />
                        </div>
                        {col.period.selectedKpis?.[col.colIndex]?.kpiInfo && (
                          <span className="text-neutral-60 flex-shrink-0 combo-kpi-info">
                            {getKpiIndicator(col.period.selectedKpis?.[col.colIndex]?.kpiInfo)}
                          </span>
                        )}
                      </div>
                    </td>
                  );
                })}
              </tr>
              {/* Row 3: KPI Values */}
              {showHeader && (
                <tr className="border-b">
                  {/* Label for KPI values - sticky */}
                  <th className="sticky left-0 top-[84px] z-50 px-3 py-1 border-r bg-neutral-50 text-left align-left"
                      style={{ height: `${ROW_HEIGHTS.ROW_3}px`, width: '300px', minWidth: '300px', maxWidth: '300px' }}>
                    <div className="flex items-center">
                      <span className="body-r text-neutral-60">Document KPI</span>
                    </div>
                  </th>                    {/* KPI values for each period */}                  {flatColumns.map((col, index) => {
                    // Get the selected KPI for this column
                    let documentKpi = col.period.documentKpis?.[col.colIndex];
                    const isDocumentKpiHovered = hoveredDocumentKpiIndex === index;
                    const isDocumentKpiChecked = checkedDocumentKpis[index];

                    return (<td
                      key={`kpi-value-${index}`}
                      className="sticky body-r py-1 px-3 top-[84px] z-30 border-r text-left align-left bg-neutral-50"
                      style={{ minWidth: "300px", height: `${ROW_HEIGHTS.ROW_3}px` }}
                      onMouseEnter={() => setHoveredDocumentKpiIndex(index)}
                      onMouseLeave={() => setHoveredDocumentKpiIndex(null)}
                    >
                      <div className="flex items-center w-full">
                        {/* Checkbox appears on hover or when checked */}
                        {/* {(isDocumentKpiHovered || isDocumentKpiChecked) &&  */}
                        <div className="mr-2">
                          <Checkbox
                            className="cursor-pointer"
                            checked={!!checkedDocumentKpis[index]}
                            onChange={(e) => {
                              // Update checked Document KPI columns state
                              setCheckedDocumentKpis({
                                ...checkedDocumentKpis,
                                [index]: e.value
                              });
                            }}
                          />
                        </div>
                        {/*  } */}
                        <span className="text-neutral-80 body-r flex-1">
                          {documentKpi ? documentKpi.text : "(No KPI)"}
                        </span>
                      </div>
                    </td>
                    );
                  })}
                </tr>
              )}
            </thead>

            <tbody>              {/* Table data rows */}
              {data.map((row, rowIndex) => (
                <tr
                  key={`row-${rowIndex}`}
                  className={`border-b ${selectedCompanies.includes(row.companyId) ? 'bg-primary-10' : ''}`}
                  // Removed onClick handler to prevent row click from selecting companies
                >
                  {/* Row label - sticky */}
                  <td className="sticky left-0 z-20 p-3 border-r bg-neutral-5"
                      style={{ boxShadow: '2px 0 5px rgba(0,0,0,0.05)', width: '300px', minWidth: '300px', maxWidth: '300px' }}>
                    <div className="flex items-center">
                      <Checkbox
                        className="mr-2"
                        checked={selectedCompanies.includes(row.companyId)}
                        onChange={(e) => handleCompanyCheckboxChange(row.companyId, e.value)}
                        data-testid={`company-checkbox-${row.companyId}`}
                        onClick={(e) => e.stopPropagation()} // Prevent row click when checkbox is clicked
                      />
                      <div className="flex items-center justify-between w-full pr-2">
                        <div 
                          className="truncate max-w-[90%] cursor-pointer" 
                          title={row.label}
                          onClick={(e) => {
                            e.stopPropagation();
                            const isCurrentlySelected = selectedCompanies.includes(row.companyId);
                            handleCompanyCheckboxChange(row.companyId, !isCurrentlySelected);
                          }}
                        >
                          <span className="text-neutral-80 body-m">{row.label}</span>
                        </div>
                        <div 
                          className="flex items-center justify-center w-6 h-6 rounded-full hover:bg-neutral-10 cursor-pointer transition-colors" 
                          title={row.currencyCode ? `Currency: ${row.currencyCode}${row.unit ? ` | Unit: ${row.unit}` : ''}` : "No currency code set"}
                        >
                          <BsCash className={!row.currencyCode ? "text-negative-100" : "text-primary-80"} />
                        </div>
                      </div>
                    </div>
                  </td>
                  {/* Row data cells */}
                  {flatColumns.map((col, colIndex) => {
                    // Get the period and KPI IDs for this cell
                    const periodId = col.period.periodId;
                    const kpiId = col.period.documentKpis?.[col.colIndex]?.kpiId;

                    // Create the key for accessing the object-based values
                    const valueKey = periodId && kpiId ? `${periodId}_${kpiId}` : null;
                    const periodIndex = col.periodIndex;

                    // Determine if this cell is being edited
                    const isEditing =
                      editCell.rowIndex === rowIndex &&
                      editCell.colIndex === colIndex &&
                      editCell.periodIndex === col.periodIndex;
                    col.isEditing = isEditing;

                    // Get the cell value object using the key
                    const cellValueObj =
                      valueKey && row.values?.[valueKey] !== undefined
                        ? row.values[valueKey]
                        : "No value found";

                    // Handle both object-based values and legacy string/number values
                    const isValueObject = cellValueObj !== "-" && typeof cellValueObj === "object";
                    const displayValue = isValueObject ? cellValueObj.value : cellValueObj;

                    // Get kpiInfo and unit from the object if available
                    const kpiInfo = isValueObject ? cellValueObj.kpiInfo : null;
                    const unit = isValueObject ? cellValueObj.unit : null;
                    const value = isValueObject ? cellValueObj.value : displayValue;

                    let existingValueObj = {};

  const handleClick = (e) => {
    setPdfData(null);
    setSelectedCellInfo(null);
    // Clear Excel data as well
    setExcelData(null);
    setSelectedCellInfoExcel(null);
    
    if (
      e &&
      e.target &&
      e.target.classList &&
      e.target.classList.contains("k-checkbox-input")
    ) {
      // If clicking on checkbox, don't start cell editing
      return;
    }
    col.isEditing = true;
    setPeriodId(periodId);
    setKpiId(kpiId);
    setOriginalCellValue(value);
    setEditCell({
      rowIndex,
      colIndex,
      periodIndex,
      value
    });
    handleCellClick(rowIndex, colIndex, periodIndex, cellValueObj);
  };return (
  <td
    key={`cell-${rowIndex}-${colIndex}`}
    className={`body-r border-r p-2 text-right text-neutral-90 ${
      !isEditing ? "cursor-pointer hover:bg-neutral-10" : ""
    } ${col.isEditing ? "editing-cell" : "p-3"}`}
    style={{
      minWidth: "180px",
      border: col.isEditing ? "3px solid #EDDA70" : "1px solid #EDEDF2",
      left: 0,
      cursor: "pointer",
      whiteSpace: "nowrap",
      textOverflow: "ellipsis",
      padding: isEditing ? "0" : undefined
    }}
    onClick={handleClick}
  >
    {col.isEditing ? (
      <input
        type="text"
        className="w-full h-full border-none focus:outline-none text-right"
        value={editCell.value}
        onChange={handleCellChange}
        
        onKeyDown={handleKeyPress}
        autoFocus
      />
    ) : (
      <div className="flex justify-end items-center">
        {/* {kpiInfo && <span className="text-xs text-neutral-60 mr-1">{getKpiIndicator(kpiInfo)}</span>} */}
        <span>{displayValue}</span>
      </div>
    )}
  </td>
);
})}
        </tr>
      ))}
    </tbody>
  </table>
)}
</div>

      {/* Viewer Section - Displayed next to the table when a cell is clicked */}
      {(showPdfViewer || showExcelViewer) && (
  <div className="w-1/2 border-l border-neutral-20 flex flex-col bg-white h-[calc(100vh-148px)] transition-width duration-300">
    {/* Common File Dropdown */}
    <div className="flex items-center justify-between px-4 py-2 bg-[#fafafa] border-b border-neutral-20">
      <div className="flex items-center gap-1 flex-grow">
        {files && files.length > 0 && (
          <div className="relative w-full px-4 border-neutral-20 z-10">
            <div
              className="flex items-center w-full border rounded px-3 py-2 body-r cursor-pointer bg-white"
              onClick={() => setFileDropdownOpen((open) => !open)}
              tabIndex={0}
            >
              <span className="flex items-center gap-2 text-neutral-60">
                {!selectedViewerFile ? (
                  <><FaFile className="text-neutral-60 w-3 h-3" /> Select the file</>
                ) : (
                    <>
                      <img src={getFileIcon(selectedViewerFile.fileName)} alt="icon" className="w-[18px] h-[18px]" />
                      {selectedViewerFile.fileName}
                    </>
                  )}
              </span>
              <svg className="ml-auto" width="18" height="18" viewBox="0 0 20 20" fill="none">
                <path d="M6 8L10 12L14 8" stroke="#888" strokeWidth="2" strokeLinecap="round" />
              </svg>
            </div>
            {fileDropdownOpen && (
              <div className="absolute left-4 right-4 mt-1 bg-white border rounded shadow-lg z-20 max-h-60 overflow-auto"
                onMouseDown={e => e.preventDefault()}>
                {files.map((file) => (
                  <div
                    key={file.fileId}
                    className={`flex items-center gap-2 px-4 py-2 cursor-pointer hover:bg-neutral-10 ${
                    selectedViewerFile?.fileId === file.fileId ? "bg-neutral-10" : ""
                  }`}
                    onClick={() => {
                      setFileDropdownOpen(false);
                      setSelectedViewerFile(file);
                      let tempCellValue = cellValue || {};

                      if (file.fileName.toLowerCase().endsWith('.pdf')) {
                        setSelectedViewerFile(file);
                        setSelectedCellInfo(selectedCellInfo);
                        setSelectedCellInfoExcel(null);                        
                        if(typeof tempCellValue === 'object'){
                          tempCellValue.source = file.fileId;
                          setOriginalCellValue(tempCellValue.value);
                        } else{
                          tempCellValue = {
                            source: file.fileId,
                            fileType: 'pdf',
                          }
                        }  
                        setCellValue(tempCellValue);  
                        handleCellClickForPdf(editCell.rowIndex, editCell.colIndex, editCell.periodIndex, tempCellValue) ; 
                      } else if (
                        file.fileName.toLowerCase().endsWith('.xls') ||
                        file.fileName.toLowerCase().endsWith('.xlsx')
                      ) {
                        setSelectedViewerFile(file);
                        setSelectedCellInfoExcel(selectedCellInfoExcel);
                        setSelectedCellInfo(null);
                        if(typeof tempCellValue === 'object'){
                          tempCellValue.source = file.fileId;
                          setOriginalCellValue(tempCellValue.value);
                        }else{
                          tempCellValue = {
                            source: file.fileId,
                            fileType: 'excel',
                          }
                        } 
                        setCellValue(tempCellValue);
                        handleCellClickExcel(editCell.rowIndex, editCell.colIndex, editCell.periodIndex, tempCellValue);
                      }
                    }}
                  >
                    <img src={getFileIcon(file.fileName)} alt="icon" className="w-[18px] h-[18px]" />
                    <span>{file.fileName}</span>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </div>
      <button
        onClick={() => {
          setSelectedCellInfo(null);
          setSelectedCellInfoExcel(null);
          setSelectedViewerFile(null);
          setShowExcelViewer(false);
          setShowPdfViewer(false);
          setIsExcelLoading(false);
        }}
        className="p-1.5 rounded hover:bg-neutral-10"
      >
        <svg width="12" height="12" viewBox="0 0 12 12" className="text-neutral-80">
          <path d="M2.5 2.5L9.5 9.5M2.5 9.5L9.5 2.5" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" />
        </svg>
      </button>
    </div>
    {(!selectedViewerFile && !selectedCellInfo && !selectedCellInfoExcel) && NoFileFound && (
      <div className="flex-1 flex items-center justify-center">
        {NoFileFound}
      </div>
    )} 
    {/* PDF Viewer */}
    {selectedViewerFile && selectedCellInfo && selectedViewerFile.file && (
      <div className="flex-1 relative">
        <PdfHolder
          beforeLoad={
            <div className="flex flex-col items-center justify-center h-full w-[800px] min-w-[800px]  overflow-auto">
              <SpinnerCircle />
              <span className="body-r pt-4 text-neutral-60">
                Processing PDF document: {selectedViewerFile?.fileName || "Unknown"}
              </span>
            </div>
          }
          base64data={selectedViewerFile.file}
          url={selectedViewerFile.file || ''}
          pdfDocument={pdfDocument}
          setPdfDocument={setPdfDocument}
        >
          {(pdfDocument) => (
            <PdfHighlighter
              ref={pdfHighlighterRef}
              pdfDocument={pdfDocument}
              highlights={highlights}
              onSelectionFinished={(position, content, hideTipAndSelection) => {
                if (editCell)
                  return captureHighlight(position, content);
              }}
              pdfScaleValue="page-width"
              enableAreaSelection={(event) => event.altKey}
              scrollRef={(scrollTo) => {
                if (scrollTo) {
                  pdfHighlighterRef.current = { scrollTo };
                }
              }}
              onScrollChange={() => { }}
              highlightTransform={(
                highlight,
                index,
                setTip,
                hideTip,
                viewportToScaled,
                screenshot,
                isScrolledTo
              ) => {
                const isTextHighlight = !highlight.content?.image;
                const component = isTextHighlight ? (
                  <Highlight
                    position={highlight.position}
                    comment={highlight.comment}
                    isScrolledTo={isScrolledTo}
                    onClick={() => {
                      console.log("Highlight clicked:", highlight);
                    }}
                  />
                ) : (
                  <AreaHighlight
                    isScrolledTo={isScrolledTo}
                    highlight={highlight}
                    onChange={(boundingRect) => {
                      updateHighlight(
                        highlight.id,
                        {
                          boundingRect: viewportToScaled(boundingRect)
                        },
                        { image: screenshot(boundingRect) }
                      );
                    }}
                  />
                );
                return (
                  <Popup
                    popupContent={
                      <div className="Highlight__popup">
                        {highlight.comment?.emoji} {highlight.comment?.text}
                      </div>
                    }
                    onMouseOver={(popupContent) =>
                      setTip(highlight, () => popupContent)
                    }
                    onMouseOut={hideTip}
                    key={index}
                  >
                    {component}
                  </Popup>
                );
              }}
            />
          )}
        </PdfHolder>
      </div>
    )}
    {/* Excel Viewer */}
    {selectedViewerFile && selectedCellInfoExcel && (
      <div className="flex-1 relative">
        {(() => {
          const foundFile = files?.find(file => file?.fileId === selectedCellInfoExcel?.value?.source);
          const s3Path = foundFile?.s3_path;
          return (
            <ExcelPreview
              excelData={selectedViewerFile}
              selectedCellInfo={selectedCellInfoExcel}
              excelHighlight={selectedCellInfoExcel?.excelHighlight}
              onClose={() => {
                setExcelData(null);
                setSelectedCellInfoExcel(null);
                setIsExcelLoading(false);
              }}
              isLoading={isExcelLoading}
              s3FilePath={selectedViewerFile.url}
            />
          );
        })()}
      </div>
    )}
  </div>
)}
      {/* Column Edit Popup */}
      <PopupcolumnComponent
        isCheckboxHeader={isColumnPopupOpen}
        editingColumnKey={editingColumnIds.length === 1 ? editingColumnIds[0] : undefined}
        editingColumnIds={editingColumnIds}
        onSave={handleColumnPopupClose}
        checkedColumnHeaders={{}}
        columnDetails={columnDetails}
        kpiConfig={kpiConfig}
        totalColumns={periods.length}
        selectedTab={selectedTabId}
        extractionType={extractionType}
      />
      {/* --- Snackbar logic for Group KPIs by Time Period --- */}
      {showGroupSnackbar && (
        <GroupKpiSnackbar
          enabled={groupSnackbarEnabled}
          count={checkedStandardKpiCount}
          onClick={handleGroupKpis}
          onDelete={() => {
            setShowDeleteConfirmPopUp(true);
          }}
        />

      )}
              <PopUp
                header={"Attention!"}
                showPopUp={showDeleteConfirmPopUp}
                cancelButtonText={"Cancel"}
                submitButtonText={"Confirm"}
                disableOutsideClick={true}
                onSubmit={() => {
                  setShowDeleteConfirmPopUp(false);

          // Collect all columns to delete
          const indicesToDelete = [...checkedindex];
          const columnsToDelete = indicesToDelete.map(idx => flatColumns[idx]);

          // Remove selectedKpis and documentKpis from periods
          const updatedPeriods = periods.map((period, pIdx) => {
            // Remove all selected columns for this period
            const newSelectedKpis = (period.selectedKpis || []).filter((_, kIdx) => {
              // Only keep if not in columnsToDelete for this period/col
              return !columnsToDelete.some(col => col.periodIndex === pIdx && col.colIndex === kIdx);
            });
            const newDocumentKpis = (period.documentKpis || []).filter((_, kIdx) => {
              return !columnsToDelete.some(col => col.periodIndex === pIdx && col.colIndex === kIdx);
            });
            return {
              ...period,
              selectedKpis: newSelectedKpis,
              documentKpis: newDocumentKpis,
              columns: newSelectedKpis.length
            };
          }).filter(period => (period.selectedKpis || []).length > 0);

          // Remove the column's values from each row in the table data
          const updatedData = data.map(row => {
            const newValues = { ...row.values };
            columnsToDelete.forEach(col => {
              const periodId = col.period.periodId;
              const kpiId = col.period.selectedKpis?.[col.colIndex]?.kpiId;
              const valueKey = periodId && kpiId ? `${periodId}_${kpiId}` : null;
              if (valueKey && newValues[valueKey] !== undefined) {
                delete newValues[valueKey];
              }
            });
            return { ...row, values: newValues };
          });

          setPeriods(updatedPeriods);
          setCurrentTableData(updatedData);
          setCheckedStandardKpis([]);
          setCheckedDocumentKpis([]);
          setCheckedindex([]);
          setIsDeleted(!isDeleted);

        }}
        cols={"col-span-4 col-start-5"}

        colGrid={"grid-cols-12"}
        setShowPopUp={onCancel}
      >
        <div className="p-6 py-4">
          <div className="my-3">Column once deleted cannot be restored</div>
        </div>
      </PopUp>
      <PeriodHeaderSidebar
        key={JSON.stringify(sidebarInitialValues) + sidebarColumnId}
        isOpen={isPeriodHeaderSidebarOpen}
        onClose={() => setIsPeriodHeaderSidebarOpen(false)}
        onSave={(sidebarData) => {
          setIsPeriodHeaderSidebarOpen(false);
          groupCheckedKpiColumns({
            checkedStandardKpis,
            sidebarData,
            periods,
            data,
            getFlattenedColumns,
            setPeriods,
            setCurrentTableData,
            setCheckedStandardKpis,
            setGroupedKpiOrder,
            onGroupKpiColumns,
            onGroupKpiData
          });
        }}
        initialValues={sidebarInitialValues}
        columnId={sidebarColumnId}
        selectedTab={selectedTabId}
        kpiConfig={kpiConfig}

      />
    </div>
  );
};

MultiColumnHeaderTable.propTypes = {
  /**
   * Array of time periods with their configurations
   * Each period can have:
   * - label: The display text (e.g. "Q1 2024")
   * - columns: Number of KPI columns for this period
   * - selectedKpis: Array of selected KPI objects for this period
   */  periods: PropTypes.arrayOf(
  PropTypes.shape({
    label: PropTypes.string.isRequired,
    columns: PropTypes.number,
    selectedKpis: PropTypes.array,
    periodId: PropTypes.string
  })
),

  /**
   * Available KPI options for the dropdown
   */
  kpiOptions: PropTypes.arrayOf(
    PropTypes.shape({
      text: PropTypes.string.isRequired,
      value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired
    })
  ),

  /**
   * Callback when KPI selection changes
   * @param {number} periodIndex - Index of the period
   * @param {number} columnIndex - Column index within the period
   * @param {Object} selectedKpi - The selected KPI option
   */
  onKpiChange: PropTypes.func,

  /**
   * Whether to show the KPI value headers
   */
  showHeader: PropTypes.bool,

  /**
   * Data rows to display in the table
   * Each row should have:
   * - label: Row label
   * - values: 2D array of values [periodIndex][columnIndex]
   */
  data: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string,
      values: PropTypes.array
    })
  ),

  /**
   * Callback when a cell value changes
   * @param {number} rowIndex - Index of the row
   * @param {number} periodIndex - Index of the period
   * @param {number} columnIndex - Column index within the period
   * @param {any} newValue - The new value for the cell
   */
  onCellValueChange: PropTypes.func,

  /**
   * PDF data to display in the PDF viewer
   */
  pdfData: PropTypes.object,

  /**
   * Callback when a cell is clicked
   * @param {number} rowIndex - Index of the row
   * @param {number} columnIndex - Column index within the period
   * @param {number} periodIndex - Index of the period
   * @param {any} value - The value of the cell
   */
  onCellClick: PropTypes.func,

  /**
   * Callback when a company is selected or deselected
   * @param {string} companyId - The ID of the company
   * @param {boolean} isChecked - Whether the company is selected
   */
  onCompanySelection: PropTypes.func,

  /**
   * Array of selected companies
   */
  selectedCompanies: PropTypes.arrayOf(PropTypes.string),

  setPeriods: PropTypes.func,
  setCurrentTableData: PropTypes.func,
  extractionType: PropTypes.string,
};

export default MultiColumnHeaderTable;
