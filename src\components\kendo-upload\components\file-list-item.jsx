import React from "react";
import PropTypes from "prop-types";
import { FiFile } from "react-icons/fi";
import { BsExclamationCircleFill } from "react-icons/bs";
import { HiTrash } from "react-icons/hi";
import { DatePicker } from "@progress/kendo-react-dateinputs";
import { ButtonIcon } from "../../../partials/atoms/button";
import { ToolTip } from "../../../partials/atoms/tool-tip";
import { FileStatusIndicator } from './file-status-indicator';
import { DropDownList } from '@progress/kendo-react-dropdowns';

export const FileListItem = ({
  file,
  handleDeleteFile,
  handleDateChange, 
  handleFilingTypeSelection,
  selectedFilingType
}) => {
  const dropdownPlaceholder = "Select Filing Type";
  const dropdownItems = ["ANL","HYL", "NME", "QTR", ];
  const getSelectedValue = (id) => {
    const selected = selectedFilingType.find((entry) => entry.id === id);
    return selected ? selected.item : dropdownPlaceholder
  };
  return (
    <div
      key={file.uid}
      className="flex justify-between items-center w-full border-neutral-10"
    >
      <div className="flex flex-col w-full h-full p-2 border-r border-neutral-10 ml-2">
        <div className="flex items-center mb-1">
          <FiFile className="w-3.5 h-3.5 text-gray-400 mr-2" />
          <span
            className="text-base text-(--neutral-gray-90) truncate w-full"
            title={file.name}
          >
            {file.name}
          </span>
          {(file.progress === 100 || file.errorMessage) && (
            <ButtonIcon
              className={"w-3 h-3 justify-items-center"}
              data-testid="delete-icon-button"
              intent={"teritorynegative"}
              data-tooltip-id={`tool-tip-delete-file-${file.uid}`}
              onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
                handleDeleteFile(file.uid);
              }}
            >
              <HiTrash className="h-4 w-4" />
              <ToolTip
                place={"left"}
                text={"Delete File"}
                toolTipId={`tool-tip-delete-file-${file.uid}`}
              />
            </ButtonIcon>
          )}
        </div>
        <FileStatusIndicator file={file} />     
        <div className="w-full flex flex-row py-2 gap-5">
          <div className="w-3/5 flex flex-col">
            <span className="text-caption-r pb-[2px]">Filing Date</span>
            <DatePicker
              placeholder="Select filing date"
              max={new Date()}
              format={"dd-MM-yyyy"}
              disabled={file.errorMessage || file.progress < 100}
              className={
                file.errorMessage || file.progress < 100
            ? "ao-disabled-datepicker h-[2rem]"
            : "h-[2rem]"
              }
              onChange={(e) => handleDateChange(e, file.uid)}
              value={file.filingDate}
            />
            {file.dateError && (
              <label className="mt-1">
                <div className="text-red-500 text-xs flex items-center">
            <BsExclamationCircleFill className="mr-1 text-red-500" />
            {file.dateError}
                </div>
              </label>
            )}{" "}
          </div> 
          <div className="w-3/5 flex flex-col">
            <span className="text-caption-r pb-[2px]">Filing Type</span>
            <DropDownList
              placeholder={dropdownPlaceholder}
              data={dropdownItems}
              value={getSelectedValue(file.uid)}
              className={
                file.errorMessage || file.progress < 100
            ? "ao-disabled-datepicker h-[2rem]"
            : "h-[2rem]"
              }
              disabled={file.errorMessage || file.progress < 100}
              onChange={(e) => handleFilingTypeSelection(e.target.value, file.uid)}
              fillMode={"solid"}                                        
            />
          </div>                             
        </div>
      </div>
      </div>
  );
};

FileListItem.propTypes = {
  file: PropTypes.shape({
    uid: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    progress: PropTypes.number,
    errorMessage: PropTypes.string,
    filingDate: PropTypes.instanceOf(Date),
    dateError: PropTypes.string
  }).isRequired,
  handleDeleteFile: PropTypes.func.isRequired,
  handleDateChange: PropTypes.func.isRequired,
  handleFilingTypeSelection: PropTypes.func.isRequired,
  selectedFilingType: PropTypes.arrayOf(
     PropTypes.shape({
       id: PropTypes.string.isRequired,
       item: PropTypes.string.isRequired
     })
   ).isRequired
};
