import { notify } from "./toaster";
import toast from "react-hot-toast";

jest.mock("react-hot-toast", () => ({
  custom: jest.fn(),
  dismiss: jest.fn()
}));

describe("notify", () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  test("success calls ToastMessage with success intent", () => {
    const message = "Success message";
    notify.success(message);
    expect(toast.custom).toHaveBeenCalledWith(expect.any(Function), {
      duration: 3000
    });
  });

  test("info calls ToastMessage with info intent", () => {
    const message = "Info message";
    notify.info(message);
    expect(toast.custom).toHaveBeenCalledWith(expect.any(Function), {
      duration: 3000
    });
  });

  test("warning calls ToastMessage with warning intent", () => {
    const message = "Warning message";
    notify.warning(message);
    expect(toast.custom).toHaveBeenCalledWith(expect.any(Function), {
      duration: 3000
    });
  });

  test("error calls ToastMessage with error intent", () => {
    const message = "Error message";
    notify.error(message);
    expect(toast.custom).toHaveBeenCalledWith(expect.any(Function), {
      duration: 3000
    });
  });
});
