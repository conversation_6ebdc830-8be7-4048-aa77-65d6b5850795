import * as React from "react";
const IconSettings = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={28}
    height={28}
    viewBox="0 0 28 28"
    {...props}
  >
    <g
      id="Settings_icon"
      data-name="Settings icon"
      transform="translate(-1217 -16)"
    >
      <g
        id="icon_bg"
        data-name="icon bg"
        transform="translate(1217 16)"
        fill="#fff"
        stroke="#e6e6e6"
        strokeWidth={1}
      >
        <circle cx={14} cy={14} r={14} stroke="none" />
        <circle cx={14} cy={14} r={13.5} fill="none" />
      </g>
      <path
        id="Icon_ionic-ios-settings"
        data-name="Icon ionic-ios-settings"
        d="M15.509,10.5a1.544,1.544,0,0,1,.991-1.44,6.118,6.118,0,0,0-.741-1.784,1.565,1.565,0,0,1-.628.134A1.54,1.54,0,0,1,13.722,5.24a6.1,6.1,0,0,0-1.781-.74,1.543,1.543,0,0,1-2.881,0,6.122,6.122,0,0,0-1.784.74A1.54,1.54,0,0,1,5.866,7.409a1.514,1.514,0,0,1-.628-.134A6.254,6.254,0,0,0,4.5,9.061a1.543,1.543,0,0,1,0,2.88,6.118,6.118,0,0,0,.741,1.784A1.541,1.541,0,0,1,7.278,15.76a6.157,6.157,0,0,0,1.784.74,1.54,1.54,0,0,1,2.875,0,6.122,6.122,0,0,0,1.784-.74,1.543,1.543,0,0,1,2.034-2.034,6.154,6.154,0,0,0,.741-1.784A1.551,1.551,0,0,1,15.509,10.5Zm-4.981,2.5a2.5,2.5,0,1,1,2.5-2.5A2.5,2.5,0,0,1,10.528,12.995Z"
        transform="translate(1220.5 19.5)"
        fill="#4061c7"
      />
    </g>
  </svg>
);
export default React.memo(IconSettings);
