import React from "react";
import PropTypes from "prop-types";
import { Spin } from "@maknowledgeservices/neptune";
import "./LoadinIndicator.css";

const LoadingIndicator = ({ isLoading }) => (
  <>
    {isLoading && (
      <div className="section-loader">
        <Spin name="default" size={24} color="var(--primary-blue-78)" />
      </div>
    )}
  </>
);

LoadingIndicator.propTypes = {
  isLoading: PropTypes.bool
};

LoadingIndicator.defaultProps = {
  isLoading: false
};

export default LoadingIndicator;
