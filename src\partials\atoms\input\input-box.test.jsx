import { render, screen } from "@testing-library/react";
import InputBox from "./input-box";

describe("Component InputBox render", () => {
  it("should render InputBox ", () => {
    render(<InputBox placeholder={"Enter Text"} />);
    expect(screen.getByTestId("input-box")).toBeInTheDocument();
  });
  it("should render InputBox with lable", () => {
    render(<InputBox label={"Test"} placeholder={"Enter Text"} />);
    expect(screen.getByTestId("input-box")).toBeInTheDocument();
  });
  it("should render InputBox with error", () => {
    render(<InputBox label={"Test"} placeholder={"Enter Text"} error={true} />);
    expect(screen.getByTestId("input-box")).toBeInTheDocument();
  });
  it("should render InputBox with mandatory label", () => {
    render(
      <InputBox
        label={"Test"}
        placeholder={"Enter Text"}
        error={true}
        mandatory={true}
      />
    );
    expect(screen.getByTestId("input-box")).toBeInTheDocument();
  });
  it("should render InputBox with error and message", () => {
    render(
      <InputBox
        label={"Test"}
        placeholder={"Enter Text"}
        error={true}
        message={"test"}
      />
    );
    expect(screen.getByTestId("input-box")).toBeInTheDocument();
  });
});
