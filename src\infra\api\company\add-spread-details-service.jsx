import { post } from "../../../general/fetcher";

const CONTROLLER = "company";

export const UPLOAD_API_URL = `${CONTROLLER}/SpreadDetails`;

export const ADD_SPREAD_DETAILS_SERVICE = ({
  processId,
  jobId,
  acuityId,
  companyID,
  companyName,
  country,
  sector,
  isPublic,
  primaryExchange,
  primaryTicker,
  isTemplate
}) => {
  const body = {
    processId: processId,
    jobId: jobId,
    extractionTypeID: isTemplate
      ? "7250eba0-31e4-4e19-bc3d-496c5063c94a"
      : "46dd8198-acc7-419b-ba4c-dcf07384c28c",
    spreadingTypeID: "3fa85f64-5717-4562-b3fc-2c963f66afa6",
    statusID: "6764f962-826b-4dc6-b318-366b747e300d",
    extractionType: isTemplate ? "Template Based" : "As Reported",
    spreadingType: "Financial Spreader",
    createdOn: new Date().toISOString(),
    status: "In Progress",
    acuityId: acuityId,
    id: companyID,
    name: companyName,
    country: country,
    sector: sector[0],
    isPublic: isPublic,
    exchange: primaryExchange,
    ticker: primaryTicker
  };
  return post(UPLOAD_API_URL, body);
};
