import React, {
  useState,
  useEffect,
  forwardRef,
  useImperativeHandle
} from "react";
import { MdOutlineInfo } from "react-icons/md";
import PublicFillings from "./public-filings";
import UploadBox from "../../kendo-upload/upload-box";
import PropTypes from "prop-types";

const FinancialDocument = forwardRef(
  (
    {
      selectedCompanyDetails,
      publicfilingsref,
      uploadFilesref,
      companyID,
      processID,
      alexnaderiaUploadDetails,
      fileDetailsForAlexanderiaUpload,
      setUploadDocumentDetails,
      uploadDocumentDetails,
      setSelectedFilingsCount
    },
    ref
  ) => {
    const [selectedChipsCount, setSelectedChipsCount] = useState(0);
    const [uploadedFilesCount, setUploadedFilesCount] = useState(0);
    
    // Derived state to check if file limit is reached
    const isFileLimitReached = (selectedChipsCount + uploadedFilesCount) >= 10;

    useEffect(() => {
      setSelectedChipsCount(0);
      setUploadedFilesCount(0);
      setSelectedFilingsCount(0);
    }, [selectedCompanyDetails, setSelectedFilingsCount]);

    useImperativeHandle(ref, () => ({
      resetfinancialcompanyState() {
        setSelectedChipsCount(0);
        setUploadedFilesCount(0);
        setSelectedFilingsCount(0);
      }
    }));

    const handleSelectedChipsChange = (count) => {
      setSelectedChipsCount((prevSelectedChipsCount) => {
        const newSelectedChipsCount = count;
        setSelectedFilingsCount(newSelectedChipsCount + uploadedFilesCount);
        return newSelectedChipsCount;
      });
    };

    const handleUploadedFilesChange = (count) => {
      setUploadedFilesCount((prevUploadedFilesCount) => {
        const newUploadedFilesCount = count;
        setSelectedFilingsCount(selectedChipsCount + newUploadedFilesCount);
        return newUploadedFilesCount;
      });
    };

    return (
      <div className="overflow-y-auto lg:max-h-[calc(66vh-16px)] w-full p-5 border border-neutral-10 rounded-lg mt-[18px]">
        <div className="flex flex-col w-full gap-4">
          <div className="w-full flex flex-row justify-between">
            <div className="font-medium text-neutral-60 text-sm font-display-2-m leading-4">
              Financial Document Source{" "}
              <span className="text-negative-100">*</span>
            </div>
            <p className="text-sm font-caption-i gap-1 text-neutral-60 flex flex-row items-center">
              {selectedChipsCount + uploadedFilesCount}/10
              <span className="relative group ">
                <MdOutlineInfo className="text-[16px]" />
                <span className="top-[1.125rem] h-14 absolute z-10 w-[25rem] p-2 text-xs bg-neutral-60 text-white border border-neutral-60 rounded shadow-md opacity-0 group-hover:opacity-100 transition-opacity bottom-full right-0 mb-2 pointer-events-none">
                  At max only 10 files can be selected or uploaded.
                  <br />
                  Example: 5 files selected from public filing & 5 files
                  uploaded = 10 files
                </span>
              </span>
            </p>
          </div>
          <div className="w-full flex flex-col gap-4">
            <PublicFillings
              ref={publicfilingsref}
              selectedCompanyId={selectedCompanyDetails?.acuity_id}
              selectedChipsCount={selectedChipsCount}
              onSelectedChipsChange={handleSelectedChipsChange}
              alexnaderiaUploadDetails={alexnaderiaUploadDetails}
              fileDetailsForAlexanderiaUpload={fileDetailsForAlexanderiaUpload}
              isFileLimitReached={isFileLimitReached}
            />
            <UploadBox
              companyID={companyID}
              processID={processID}
              uploadedFilesCount={uploadedFilesCount}
              onUploadedFilesCountChange={handleUploadedFilesChange}
              uploadDocumentDetails={uploadDocumentDetails}
              setUploadDocumentDetails={setUploadDocumentDetails}
              ref={uploadFilesref}
              isFileLimitReached={isFileLimitReached}
              selectedChipsCount={selectedChipsCount}
            />
          </div>
        </div>
      </div>
    );
  }
);
FinancialDocument.propTypes = {
  selectedCompanyDetails: PropTypes.object,
  publicfilingsref: PropTypes.object,
  uploadFilesref: PropTypes.object,
  setSelectedFilingsCount: PropTypes.func,
  companyID: PropTypes.string,
  processID: PropTypes.string,
  alexnaderiaUploadDetails: PropTypes.func,
  fileDetailsForAlexanderiaUpload: PropTypes.object,
  setUploadDocumentDetails: PropTypes.func,
  uploadDocumentDetails: PropTypes.object
};
export default FinancialDocument;
