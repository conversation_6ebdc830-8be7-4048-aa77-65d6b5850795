import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import ToolTip from "./tool-tip";

describe("DarkToolTip tests", () => {
  beforeEach(() => {
    //Arrange
  });
  afterEach(() => {
    jest.resetAllMocks();
  });

  it("renders the DarkToolTip  correctly", () => {
    //Act
    render(<ToolTip place={"bottom"} text={"toolTip"} toolTipId={"test-id"} />);
    const element = screen.getByTestId(`tool-tip`);
    //Assert
    expect(element).toBeInTheDocument();
  });
});
it("renders the DarkToolTip with default place when not specified", () => {
  render(<ToolTip text={"tooltip text"} toolTipId={"test-id"} />);
  const tooltip = screen.getByTestId("tool-tip");
  expect(tooltip).toBeInTheDocument();
});

it("attaches event listeners to the target element", () => {
  // Setup mock element with the data-tooltip-id
  const mockElement = document.createElement("div");
  mockElement.setAttribute("data-tooltip-id", "test-id");
  document.body.appendChild(mockElement);

  // Spy on addEventListener
  const addEventSpy = jest.spyOn(mockElement, "addEventListener");

  render(<ToolTip text={"tooltip text"} toolTipId={"test-id"} />);

  // Check if appropriate event listeners were added
  expect(addEventSpy).toHaveBeenCalledWith("mouseenter", expect.any(Function));
  expect(addEventSpy).toHaveBeenCalledWith("mouseleave", expect.any(Function));
  expect(addEventSpy).toHaveBeenCalledWith("click", expect.any(Function));

  // Cleanup
  document.body.removeChild(mockElement);
  addEventSpy.mockRestore();
});

it("removes event listeners on unmount", () => {
  // Setup mock element
  const mockElement = document.createElement("div");
  mockElement.setAttribute("data-tooltip-id", "test-id");
  document.body.appendChild(mockElement);

  // Spy on removeEventListener
  const removeEventSpy = jest.spyOn(mockElement, "removeEventListener");

  const { unmount } = render(
    <ToolTip text={"tooltip text"} toolTipId={"test-id"} />
  );
  unmount();

  // Check if event listeners were removed
  expect(removeEventSpy).toHaveBeenCalledWith(
    "mouseenter",
    expect.any(Function)
  );
  expect(removeEventSpy).toHaveBeenCalledWith(
    "mouseleave",
    expect.any(Function)
  );
  expect(removeEventSpy).toHaveBeenCalledWith("click", expect.any(Function));

  // Cleanup
  document.body.removeChild(mockElement);
  removeEventSpy.mockRestore();
});

it("does nothing when target element is not found", () => {
  // Using a non-existent ID
  const consoleErrorSpy = jest
    .spyOn(console, "error")
    .mockImplementation(() => {});

  render(<ToolTip text={"tooltip text"} toolTipId={"non-existent-id"} />);

  // No errors should be thrown
  expect(consoleErrorSpy).not.toHaveBeenCalled();

  consoleErrorSpy.mockRestore();
});
