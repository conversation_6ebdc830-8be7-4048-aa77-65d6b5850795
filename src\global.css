/* stylelint-disable */

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Highcharts Global CSS overides */

.highcharts-root {
  font-family: "Helvetica Neue LT W05 55 Roman" !important;
}

/* Highcharts Global CSS overides */

@layer base {
  ::-webkit-scrollbar {
    width: 12px;
    height: 12px;
    border: 1px;
  }

  ::-webkit-scrollbar-track {
    background-color: #ffff;
  }
  ::-webkit-scrollbar-thumb {
    background-color: #d1d5db;
    border-radius: 20px;
    border: 1px solid transparent;
    background-clip: content-box;
  }

  ::-webkit-scrollbar-thumb:hover {
    background-color: #9ca3af;
  }

  input[type="checkbox"]:checked {
    background-image: url("./resources/images/check-box.svg");
    background-color: #4061c7;
  }

  input[type="checkbox"]:checked:focus {
    background-image: url("./resources/images/check-box.svg");
    background-color: #4061c7;
  }

  input[type="checkbox"]:indeterminate {
    background-image: url("./resources/images/Check-box-Indeterminate.svg");
    background-color: #4061c7;
    background-size: 10px;
  }

  input[type="checkbox"]:disabled:checked {
    background-image: url("./resources/images/check-box-disabled.svg");
    background-size: 10px;
  }

  input[type="checkbox"]:disabled:indeterminate {
    background-image: url("./resources/images/check-box-disabled-intermediate.svg");
    background-size: 10px;
  }
  [type="text"]:focus {
    box-shadow: none;
  }
}
html,
body {
  height: 100%;
  overflow: hidden;
}

@layer utilities {
  .display-1-r {
    @apply font-display-1-r text-display-1-r;
  }

  .display-1-m {
    @apply font-display-1-m text-display-1-m;
  }

  .display-2-r {
    @apply font-display-2-r text-display-2-r;
  }

  .display-2-m {
    @apply font-display-2-m text-display-2-m;
  }

  .heading-1-m {
    @apply font-heading-1-m text-heading-1-m;
  }

  .heading-1-b {
    @apply font-heading-1-b text-heading-1-b;
  }

  .heading-2-r {
    @apply font-heading-2-r text-heading-2-r;
  }

  .heading-2-m {
    @apply font-heading-2-m text-heading-2-m;
  }

  .heading-2-b {
    @apply font-heading-2-b text-heading-2-b;
  }

  .body-r {
    @apply font-body-r text-body-r;
  }
  .body-m {
    @apply font-body-m text-body-m;
  }

  .body-b {
    @apply font-body-b text-body-b;
  }

  .body-i {
    @apply font-body-i text-body-i;
  }

  .caption-r {
    @apply font-caption-r text-caption-r;
  }

  .caption-m {
    @apply font-caption-m text-caption-m;
  }

  .caption-i {
    @apply font-caption-i text-caption-i;
  }
}
.notes-grid {
  max-height: calc(100vh - 180px);
  overflow: scroll;
}
.metta-grid .k-grid-content {
  max-height: calc(100vh - 165px);
}
.text-sm {
  font-size: 0.75rem; /* 12px */
}
@media (min-width: 1366px) {
  .custom-padding-1366 {
    padding-left: 6rem !important; /* 96px */
    padding-right: 6rem !important; /* 96px   */
  }
}
@media (min-width: 1440px) {
  .custom-padding-body-1440 {
    padding-left: 7.5rem !important; /* 120px */
    padding-right: 7.5rem !important; /* 120px */
  }
}
@media (min-width: 1920px) {
  .custom-padding-body-1920 {
    padding-left: 15rem !important; /* 240px */
    padding-right: 15rem !important; /* 240px  */
  }
}

.publish-button svg {
  color: #fff;
}
.k-input-value-text {
  font-family: "Helvetica Neue LT W05 55 Roman";
  font-weight: 500;
  font-size: 14px;
  line-height: 24px;
}
#pushdocumenttooltip {
  width: 176px;
  text-wrap: wrap;
  padding: 0;
}
.k-grid td:first-child {
  position: sticky;
  left: 0;
  box-shadow: 2px 0 5px -2px rgba(0,0,0,0.1);
  z-index: 20;
}
.k-tabstrip-items.k-tabstrip-items-scroll.k-reset.k-tabstrip-items-start{
  display: none;
}
.k-toolbar.k-toolbar-md.k-toolbar-solid.k-spreadsheet-toolbar{
  display: none;
}
.k-spreadsheet-action-bar{
  display: none;
}
.iframe{
  display: none;
}
.k-spreadsheet-action-bar{
  display: none !important;
}
.k-item.k-tabstrip-item.k-active.k-state-tab-on-top.k-spreadsheet-sheets-bar-active{
  color:green
}
.k-selection-partial{
  position: absolute;
    left: 0px;
    width: 76px;
    height: 31px;
}
.MuiTabs-scrollButtons.Mui-disabled {
  opacity: 0.3 !important;
}
.MuiSvgIcon-root.MuiSvgIcon-fontSizeSmall.css-120dh41-MuiSvgIcon-root{
  color: #666666 !important;
}