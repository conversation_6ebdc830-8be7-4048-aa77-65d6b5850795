import React, {
  useEffect,
  useState,
  forwardRef,
  useImperativeHandle
} from "react";
import { MdExpandLess, MdExpandMore } from "react-icons/md";
import Dropdown from "../../dropdown";
import TabsFilledGroup from "../../../partials/molecules/tabs-filled-group/tabs-filled-group";
import Chip from "../../chip";
import { format } from "date-fns";
import PropTypes from "prop-types";
import NoResults from "../../../resources/images/NoResults.svg";
import { FILINGS_API } from "../../../infra/api/filings/get-filing-service";
import { UploadFiling } from "../../../infra/api/filings/upload-filing-service";
import SpinnerCircle from "../../../partials/atoms/loader/spinner-circle";

const PLACEHOLDER_TEXT = "Choose from recent Filings";
const DROPDOWN_ITEMS = [];
const MAX_SELECTED_CHIPS = 10;
const TAB_ANNUAL = "Annual";
const TAB_QUARTERLY = "Quarterly";
const TAB_KEY_1 = "tab1";
const TAB_KEY_2 = "tab2";
const NO_DATA_FOUND_TEXT = "No data found!";

const PublicFillings = forwardRef(
  (
    {
      selectedChipsCount,
      selectedCompanyId,
      onSelectedChipsChange,
      alexnaderiaUploadDetails,
      fileDetailsForAlexanderiaUpload,
      isFileLimitReached
    },
    ref
  ) => {
    const [selectedChips, setSelectedChips] = useState([]);
    const [isCollapsed, setIsCollapsed] = useState(false);
    const [loading, setLoading] = useState(true);
    const Filings = FILINGS_API(selectedCompanyId, "2022-01-30", "2025-01-30");

    useEffect(() => {
      const fetchData = async () => {
        try {
          await Filings;
        } catch (error) {
          console.error("Error fetching filings data:", error);
        } finally {
          setLoading(false);
        }
      };

      fetchData();
    }, [Filings]);

    const extracted_data = Filings?.data?.data?.map((company) => ({
      acuity_id: company.acuity_id,
      filing_date: company.filing_date,
      doc_url: company.doc.url,
      type: company.doc.type,
      filing_type: company.filing_type,
      doc_id: company.doc.doc_id
    }));
    const annualFilingTypes = [
      "10-K",
      "10-K/A",
      "20-F",
      "20-F/A",
      "40-F",
      "40-F/A",
      "Annual Reports",
      "AR",
      "AA",
      "ACS",
      "202"
    ];
    const quarterlyFilingTypes = [
      "1st Quarter results",
      "3rd Quarter results",
      "Quarterly Reports",
      "10-Q/A",
      "10-Q"
    ];

    const annualFilings = extracted_data
      ?.filter(
        (item) =>
          annualFilingTypes.includes(item.filing_type) && item.type === "pdf"
      )
      ?.reduce((unique, item) => {
        if (
          !unique.some(
            (filing) =>
              filing.filing_date === item.filing_date &&
              filing.filing_type === item.filing_type
          )
        ) {
          unique.push(item);
        }
        return unique;
      }, []);

    const quarterlyFilings = extracted_data
      ?.filter(
        (item) =>
          quarterlyFilingTypes.includes(item.filing_type) && item.type === "pdf"
      )
      ?.reduce((unique, item) => {
        if (
          !unique.some(
            (filing) =>
              filing.filing_date === item.filing_date &&
              filing.filing_type === item.filing_type
          )
        ) {
          unique.push(item);
        }
        return unique;
      }, []);

    useEffect(() => {
      onSelectedChipsChange(selectedChips.length);
    }, [selectedChips, onSelectedChipsChange]);

    const handleChipClick = async (item) => {
      if (isChipSelected(item)) {
        setSelectedChips(
          selectedChips.filter((chip) => chip.filing_date !== item.filing_date)
        );
        alexnaderiaUploadDetails((prevDetails) =>
          prevDetails.map((detail) =>
            detail.doc_id === item.doc_id
              ? { ...detail, isSelected: false }
              : detail
          )
        );
      } else if (selectedChips.length < MAX_SELECTED_CHIPS && !isFileLimitReached) {
        // Only allow selection if file limit isn't reached
        setSelectedChips([...selectedChips, item]);

        if (
          !fileDetailsForAlexanderiaUpload.some(
            (detail) => detail.doc_id === item.doc_id
          )
        ) {
          await UploadFiling({
            acuityId: item.acuity_id,
            docId: item.doc_id,
            filingType: item.filing_type,
            filingDate: item.filing_date
          }).then((response) => {
            if (!response?.success) {
              alexnaderiaUploadDetails((prevDetails) => [
                ...prevDetails,
                {
                  acuity_id: item.acuity_id,
                  doc_id: item.doc_id,
                  s3Url: response.s3Url,
                  isSelected: true,
                  generatedDocId: response.documentId
                }
              ]);
            }
          });
        } else {
          alexnaderiaUploadDetails((prevDetails) =>
            prevDetails.map((detail) =>
              detail.doc_id === item.doc_id
                ? { ...detail, isSelected: true }
                : detail
            )
          );
        }
      }
    };

    const isChipSelected = (item) => {
      return selectedChips.some(
        (chip) => chip.filing_date === item.filing_date
      );
    };

    const isChipDisabled = (item) => {
      // Chip is disabled if already at max selected chips OR file limit is reached
      return (selectedChips.length === MAX_SELECTED_CHIPS || isFileLimitReached) && !isChipSelected(item);
    };

    const mockTabs = [
      {
        name: TAB_ANNUAL,
        key: TAB_KEY_1,
        component: null,
        disabled: annualFilings?.length === 0
      },
      {
        name: TAB_QUARTERLY,
        key: TAB_KEY_2,
        component: null,
        disabled: quarterlyFilings?.length === 0
      }
    ];

    const isEmptyState =
      annualFilings?.length === 0 && quarterlyFilings?.length === 0;
    const initialActiveTab =
      mockTabs.find((tab) => !tab.disabled)?.key || TAB_KEY_1;
    const [activeTab, setActiveTab] = useState(initialActiveTab);
    const activeTabIsDisabled = mockTabs.find(
      (tab) => tab.key === activeTab
    )?.disabled;

    useEffect(() => {
      if (mockTabs.find((tab) => tab.key === TAB_KEY_1)?.disabled) {
        setActiveTab(TAB_KEY_2);
      }
    }, [mockTabs]);

    useImperativeHandle(ref, () => ({
      resetpublicfilingsrefState() {
        setSelectedChips([]);
        setIsCollapsed(false);
        setActiveTab(initialActiveTab); // Reset activeTab to initialActiveTab
      }
    }));

    const EMPTY_STRING = "";
    return (
      <div className="rounded-lg border border-neutral-10 gap-6 w-full">
        <div
          onClick={() => setIsCollapsed(!isCollapsed)}
          className={`rounded gap-3 justify-between flex flex-row py-3 px-4 cursor-pointer hover:bg-primary-35 ${
            !isCollapsed ? "bg-primary-43" : EMPTY_STRING
          }`}
        >
          <div className="flex flex-row gap-3">
            <button className="flex justify-center items-center ">
              {isCollapsed ? (
                <MdExpandMore data-testid="expand-more-icon" />
              ) : (
                <MdExpandLess data-testid="expand-less-icon" />
              )}
            </button>
            <div
              className={`text-neutral-90 ${
                !isCollapsed ? "heading-2-m" : "m-m"
              }`}
            >
              Public Filling(s)
            </div>
          </div>
          {selectedChipsCount > 0 && (
            <div className="text-primary-78 text-sm font-caption-i font-normal">
              {selectedChipsCount} selected
            </div>
          )}
        </div>
        {!isCollapsed && (
          <div className="flex flex-col py-4 px-7 gap-4">
            <div>
              <Dropdown
                items={DROPDOWN_ITEMS}
                placeholder={PLACEHOLDER_TEXT}
                selectedValue={PLACEHOLDER_TEXT}
                disabled={true}
              />
            </div>
            {isEmptyState ? (
              <div className="gap-4 rounded p-3 flex flex-col items-center border border-neutral-5">
                <div>
                  <img src={NoResults} alt="No Results" />
                </div>
                <p className="text-body-r font-body-r text-neutral-60 text-sm">
                  {NO_DATA_FOUND_TEXT}
                </p>
              </div>
            ) : (
              <div className="flex flex-col gap-4">
                <TabsFilledGroup
                  tabs={mockTabs}
                  activeTab={activeTab}
                  setActiveTab={setActiveTab}
                  disabledTab={mockTabs.find((tab) => tab.disabled)?.key}
                  tabClassName={(tab) =>
                    tab.key === activeTab
                      ? "hover:bg-primary-90"
                      : "hover:bg-primary-40 hover:text-neutral-60"
                  }
                />
                {!activeTabIsDisabled && (
                  <div className="max-h-20 overflow-y-auto flex flex-row rounded p-3 gap-3 border border-neutral-5">
                    {loading ? (
                      <div className="flex justify-center items-center w-full">
                        <SpinnerCircle size={6} />
                      </div>
                    ) : activeTab === TAB_KEY_1 ? (
                      <div className="flex flex-row flex-wrap gap-2">
                        {annualFilings?.map((item, index) => (
                          <Chip
                            key={index}
                            text={format(
                              new Date(item.filing_date),
                              "dd MMM yyyy"
                            )}
                            size="large"
                            themeColor="primary-78"
                            fillMode="outline"
                            rounded={true}
                            onClick={() => handleChipClick(item)}
                            isSelected={isChipSelected(item)}
                            className={`${(() => {
                              if (isChipDisabled(item)) {
                                return "cursor-not-allowed bg-neutral-5 text-neutral-70 border border-neutral-40";
                              } else if (isChipSelected(item)) {
                                return "hover:bg-primary-90 ";
                              } else {
                                return "hover:bg-primary-40 hover:text-primary-78";
                              }
                            })()}`}
                          />
                        ))}
                      </div>
                    ) : (
                      <div className="flex flex-row flex-wrap gap-2">
                        {quarterlyFilings?.map((item, index) => (
                          <Chip
                            key={index}
                            text={format(
                              new Date(item.filing_date),
                              "dd MMM yyyy"
                            )}
                            size="large"
                            themeColor="primary-78"
                            fillMode="outline"
                            rounded={true}
                            onClick={() => handleChipClick(item)}
                            isSelected={isChipSelected(item)}
                            className={`${(() => {
                              if (isChipDisabled(item)) {
                                return "cursor-not-allowed bg-neutral-5 text-neutral-70 border border-neutral-40";
                              } else if (isChipSelected(item)) {
                                return "hover:bg-primary-90 ";
                              } else {
                                return "hover:bg-primary-40 hover:text-primary-78";
                              }
                            })()}`}
                          />
                        ))}
                      </div>
                    )}
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    );
  }
);
PublicFillings.propTypes = {
  selectedChipsCount: PropTypes.number.isRequired,
  selectedCompanyId: PropTypes.string.isRequired,
  onSelectedChipsChange: PropTypes.func.isRequired,
  alexnaderiaUploadDetails: PropTypes.func.isRequired,
  fileDetailsForAlexanderiaUpload: PropTypes.object.isRequired,
  isFileLimitReached: PropTypes.bool.isRequired
};
export default PublicFillings;
