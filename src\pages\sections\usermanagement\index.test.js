import UserManagementContainer from ".";
import { render, screen } from "@testing-library/react";

describe("UserManagementContainer", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test("renders UserManagement component with correct props", () => {
    const mockConfig = {
      oidcConfig: {
        authority: "test-authority",
        client_id: "test-client"
      }
    };

    jest.mock("../../../utils/authHelper", () => ({
      getAppConfig: () => mockConfig
    }));

    const { getByText } = render(<UserManagementContainer />);

    expect(getByText("user-management")).toBeInTheDocument();

    const userManagementProps = screen.getByTestId(
      "user-management-component"
    ).props;
    expect(userManagementProps).toEqual(
      expect.objectContaining({
        title: "user-management",
        accessType: "role",
        roles: [],
        applicationName: "bh",
        environment: "dev",
        client: "pod",
        roleSeparator: "-",
        environmentSeparator: "-"
      })
    );
  });

  test("initializes UserManager with correct config", () => {
    const mockConfig = {
      oidcConfig: {
        authority: "test-authority",
        client_id: "test-client"
      }
    };

    jest.mock("../../../utils/authHelper", () => ({
      getAppConfig: () => mockConfig
    }));

    const mockUserManager = jest.spyOn(UserManager.prototype, "constructor");

    render(<UserManagementContainer />);

    expect(mockUserManager).toHaveBeenCalledWith(mockConfig.oidcConfig);
  });

  test("getAppConfig is called on component render", () => {
    const getAppConfigMock = jest.fn().mockReturnValue({
      oidcConfig: {}
    });

    jest.mock("../../../utils/authHelper", () => ({
      getAppConfig: getAppConfigMock
    }));

    render(<UserManagementContainer />);

    expect(getAppConfigMock).toHaveBeenCalledTimes(1);
  });
});
