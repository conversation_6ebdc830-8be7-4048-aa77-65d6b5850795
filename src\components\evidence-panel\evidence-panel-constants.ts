import { FinancialStatements } from "../../constants";
// Tab Labels
export const TAB_LABELS = {
  INCOME_STATEMENT: FinancialStatements[0],
  BALANCE_SHEET: FinancialStatements[1], 
  CASH_FLOW: FinancialStatements[2],
  NOTES: "Notes"
};

// Financial Types mapping
export const TAB_LABEL_TO_FS_TYPE = {
  [TAB_LABELS.INCOME_STATEMENT]: "IS",
  [TAB_LABELS.BALANCE_SHEET]: "BS",
  [TAB_LABELS.CASH_FLOW]: "CF",
  [TAB_LABELS.NOTES]: "IS" // Default
};

// Inverse mapping from FS_TYPE to TAB_LABEL
export const FS_TYPE_TO_TAB_LABEL = {
  "IS": TAB_LABELS.INCOME_STATEMENT,
  "BS": TAB_LABELS.BALANCE_SHEET,
  "CF": TAB_LABELS.CASH_FLOW
};

// Legend Colors
export const LEGEND_COLORS = {
  MAPPED: "#2881EA",
  UNMAPPED: "#B80D10",
  MATCH_FOUND: "#FF984A",
  NO_MATCH_FOUND: "#EDDA70",
};

// Column Widths
export const COLUMN_WIDTHS = {
  STANDARD_LINE_ITEM: "325px",
  DOCUMENT_LINE_ITEM: "250px",
  PERIOD_COLUMN: "200px"
};

// Success Messages
export const SUCCESS_MESSAGES = {
  LINE_ITEM_PUSHED: "Document line item pushed to standard line item successfully"
};

// Notes Related
export const NOTES_PREFIX = "Note_";

// Cell Styles
export const CELL_STYLES = {
  HEADER: "header",
  LINE_ITEM: "lineitem"
};

// Document Line Items
export const DOCUMENT_LINE_ITEMS_TEXT = "Document Line Items";
export const Standard_KPI ="Standard KPI";
// Header height
export const HEADER_HEIGHT = "54px";

// PDF Element Selectors
export const PDF_SELECTORS = {
  OVERLAY: ".pdf-overlay",
  GRID_CONTENT: ".k-grid-content"
};

// Highlight Colors
export const HIGHLIGHT_COLORS = {
  EDITING_BORDER: "3px solid #EDDA70"
};

// Default parent text for uncategorized items
export const DEFAULT_PARENT_TEXT = "Uncategorized";

// No value message
export const NO_VALUE_TEXT = "no value found";
