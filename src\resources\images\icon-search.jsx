import * as React from "react";
const IconSearch = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={14.414}
    height={14.414}
    viewBox="0 0 14.414 14.414"
    {...props}
  >
    <g
      id="Icon_feather-search"
      data-name="Icon feather-search"
      transform="translate(1 1)"
    >
      <path
        id="Path_32729"
        data-name="Path 32729"
        d="M13.611,9.055A4.555,4.555,0,1,1,9.055,4.5a4.555,4.555,0,0,1,4.555,4.555Z"
        transform="translate(-4.5 -4.5)"
        fill="none"
        stroke="#4061c7"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
      />
      <path
        id="Path_32730"
        data-name="Path 32730"
        d="M29.084,29.084l-4.109-4.109"
        transform="translate(-17.084 -17.084)"
        fill="none"
        stroke="#4061c7"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
      />
    </g>
  </svg>
);
export default React.memo(IconSearch);
