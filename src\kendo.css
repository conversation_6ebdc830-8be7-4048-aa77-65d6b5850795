.modal-Dialog-Box > div > div {
  background-image: none !important;
  border-radius: 8px;
  height: 64px;
}
.modal-Dialog-Box > div > .k-window-titlebar,
.modal-Dialog-Box > div > .k-dialog-titlebar {
  background-color: #ebf3ff;
  height: 56px;
  border-bottom: none;
  border-bottom-left-radius: 0px;
  border-bottom-right-radius: 0px;
}
.k-button-solid-primary {
  background-color: #021155 !important;
  border-color: #021155 !important;
  color: #fff !important;
}
.k-button-outline-secondary:hover {
  background-color: #ffffff !important;
  border-color: #021155 !important;
  color: #021155 !important;
}
.k-button-outline-secondary:focus {
  box-shadow: none !important;
}

.k-button-solid-primary:focus {
  box-shadow: none !important;
}
.k-input-solid:focus-within {
  border-color: #021155 !important;
  box-shadow: none !important;
}
.k-chip-solid-base:focus,
.k-chip-solid-base.k-focus {
  box-shadow: none;
}
.custom-combobox .k-button-md.k-icon-button {
  display: none;
}
.k-list-item.k-focus {
  box-shadow: inset 0 0 0 0 #ffffff;
}
.k-chip-solid-base {
  background-color: #ffffff;
}

.k-input-md .k-input-inner {
  font-size: 13px;
}
.k-input {
  font-family: "Helvetica Neue LT W05 55 Roman";
}
.k-input-value-text {
  font-family: "Helvetica Neue LT W05 55 Roman";
}
.k-popup {
  border-radius: 4px;
  font-family: "Helvetica Neue LT W05 55 Roman";
}
.k-input-md,
.k-input-inner {
  font-size: 14px;
  color: #333333;
}
.k-list-content {
  max-height: 218px !important;
  overflow-y: auto;
  padding-bottom: 0.5rem;
}
.k-list-ul .list-item {
  padding-left: 0 !important; /* Remove padding from kendo css*/
}
.modal-Dialog-Box .k-window {
  width: 600px;
  height: 200px;
  box-shadow: none;
  border-radius: 4px;
}

.k-input-inner::placeholder {
  color: #808080;
}

.k-upload .k-upload-files {
  max-height: none;
  overflow-y: hidden; /* Enable scrolling */
  -ms-overflow-style: none;
  scrollbar-width: none;
}

/* Hide the scrollbar for WebKit browsers (Chrome, Safari, and Opera) */
.k-upload .k-upload-files::-webkit-scrollbar {
  display: none;
}

.k-list-item,
.k-popup {
  background-color: white;
  padding: 0;
  border-bottom: 1px solid #e6e6e6;
}

.k-list-item:hover {
  background-color: #f5f9ff;
  border: 1px solid #e6e6e6;
}

.k-list-item:first-child {
  border-top: 1px solid #e6e6e6;
}

.legends-dropdown {
  width: 126px;
  border: 1px solid #e6e6e6;
  border-radius: 4px;
  display: flex;
  justify-content: center;
  padding: 6px 16px;
}

.legends-dropdown:hover {
  border: 1px solid #e6e6e6 !important;
  border-radius: 4px;
}
.k-dropdownlist.k-picker.k-picker-md.k-picker-solid.legends-dropdown {
  background-color: white;
  border: 1px solid #e6e6e6;
}
/* Style for dropdown popup container */

.legends-item:hover {
  background-color: white !important;
  border: none;
}

.k-popup.k-list-container.k-dropdownlist-popup {
  padding: 10px;
  gap: 10px;
  border: 1px solid #e6e6e6;
  border-radius: 8px;
  box-shadow: 0px 12px 24px 6px #0000000d;
  border-color: transparent;
  border-bottom: none;
  margin-top: 4px;
}
.k-list-item.legends-item {
  border: none;
  border-top: none;
  cursor: default;
  box-shadow: none;
}
#legends-listbox-id {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
.k-list-item.k-selected.k-focus.legends-item {
  background-color: white;
}
.statement-dropdown input {
  background: #FAFAFA !important;
}
.statement-dropdown .k-combobox ,.statement-dropdown button:hover{
  border: transparent !important;
  background: transparent;
}
.statement-dropdown button
{
  border: transparent;
  background: #FAFAFA;
}
.k-table-th.k-grid-header-sticky
{
  box-shadow: none !important;
  border-right: 1px solid #e6e6e6 !important;
  border-left-width: 0 !important;
}
.k-grid td:first-child{
  box-shadow: none !important;
  border-right-width: 1px !important;
  border-bottom-width: 1px !important;
}
.k-grid td:last-child{
  box-shadow: none !important;
}
.k-grid .k-table-th{
  border-right: none;
}
.k-grid .k-table-th, .k-grid td, .k-grid .k-table-td
{
  border-inline-start-width: 0px;
}
.k-table-tbody tr:first-child > td {
  border-top-width: 0px !important;
}
.custom-s-combo-box{
  background: #FAFAFA !important;
  border:none !important;
  padding-left: 0px !important;
}
.custom-s-combo-box input{
  padding: 0px !important;
  width:90%;
}
.custom-s-combo-box button{
  background: #FAFAFA !important;
  border:none !important;
  padding-right: 0 !important;
}
.custom-unit-currency  button{
 background: #FFFFFF !important;
}
.combo-kpi-info {
  right:40px;
  position: absolute;
}