/**
 * @jest-environment jsdom
 */
import React from 'react';
import { render, screen, fireEvent, act } from '@testing-library/react';
import '@testing-library/jest-dom';
import Template from './template-selection';
import { DropdownItemsEnum } from './dropdown-list';

// Mock the Dropdown component
jest.mock('../../components/dropdown', () => {
  return ({ items, placeholder, disabled, selectedValue, setSelectedValue }) => (
    <div data-testid="dropdown">
      <select 
        disabled={disabled}
        value={selectedValue}
        onChange={(e) => setSelectedValue(e.target.value)}
        data-testid="template-dropdown"
      >
        <option value={placeholder}>{placeholder}</option>
        {items.map((item, index) => (
          <option key={index} value={item}>{item}</option>
        ))}
      </select>
    </div>
  );
});

// Mock the RadioButton component
jest.mock('../../partials/atoms/radio-button/radio', () => {
  return ({ options, name, selectedValue, onChange, ...props }) => (
    <div data-testid={props['data-testid'] || 'radio-button'}>
      {options.map((option, index) => (
        <label key={index}>
          <input
            type="radio"
            name={name}
            value={option.value}
            checked={selectedValue === option.value}
            onChange={onChange}
            data-testid={`${props['data-testid']}-input`}
          />
          {option.label}
        </label>
      ))}
    </div>
  );
});

describe('Template Component', () => {
  // Default props for our tests
  const defaultProps = {
    setSelectedTemplateType: jest.fn(),
    setSelectedTemplateOption: jest.fn(),
    onOptionChange: jest.fn()
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  // Test initial rendering with default selection
  test('renders with default template-based option selected', () => {
    render(<Template {...defaultProps} />);
    
    // The template based radio button should be checked
    const radioInput = screen.getByTestId('template-based-input');
    expect(radioInput).toBeChecked();
    
    // The dropdown should be visible
    expect(screen.getByTestId('dropdown')).toBeInTheDocument();
    
    // Check that the other radio buttons are not checked
    expect(screen.getByTestId('as-reported-input')).not.toBeChecked();
    expect(screen.getByTestId('specific-kpis-input')).not.toBeChecked();
  });

  // Test changing selection to "As Reported"
  test('changes selection to "As Reported" and hides dropdown', () => {
    render(<Template {...defaultProps} />);
    
    // Initially the dropdown should be visible
    expect(screen.getByTestId('dropdown')).toBeInTheDocument();
    
    // Click the "As Reported" radio button
    fireEvent.click(screen.getByTestId('as-reported-input'));
    
    // The "As Reported" radio should now be checked
    expect(screen.getByTestId('as-reported-input')).toBeChecked();
    
    // The dropdown should not be visible anymore
    expect(screen.queryByTestId('dropdown')).not.toBeInTheDocument();
    
    // Check that callback functions were called with correct values
    expect(defaultProps.setSelectedTemplateOption).toHaveBeenCalledWith(DropdownItemsEnum.AS_REPORTED);
    expect(defaultProps.setSelectedTemplateType).toHaveBeenCalledWith(DropdownItemsEnum.CHOOSE_TEMPLATES);
    expect(defaultProps.onOptionChange).toHaveBeenCalledWith(DropdownItemsEnum.AS_REPORTED);
  });

  // Test changing selection to "Specific KPIs"
  test('changes selection to "Specific KPIs" and hides dropdown', () => {
    render(<Template {...defaultProps} />);
    
    // Click the "Specific KPIs" radio button
    fireEvent.click(screen.getByTestId('specific-kpis-input'));
    
    // The "Specific KPIs" radio should now be checked
    expect(screen.getByTestId('specific-kpis-input')).toBeChecked();
    
    // The dropdown should not be visible anymore
    expect(screen.queryByTestId('dropdown')).not.toBeInTheDocument();
    
    // Check that callback functions were called with correct values
    expect(defaultProps.setSelectedTemplateOption).toHaveBeenCalledWith(DropdownItemsEnum.SPECIFIC_KPIS);
    expect(defaultProps.setSelectedTemplateType).toHaveBeenCalledWith(DropdownItemsEnum.CHOOSE_TEMPLATES);
    expect(defaultProps.onOptionChange).toHaveBeenCalledWith(DropdownItemsEnum.SPECIFIC_KPIS);
  });

  // Test changing back to "Template Based"
  test('changes selection back to "Template Based" and shows dropdown', () => {
    render(<Template {...defaultProps} />);
    
    // First change to "As Reported"
    fireEvent.click(screen.getByTestId('as-reported-input'));
    
    // Then change back to "Template Based"
    fireEvent.click(screen.getByTestId('template-based-input'));
    
    // The "Template Based" radio should now be checked
    expect(screen.getByTestId('template-based-input')).toBeChecked();
    
    // The dropdown should be visible again
    expect(screen.getByTestId('dropdown')).toBeInTheDocument();
    
    // Check that callback functions were called with correct values
    expect(defaultProps.setSelectedTemplateOption).toHaveBeenCalledWith(DropdownItemsEnum.TEMPLATE_BASED);
  });

  // Test template selection from dropdown
  test('selects a template from the dropdown', () => {
    render(<Template {...defaultProps} />);
    
    // Get the dropdown and change its value
    const dropdown = screen.getByTestId('template-dropdown');
    fireEvent.change(dropdown, { target: { value: DropdownItemsEnum.TEMPLATE_NAME[0] } });
    
    // Check that callback function was called with correct value
    expect(defaultProps.setSelectedTemplateType).toHaveBeenCalledWith(DropdownItemsEnum.TEMPLATE_NAME[0]);
  });

  // Test reset functionality using ref
  test('resets to default state when resetTemplateState is called', async () => {
    const ref = React.createRef();
    render(<Template {...defaultProps} ref={ref} />);
    
    // First change to "As Reported"
    fireEvent.click(screen.getByTestId('as-reported-input'));
    
    // Verify "As Reported" is selected
    expect(screen.getByTestId('as-reported-input')).toBeChecked();
    
    // Then reset using act to wrap state changes
    await act(async () => {
      ref.current.resetTemplateState();
    });
    
    // Re-render to ensure updates are applied
    await act(async () => {
      // Force a re-render
      fireEvent.click(document.body);
    });
    
    // Now verify that callbacks were called with correct values
    expect(defaultProps.setSelectedTemplateOption).toHaveBeenCalledWith(DropdownItemsEnum.TEMPLATE_BASED);
    expect(defaultProps.setSelectedTemplateType).toHaveBeenCalledWith(DropdownItemsEnum.CHOOSE_TEMPLATES);
    
    // The dropdown should be visible
    expect(screen.getByTestId('dropdown')).toBeInTheDocument();
  });
}); 