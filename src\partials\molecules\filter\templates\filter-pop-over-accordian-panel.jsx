import { React, useEffect, useState } from "react";
import { PropTypes } from "prop-types";
import CheckBox from "../../../atoms/check-box";
import EmptySearchImage from "../../../../resources/images/empty-search-image";
import {
  Disclosure,
  Combobox,
  ComboboxInput,
  ComboboxButton,
  DisclosurePanel,
  DisclosureButton
} from "@headlessui/react";
import { HiChevronDown } from "react-icons/hi";
import { FaSearch, FaTimes } from "react-icons/fa";
import useScreenerStore from "../../../../pages/screener/screener.store";
import useWatchListStore from "../../../../pages/watch-list/watch-list.store";

const FilterAccordianPanel = ({
  data,
  onSelect,
  selectedFilterId,
  context
}) => {
  const screenerStore = useScreenerStore((state) => state);
  const watchlistStore = useWatchListStore((state) => state);
  const { setSelectedFilter, setAppliedFilterType } =
    context === "screener" ? screenerStore : watchlistStore;

  const [accordianMenu, setAccordianMenu] = useState([]);
  const [query, setQuery] = useState("");
  const [selectedAll, setSelectedAll] = useState(false);
  const [indeterminate, setIndeterminate] = useState(false);
  const [matchedData, setMatchedData] = useState([]);
  const [appliedFilterCount, setAppliedFilterCount] = useState(0);

  useEffect(() => {
    setAccordianMenu(data);
    const selectedCount = data.filter((x) => x.selected).length;
    setSelectedAll(selectedCount === data.length);
    setIndeterminate(selectedCount > 0 && selectedCount < data.length);
  }, [data, selectedFilterId]);

  const onSelection = (e) => {
    const updatedData = accordianMenu.map((item) => ({
      ...item,
      data: item.data.map((subItem) =>
        subItem.value === e.target.value
          ? { ...subItem, selected: e.target.checked }
          : subItem
      )
    }));
    updateSelectionState(updatedData);
    if (context === "screener") {
      setSelectedFilter(updatedData);
      setAppliedFilterType(true);
    }
  };

  const updateSelectionState = (updatedData) => {
    const selectedCount = updatedData.map((item) => ({
      ...item,
      selected: item.data.filter((subItem) => subItem.selected).length
    }));
    const totalSelected = selectedCount.reduce(
      (acc, item) => acc + item.selected,
      0
    );
    setAccordianMenu(selectedCount);
    onSelect(selectedCount, selectedFilterId, totalSelected);
  };

  const onClickParent = (item) => {
    const updatedData = accordianMenu.map((categories) => {
      if (item.value === categories.value) {
        const allSelected = item.data.every((subItem) => subItem.selected);
        return {
          ...categories,
          data: categories.data.map((category) => ({
            ...category,
            selected: !allSelected
          })),
          selected: !allSelected ? categories.data.length : 0
        };
      }
      return categories;
    });
    updateSelectionState(updatedData);
    if (context === "screener") {
      setSelectedFilter(updatedData);
      setAppliedFilterType(true);
    }
  };

  const onClickSelectAll = () => {
    const updatedData = accordianMenu.map((categories) => ({
      ...categories,
      data: categories.data.map((category) => ({
        ...category,
        selected: !selectedAll
      }))
    }));
    updateSelectionState(updatedData);
    setSelectedAll(!selectedAll);
    if (context === "screener") {
      setSelectedFilter(updatedData);
      setAppliedFilterType(true);
    }
  };

  useEffect(() => {
    const allSelected = accordianMenu.every((categories) =>
      categories.data.every((category) => category.selected)
    );
    const noneSelected = accordianMenu.every((categories) =>
      categories.data.every((category) => !category.selected)
    );

    setSelectedAll(allSelected);
    setIndeterminate(!allSelected && !noneSelected);
  }, [accordianMenu]);

  useEffect(() => {
    if (query.length > 0) {
      const matched = accordianMenu
        .map((item) => {
          const filteredData = item.data.filter((x) =>
            x.label.toLowerCase().includes(query.toLowerCase())
          );
          return {
            ...item,
            data: filteredData,
            selected: item.selected ?? 0
          };
        })
        .filter((item) => item.data.length > 0);
      setMatchedData(matched);
    } else {
      setMatchedData(data);
    }
  }, [query, accordianMenu, data]);

  const countSelectedTrue = (dataArray) => {
    let count = 0;
    dataArray.forEach((item) => {
      count += countSelectedInItem(item);
    });
    return count;
  };

  const countSelectedInItem = (item) => {
    let count = 0;
    if (item.data) {
      item.data.forEach((category) => {
        if (category.selected) {
          count += 1;
        }
      });
    }
    return count;
  };

  useEffect(() => {
    const count = countSelectedTrue(matchedData);
    setAppliedFilterCount(count);
  }, [matchedData]);

  return (
    <div className="flex h-full w-full flex-col text-neutral-80">
      <div className="relative border-b px-2 py-3">
        <Combobox value={query}>
          <ComboboxInput
            data-testid="filter-pop-over-accordian-panel-search-input"
            value={query}
            maxLength={40}
            className="body-r relative h-8 w-full rounded border border-neutral-10 px-9 py-1.5 text-neutral-80 placeholder-neutral-30 hover:border-primary-78 focus:border-primary-90 focus:outline-none focus:ring-0"
            placeholder={"Search here...."}
            autoComplete="off"
            onChange={(e) => {
              setQuery(e.target.value);
            }}
          ></ComboboxInput>
          <ComboboxButton className="absolute left-6 top-4 z-30 h-3 w-3 pr-2">
            <FaSearch
              data-testid={"filter-pop-over-accordian-panel-search-icon"}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
              }}
              className="absolute h-3 w-3 cursor-pointer text-primary-78 focus:text-neutral-60"
              strokeWidth={1}
            />
          </ComboboxButton>
          <ComboboxButton
            data-testid="filter-pop-over-accordian-panel-search-cross"
            className="absolute right-4 top-1 z-30 h-3 w-3"
          >
            {query.length > 0 && (
              <FaTimes
                onClick={(e) => {
                  setQuery("");
                  e.stopPropagation();
                  e.preventDefault();
                }}
                className="absolute top-[1.15rem] h-3 w-3 cursor-pointer text-neutral-60"
                strokeWidth={1}
              />
            )}
          </ComboboxButton>
        </Combobox>
      </div>
      <div className="h-full overflow-hidden">
        {matchedData.length > 0 && (
          <div className="flex h-10 items-center gap-3 border-b pl-7">
            <CheckBox
              data-testid={
                "filter-pop-over-accordian-panel-select-all-check-box"
              }
              indeterminate={indeterminate}
              id={"header-check-box"}
              checked={selectedAll}
              onChange={() => {
                onClickSelectAll();
              }}
            ></CheckBox>
            <div className="body-m text-neutral-80">
              Select All <span className="body-r">({appliedFilterCount})</span>
            </div>
          </div>
        )}
        <div className="flex h-[calc(100%-2.5rem)] w-full flex-col overflow-auto">
          {matchedData.length > 0 ? (
            matchedData.map((item) => (
              <div key={item.value}>
                <Disclosure>
                  {({ open }) => (
                    <div className="h-full w-full">
                      <DisclosureButton
                        className={`body-m flex h-10 w-full items-center justify-between gap-3 text-neutral-80 ${open ? "" : "border-b"} px-7`}
                      >
                        <div className="flex w-full justify-between gap-3">
                          <div className="flex justify-start gap-3 text-start">
                            <CheckBox
                              data-testid={
                                "filter-pop-over-accordian-panel-check-box"
                              }
                              onChange={() => {
                                onClickParent(item);
                              }}
                              checked={item.data?.every(
                                (subItem) => subItem.selected
                              )}
                              indeterminate={
                                item.data?.some(
                                  (subItem) => subItem.selected
                                ) &&
                                !item.data?.every((subItem) => subItem.selected)
                              }
                            ></CheckBox>
                            <span
                              title={item.label}
                              className="max-w-36 overflow-hidden text-ellipsis whitespace-nowrap"
                            >
                              {item.label}
                            </span>
                          </div>
                          <div className="flex justify-end">
                            <span className="body-r">
                              ({item.data?.length})
                            </span>
                          </div>
                        </div>
                        <HiChevronDown
                          className={`${open ? "rotate-180 transform" : ""} size-4 justify-end`}
                        />
                      </DisclosureButton>
                      <DisclosurePanel className="body-m w-full justify-between gap-3 border-b text-neutral-80">
                        {item.data?.map((subItem) => (
                          <div
                            key={subItem.value}
                            className="body-r flex h-10 w-full items-center justify-start gap-3 border-t px-9"
                          >
                            <CheckBox
                              data-testid={
                                "filter-pop-over-accordian-panel-check-box"
                              }
                              value={subItem.value}
                              checked={subItem?.selected ?? false}
                              onChange={onSelection}
                            ></CheckBox>
                            <span>{subItem.label}</span>
                          </div>
                        ))}
                      </DisclosurePanel>
                    </div>
                  )}
                </Disclosure>
              </div>
            ))
          ) : (
            <div className="flex h-full flex-col items-center justify-center gap-3 overflow-auto text-center">
              <EmptySearchImage className="" />
              <span className="body-r">No Matches Found!</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default FilterAccordianPanel;

FilterAccordianPanel.propTypes = {
  data: PropTypes.array,
  onSelect: PropTypes.func,
  selectedFilterId: PropTypes.string,
  context: PropTypes.string.isRequired
};
