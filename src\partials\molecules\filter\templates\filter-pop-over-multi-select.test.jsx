import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import FilterMultiSelect from "./filter-pop-over-multi-select";

const mockData = [
  { value: "1", label: "Option 1", selected: false },
  { value: "2", label: "Option 2", selected: true },
  { value: "3", label: "Option 3", selected: false }
];

const mockOnSelect = jest.fn();

const queryClient = new QueryClient();

describe("FilterMultiSelect Component", () => {
  test("renders search input", () => {
    render(
      <QueryClientProvider client={queryClient}>
        <FilterMultiSelect
          data={mockData}
          onSelect={mockOnSelect}
          selectedFilterId="1"
          clearAll={false}
        />
      </QueryClientProvider>
    );
    expect(screen.getByPlaceholderText("Search here....")).toBeInTheDocument();
  });

  test("renders filter options", () => {
    render(
      <QueryClientProvider client={queryClient}>
        <FilterMultiSelect
          data={mockData}
          onSelect={mockOnSelect}
          selectedFilterId="1"
          clearAll={false}
        />
      </QueryClientProvider>
    );
    expect(screen.getByText("Option 1")).toBeInTheDocument();
    expect(screen.getByText("Option 2")).toBeInTheDocument();
    expect(screen.getByText("Option 3")).toBeInTheDocument();
  });

  test("selects all options", () => {
    render(
      <QueryClientProvider client={queryClient}>
        <FilterMultiSelect
          data={mockData}
          onSelect={mockOnSelect}
          selectedFilterId="1"
          clearAll={false}
        />
      </QueryClientProvider>
    );
    fireEvent.click(
      screen.getByTestId("filter-pop-over-multi-select-select-all")
    );
  });

  test("filters options based on search query", () => {
    render(
      <QueryClientProvider client={queryClient}>
        <FilterMultiSelect
          data={mockData}
          onSelect={mockOnSelect}
          selectedFilterId="1"
          clearAll={false}
        />
      </QueryClientProvider>
    );
    fireEvent.change(screen.getByPlaceholderText("Search here...."), {
      target: { value: "Option 1" }
    });
    expect(screen.getByText("Option 1")).toBeInTheDocument();
    expect(screen.queryByText("Option 2")).not.toBeInTheDocument();
  });

  test("handles option selection", () => {
    render(
      <QueryClientProvider client={queryClient}>
        <FilterMultiSelect
          data={mockData}
          onSelect={mockOnSelect}
          selectedFilterId="1"
          clearAll={false}
        />
      </QueryClientProvider>
    );
    fireEvent.click(
      screen.getAllByTestId("filter-pop-over-multi-select-check-box")[0]
    );
  });
});
