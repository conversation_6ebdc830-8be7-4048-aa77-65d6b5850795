import React from "react";
import { useNavigate } from "react-router-dom";
import IconCard from "../components/cards/icon-card";
import spreaderIcon from "../resources/images/spreader.svg";
import { EXTRACTION_TYPES_SERVICE } from "../infra/api/tenant/get-extraction-type-service";
import { STATUS_TYPES_SERVICE } from "../infra/api/tenant/get-job-status-service";

const HomePage = () => {
  const navigate = useNavigate();

  React.useEffect(() => {
    const fetchExtractionTypes = async () => {
      try {
        const response = await EXTRACTION_TYPES_SERVICE();
        sessionStorage.setItem("ExtractionTypes", JSON.stringify(response));
      } catch (error) {
        console.error("Failed to fetch extraction types", error);
      }
    };

    const fetchStatusTypes = async () => {
      try {
        const response = await STATUS_TYPES_SERVICE();
        sessionStorage.setItem("StatusTypes", JSON.stringify(response));
      } catch (error) {
        console.error("Failed to fetch status types", error);
      }
    };

    const fetchData = async () => {
      await Promise.all([fetchExtractionTypes(), fetchStatusTypes()]);
    };

    if (
      !sessionStorage.getItem("ExtractionTypes") ||
      !sessionStorage.getItem("StatusTypes")
    ) {
      fetchData();
    }
  }, []);

  const cards = [
    {
      icon: spreaderIcon,
      header: "Financial Spreader",
      subtitle:
        "Spread financial reports of private and public companies. Some public company data can be sourced from public sources.",
      onClick: () => {
        navigate("/spreader");
      }
    },
    {
      icon: spreaderIcon,
      header: "Tax Return Spreader",
      subtitle:
        "Spread financial reports of private and public companies. Some public company data can be sourced from public sources.",
      disabled: true
    }
  ];

  return (
    <div className="flex flex-col gap-6 px-6 pt-9">
      <p className="font-heading-1-m  text-heading-1-m ">Welcome to Foliosure!</p>
      <div className="flex  gap-[22px]">
        {cards.map((card, index) => (
          <IconCard
            key={index}
            icon={card.icon}
            header={card.header}
            subtitle={card.subtitle}
            onClick={card.onClick}
            disabled={card.disabled}
          />
        ))}
      </div>
    </div>
  );
};

export default HomePage;
