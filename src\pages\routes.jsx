import React, { Suspense, useState, useEffect, lazy } from "react";
import { Route, Routes, Navigate } from "react-router-dom";
import Header from "../partials/molecules/header/header.jsx";
import SpinnerCircle from "../partials/atoms/loader/spinner-circle";
import ScreenNotSupport from "../partials/molecules/not-support";
import { NotFound } from "../partials/atoms/not-found";
import HomePage from "./home";
import { Toaster } from "react-hot-toast";
import IdleTimeout from "../partials/molecules/idle-timeout/idle-timeout.jsx";

// Lazy load components
const PdfPage = lazy(() => import("./pdf-preview/PdfPage.jsx"));
const EvidencePanel = lazy(() => import("../components/evidence-panel/evidence-panel.tsx"));
const SpecificKpiPanel = lazy(() => import("./specific-kpi/specific-kpi.jsx"));

const RoutesConfig = () => {
  const [screenSize, setScreenSize] = useState(window.innerWidth);

  useEffect(() => {
    const handleResize = () => {
      setScreenSize(window.innerWidth);
    };
    
    window.addEventListener("resize", handleResize);
    
    // Cleanup the event listener when component unmounts
    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  if (screenSize < 600) return <ScreenNotSupport />;

  const mockProps = {
    handleBackfunc: () => console.log("Back button clicked"),
    processId: "", // Using the ID from your JSON data
    companyName: "", // From your JSON data
    companyTicker: "A010140", // From your JSON data
    acuityid: "KRPU547147066" // Using companyId from JSON data
  };

  return (
    <div className="flex h-[100%] min-h-screen flex-col bg-white">
      <main
        data-testid="main-container"
        className=" flex h-full grow flex-col "
      >
        <Routes>
          {/* Adding a default route that redirects to panel */}
          <Route path="/preview" element={
            <Suspense fallback={<SpinnerCircle />}>
              <PdfPage />
            </Suspense>
          } />
          <Route path="/panel" element={
            <Suspense fallback={<SpinnerCircle />}>
              <EvidencePanel  
                handleBackfunc={mockProps.handleBackfunc}
                processId={mockProps.processId}
                companyName={mockProps.companyName}
                companyTicker={mockProps.companyTicker}
                acuityid={mockProps.acuityid} 
              />
            </Suspense>
          } />
          <Route path="/specific-kpi" element={
            <Suspense fallback={<SpinnerCircle />}>
              <SpecificKpiPanel />
            </Suspense>
          } />
          <Route path="*" element={<NotFound />} />
        </Routes>
      </main>
      <Toaster />
      <IdleTimeout />
    </div>
  );
};

export default RoutesConfig;
