import React, { useState, forwardRef, useImperativeHandle } from "react";
import PropTypes from "prop-types";
import { Upload } from "@progress/kendo-react-upload";
import { FaRegHandPointRight } from "react-icons/fa";
import {
  IntlProvider,
  LocalizationProvider,
  loadMessages
} from "@progress/kendo-react-intl";

import bgMessages from "./bg.json";
import { FileListItem } from "./components/file-list-item";
import { UploadPanelHeader } from "./components/upload-panel-header";
import { useFileUpload } from "./hooks/use-file-upload";
import { useFileDelete } from "./hooks/use-file-delete";

loadMessages(bgMessages, "personal");

const CONSTANTS = {
  UPLOAD_BOX_TITLE: "Upload Document(s)",
  SAVE_URL: "#",
  REMOVE_URL: "#",
  ACCEPTED_FILE_FORMATS: "Accepted file format is .pdf only",
  FILE_SIZE_LIMIT: "Each file size should not exceed 50MB",
  MAX_FILE_LIMIT: 10
};

const UploadBox = forwardRef(
  (
    {
      companyID,
      processID,
      uploadedFilesCount,
      onUploadedFilesCountChange,
      uploadDocumentDetails,
      setUploadDocumentDetails,
      isFileLimitReached,
      selectedChipsCount
    },
    ref
  ) => {
    const [expanded, setExpanded] = useState(false);

    const [selectedFilingType, setSelectedFilingType] = useState([]);
   

    
    // Use custom hooks to separate concerns
    const {
      files,
      setFiles,
      fileCount,
      setFileCount,
      progress,
      setProgress,
      fileDocumentMap,
      setFileDocumentMap,
      uploadKey,
      setUploadKey,
      resetState,
      handleUpload,
      handleAddFiles
    } = useFileUpload(
      companyID,
      processID,
      onUploadedFilesCountChange,
      setUploadDocumentDetails
    );

    const { handleDeleteFile } = useFileDelete(
      files,
      setFiles,
      fileDocumentMap,
      setFileDocumentMap,
      setProgress,
      setFileCount,
      onUploadedFilesCountChange,
      uploadDocumentDetails,
      setUploadDocumentDetails,
      setUploadKey
    );

    useImperativeHandle(ref, () => ({
      resetuploadFilesrefState() {
        setExpanded(false);
        resetState();
      }
    }));

    const handlePanelToggle = () => {
      setExpanded(!expanded);
    };

    const handleFilingTypeSelection = (item, id) => {
      setSelectedFilingType((prevSelected) => {
      const existingIndex = prevSelected.findIndex((entry) => entry.id === id);
      if (existingIndex !== -1) {
        // Update existing entry
        const updatedSelected = [...prevSelected];
        updatedSelected[existingIndex].item = item;
        return updatedSelected;
      } else {
        // Add new entry
        return [...prevSelected, { id, item }];
      }
      });
    };

    const handleDateChange = (e, fileId) => {
      const selectedDate = e.target.value;
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      const fileIndex = files.findIndex((f) => f.uid === fileId);
      if (fileIndex !== -1) {
        setFiles((prevFiles) => {
          const newFiles = [...prevFiles];
          if (selectedDate && selectedDate > today) {
            newFiles[fileIndex] = {
              ...newFiles[fileIndex],
              dateError: "Future date selected",
              filingDate: selectedDate
            };
          } else {
            newFiles[fileIndex] = {
              ...newFiles[fileIndex],
              dateError: null,
              filingDate: selectedDate
            };
          }
          return newFiles;
        });
      }
    };

    const listItemUI = (props) => {
      const { files: propFiles } = props;
      return propFiles?.map((propfile) => {
        let file = files.find((x) => x.uid === propfile.uid);
        if (!file) return null;

        // Update file progress if needed
        const fileIndex = progress.findIndex((p) => p.fileId === file.uid);
        if (fileIndex !== -1) {
          file.progress = progress[fileIndex].progress;
        }

        return (
          <FileListItem
            key={file.uid}
            file={file}
            handleDeleteFile={handleDeleteFile}
            handleDateChange={handleDateChange}
            handleFilingTypeSelection={handleFilingTypeSelection}
            selectedFilingType={selectedFilingType}
          />
        );
      });
    };

    return (
      <div className="rounded-lg border border-(--neutral-gray-10) gap-6 w-full">
        <UploadPanelHeader
          expanded={expanded}
          handlePanelToggle={handlePanelToggle}
          fileCount={fileCount}
          title={CONSTANTS.UPLOAD_BOX_TITLE}
        />

        {expanded && (
          <div className="flex flex-col py-4 gap-5 text-body-r font-body-r">
            <LocalizationProvider language="personal">
              <IntlProvider locale="en-US">
                <Upload
                  key={uploadKey}
                  batch={false}
                  multiple={true}
                  files={files}
                  defaultFiles={files}
                  withCredentials={false}
                  saveUrl={CONSTANTS.SAVE_URL}
                  removeUrl={CONSTANTS.REMOVE_URL}
                  onAdd={(e) =>
                    handleAddFiles(
                      e,
                      CONSTANTS.MAX_FILE_LIMIT,
                      selectedChipsCount
                    )
                  }
                  onRemove={(e) => {
                    e.preventDefault();
                    if (e.affectedFiles && e.affectedFiles.length > 0) {
                      const processFiles = async () => {
                        for (const file of e.affectedFiles) {
                          await handleDeleteFile(file.uid);
                        }
                        handleUpload(e);
                      };
                      processFiles();
                    } else {
                      handleUpload(e);
                    }
                  }}
                  listItemUI={listItemUI}
                  onChange={handleUpload}
                  className={`ao-custom-upload-area ao-custom-upload-preview-box ao-custom-upload-file ao-custom-upload-top ao-custom-select-file ao-custom-button-background ao-custom-button-text-background ao-custom-icon ${
                    isFileLimitReached ? "file-selection-disabled" : ""
                  }`}
                />
              </IntlProvider>
            </LocalizationProvider>

            {files.length === 0 && (
              <div className="border border-(--neutral-gray-10) px-1"></div>
            )}

            <div className="flex items-center ml-4">
              <FaRegHandPointRight className="mr-2 text-(--neutral-gray-90)" />
              <span
                className="text-(--neutral-gray-90)"
                style={{ fontSize: "12px" }}
              >
                {CONSTANTS.ACCEPTED_FILE_FORMATS}
              </span>
            </div>

            <div className="flex items-center ml-4">
              <FaRegHandPointRight className="mr-2 text-(--neutral-gray-90)" />
              <span
                className="text-body-r font-body-r text-(--neutral-gray-90)"
                style={{ fontSize: "12px" }}
              >
                {CONSTANTS.FILE_SIZE_LIMIT}
              </span>
            </div>
          </div>
        )}
      </div>
    );
  }
);

UploadBox.propTypes = {
  companyID: PropTypes.string.isRequired,
  processID: PropTypes.string.isRequired,
  uploadedFilesCount: PropTypes.number.isRequired,
  onUploadedFilesCountChange: PropTypes.func.isRequired,
  uploadDocumentDetails: PropTypes.array.isRequired,
  setUploadDocumentDetails: PropTypes.func.isRequired,
  isFileLimitReached: PropTypes.bool.isRequired,
  selectedChipsCount: PropTypes.number.isRequired
};

export default UploadBox;
