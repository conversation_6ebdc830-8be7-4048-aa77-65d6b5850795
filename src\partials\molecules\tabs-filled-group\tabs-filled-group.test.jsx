import { render, screen, fireEvent } from "@testing-library/react";
import TabsFilledGroup from "./tabs-filled-group";

const tabs = [
  { name: "Tab 1", key: "tab1", component: <div>Content 1</div> },
  { name: "Tab 2", key: "tab2", component: <div>Content 2</div> }
];

describe("TabsFilledGroup", () => {
  test("renders all tabs", () => {
    render(
      <TabsFilledGroup tabs={tabs} activeTab="tab1" setActiveTab={jest.fn()} />
    );
    tabs.forEach((tab) => {
      expect(screen.getByText(tab.name)).toBeInTheDocument();
    });
  });

  test("renders the active tab content", () => {
    render(
      <TabsFilledGroup tabs={tabs} activeTab="tab1" setActiveTab={jest.fn()} />
    );
    expect(screen.getByText("Content 1")).toBeInTheDocument();
    expect(screen.queryByText("Content 2")).not.toBeInTheDocument();
  });

  test("calls setActiveTab when a tab is clicked", () => {
    const setActiveTab = jest.fn();
    render(
      <TabsFilledGroup
        tabs={tabs}
        activeTab="tab1"
        setActiveTab={setActiveTab}
      />
    );
    fireEvent.click(screen.getByText("Tab 2"));
    expect(setActiveTab).toHaveBeenCalledWith("tab2");
  });

  test("applies active class to the active tab", () => {
    render(
      <TabsFilledGroup tabs={tabs} activeTab="tab1" setActiveTab={jest.fn()} />
    );
    expect(screen.getByText("Tab 1")).toHaveClass(
      "inline-block items-center truncate"
    );
    expect(screen.getByText("Tab 2")).not.toHaveClass(
      "bg-primary-78 text-white"
    );
  });
});
