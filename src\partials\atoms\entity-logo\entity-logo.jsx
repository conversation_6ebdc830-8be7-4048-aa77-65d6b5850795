import React, { useState } from "react";
import PropTypes from "prop-types";
import LogoPlaceholder from "../../../resources/images/icon-logo-placeholder";

const EntityLogo = ({ src }) => {
  const logoSrc = src?.length > 0 && src[0].value;
  const [error] = useState(!logoSrc);

  return error ? (
    <LogoPlaceholder />
  ) : (
    <img className="h-4 w-4" src={logoSrc} alt={""} />
  );
};

EntityLogo.propTypes = {
  src: PropTypes.array
};

export default EntityLogo;
