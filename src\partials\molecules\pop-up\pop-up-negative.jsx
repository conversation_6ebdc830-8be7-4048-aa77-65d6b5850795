import { React, Fragment } from "react";
import PropTypes from "prop-types";
import {
  Dialog,
  Transition,
  TransitionChild,
  DialogPanel
} from "@headlessui/react";
import { FaTimes } from "react-icons/fa";
import { Button } from "../../atoms/button";

const PopUpNegative = ({
  header,
  children,
  showPopUp,
  setShowPopUp,
  cancelButtonText,
  submitButtonText,
  onSubmit,
  disabled,
  cols,
  showBackbutton,
  onClickBack,
  colGrid
}) => {
  return (
    <div>
      <Transition appear show={showPopUp} as={Fragment}>
        <Dialog as="div" className="relative z-50" onClose={setShowPopUp}>
          <TransitionChild
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-neutral-80 bg-opacity-50" />
          </TransitionChild>

          <div className="fixed inset-0 overflow-y-auto">
            <div
              className={`grid min-h-full ${colGrid} items-center justify-center text-center`}
            >
              <TransitionChild
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <DialogPanel
                  className={`${cols} transform overflow-hidden rounded-lg bg-white text-left align-middle shadow-xl transition-all`}
                >
                  <div className="heading-2-m flex h-14 items-center justify-between bg-primary-40 p-4 px-6 text-left leading-6 text-neutral-90">
                    <div className="flex items-center justify-start">
                      {header}
                    </div>
                    <div className="rounded text-neutral-60 hover:bg-neutral-20 hover:bg-opacity-30 focus:bg-neutral-30 active:bg-neutral-30 active:bg-opacity-60">
                      <span className="flex cursor-pointer place-items-end items-center justify-end p-1 hover:opacity-100">
                        <FaTimes
                          onClick={() => {
                            setShowPopUp(false);
                          }}
                        />
                      </span>
                    </div>
                  </div>

                  {children}

                  <div className="flex justify-between border-t border-neutral-10 px-6 py-4">
                    <div className="justify-start">
                      {showBackbutton && (
                        <Button
                          data-testid="pop-up-back-button"
                          onClick={onClickBack}
                          intent={"teritory"}
                        >
                          Back
                        </Button>
                      )}
                    </div>
                    <div className="flex justify-end gap-2">
                      <Button
                        onClick={() => {
                          setShowPopUp(false);
                        }}
                        intent={"secondary"}
                      >
                        {cancelButtonText}
                      </Button>

                      <Button
                        data-testid="popup-negative-submit"
                        onClick={() => {
                          onSubmit();
                        }}
                        disabled={disabled}
                        intent={"primarynegative"}
                      >
                        {submitButtonText}
                      </Button>
                    </div>
                  </div>
                </DialogPanel>
              </TransitionChild>
            </div>
          </div>
        </Dialog>
      </Transition>
    </div>
  );
};

export default PopUpNegative;

PopUpNegative.propTypes = {
  header: PropTypes.node.isRequired,
  children: PropTypes.node,
  showPopUp: PropTypes.bool.isRequired,
  setShowPopUp: PropTypes.func.isRequired,
  cancelButtonText: PropTypes.string,
  submitButtonText: PropTypes.string,
  onSubmit: PropTypes.func.isRequired,
  disabled: PropTypes.bool,
  cols: PropTypes.string,
  showBackbutton: PropTypes.bool,
  onClickBack: PropTypes.func,
  colGrid: PropTypes.string
};
