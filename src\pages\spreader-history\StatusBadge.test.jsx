import React from "react";
import { render, screen } from "@testing-library/react";
import StatusBadge from "./StatusBadge";

// Mock the icon components
jest.mock("react-icons/fa", () => ({
  FaCheckCircle: () => <span data-testid="fa-check-circle" />
}));

jest.mock("react-icons/bs", () => ({
  BsExclamationCircleFill: () => (
    <span data-testid="bs-exclamation-circle-fill" />
  )
}));

jest.mock("react-icons/cg", () => ({
  CgSpinner: () => <span data-testid="cg-spinner" className="animate-spin" />
}));

describe("StatusBadge", () => {
  test('renders "In Progress" status correctly', () => {
    const { container } = render(<StatusBadge status="In Progress" />);

    // Check text content
    expect(screen.getByText("In Progress")).toBeInTheDocument();
    
    // Check spinner icon
    expect(screen.getByTestId("cg-spinner")).toBeInTheDocument();
    expect(screen.getByTestId("cg-spinner")).toHaveClass("animate-spin");
    
    // Check container styling
    const badgeContainer = container.firstChild;
    expect(badgeContainer).toHaveClass(
      "body-m",
      "flex",
      "w-fit",
      "items-center",
      "gap-2",
      "rounded-full",
      "border",
      "border-noticeable-60",
      "bg-noticeable-50",
      "px-3",
      "py-1",
      "text-neutral-80"
    );
  });

  test('renders "Completed" status correctly', () => {
    const { container } = render(<StatusBadge status="Completed" />);

    // Check text content
    expect(screen.getByText("Extraction completed")).toBeInTheDocument();
    
    // Check check icon
    expect(screen.getByTestId("fa-check-circle")).toBeInTheDocument();
    
    // Check container styling
    const badgeContainer = container.firstChild;
    expect(badgeContainer).toHaveClass(
      "body-m",
      "flex",
      "w-fit",
      "items-center",
      "gap-2",
      "rounded-full",
      "border",
      "border-positive-60",
      "bg-[#ECFFF1]",
      "px-3",
      "py-1",
      "text-neutral-80"
    );
  });

  test('renders "Failed" status correctly', () => {
    const { container } = render(<StatusBadge status="Failed" />);

    // Check text content
    expect(screen.getByText("Failed")).toBeInTheDocument();
    
    // Check exclamation icon
    expect(screen.getByTestId("bs-exclamation-circle-fill")).toBeInTheDocument();
    
    // Check container styling
    const badgeContainer = container.firstChild;
    expect(badgeContainer).toHaveClass(
      "body-m",
      "flex",
      "w-fit",
      "items-center",
      "gap-2",
      "rounded-full",
      "border",
      "border-negative-60",
      "bg-[#FFEFF0]",
      "px-3",
      "py-1",
      "text-neutral-80"
    );
  });

  test("renders default status for unknown status values", () => {
    const { container } = render(<StatusBadge status="Unknown" />);

    // Check text content
    expect(screen.getByText("Extraction completed")).toBeInTheDocument();
    
    // Check check icon
    expect(screen.getByTestId("fa-check-circle")).toBeInTheDocument();
    
    // Check container styling - should be the same as "Completed"
    const badgeContainer = container.firstChild;
    expect(badgeContainer).toHaveClass(
      "body-m",
      "flex",
      "w-fit",
      "items-center",
      "gap-2",
      "rounded-full",
      "border",
      "border-positive-60",
      "bg-[#ECFFF1]",
      "px-3",
      "py-1",
      "text-neutral-80"
    );
  });
});
