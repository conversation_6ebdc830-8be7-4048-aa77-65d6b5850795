import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import TabsGroup from "./tabs-group";
import useDocumentViewerStore from "../../organisms/document-viewer/document-viewer.store";

jest.mock("../../organisms/document-viewer/document-viewer.store", () => ({
  __esModule: true,
  default: jest.fn()
}));

describe("TabsGroup", () => {
  const tabs = [
    { name: "Filings", key: "filings", component: <div>Filings Content</div> },
    {
      name: "Transcripts",
      key: "transcripts",
      component: <div>Transcripts Content</div>
    }
  ];

  beforeEach(() => {
    useDocumentViewerStore.mockReturnValue({ isPreviewOpen: false });
  });

  test("renders without crashing", () => {
    render(
      <TabsGroup tabs={tabs} activeTab="filings" setActiveTab={() => {}} />
    );
    expect(screen.getByText("Filings")).toBeInTheDocument();
    expect(screen.getByText("Transcripts")).toBeInTheDocument();
  });

  test("renders the correct number of tabs", () => {
    render(
      <TabsGroup tabs={tabs} activeTab="filings" setActiveTab={() => {}} />
    );
    expect(screen.getAllByRole("tab")).toHaveLength(tabs.length);
  });

  test("highlights the active tab", () => {
    render(
      <TabsGroup tabs={tabs} activeTab="filings" setActiveTab={() => {}} />
    );
    const activeTab = screen.getByText("Filings");
    expect(activeTab).toHaveClass("inline-block items-center truncate");
  });

  test("changes active tab on click", () => {
    render(
      <TabsGroup tabs={tabs} activeTab="filings" setActiveTab={() => {}} />
    );
    const transcriptsTab = screen.getByText("Transcripts");
    fireEvent.click(transcriptsTab);
  });
});
