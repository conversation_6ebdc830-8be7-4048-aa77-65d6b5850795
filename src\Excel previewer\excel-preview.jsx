import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Spreadsheet } from '@progress/kendo-react-spreadsheet';
import * as XLSX from 'xlsx';
import '@progress/kendo-theme-default/dist/all.css';
import { baseUrl } from "../constants/config";

const EXCEL_URL = `${baseUrl}extraction/api/download-file?fileKey=Documents%2F00000000-0000-0000-0000-000000000001%2F39cde052-8244-408c-99a5-1be402d633af%2F256c3b2b-b81d-4743-9918-b7dda037ddc8.xlsx`;
const DEFAULT_SHEET_NAME = "Example Test";
const DEFAULT_CELL_ADDRESS = "B4";

const ExcelPreview = ({ excelHighlight, excelData, selectedCellInfo, onClose, isLoading, s3FilePath }) => {
  const spreadsheetRef = useRef(null);
  const [loading, setLoading] = useState(isLoading);
  const [error, setError] = useState(null);
  const [spreadsheetData , setSpreadsheetData] = useState(null);
  const [shouldHighlight, setShouldHighlight] = useState(false);
  const hasHighlightedRef = useRef(false);

  // Use excelHighlight data if available, otherwise use defaults
  const [sheetName, setSheetName] = useState(
    excelHighlight?.sheet || DEFAULT_SHEET_NAME
  );
  const [cellAddress, setCellAddress] = useState(
    excelHighlight?.reference || DEFAULT_CELL_ADDRESS
  );
  const [initialLoadComplete, setInitialLoadComplete] = useState(false);

  // Construct the Excel URL using the S3 file path
  const getExcelUrl = useCallback(() => {
    if (s3FilePath) {
      // Remove everything before and including 'Documents/'
    let cleanedKey = s3FilePath.replace(/^.*Documents\//, 'Documents/');  
      // Use the S3 file path to construct the download URL
      const encodedFileKey = encodeURIComponent(cleanedKey);
      return `${baseUrl}extraction/api/download-file?fileKey=${encodedFileKey}`;
    }
    // Fallback to the default URL if no S3 path is provided
    return EXCEL_URL;
  }, [s3FilePath]);


  const loadWorkbook = useCallback(async (spreadsheet) => {
    if (!spreadsheet) return;
    try {
      let arrayBuffer;
      
      // Use provided excelData if available, otherwise fallback to URL
      if (excelData && excelData.file) {
        // If excelData.file is already an ArrayBuffer
        if (excelData.file instanceof ArrayBuffer) {
          arrayBuffer = excelData.file;
        } 
        // If it's a base64 string
        else if (typeof excelData.file === 'string') {
          const base64Data = excelData.file.includes(',') ? excelData.file.split(',')[1] : excelData.file;
          const binaryString = atob(base64Data);
          const bytes = new Uint8Array(binaryString.length);
          for (let i = 0; i < binaryString.length; i++) {
            bytes[i] = binaryString.charCodeAt(i);
          }
          arrayBuffer = bytes.buffer;
        }
        // If it's a Blob or File
        else if (excelData.file instanceof Blob) {
          arrayBuffer = await excelData.file.arrayBuffer();
        }
        else {
          throw new Error('Unsupported excelData.file format');
        }
      } else {
        // Fallback to fetching from URL using the S3 file path
        const excelUrl = getExcelUrl();
        const response = await fetch(excelUrl);
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        arrayBuffer = await response.arrayBuffer();
      }
      

    const data = new Uint8Array(arrayBuffer);
    const workbook = XLSX.read(data, { type: 'array' });

    const xlsxData = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });
    const blob = new Blob([xlsxData], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });
    const file = new File([blob], excelData?.fileName || 'converted.xlsx', {
      type: blob.type,
    });

    setSpreadsheetData(workbook);
    spreadsheet.fromFile(file); 
    setShouldHighlight(true);
  } catch (e) {
    setError(e.message);
    setLoading(false);
  }
}, [excelData, getExcelUrl]);

const spreadsheetRefCallback = useCallback(node => {
  if (node) {
    spreadsheetRef.current = node;
  }
}, []);

  useEffect(() => {
  if (spreadsheetRef.current) {
    loadWorkbook(spreadsheetRef.current);
    if (cellAddress) {
       setTimeout(() => {
      const sheet = spreadsheetRef.current.activeSheet();
      const range = sheet.range(cellAddress);
      range.select();
      hasHighlightedRef.current = true;
       }, 100); // Delay ensures rendering is complete
    }
  }
}, [loadWorkbook]);



const handleExcelImport = () => {
  if (spreadsheetRef.current && spreadsheetData) {
    const spreadsheet = spreadsheetRef.current;
    if (cellAddress) {
      setTimeout(() => {
      const sheet = spreadsheet.activeSheet();
      const range = sheet.range(cellAddress);
      range.select();
       }, 100); // Delay ensures rendering is complete
    }
      setLoading(false);
  }
};

// Set cellAddress and sheetName from excelHighlight only when excelHighlight changes
useEffect(() => {
  if (excelHighlight?.reference) {
    setCellAddress(excelHighlight.reference);
  }
  if (excelHighlight?.sheet) {
    setSheetName(excelHighlight.sheet);
  }
  if (spreadsheetRef.current && spreadsheetData) {
    const spreadsheet = spreadsheetRef.current;
      const sheet = spreadsheet.activeSheet();
      sheet.select(excelHighlight.reference);
      const range = sheet.range(excelHighlight.reference);
      range.select();
      setShouldHighlight(false);
  }
}, [excelHighlight]);

// Select the cell after spreadsheet is loaded and cellAddress is set
useEffect(() => {
  if (
    spreadsheetRef.current &&
    cellAddress &&
    sheetName &&
    !loading &&
    spreadsheetData
  ) {
    // Switch to the correct sheet if needed
    const tabs = document.querySelectorAll('.k-tabstrip-item .k-link');
    for (let tab of tabs) {
      if (tab.textContent.trim() === sheetName) {
        tab.click();
        break;
      }
    }
    setTimeout(() => {
      try {
        if (spreadsheetRef.current && spreadsheetData) {
            const spreadsheet = spreadsheetRef.current;
            const sheet = spreadsheet.activeSheet();
            if (sheet && cellAddress) {
              sheet.select(cellAddress);
              const range = sheet.range(cellAddress);
          range.select();
        }
        }
      } catch (e) {
        // ignore
      }
    }, 300); // Slightly longer delay to ensure sheet is rendered
  }
}, [loading, cellAddress, sheetName, spreadsheetData]);

  const clickTabByName = async (sheetName, retries = 3) => {
    for (let i = 0; i < retries; i++) {
      // Wait for DOM to be ready
      await new Promise(resolve => setTimeout(resolve, 100));
      
      const tabs = document.querySelectorAll('.k-tabstrip-item .k-link');
      
      // Exact match
      for (let tab of tabs) {
        if (tab.textContent.trim() === sheetName) {
          tab.click();
          return true;
        }
      }
    }
    
    alert(`❌ Tab "${sheetName}" not found`);
    return false;
  };

  if (error) {
    return <div>Error loading spreadsheet: {error}</div>;
  }

  return (
    <div style={{ display: 'flex', height: '100%' }}>
      <div style={{ flexGrow: 1, position: 'relative' }}>
       
      <Spreadsheet
        ref={spreadsheetRefCallback}
        data={spreadsheetData}
        onExcelImport={handleExcelImport}
        style={{ width: '100%', height: '100%' }}
      />
        {loading && (
          <div style={{
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '100%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            backgroundColor: 'rgba(255, 255, 255, 0.8)',
            zIndex: 10,
          }}>
            Loading Spreadsheet...
          </div>
        )}
      </div>
    </div>
  );
};

export default ExcelPreview;
