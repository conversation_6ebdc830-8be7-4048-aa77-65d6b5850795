import React from "react";
import { render, screen, act, waitFor } from "@testing-library/react";
import SpreaderHistory from "./spreader-history";
import { GET_SPREADER_HISTORY_API } from "../../infra/api/SpreaderHistory/spreader-history-service";

// Mock the API call
jest.mock("../../infra/api/SpreaderHistory/spreader-history-service");

// Mock the CompanyCard component
jest.mock("./CompanyCard", () => {
  return function MockCompanyCard({ company, onClick }) {
    return (
      <div
        data-testid={`company-card-${company.processId}`}
        data-company-id={company.processId}
        data-company-status={company.status}
        onClick={() => onClick(company)}
      >
        {company.name}
      </div>
    );
  };
});

describe("SpreaderHistory", () => {
  const mockHistoryData = [
    {
      processId: "123",
      jobId: "JOB123",
      extractionType: "PDF",
      acuityId: "ACU123",
      name: "Company A",
      ticker: "CMPA",
      createdOn: "2023-01-01T12:00:00Z",
      status: "Completed"
    },
    {
      processId: "456",
      jobId: "JOB456",
      extractionType: "Excel",
      acuityId: "ACU456",
      name: "Company B",
      ticker: "CMPB",
      createdOn: "2023-01-02T12:00:00Z",
      status: "In Progress"
    },
    {
      processId: "789",
      jobId: "JOB789",
      extractionType: "Word",
      acuityId: "ACU789",
      name: "Company C",
      ticker: "CMPC",
      createdOn: "2023-01-03T12:00:00Z",
      status: "Failed"
    }
  ];

  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
    GET_SPREADER_HISTORY_API.mockResolvedValue([...mockHistoryData]);
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  test("renders company cards from provided history data", async () => {
    render(<SpreaderHistory historyData={mockHistoryData} />);

    // Wait for useState to be processed
    await waitFor(() => {
      expect(screen.getByTestId("company-card-123")).toBeInTheDocument();
      expect(screen.getByTestId("company-card-456")).toBeInTheDocument();
      expect(screen.getByTestId("company-card-789")).toBeInTheDocument();
    });
  });

  test("calls onCardSelect with company when a completed card is clicked", async () => {
    const mockOnCardSelect = jest.fn();

    render(
      <SpreaderHistory
        historyData={mockHistoryData}
        onCardSelect={mockOnCardSelect}
      />
    );

    await waitFor(() => {
      expect(screen.getByTestId("company-card-123")).toBeInTheDocument();
    });

    // Click on a card with "Completed" status
    const completedCard = screen.getByTestId("company-card-123");
    completedCard.click();

    expect(mockOnCardSelect).toHaveBeenCalled();
    // The first argument should be the company object
    expect(mockOnCardSelect.mock.calls[0][0]).toHaveProperty(
      "processId",
      "123"
    );
  });

  test("does not call onCardSelect when a non-completed card is clicked", async () => {
    const mockOnCardSelect = jest.fn();

    render(
      <SpreaderHistory
        historyData={mockHistoryData}
        onCardSelect={mockOnCardSelect}
      />
    );

    await waitFor(() => {
      expect(screen.getByTestId("company-card-456")).toBeInTheDocument();
      expect(screen.getByTestId("company-card-789")).toBeInTheDocument();
    });

    // Click on cards with non-completed statuses
    screen.getByTestId("company-card-456").click();
    screen.getByTestId("company-card-789").click();

    expect(mockOnCardSelect).not.toHaveBeenCalled();
  });

  test("updates history list periodically using API call", async () => {
    render(<SpreaderHistory historyData={mockHistoryData} />);

    await waitFor(() => {
      expect(screen.getByTestId("company-card-123")).toBeInTheDocument();
    });

    // Update mock API response for the next call
    const updatedMockData = [
      {
        processId: "999",
        jobId: "JOB999",
        extractionType: "PDF",
        acuityId: "ACU999",
        name: "Company D",
        ticker: "CMPD",
        createdOn: "2023-01-04T12:00:00Z",
        status: "Completed"
      }
    ];
    GET_SPREADER_HISTORY_API.mockResolvedValue(updatedMockData);

    // Fast-forward time to trigger the API call
    await act(async () => {
      jest.advanceTimersByTime(30000);
    });

    // After the API call, the component should display the updated data
    await waitFor(() => {
      expect(screen.getByTestId("company-card-999")).toBeInTheDocument();
      expect(screen.queryByTestId("company-card-123")).not.toBeInTheDocument();
    });
  });

  test("handles empty history data gracefully", () => {
    render(<SpreaderHistory historyData={[]} />);
    expect(screen.queryByTestId(/company-card/)).not.toBeInTheDocument();
  });

  test("handles null history data gracefully", () => {
    render(<SpreaderHistory historyData={null} />);
    expect(screen.queryByTestId(/company-card/)).not.toBeInTheDocument();
  });
});
