import React from "react";
import PropTypes from "prop-types";
import { FaSearch, FaTimes } from "react-icons/fa";

const SearchBox = ({ searchQuery, setSearchQuery, currentTabLabel }) => {
  const handleInputChange = (e) => {
    setSearchQuery(e.target.value);
  };
  const clearSearch = () => {
    setSearchQuery("");
  };
  return (
    <div className="flex w-[24.75rem] rounded  border gap-4 p-1.5 px-4 border-neutral-10">
      <div className="flex flex-row gap-2 items-center w-full">
        <FaSearch className="text-primary-78" />
        <input
          type="text"
          value={searchQuery}
          placeholder={`Search line item or value in ${currentTabLabel}`}
          className="w-full text-body-r font-body-r text-neutral-80 placeholder:text-neutral-30 p-0 border-transparent border-0 focus:outline-none focus:ring-0"
          onChange={handleInputChange}
        />
        {searchQuery && (
          <FaTimes
            onClick={clearSearch}
            className="cursor-pointer text-neutral-60"
          />
        )}
      </div>
    </div>
  );
};
SearchBox.propTypes = {
  searchQuery: PropTypes.string.isRequired,
  setSearchQuery: PropTypes.func.isRequired,
  currentTabLabel: PropTypes.string.isRequired
};

export default SearchBox;
