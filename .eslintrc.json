{
  "env": {
    "browser": true,
    "jest": true,
    "es6": true,
    "node": true
  },
  "plugins": ["react"],
  "extends": ["react-app", "eslint:recommended"],
  "parserOptions": {
    "ecmaVersion": 2018,
    "sourceType": "module"
  },
  "rules": {
    "class-methods-use-this": [1],
    "constructor-super": "error",
    "default-case": "error",
    "eqeqeq": "error",
    "for-direction": "error",
    "getter-return": "error",
    "import/first": "error",
    "no-alert": "error",
    "no-async-promise-executor": "error",
    "no-case-declarations": "error",
    "no-class-assign": "error",
    "no-compare-neg-zero": "error",
    "no-cond-assign": "error",
    "no-console": "error",
    "no-constant-condition": "error",
    "no-const-assign": "error",
    "no-control-regex": "error",
    "no-debugger": "error",
    "no-delete-var": "error",
    "no-dupe-args": "error",
    "no-dupe-class-members": "error",
    "no-dupe-else-if": "error",
    "no-dupe-keys": "error",
    "no-duplicate-case": "error",
    "no-empty": "error",
    "no-empty-character-class": "error",
    "no-empty-pattern": "error",
    "no-ex-assign": "error",
    "no-extra-boolean-cast": "error",
    "no-extra-semi": "error",
    "no-fallthrough": "error",
    "no-func-assign": "error",
    "no-global-assign": "error",
    "no-import-assign": "error",
    "no-inner-declarations": "error",
    "no-invalid-regexp": "error",
    "no-irregular-whitespace": "error",
    "no-loop-func": "warn",
    "no-loss-of-precision": "error",
    "no-misleading-character-class": "error",
    "no-mixed-spaces-and-tabs": "error",
    "no-multiple-empty-lines": [
      2,
      {
        "max": 1,
        "maxEOF": 0,
        "maxBOF": 0
      }
    ],
    "no-new-symbol": "error",
    "no-nonoctal-decimal-escape": "error",
    "no-obj-calls": "error",
    "no-octal": "error",
    "no-prototype-builtins": "error",
    "no-redeclare": "error",
    "no-regex-spaces": "error",
    "no-return-assign": [1],
    "no-self-assign": "error",
    "no-setter-return": "error",
    "no-shadow": "error",
    "no-shadow-restricted-names": "error",
    "no-sparse-arrays": "error",
    "no-this-before-super": "error",
    "no-undef": "error",
    "no-underscore-dangle": [0, "always"],
    "no-unexpected-multiline": "error",
    "no-unreachable": "error",
    "no-unsafe-finally": "error",
    "no-unsafe-negation": "error",
    "no-unused-labels": "error",
    "no-unused-vars": "error",
    "no-useless-backreference": "error",
    "no-useless-catch": "error",
    "no-useless-concat": "error",
    "no-useless-escape": "error",
    "no-with": "error",
    "react/jsx-filename-extension": 0,
    "react/jsx-uses-react": 2,
    "react/jsx-uses-vars": 2,
    "react/no-array-index-key": [0],
    "react/no-multi-comp": 2,
    "react/prefer-es6-class": 2,
    "react/prop-types": "error",
    "require-yield": "error",
    "semi": "error",
    "space-in-parens": [0, "always"],
    "use-isnan": "error",
    "valid-typeof": "error",
    "no-unsafe-optional-chaining": "warn",
    "import/no-anonymous-default-export": "error",
    "react-hooks/exhaustive-deps": 0,
    "no-nested-ternary": "error",
    "array-bracket-spacing": "warn",
    "block-scoped-var": 1,
    "block-spacing": "warn",

    "camelcase": ["warn", { "properties": "never" }],
    "complexity": ["warn", 20],
    "computed-property-spacing": ["warn", "never"],
    "func-call-spacing": ["warn", "never"],
    // "indent": ["warn", 2, { "SwitchCase": 1 }],
    "keyword-spacing": ["warn", { "before": true, "after": true }],
    "max-depth": ["error", { "max": 5 }],
    "max-nested-callbacks": ["error", { "max": 10 }],
    "no-eval": "error",
    "no-implicit-globals": "error",
    "no-lonely-if": "error",
    // "no-magic-numbers": [ "error", { "ignore": [ -1, 0, 1 ] } ],
    "no-multi-str": "error",
    "no-new": "error",
    "no-param-reassign": ["error", { "props": false }],
    "no-script-url": "error",
    "no-sequences": "error",
    "no-use-before-define": ["error", { "functions": false }],

    "space-before-blocks": ["warn", "always"],

    "strict": "error",

    "max-lines": [
      0,
      {
        "max": 500,
        "skipBlankLines": true,
        "skipComments": true
      }
    ]
  },
  "overrides": [
    {
      "files": ["*.js", "*.jsx"],
      "excludedFiles": ["*.test.js*", "*.html"]
    }
  ]
}
