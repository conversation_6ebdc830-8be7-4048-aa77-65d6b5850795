import React, { useState } from "react";
import { FaChevronDown, FaChevronUp } from "react-icons/fa";
import { DropDownList } from "@progress/kendo-react-dropdowns";

const LegendsDropdown = () => {
  const [isOpen, setIsOpen] = useState(false);
  const categories = ["Mapped", "Unmapped"];

  // Custom rendering for the value
  const valueRender = (element) => {
    return (
      <div className="flex items-center justify-between gap-2 ">
        <span className="text-body-r font-body-r text-neutral-80 whitespace-nowrap">
          All Legends
        </span>
        {isOpen ? (
          <FaChevronUp className="text-neutral-60 w-3 h-3" />
        ) : (
          <FaChevronDown className="text-neutral-60 w-3 h-3" />
        )}
      </div>
    );
  };

  // Custom rendering for dropdown items
  const itemRender = (li, itemProps) => {
    const itemChildren = (
      <div className="flex items-center gap-2  p-1">
        <div
          className="w-1 h-4 rounded"
          style={{
            backgroundColor:
              itemProps.dataItem === "Mapped"
                ? "#438A0C" // green-salad-120 equivalent
                : "#B80D10" // negative-110 equivalent
          }}
        ></div>
        <div className="text-body-r font-body-r text-neutral-80">
          {itemProps.dataItem}
        </div>
      </div>
    );

    // Clone with custom className to target with CSS
    return React.cloneElement(
      li,
      {
        ...li.props,
        className: `${li.props.className} legends-item`
      },
      itemChildren
    );
  };
  
  // Prevents selection in the dropdown
  const handleChange = (event) => {
    return false; // Prevents selection
  };

  return (
    <DropDownList
      className="legends-dropdown"
      id="legends"
      defaultValue={"Legends"}
      data={categories}
      onChange={handleChange}
      valueRender={valueRender}
      itemRender={itemRender}
      onOpen={() => setIsOpen(true)}
      onClose={() => setIsOpen(false)}
      popupProps={{
        className: "legends-dropdown-popup"
      }}
    />
  );
};

export default LegendsDropdown;
