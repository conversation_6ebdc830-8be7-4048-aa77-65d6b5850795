import React from "react";

function EmptyCompanyData() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="206"
      height="164"
      fill="none"
      viewBox="0 0 206 164"
    >
      <g clipPath="url(#clip0_10607_684)">
        <path
          fill="url(#paint0_linear_10607_684)"
          d="M74.968 32.946S103.542 2.5 138.26 2.5c34.718 0 29.345 28.176 43.179 30.446 13.835 2.27-17.024 29.704-15.191 37.824 1.832 8.121 18.405-18.188 15.191-3.033-3.214 15.155 34.406 20.282 19.599 39.358-14.808 19.077-58.328 34.689-78.827 36.948-20.499 2.258-29.02 18.317-51.133 18.317-22.112 0-22.15-7.269-37.318-18.317-15.169-11.049-19.398-4.998-27.98-19.922S-.373 81.337 9.098 67.234c9.47-14.102 36.557 8.652 39.597 6.942 3.04-1.71 26.273-41.23 26.273-41.23z"
        ></path>
        <g filter="url(#filter0_d_10607_684)">
          <path
            fill="#fff"
            stroke="#E5E5E5"
            d="M123.647 154.169H81.363l1.433-16.546h39.418l1.433 16.546z"
          ></path>
        </g>
        <g filter="url(#filter1_d_10607_684)">
          <path
            fill="#fff"
            d="M179.679 138.542H25.964c-3.577 0-6.488-2.922-6.488-6.513V21.813c0-3.591 2.91-6.513 6.488-6.513H179.68c3.578 0 6.488 2.922 6.488 6.513v110.216c0 3.591-2.91 6.513-6.488 6.513z"
          ></path>
          <path
            fill="#B3B3B3"
            d="M25.964 15.801c-3.302 0-5.989 2.697-5.989 6.012V132.03c0 3.315 2.687 6.012 5.99 6.012h153.714c3.303 0 5.989-2.697 5.989-6.012V21.813c0-3.315-2.686-6.012-5.989-6.012H25.964zm0-1.002H179.68c3.859 0 6.987 3.14 6.987 7.014V132.03c0 3.873-3.128 7.014-6.987 7.014H25.964c-3.858 0-6.987-3.141-6.987-7.014V21.813c0-3.873 3.128-7.014 6.987-7.014z"
          ></path>
        </g>
        <path
          fill="url(#paint1_linear_10607_684)"
          d="M103.361 133.121c2.756 0 4.991-2.243 4.991-5.01 0-2.766-2.235-5.009-4.991-5.009s-4.99 2.243-4.99 5.009c0 2.767 2.234 5.01 4.99 5.01z"
        ></path>
        <path stroke="#E5E5E5" d="M20.474 29.849H185.04"></path>
        <path
          fill="#4061C7"
          d="M41.487 24.744a1.52 1.52 0 001.517-1.523 1.52 1.52 0 00-1.517-1.523 1.52 1.52 0 00-1.518 1.523c0 .841.68 1.523 1.518 1.523zM47.248 24.744a1.52 1.52 0 001.517-1.523 1.52 1.52 0 00-1.517-1.523 1.52 1.52 0 00-1.517 1.523c0 .841.679 1.523 1.517 1.523zM53.008 24.744a1.52 1.52 0 001.518-1.523 1.52 1.52 0 00-1.518-1.523 1.52 1.52 0 00-1.517 1.523c0 .841.68 1.523 1.517 1.523z"
        ></path>
        <path
          fill="#fff"
          d="M118.876 45.38h-31.94a4 4 0 00-3.993 4.007v48.094a4 4 0 003.993 4.008h31.94a4 4 0 003.993-4.008V49.387a4 4 0 00-3.993-4.008z"
        ></path>
        <path
          stroke="#E5E5E5"
          d="M118.876 45.88h-31.94a3.5 3.5 0 00-3.494 3.507v48.094a3.5 3.5 0 003.493 3.507h31.941a3.5 3.5 0 003.494-3.507V49.387a3.5 3.5 0 00-3.494-3.507z"
        ></path>
        <path
          fill="#fff"
          d="M165.246 45.38h-31.941a4 4 0 00-3.992 4.007v48.094a4 4 0 003.992 4.008h31.941a4 4 0 003.993-4.008V49.387a4 4 0 00-3.993-4.008z"
        ></path>
        <path
          stroke="#E5E5E5"
          d="M165.246 45.88h-31.941a3.5 3.5 0 00-3.493 3.507v48.094a3.5 3.5 0 003.493 3.507h31.941a3.5 3.5 0 003.494-3.507V49.387a3.5 3.5 0 00-3.494-3.507z"
        ></path>
        <path
          fill="#E6E6E6"
          d="M88.4 50.387a.512.512 0 00-.511.514v26.007a.516.516 0 00.512.514h29.919a.512.512 0 00.512-.514V50.902a.514.514 0 00-.512-.514H88.4zM88.948 88.293a1.066 1.066 0 000 2.127h28.13a1.051 1.051 0 00.77-.3 1.073 1.073 0 000-1.528 1.053 1.053 0 00-.77-.3h-28.13zM88.948 93.352a1.058 1.058 0 00-1.028 1.064 1.066 1.066 0 001.028 1.064h12.665a1.059 1.059 0 001.028-1.064 1.065 1.065 0 00-1.028-1.064H88.948zM88.948 83.067a1.058 1.058 0 00-1.028 1.064 1.066 1.066 0 001.028 1.063h28.13a1.052 1.052 0 00.77-.3 1.072 1.072 0 000-1.528 1.055 1.055 0 00-.77-.299h-28.13zM134.316 50.387a.512.512 0 00-.513.514v26.007a.512.512 0 00.513.514h29.919a.512.512 0 00.512-.514V50.902a.514.514 0 00-.512-.514h-29.919zM134.862 88.293a1.058 1.058 0 00-1.027 1.063 1.066 1.066 0 001.027 1.064h28.13a1.056 1.056 0 001.009-.65 1.074 1.074 0 000-.829 1.057 1.057 0 00-1.009-.648h-28.13zM134.862 93.352a1.058 1.058 0 00-1.027 1.064 1.065 1.065 0 001.027 1.064h12.666a1.066 1.066 0 00.728-1.804 1.057 1.057 0 00-.728-.324h-12.666zM134.862 83.067a1.058 1.058 0 00-1.027 1.064 1.065 1.065 0 001.027 1.063h28.13a1.056 1.056 0 001.009-.649 1.073 1.073 0 000-.829 1.057 1.057 0 00-1.009-.649h-28.13z"
        ></path>
        <path
          fill="#fff"
          d="M73.416 100.738h-31.94a3.254 3.254 0 01-3.244-3.257V49.387a3.254 3.254 0 013.244-3.256h31.94a3.254 3.254 0 013.245 3.256v48.094a3.254 3.254 0 01-3.245 3.257z"
        ></path>
        <path
          fill="#93B0ED"
          d="M41.476 46.882a2.503 2.503 0 00-2.496 2.505v48.094a2.503 2.503 0 002.496 2.505h31.94a2.503 2.503 0 002.496-2.505V49.387a2.503 2.503 0 00-2.496-2.505h-31.94zm0-1.503h31.94a4 4 0 013.993 4.008v48.094a4 4 0 01-3.993 4.008h-31.94a4 4 0 01-3.993-4.008V49.387a4 4 0 013.993-4.008z"
        ></path>
        <path
          fill="url(#paint2_linear_10607_684)"
          d="M43.4 52.263a.481.481 0 00-.482.483v24.42a.485.485 0 00.481.483h28.095a.481.481 0 00.481-.483v-24.42a.485.485 0 00-.481-.483H43.399z"
        ></path>
        <path
          fill="#4061C7"
          d="M43.91 87.856a.997.997 0 00-.922 1 1.004 1.004 0 00.921.998h26.415a.994.994 0 00.992-.597 1.005 1.005 0 00-.587-1.348.996.996 0 00-.405-.053H43.91zM43.91 92.608a.997.997 0 00-.922.999 1.004 1.004 0 00.921.999h11.893a.997.997 0 00.921-1 1.004 1.004 0 00-.92-.998H43.908zM43.91 82.95a.997.997 0 00-.922.999 1.004 1.004 0 00.921.999h26.415a.994.994 0 00.992-.598 1.005 1.005 0 00-.587-1.348.996.996 0 00-.405-.052H43.91z"
        ></path>
        <path stroke="#E5E5E5" d="M20.474 117.45h164.912"></path>
        <g filter="url(#filter2_d_10607_684)">
          <path
            fill="#fff"
            stroke="#E5E5E5"
            d="M131.658 154.172H74.375v4.317h57.283v-4.317z"
          ></path>
        </g>
      </g>
      <defs>
        <filter
          id="filter0_d_10607_684"
          width="55.375"
          height="29.546"
          x="74.818"
          y="132.123"
          colorInterpolationFilters="sRGB"
          filterUnits="userSpaceOnUse"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix"></feFlood>
          <feColorMatrix
            in="SourceAlpha"
            result="hardAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          ></feColorMatrix>
          <feOffset dy="1"></feOffset>
          <feGaussianBlur stdDeviation="3"></feGaussianBlur>
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.161 0"></feColorMatrix>
          <feBlend
            in2="BackgroundImageFix"
            result="effect1_dropShadow_10607_684"
          ></feBlend>
          <feBlend
            in="SourceGraphic"
            in2="effect1_dropShadow_10607_684"
            result="shape"
          ></feBlend>
        </filter>
        <filter
          id="filter1_d_10607_684"
          width="179.689"
          height="136.243"
          x="12.977"
          y="9.799"
          colorInterpolationFilters="sRGB"
          filterUnits="userSpaceOnUse"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix"></feFlood>
          <feColorMatrix
            in="SourceAlpha"
            result="hardAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          ></feColorMatrix>
          <feOffset dy="1"></feOffset>
          <feGaussianBlur stdDeviation="3"></feGaussianBlur>
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.18 0"></feColorMatrix>
          <feBlend
            in2="BackgroundImageFix"
            result="effect1_dropShadow_10607_684"
          ></feBlend>
          <feBlend
            in="SourceGraphic"
            in2="effect1_dropShadow_10607_684"
            result="shape"
          ></feBlend>
        </filter>
        <filter
          id="filter2_d_10607_684"
          width="70.283"
          height="17.316"
          x="67.875"
          y="148.672"
          colorInterpolationFilters="sRGB"
          filterUnits="userSpaceOnUse"
        >
          <feFlood floodOpacity="0" result="BackgroundImageFix"></feFlood>
          <feColorMatrix
            in="SourceAlpha"
            result="hardAlpha"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
          ></feColorMatrix>
          <feOffset dy="1"></feOffset>
          <feGaussianBlur stdDeviation="3"></feGaussianBlur>
          <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.161 0"></feColorMatrix>
          <feBlend
            in2="BackgroundImageFix"
            result="effect1_dropShadow_10607_684"
          ></feBlend>
          <feBlend
            in="SourceGraphic"
            in2="effect1_dropShadow_10607_684"
            result="shape"
          ></feBlend>
        </filter>
        <linearGradient
          id="paint0_linear_10607_684"
          x1="102.503"
          x2="102.503"
          y1="2.5"
          y2="162.36"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFF0FC"></stop>
          <stop offset="1" stopColor="#CED4F9"></stop>
        </linearGradient>
        <linearGradient
          id="paint1_linear_10607_684"
          x1="99.528"
          x2="106.936"
          y1="130.656"
          y2="124.752"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2F85E9"></stop>
          <stop offset="1" stopColor="#65CBD9"></stop>
        </linearGradient>
        <linearGradient
          id="paint2_linear_10607_684"
          x1="46.289"
          x2="65.47"
          y1="71.404"
          y2="53.841"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#2F85E9"></stop>
          <stop offset="1" stopColor="#65CBD9"></stop>
        </linearGradient>
        <clipPath id="clip0_10607_684">
          <path
            fill="#fff"
            d="M0 0H205V163H0z"
            transform="translate(.5 .5)"
          ></path>
        </clipPath>
      </defs>
    </svg>
  );
}

export default EmptyCompanyData;
