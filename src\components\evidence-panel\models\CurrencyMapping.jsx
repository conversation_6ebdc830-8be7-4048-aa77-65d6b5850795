// Currency mapping configuration
export const CURRENCY_MAPPING = {
  american: {
    dollar: "USD"
  },
  indian: {
    rupee: "INR"
  },
  british: {
    pound: "GBP"
  },
  european: {
    euro: "EUR"
  },
  japanese: {
    yen: "JPY"
  }
};

/**
 * Gets the currency code based on format and type
 * @param format The number format (e.g., 'american', 'indian')
 * @param type The currency type (e.g., 'dollar', 'rupee')
 * @returns The currency code (e.g., 'USD', 'INR') or 'USD' as default
 */
export function getCurrencyCode(format, type) {
  const normalizedFormat = format?.toLowerCase();
  const normalizedType = type?.toLowerCase();

  return CURRENCY_MAPPING[normalizedFormat]?.[normalizedType] || "";
}
