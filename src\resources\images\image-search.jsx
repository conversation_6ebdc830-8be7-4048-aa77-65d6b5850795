import * as React from "react";
const ImageSearch = (props) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width={177}
    height={145}
    fill="none"
    {...props}
  >
    <path
      fill="url(#a)"
      d="M92.217 42.685s-41-19.672-48.206-20.8c-7.206-1.128-31.214 25.812-31.214 25.812S-1.164 61.135 1.885 92.686c3.049 31.55 32.084 14.449 32.084 14.449s20.443 28.469 40.709 35.162c20.266 6.693 25.278-9.378 45.552-15.199s16.847-3.201 35.541-8.073c18.694-4.872 24.653-27.763 15.165-41.841s16.994-65.643-19.5-75.42c-36.494-9.777-59.22 40.921-59.22 40.921Z"
    />
    <g filter="url(#b)">
      <path
        fill="#fff"
        d="M156.629 11.124H17.48a4 4 0 0 0-4 4v109.719a4 4 0 0 0 4 4H156.63a4 4 0 0 0 4-4V15.124a4 4 0 0 0-4-4Z"
      />
      <path
        stroke="#B3B3B3"
        d="M156.629 11.624H17.48a3.5 3.5 0 0 0-3.5 3.5v109.719a3.5 3.5 0 0 0 3.5 3.5H156.63a3.5 3.5 0 0 0 3.5-3.5V15.124a3.5 3.5 0 0 0-3.5-3.5Z"
      />
    </g>
    <path
      fill="#4061C7"
      d="M22.676 20.32a1.839 1.839 0 1 0 0-3.677 1.839 1.839 0 0 0 0 3.678ZM30.034 20.32a1.839 1.839 0 1 0 0-3.677 1.839 1.839 0 0 0 0 3.678ZM37.391 20.32a1.839 1.839 0 1 0 0-3.677 1.839 1.839 0 0 0 0 3.678Z"
    />
    <path
      fill="#ECECEC"
      d="M150.753 31.03H45.428a2 2 0 0 0-2 2v7.036a2 2 0 0 0 2 2h105.325a2 2 0 0 0 2-2V33.03a2 2 0 0 0-2-2Z"
    />
    <path
      fill="#E6E6E6"
      d="M56.624 52.264h-25.59a1 1 0 0 0-1 1v14.554a1 1 0 0 0 1 1h25.59a1 1 0 0 0 1-1V53.264a1 1 0 0 0-1-1Z"
    />
    <path
      fill="url(#c)"
      d="M73.18 77.34H40.23a1 1 0 0 0-1 1v14.555a1 1 0 0 0 1 1H73.18a1 1 0 0 0 1-1V78.34a1 1 0 0 0-1-1Z"
    />
    <path
      fill="#E6E6E6"
      d="M94.448 99.414H60.464a1 1 0 0 0-1 1v14.554a1 1 0 0 0 1 1h33.984a1 1 0 0 0 1-1v-14.554a1 1 0 0 0-1-1ZM101.769 54.103H63.143a.92.92 0 1 0 0 1.84h38.626a.92.92 0 1 0 0-1.84Z"
    />
    <path
      fill="#4061C7"
      d="M119.243 81.02H80.617a.92.92 0 1 0 0 1.839h38.626a.92.92 0 1 0 0-1.84Z"
    />
    <path
      fill="#E6E6E6"
      d="M139.673 103.092h-38.626a.92.92 0 1 0 0 1.839h38.626a.92.92 0 0 0 0-1.839ZM101.769 57.781H63.143a.92.92 0 1 0 0 1.84h38.626a.92.92 0 1 0 0-1.84Z"
    />
    <path
      fill="#4061C7"
      d="M119.243 84.698H80.617a.92.92 0 1 0 0 1.84h38.626a.92.92 0 1 0 0-1.84Z"
    />
    <path
      fill="#E6E6E6"
      d="M139.673 106.771h-38.626a.92.92 0 1 0 0 1.839h38.626a.92.92 0 0 0 0-1.839ZM79.697 61.46H63.143a.92.92 0 1 0 0 1.84h16.554a.92.92 0 1 0 0-1.84Z"
    />
    <path
      fill="#4061C7"
      d="M97.171 88.378H80.617a.92.92 0 1 0 0 1.839h16.554a.92.92 0 1 0 0-1.84Z"
    />
    <path
      fill="#E6E6E6"
      d="M117.601 110.449h-16.554a.92.92 0 0 0 0 1.839h16.554a.92.92 0 1 0 0-1.839Z"
    />
    <path
      fill="url(#d)"
      d="m164.746 137.912-24.186-25.43-4.499 4.196 24.093 25.545a3.602 3.602 0 0 0 4.324 0l.269-.244a3.744 3.744 0 0 0-.001-4.067Z"
    />
    <path
      fill="#4061C7"
      d="m135.439 118.771 4.848 5.201a.67.67 0 0 0 .458.209.646.646 0 0 0 .472-.177l5.895-5.497a.663.663 0 0 0 .168-.712.649.649 0 0 0-.135-.218l-4.849-5.199-6.857 6.393Z"
    />
    <g filter="url(#e)">
      <path
        fill="#fff"
        d="M118.519 125.176a29.05 29.05 0 0 1-20.241-9.344 29.03 29.03 0 0 1-5.87-9.501 28.942 28.942 0 0 1-1.835-11.42 29.032 29.032 0 0 1 9.345-20.241 29.036 29.036 0 0 1 20.921-7.705 28.942 28.942 0 0 1 11.237 2.739 29.018 29.018 0 0 1 9.003 6.606 29.031 29.031 0 0 1 7.705 20.92 29.037 29.037 0 0 1-9.345 20.241 29.04 29.04 0 0 1-20.92 7.705Zm2.116-53.092a23.936 23.936 0 0 0-17.242 6.35 23.933 23.933 0 0 0-7.701 16.681 23.856 23.856 0 0 0 1.513 9.412 23.91 23.91 0 0 0 4.837 7.829 23.922 23.922 0 0 0 16.681 7.702 23.94 23.94 0 0 0 17.242-6.35 23.927 23.927 0 0 0 7.701-16.681 23.937 23.937 0 0 0-6.35-17.241 23.918 23.918 0 0 0-7.42-5.445 23.848 23.848 0 0 0-9.261-2.257Z"
      />
      <path
        fill="#B3B3B3"
        d="M120.819 67.465a28.531 28.531 0 0 0-20.562 7.572 28.535 28.535 0 0 0-9.184 19.894 28.529 28.529 0 0 0 7.572 20.56 28.509 28.509 0 0 0 8.849 6.494 28.452 28.452 0 0 0 11.045 2.692 28.535 28.535 0 0 0 20.561-7.573 28.527 28.527 0 0 0 9.184-19.893 28.444 28.444 0 0 0-1.804-11.224 28.526 28.526 0 0 0-5.768-9.337 28.536 28.536 0 0 0-19.893-9.185Zm-2.116 53.093a24.353 24.353 0 0 1-9.455-2.305 24.414 24.414 0 0 1-7.574-5.557 24.424 24.424 0 0 1-6.482-17.6 24.424 24.424 0 0 1 7.862-17.03 24.422 24.422 0 0 1 17.6-6.482c3.306.132 6.487.907 9.455 2.305a24.426 24.426 0 0 1 7.575 5.557 24.435 24.435 0 0 1 6.482 17.6 24.353 24.353 0 0 1-2.305 9.456 24.414 24.414 0 0 1-5.557 7.574 24.418 24.418 0 0 1-7.992 4.937 24.35 24.35 0 0 1-9.609 1.545Zm2.155-54.092c16.351.651 29.078 14.434 28.426 30.785-.651 16.351-14.434 29.077-30.785 28.425-16.351-.651-29.077-14.434-28.426-30.785.652-16.35 14.434-29.077 30.785-28.425Zm-2.115 53.092c12.972.517 23.906-9.58 24.423-22.551.517-12.972-9.58-23.906-22.551-24.423-12.972-.517-23.907 9.58-24.423 22.551-.517 12.972 9.579 23.906 22.551 24.423Z"
      />
    </g>
    <path
      fill="#F0F0F0"
      d="M119.276 118.924c12.968.453 23.847-9.693 24.3-22.66.453-12.969-9.692-23.849-22.66-24.301-12.969-.453-23.848 9.692-24.301 22.66-.453 12.968 9.693 23.848 22.661 24.301Z"
      opacity={0.8}
    />
    <path
      fill="#fff"
      d="M105.057 79.316c-3.516 3.28-5.677 6.672-4.828 7.581.848.91 4.384-1.01 7.899-4.289 3.516-3.278 5.677-6.672 4.83-7.58-.848-.91-4.384 1.009-7.901 4.288Z"
    />
    <path stroke="#E5E5E5" d="M14.48 24.839h145.688M36.48 24.839V129.14" />
    <defs>
      <linearGradient
        id="a"
        x1={88.419}
        x2={88.419}
        y1={0.512}
        y2={143.899}
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#FFF0FC" />
        <stop offset={1} stopColor="#CED4F9" />
      </linearGradient>
      <linearGradient
        id="c"
        x1={43.285}
        x2={57.97}
        y1={89.823}
        y2={62.622}
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#EDCF28" />
        <stop offset={1} stopColor="#DEA509" />
      </linearGradient>
      <linearGradient
        id="d"
        x1={143.368}
        x2={166.091}
        y1={123.808}
        y2={139.496}
        gradientUnits="userSpaceOnUse"
      >
        <stop stopColor="#EDCF28" />
        <stop offset={1} stopColor="#DEA509" />
      </linearGradient>
      <filter
        id="b"
        width={159.149}
        height={129.719}
        x={7.48}
        y={6.124}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={1} />
        <feGaussianBlur stdDeviation={3} />
        <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.18 0" />
        <feBlend
          in2="BackgroundImageFix"
          result="effect1_dropShadow_1213_11854"
        />
        <feBlend
          in="SourceGraphic"
          in2="effect1_dropShadow_1213_11854"
          result="shape"
        />
      </filter>
      <filter
        id="e"
        width={71.264}
        height={71.264}
        x={84.047}
        y={61.439}
        colorInterpolationFilters="sRGB"
        filterUnits="userSpaceOnUse"
      >
        <feFlood floodOpacity={0} result="BackgroundImageFix" />
        <feColorMatrix
          in="SourceAlpha"
          result="hardAlpha"
          values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
        />
        <feOffset dy={1} />
        <feGaussianBlur stdDeviation={3.001} />
        <feColorMatrix values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.18 0" />
        <feBlend
          in2="BackgroundImageFix"
          result="effect1_dropShadow_1213_11854"
        />
        <feBlend
          in="SourceGraphic"
          in2="effect1_dropShadow_1213_11854"
          result="shape"
        />
      </filter>
    </defs>
  </svg>
);
export default React.memo(ImageSearch);
