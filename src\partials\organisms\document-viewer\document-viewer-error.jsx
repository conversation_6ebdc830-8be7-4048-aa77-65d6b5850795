import React from "react";
import { ButtonIconText } from "../../../partials/atoms/button";
import { FiExternalLink } from "react-icons/fi";
import { FaExclamationCircle, FaTimes } from "react-icons/fa";
import { notify } from "../../../partials/molecules/toaster";
import { Tooltip } from "@progress/kendo-react-tooltip";
import useDocumentViewerStore from "./document-viewer.store";
import PropTypes from "prop-types";

const DocumentViewerError = ({ readOnly }) => {
  const { fileUrl, fileExtension, NEW_TAB_FILE_TYPES, setPreviewClose } =
    useDocumentViewerStore((state) => state);

  const handleNewTab = (e) => {
    if (!NEW_TAB_FILE_TYPES.includes(fileExtension)) {
      e.preventDefault();
      notify.error("File format not supported");
    }
  };

  return (
    <>
      <div className="flex h-12 w-full justify-between border-b">
        {!readOnly && (
          <div className="flex w-full justify-end">
            <Tooltip openDelay={100} position="left" anchorElement="target">
              <button
                title="Close preview"
                data-testid="document-viewer-error-close-button"
                onClick={setPreviewClose}
                className="flex size-12 h-full cursor-pointer items-center justify-end border-l text-center text-neutral-60 hover:bg-primary-40 active:bg-primary-50"
              >
                <div
                  title="Close  preview"
                  className="flex w-12 items-center justify-center"
                >
                  <FaTimes
                    data-testid="document-viewer-error-close-button-icon"
                    title="Close preview"
                    className="justify-center"
                    onClick={setPreviewClose}
                  />
                </div>
              </button>
            </Tooltip>
          </div>
        )}
      </div>
      <div
        data-testid="document-viewer-error-preview"
        className="m-auto flex h-full w-full grow flex-col items-center justify-center text-center"
      >
        <FaExclamationCircle className="size-10 text-noticeable-100" />
        <span className="body-r pt-4">
          We are unable to process this file type, please try accessing the
          link.
        </span>
        <a
          data-testid="document-viewer-error-preview-open-link"
          href={fileUrl}
          target="_blank"
          rel="noopener noreferrer"
          onClick={(e) => handleNewTab(e)}
        >
          <ButtonIconText className="body-sr mt-3">
            <FiExternalLink />
            Open link
          </ButtonIconText>
        </a>
      </div>
    </>
  );
};

export default DocumentViewerError;

DocumentViewerError.propTypes = { readOnly: PropTypes.bool };
