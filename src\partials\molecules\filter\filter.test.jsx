import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import Filter from "./filter";

const mockData = [
  {
    label: "Company",
    type: "accordian-panel",
    value: "1",
    data: [
      {
        value: "1",
        label: "Category 1",
        data: [
          { label: "Option 1", selected: false },
          { label: "Option 2", selected: true }
        ],
        selectedCount: 1,
        type: "multi-select"
      },
      {
        value: "2",
        label: "Category 2",
        data: [
          { label: "Option A", selected: false },
          { label: "Option B", selected: false }
        ],
        selectedCount: 0,
        type: "accordian-panel"
      }
    ]
  },
  {
    label: "Filing Type",
    type: "multi-select",
    value: "2",
    data: [
      {
        value: "1",
        label: "Category 1",
        data: [
          { label: "Option 1", selected: false },
          { label: "Option 2", selected: true }
        ],
        selectedCount: 1,
        type: "multi-select"
      },
      {
        value: "2",
        label: "Category 2",
        data: [
          { label: "Option A", selected: false },
          { label: "Option B", selected: false }
        ],
        selectedCount: 0,
        type: "accordian-panel"
      }
    ]
  },
  {
    label: "Period",
    type: "date-range",
    value: "3",
    data: [
      {
        value: "1",
        label: "Category 1",
        data: [
          { value: "1-1", label: "Option 1", selected: false },
          { value: "1-2", label: "Option 2", selected: true }
        ],
        selected: 1
      },
      {
        value: "2",
        label: "Category 2",
        data: [
          { value: "2-1", label: "Option A", selected: false },
          { value: "2-2", label: "Option B", selected: false }
        ],
        selected: 0
      }
    ]
  }
];

const mockOnFilter = jest.fn();

describe("Filter Component", () => {
  test("renders Filter button", () => {
    render(<Filter data={mockData} onFilter={mockOnFilter} />);
    expect(screen.getByText("Filter")).toBeInTheDocument();
  });

  test("opens filter dropdown on button click", () => {
    render(<Filter data={mockData} onFilter={mockOnFilter} />);
    fireEvent.click(screen.getByText("Filter"));
    expect(screen.getByText("Filters")).toBeInTheDocument();
  });

  test("displays correct filter categories", () => {
    render(<Filter data={mockData} onFilter={mockOnFilter} />);
    fireEvent.click(screen.getByText("Filter"));
    expect(screen.getByText("Category 1")).toBeInTheDocument();
    expect(screen.getByText("Category 2")).toBeInTheDocument();
  });

  test("calls onFilter with updated data on Apply click", () => {
    render(<Filter data={mockData} onFilter={mockOnFilter} />);
    fireEvent.click(screen.getByText("Filter"));
    fireEvent.click(screen.getByText("Apply"));
  });

  test("clears all filters on Clear all filters click", () => {
    render(<Filter data={mockData} onFilter={mockOnFilter} />);
    fireEvent.click(screen.getByTestId("filter-button"));
    fireEvent.click(screen.getAllByTestId("filter-button-selected")[0]);
    fireEvent.click(screen.getAllByTestId("filter-button-selected")[1]);
    fireEvent.click(screen.getAllByTestId("filter-button-selected")[2]);
    fireEvent.click(screen.getByTestId("filter-button-clear"));
    fireEvent.click(screen.getByTestId("filter-button-close"));
  });
});
