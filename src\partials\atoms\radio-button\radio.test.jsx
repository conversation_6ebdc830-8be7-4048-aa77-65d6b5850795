import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import RadioButton from "./radio";

const options = [
  { label: "Option 1", value: "1" },
  { label: "Option 2", value: "2" },
  { label: "Option 3", value: "3" }
];

describe("RadioButton Component", () => {
  test("renders all radio button options", () => {
    render(
      <RadioButton
        options={options}
        name="test"
        selectedValue="1"
        onChange={() => {}}
      />
    );

    options.forEach((option) => {
      expect(screen.getByLabelText(option.label)).toBeInTheDocument();
    });
  });

  test("checks the correct radio button based on selectedValue", () => {
    render(
      <RadioButton
        options={options}
        name="test"
        selectedValue="2"
        onChange={() => {}}
      />
    );

    expect(screen.getByLabelText("Option 2")).toBeChecked();
  });

  test("calls onChange when a radio button is clicked", () => {
    const handleChange = jest.fn();
    render(
      <RadioButton
        options={options}
        name="test"
        selectedValue="1"
        onChange={handleChange}
      />
    );

    fireEvent.click(screen.getByLabelText("Option 3"));
    expect(handleChange).toHaveBeenCalled();
  });

  test("updates the selected radio button when a new value is selected", () => {
    const handleChange = jest.fn();
    render(
      <RadioButton
        options={options}
        name="test"
        selectedValue="2"
        onChange={handleChange}
      />
    );

    fireEvent.click(screen.getByLabelText("Option 2"));
    expect(screen.getByLabelText("Option 2")).toBeChecked();
  });
});
