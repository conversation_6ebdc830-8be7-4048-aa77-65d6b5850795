import React, { useEffect, useRef, useState } from 'react';
import { Checkbox } from '@progress/kendo-react-inputs';
import './PdfStyles.css';
import checkBoxIcon from '../../resources/images/check-box.svg';

const PdfThumbnailList = ({
    pdfDoc,
    totalPages,
    currentPage,
    onSelectPage,
    onPagesSelected,
    onRegisterSelectionCallback,
    initialSelectedPages = []
}) => {
    const canvasRefs = useRef([]);
    const thumbnailRefs = useRef([]);
    const [selectedPages, setSelectedPages] = useState(initialSelectedPages || []);

    // Register the callback function to allow parent to update selection
    useEffect(() => {
        if (onRegisterSelectionCallback) {
            onRegisterSelectionCallback(updateSelection);
        }
    }, [onRegisterSelectionCallback]);

    // Initialize selection from props
    useEffect(() => {
        setSelectedPages(initialSelectedPages || []);
    }, [initialSelectedPages]);

    // Method to update selection from parent component
    const updateSelection = (pages) => {
        setSelectedPages(pages || []);
    };

    // Render thumbnails
    useEffect(() => {
        const renderThumbnails = async () => {
            if (!pdfDoc) return;

            for (let i = 1; i <= totalPages; i++) {
                const page = await pdfDoc.getPage(i);
                const canvas = canvasRefs.current[i - 1];
                if (!canvas) continue;

                const context = canvas.getContext('2d');
                const viewport = page.getViewport({ scale: 0.3 });
                canvas.height = viewport.height;
                canvas.width = viewport.width;
                await page.render({ canvasContext: context, viewport }).promise;
            }
        };

        renderThumbnails();
    }, [pdfDoc, totalPages]);

    // Auto-scroll thumbnails to keep current page visible
    useEffect(() => {
        if (currentPage <= 0 || currentPage > totalPages) return;

        const thumbnailElement = thumbnailRefs.current[currentPage - 1];
        if (thumbnailElement) {
            thumbnailElement.scrollIntoView({
                behavior: 'smooth',
                block: 'nearest'
            });
        }
    }, [currentPage, totalPages]);

    // Handle page selection and notify parent component
    const handlePageSelection = (pageNumber, checked) => {
        const newSelectedPages = checked
            ? [...(selectedPages.includes(pageNumber) ? selectedPages : [...selectedPages, pageNumber])]
            : selectedPages.filter(page => page !== pageNumber);

        // Sort the selected pages in ascending order
        newSelectedPages.sort((a, b) => a - b);

        setSelectedPages(newSelectedPages);

        // Notify parent about the selection change
        if (onPagesSelected) {
            onPagesSelected(newSelectedPages);
        }
    };

    // Check if a page is selected
    const isPageSelected = (pageNumber) => {
        return selectedPages.includes(pageNumber);
    };

    // Handle select all pages
    const handleSelectAll = () => {
        let newSelectedPages = [];

        if (selectedPages.length === totalPages) {
            // If all pages are already selected, unselect all
            newSelectedPages = [];
        } else {
            // Otherwise, select all pages
            newSelectedPages = Array.from({ length: totalPages }, (_, i) => i + 1);
        }

        setSelectedPages(newSelectedPages);

        // Notify parent about the selection change
        if (onPagesSelected) {
            onPagesSelected(newSelectedPages);
        }
    };

    // Handle thumbnail click - navigate to the page and toggle selection
    const handleThumbnailClick = (pageNumber, e) => {
        // Don't trigger if the click was on the checkbox area
        if (e.target.closest('.checkbox-container')) {
            return;
        }
        
        // Always navigate to the page first
        if (onSelectPage) {
            onSelectPage(pageNumber);
        }
        handlePageSelection(pageNumber, true);
    };

    return (
        <div className="flex flex-col h-full thumbnail-section">
            {/* Selection counter */}
            <div className="pdf-selection-counter border-neutral-10 border-t border-b bg-neutral-2 sticky top-0 z-10 flex justify-between items-center">

                <span className='text-primary-78 caption-r py-1'>{selectedPages.length} of {totalPages} page(s) selected</span>

            </div>

            {/* Thumbnails list */}
            <div className="w-full overflow-y-auto flex-grow mt-2">
                {Array.from({ length: totalPages }, (_, i) => {
                    const pageNumber = i + 1;
                    const isCurrentPage = currentPage === pageNumber;
                    const isSelected = isPageSelected(pageNumber);

                    return (
                        <div
                            key={i}
                            ref={el => thumbnailRefs.current[i] = el}
                            onClick={(e) => handleThumbnailClick(pageNumber, e)}
                            className={`mb-3 relative cursor-pointer transition-all duration-200 ${isCurrentPage
                                ? ''
                                : ''
                                } ${isSelected ? '' : ''
                                }`}
                        >
                            {/* Page Number */}
                            <div className="flex justify-between items-center mb-1">
                                <span className={`text-sm font-bold ${isCurrentPage ? 'text-blue-800' : 'text-gray-800'}`}>
                                    {pageNumber}.
                                </span>
                            </div>

                            {/* Canvas Thumbnail with checkbox overlay */}
                            <div className="relative" onClick={(e) => handleThumbnailClick(pageNumber, e)}>
                                {/* Checkbox positioned in the center of canvas */}
                                <div
                                    className={`absolute inset-0 flex justify-center items-center z-10 checkbox-container border-[#EDEDF2] shadow-[0px_0px_7px_0px_#0000001F] rounded-[1.75px] border-[0.87px] ${isSelected ? 'visible' : 'invisible'}`}
                                >
                                    <div className="">
                                        <Checkbox
                                            value={isSelected}
                                            checked={isSelected}
                                            onChange={(e) => {
                                                handlePageSelection(pageNumber, e.value);
                                            }}
                                            className="k-checkbox-md"
                                        />
                                    </div>
                                </div>

                                <canvas
                                    ref={(el) => (canvasRefs.current[i] = el)}
                                    onClick={(e) => handleThumbnailClick(pageNumber, e)}
                                    className={`mx-auto block ${isCurrentPage ? 'bg-primary-78 bg-opacity-30' : ''
                                        } ${isSelected ? '' : ''
                                        }`}
                                />

                                {/* Background overlay for the entire thumbnail */}
                                <div
                                    onClick={(e) => handleThumbnailClick(pageNumber, e)}
                                    className={`absolute inset-0
                                        ${isCurrentPage ? 'bg-primary-78 bg-opacity-30' : ''
                                        }
                                         ${isSelected ? 'bg-primary-78 bg-opacity-30' : ''
                                        }`}
                                />
                            </div>
                        </div>
                    );
                })}
            </div>
        </div>
    );
};

export default PdfThumbnailList;