import React, { useEffect, useState } from "react";
import PropTypes from "prop-types";
import clsx from "clsx";
import { NotFound } from "../not-found";

const ButtonGroup = ({ onClick, active, data }) => {
  const [selected, setSelected] = useState(active);

  useEffect(() => {
    onClick(selected);
  }, [selected]);

  if (data.length === 0) return <NotFound />;

  return (
    <>
      {data.map((item) => {
        return (
          <button
            key={`button-group-${item.id}`}
            data-testid={`button-group-${item.id}`}
            type="button"
            onClick={() => {
              setSelected(item);
            }}
            className={clsx(
              `relative inline-flex items-center border-y-[0.5px] border-r-[0.5px] border-neutral-10 px-4 py-2 
              first:rounded-l first:border-l-[0.5px] last:rounded-r hover:duration-500 focus:bg-primary-78 focus:text-white`,
              selected.id === item.id
                ? "s-r bg-primary-78 text-white"
                : "s-r bg-white text-neutral-60 hover:bg-primary-40 hover:text-primary-78"
            )}
          >
            {item.name}
          </button>
        );
      })}
    </>
  );
};

ButtonGroup.propTypes = {
  onClick: PropTypes.func,
  active: PropTypes.object.isRequired,
  data: PropTypes.array.isRequired
};

export default ButtonGroup;
