import { post } from "../../../general/fetcher";
const CONTROLLER = "tenant";

export const ADD_JOB_API_URL = `${CONTROLLER}/Job`;

export const ADD_JOB_DETAILS_SERVICE = ({ companyId, jobId, processId }) => {
  const body = {
    id: companyId,
    jobID: jobId,
    processID: processId,
    tenantId: "00000000-0000-0000-0000-000000000001",
    extractionTypeID: "46dd8198-acc7-419b-ba4c-dcf07384c28c",
    spreadingTypeId: "46dd8198-acc7-419b-ba4c-dcf07384c28c",
    statusID: "6764f962-826b-4dc6-b318-366b747e300d"
  };
  return post(ADD_JOB_API_URL, body);
};
