/**
 * @jest-environment jsdom
 */
import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import FundCard from './fund-card';

// Mock the CurrencyDropdown and CurrencyUnitDropdown components
jest.mock('../evidence-panel/currency-dropdown', () => ({
  __esModule: true,
  default: ({ currency, onCurrencyChange }) => (
    <div data-testid="currency-dropdown">
      Currency: {currency}
      <button onClick={() => onCurrencyChange({ currencyCode: 'EUR' })}>
        Change Currency
      </button>
    </div>
  )
}));

jest.mock('../evidence-panel/currency-unit-dropdown', () => ({
  __esModule: true,
  default: ({ currencyUnit, onCurrencyUnitChange }) => (
    <div data-testid="currency-unit-dropdown">
      Unit: {currencyUnit}
      <button onClick={() => onCurrencyUnitChange({ label: 'Millions' })}>
        Change Unit
      </button>
    </div>
  )
}));

describe('FundCard Component', () => {
  // Default props for testing
  const defaultProps = {
    fundName: 'Acuity Test Fund',
    companyCount: 5,
    currencyList: [
      { currencyCode: 'USD', currencyName: 'US Dollar' },
      { currencyCode: 'EUR', currencyName: 'Euro' }
    ]
  };

  // Test basic rendering
  test('renders with required props', () => {
    render(<FundCard {...defaultProps} />);
    
    // Check that the fund name is rendered
    expect(screen.getByText('Acuity Test Fund')).toBeInTheDocument();
    
    // Check that the company count text is rendered
    expect(screen.getByText('5 Companies are added')).toBeInTheDocument();
    
    // Check that the initials are rendered (AT for Acuity Test)
    expect(screen.getByText('AT')).toBeInTheDocument();
  });
  
  // Test with singular company count
  test('renders singular text when company count is 1', () => {
    render(<FundCard {...defaultProps} companyCount={1} />);
    
    // Check for singular text
    expect(screen.getByText('1 Company is added')).toBeInTheDocument();
  });
  
  // Test with custom style props
  test('renders with custom styles', () => {
    const customProps = {
      ...defaultProps,
      backgroundColor: '#EFEFEF',
      borderColor: '#DDDDDD',
      fundNameColor: '#123456',
      countColor: '#654321',
      avatarColor: '#ABCDEF'
    };
    
    render(<FundCard {...customProps} />);
    
    // Check that the fund name has the custom color
    const fundName = screen.getByText('Acuity Test Fund');
    expect(fundName).toHaveStyle({ color: '#123456' });
  });
  
  // Test initials with single word fund name
  test('generates correct initials for single word fund name', () => {
    render(<FundCard {...defaultProps} fundName="Blackstone" />);
    
    // Should take first two letters
    expect(screen.getByText('BL')).toBeInTheDocument();
  });
  
  // Test initials with multiple word fund name
  test('generates correct initials for multiple word fund name', () => {
    render(<FundCard {...defaultProps} fundName="Venture Capital Partners" />);
    
    // Should take first letter of first two words
    expect(screen.getByText('VC')).toBeInTheDocument();
  });
  
  // Test with empty fund name
  test('handles empty fund name gracefully', () => {
    render(<FundCard {...defaultProps} fundName="" />);
    
    // Should default to "FD"
    expect(screen.getByText('FD')).toBeInTheDocument();
  });
  
  // Test currency dropdown rendering
  test('renders currency dropdown with correct props', () => {
    render(<FundCard {...defaultProps} currency="EUR" />);
    
    const currencyDropdown = screen.getByTestId('currency-dropdown');
    expect(currencyDropdown).toBeInTheDocument();
    expect(currencyDropdown).toHaveTextContent('Currency: EUR');
  });
  
  // Test currency unit dropdown rendering
  test('renders currency unit dropdown with correct props', () => {
    render(<FundCard {...defaultProps} currencyUnit="Millions" />);
    
    const unitDropdown = screen.getByTestId('currency-unit-dropdown');
    expect(unitDropdown).toBeInTheDocument();
    expect(unitDropdown).toHaveTextContent('Unit: Millions');
  });
  
  // Test currency change callback
  test('calls onCurrencyChange callback', () => {
    const onCurrencyChange = jest.fn();
    render(<FundCard {...defaultProps} onCurrencyChange={onCurrencyChange} />);
    
    // Find and click the change currency button in the mock
    const changeButton = screen.getByText('Change Currency');
    changeButton.click();
    
    // Check that the callback was called with the right arguments
    expect(onCurrencyChange).toHaveBeenCalledWith({ currencyCode: 'EUR' });
  });
  
  // Test currency unit change callback
  test('calls onCurrencyUnitChange callback', () => {
    const onCurrencyUnitChange = jest.fn();
    render(<FundCard {...defaultProps} onCurrencyUnitChange={onCurrencyUnitChange} />);
    
    // Find and click the change unit button in the mock
    const changeButton = screen.getByText('Change Unit');
    changeButton.click();
    
    // Check that the callback was called with the right arguments
    expect(onCurrencyUnitChange).toHaveBeenCalledWith({ label: 'Millions' });
  });
}); 