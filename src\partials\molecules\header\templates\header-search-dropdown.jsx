import { React } from "react";
import { <PERSON>tonIcon, ButtonIconText } from "../../../atoms/button";
import { BsArrowRight, BsBookmarkStar } from "react-icons/bs";
import { ToolTip } from "../../../atoms/tool-tip";
import PropTypes from "prop-types";
import EmptySearchImage from "../../../../resources/images/empty-search-image";
import SpinnerCircle from "../../../atoms/loader/spinner-circle";
import Parser from "html-react-parser";
import { useNavigate } from "react-router-dom";
import useWatchListStore from "../../../../pages/watch-list/watch-list.store";
import useCompanyDetailsStore from "../../../../pages/company-details/company-details.store";
import EntityLogo from "../../entity-logo/entity-logo";

const HeaderSearchDropDownResults = ({
  results,
  isKeywordSearch,
  setDropdownOpen
}) => {
  const {
    setAddWatchListPopUp,
    setResult,
    query,
    setQuery,
    setWatchlistPopoverKey
  } = useWatchListStore();
  const { setSelectedCompany, setEntityDetails } = useCompanyDetailsStore();

  const onClickAddWatchList = (result) => {
    setResult(result);
    setAddWatchListPopUp(true);
    setWatchlistPopoverKey("add-companies-to-watchlist");
  };

  const navigate = useNavigate();
  const replaceEmTags = (inputString) => {
    return inputString.replace(/em>/g, "mark>");
  };

  const onClickEventHandler = (e, result) => {
    e.preventDefault();
    e.stopPropagation();
    setDropdownOpen(false);
    setSelectedCompany(result.name);
    setResult(result);
    setEntityDetails({
      acuityID: result.acuity_id,
      companyName: result.name
    });
    setQuery("");
    navigate(`company/${result.acuity_security_id}`);
  };

  return (
    <>
      <div className="caption-i px-3 text-left text-neutral-60">
        {isKeywordSearch ? "Keyword Results" : "Company/Ticker Results"}
      </div>
      <div className={`${isKeywordSearch && "max-h-[30rem] overflow-y-auto"}`}>
        {results.map((result) => (
          <button
            data-testid="header-search-dropdown-result"
            key={result.acuity_security_id}
            className="group w-full cursor-pointer px-3 py-2 hover:bg-primary-35 active:bg-primary-43"
            onClick={(e) => onClickEventHandler(e, result)}
            onKeyDown={(e) => {
              if (e.key === "Enter" || e.key === " ") {
                onClickEventHandler(e, result);
              }
            }}
          >
            <div className="flex justify-between">
              <div className="flex items-center justify-start gap-1">
                {result.is_public ? (
                  <div className="h-6 w-[3px] shrink-0 bg-turquoise-100"></div>
                ) : (
                  <div className="h-6 w-[3px] shrink-0 bg-yellow-pale-110"></div>
                )}

                <div>
                  <EntityLogo
                    acuityID={result.acuity_id}
                    companyName={result.name}
                    logoSize="medium"
                  />
                </div>
                <div
                  className="body-m w-48 truncate pl-1 text-start text-neutral-60"
                  title={result.name}
                >
                  <HighlightText highlight={query} text={result.name} />
                </div>
              </div>
              <div className="flex items-center justify-end gap-2">
                <div className="caption-r w-10 truncate">
                  {result.primary_ticker}
                </div>
                <button
                  className="invisible group-hover:visible"
                  onClick={(e) => e.stopPropagation()}
                  onKeyDown={(e) => {
                    if (e.key === "Enter" || e.key === " ") {
                      e.stopPropagation();
                    }
                  }}
                >
                  <ButtonIcon
                    data-testid="header-search-dropdown-add-to-watchlist"
                    intent={"teritory"}
                    size={"small"}
                    data-tooltip-id="tool-tip-add-to-watchlist"
                    onClick={() => {
                      onClickAddWatchList(result);
                    }}
                  >
                    <BsBookmarkStar className="h-3 w-3" />
                    <ToolTip
                      place={"bottom-end"}
                      text={"Add to watchlist(s)"}
                      toolTipId={"tool-tip-add-to-watchlist"}
                    />
                  </ButtonIcon>
                </button>
              </div>
            </div>

            {isKeywordSearch && (
              <div
                title={result.company_desc[0]?.value}
                className="body-m caption-r ml-2 mr-6 mt-1 line-clamp-3 text-neutral-60"
              >
                {result.highlight &&
                  Parser(replaceEmTags(result.highlight[0].value))}
              </div>
            )}
          </button>
        ))}
      </div>

      <div className="w-full">
        <div className="flex justify-between border-t px-3 py-1">
          <div className="flex gap-4 py-1">
            <div className="flex items-center gap-2">
              <div className="h-4 w-[3px] shrink-0 bg-turquoise-100"></div>
              <span>Public</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="h-4 w-[3px] shrink-0 bg-yellow-pale-110"></div>
              <span>Private</span>
            </div>
          </div>
          <div>
            <FilterButton />
          </div>
        </div>
      </div>
    </>
  );
};

HeaderSearchDropDownResults.propTypes = {
  results: PropTypes.array.isRequired,
  isKeywordSearch: PropTypes.bool,
  setDropdownOpen: PropTypes.func
};

const HeaderSearchDropdownNoResults = () => {
  return (
    <>
      <div className="m-auto" data-testid="search-no-results">
        <EmptySearchImage />
        <div className="flex justify-center">Oops!!! No results found!</div>
      </div>
      <div className="flex w-full justify-center border-t py-1">
        <FilterButton />
      </div>
    </>
  );
};

const HeaderSearchDropdownLoader = () => {
  return (
    <div
      data-testid={"search-loading"}
      className="flex flex-col items-center justify-center gap-3 pb-3"
    >
      <SpinnerCircle size={6} />
      <div>Please wait, we are getting the results ready</div>
    </div>
  );
};

export {
  HeaderSearchDropDownResults,
  HeaderSearchDropdownNoResults,
  HeaderSearchDropdownLoader
};

const HighlightText = ({ text, highlight }) => {
  const escapedHighlight = highlight.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
  const parts = text.split(new RegExp(`(${escapedHighlight})`, "gi"));
  return (
    <span className="h-24 overflow-hidden text-ellipsis break-all">
      {parts.map((part, i) => {
        const key = `${part}-${i}`;
        return part.toLowerCase() === highlight.toLowerCase() ? (
          <span key={key} className={"text-neutral-80"}>
            {part}
          </span>
        ) : (
          part
        );
      })}
    </span>
  );
};

HighlightText.propTypes = {
  text: PropTypes.string,
  highlight: PropTypes.string
};

const FilterButton = () => {
  return (
    <ButtonIconText
      intent={"teritory"}
      className="flex items-center whitespace-nowrap"
    >
      Screener (advance filters)
      <BsArrowRight className="h-4 w-4" />
    </ButtonIconText>
  );
};
