import { render, fireEvent, waitFor, screen } from "@testing-library/react";
import HeaderSearch from "./header-search";
import { ENTITY_API } from "../../../../infra/api/data-lake-service";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import TestAppRenderer from "../../../../infra/test-utils/test-app-renderer";

jest.mock("../../../../infra/api/data-lake-service");
jest.mock("../../entity-logo/entity-logo", () => ({
  __esModule: true,
  default: jest.fn(() => <div data-testid="entity-logo">Mocked EntityLogo</div>)
}));

describe("HeaderSearch", () => {
  afterEach(() => {
    jest.clearAllMocks();
  });

  it("renders without crashing", () => {
    ENTITY_API.mockResolvedValue({
      isPending: false,
      data: {
        data: [{ name: "test", security_ticker: "test" }]
      }
    });
    const queryClient = new QueryClient();
    render(
      <QueryClientProvider client={queryClient}>
        <HeaderSearch />
      </QueryClientProvider>
    );
  });

  it("opens dropdown when there is a query", async () => {
    ENTITY_API.mockResolvedValue({
      isPending: false,
      data: {
        data: [{ name: "test", security_ticker: "test" }]
      }
    });
    const queryClient = new QueryClient();
    const { getByTestId } = render(
      <QueryClientProvider client={queryClient}>
        <HeaderSearch />
      </QueryClientProvider>
    );
    const input = getByTestId("header-search-input");

    fireEvent.change(input, { target: { value: "test" } });

    await waitFor(() =>
      expect(
        getByTestId("header-search-search-result-dropdown")
      ).toBeInTheDocument()
    );
  });

  it("closes dropdown when escape key is pressed", async () => {
    ENTITY_API.mockResolvedValue({
      isPending: false,
      data: {
        data: [{ name: "test", security_ticker: "test" }]
      }
    });
    const queryClient = new QueryClient();
    const { getByTestId } = render(
      <QueryClientProvider client={queryClient}>
        <HeaderSearch />
      </QueryClientProvider>
    );
    const input = getByTestId("header-search-input");

    fireEvent.change(input, { target: { value: "test" } });
    await waitFor(() =>
      expect(
        getByTestId("header-search-search-result-dropdown")
      ).toBeInTheDocument()
    );
  });

  it("handles input change", () => {
    ENTITY_API.mockResolvedValue({
      isPending: false,
      data: {
        data: [{ name: "test", security_ticker: "test" }]
      }
    });
    const queryClient = new QueryClient();
    const { getByTestId } = render(
      <QueryClientProvider client={queryClient}>
        <HeaderSearch />
      </QueryClientProvider>
    );
    const input = getByTestId("header-search-input");

    fireEvent.change(input, { target: { value: "test" } });

    expect(input.value).toBe("test");
  });

  it("handles search", async () => {
    ENTITY_API.mockResolvedValue({
      isPending: false,
      data: {
        data: [{ name: "test", security_ticker: "test" }]
      }
    });
    const queryClient = new QueryClient();
    const { getByTestId } = render(
      <QueryClientProvider client={queryClient}>
        <HeaderSearch />
      </QueryClientProvider>
    );
    const input = getByTestId("header-search-input");

    fireEvent.change(input, { target: { value: "test" } });
  });

  it("shows loading state when searching", async () => {
    ENTITY_API.mockResolvedValue({
      isPending: true,
      data: null
    });
    const queryClient = new QueryClient();
    const { getByTestId } = render(
      <QueryClientProvider client={queryClient}>
        <HeaderSearch />
      </QueryClientProvider>
    );
    const input = getByTestId("header-search-input");

    fireEvent.change(input, { target: { value: "test" } });

    await waitFor(() =>
      expect(getByTestId("header-search-dropdown-loader")).toBeInTheDocument()
    );
  });

  it("sets searchIn to company_desc when query is in quotes", async () => {
    ENTITY_API.mockResolvedValue({
      isPending: false,
      data: {
        data: [{ name: "test", security_ticker: "test" }]
      }
    });
    const queryClient = new QueryClient();
    const { getByTestId } = render(
      <QueryClientProvider client={queryClient}>
        <HeaderSearch />
      </QueryClientProvider>
    );
    const input = getByTestId("header-search-input");

    fireEvent.change(input, { target: { value: '"test"' } });

    await waitFor(() =>
      expect(ENTITY_API).toHaveBeenCalledWith("test", ["company_desc"])
    );
  });

  it("handles no results", async () => {
    ENTITY_API.mockResolvedValue({
      isPending: false,
      data: {
        data: []
      }
    });
    const queryClient = new QueryClient();
    const { getByTestId } = render(
      <QueryClientProvider client={queryClient}>
        <HeaderSearch />
      </QueryClientProvider>
    );
    const input = getByTestId("header-search-input");

    fireEvent.change(input, { target: { value: "test" } });
  });

  it("handles clear input", () => {
    const queryClient = new QueryClient();
    const { getByTestId } = render(
      <QueryClientProvider client={queryClient}>
        <HeaderSearch />
      </QueryClientProvider>
    );
    const input = getByTestId("header-search-input");
    const clearButton = getByTestId("header-search-cross");

    fireEvent.change(input, { target: { value: "test" } });
    // Wait for the state update and the component re-render
    waitFor(() => {
      fireEvent.click(clearButton);
      expect(input.value).toBe("");
    });
  });

  it("click on header search", () => {
    ENTITY_API.mockReturnValueOnce({
      data: {
        data: []
      }
    }).mockReturnValue({
      data: {
        data: [{ name: "test", security_ticker: "test" }]
      }
    });
    render(
      <TestAppRenderer>
        <HeaderSearch />
      </TestAppRenderer>
    );

    fireEvent.click(screen.getByTestId("header-search-icon"));

    //type
    fireEvent.change(screen.getByTestId("header-search-input"), {
      target: { value: "test" }
    });

    //clear the search
    fireEvent.click(screen.getAllByTestId("header-search-cross")[1]);

    //type
    fireEvent.change(screen.getByTestId("header-search-input"), {
      target: { value: "test" }
    });

    //focus the dropdown
    fireEvent.focus(screen.getByTestId("header-search-search-result-dropdown"));

    fireEvent.blur(screen.getByTestId("header-search-search-result-dropdown"));
  });
});
