// Global events for managing loading state
let loadingCount = 0;
const subscribers = [];

export const loadingEvents = {
  // Subscribe to loading state changes
  subscribe: (callback) => {
    subscribers.push(callback);
    return () => {
      const index = subscribers.indexOf(callback);
      if (index > -1) {
        subscribers.splice(index, 1);
      }
    };
  },

  // Start loading
  start: () => {
    loadingCount++;
    notifySubscribers();
  },

  // End loading
  end: () => {
    loadingCount = Math.max(0, loadingCount - 1);
    notifySubscribers();
  },

  // Get current loading state
  isLoading: () => loadingCount > 0,

  // Reset loading state
  reset: () => {
    loadingCount = 0;
    notifySubscribers();
  }
};

// Notify all subscribers of state change
function notifySubscribers() {
  subscribers.forEach(callback => callback(loadingCount > 0));
}
