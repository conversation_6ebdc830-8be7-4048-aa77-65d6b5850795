import React from "react";
import { render, screen } from "@testing-library/react";
import { BrowserRouter as Router } from "react-router-dom";
import Header from "./header";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

jest.mock("react-router-dom", () => ({
  ...jest.requireActual("react-router-dom"),
  useNavigate: () => jest.fn()
}));

describe("Header", () => {
  const queryClient = new QueryClient();

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders without crashing", () => {
    render(
      <QueryClientProvider client={queryClient}>
        <Router>
          <Header />
        </Router>
      </QueryClientProvider>
    );

    expect(screen.getByTestId("header-user-profile")).toBeInTheDocument();
  });

  it("renders HeaderMenuBar, HeaderHelpIcon, HeaderUserProfile, and HeaderSearch", () => {
    render(
      <QueryClientProvider client={queryClient}>
        <Router>
          <Header />
        </Router>
      </QueryClientProvider>
    );

    expect(screen.getByTestId("header-menu-bar")).toBeInTheDocument();
    expect(screen.getByTestId("header-help-icon")).toBeInTheDocument();
    expect(
      screen.getByTestId("header-user-profile-dropdown")
    ).toBeInTheDocument();
    // expect(screen.getByTestId('header-search')).toBeInTheDocument();
  });
});
