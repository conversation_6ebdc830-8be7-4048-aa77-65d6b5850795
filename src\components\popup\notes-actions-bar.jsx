import React from "react";
import PropTypes from "prop-types";
import { HiOutlineTrash } from "react-icons/hi";
import { BiComment } from "react-icons/bi";
import ButtonIcon from "../../partials/atoms/button/button-icon";
import { FaTimes } from "react-icons/fa";

const NotesActionBar = ({
  selectedNotesCount,
  setShowNotesActionBar,
  clearSelections,
  deleteSelectedNotes
}) => {
  return (
    <div className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-[20]">
      <div className="bg-white flex gap-4 flex-row items-center rounded-lg border border-neutral-10 py-3 px-5 shadow-lg ">
        <div className="flex flex-row items-center gap-2">
          <h2 className="text-body-b font-body-b text-[#333333] whitespace-nowrap">
            {selectedNotesCount} note(s) selected
          </h2>
        </div>
        <div className="flex flex-row items-center gap-2">
          <ButtonIcon intent={"teritory"} disabled={true}>
            <BiComment></BiComment>
          </ButtonIcon>
          <ButtonIcon
            onClick={deleteSelectedNotes}
            intent={"teritorynegative"}
          >
            <HiOutlineTrash />
          </ButtonIcon>
        </div>
        <div className="border border-neutral-10 h-[24px]"></div>
        <div>
          <ButtonIcon intent={"teritory"}
            onClick={() => {
              setShowNotesActionBar(false);
              clearSelections();
            }}
          >
            <FaTimes className="text-neutral-60" />
          </ButtonIcon>
        </div>
      </div>
    </div>
  );
};

NotesActionBar.propTypes = {
  selectedNotesCount: PropTypes.number,
  setShowNotesActionBar: PropTypes.func,
  clearSelections: PropTypes.func,
  deleteSelectedNotes: PropTypes.func
};

export default NotesActionBar;
