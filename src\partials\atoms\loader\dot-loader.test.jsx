import React from "react";
import { render } from "@testing-library/react";
import <PERSON><PERSON>oa<PERSON> from "./dot-loader";

describe("DotLoader Component", () => {
  test("renders the loader with three dots", () => {
    const { container } = render(<DotLoader />);
    
    // Check if the container div is rendered
    const loaderContainer = container.querySelector("div");
    expect(loaderContainer).toBeInTheDocument();
    expect(loaderContainer).toHaveClass("flex", "items-center", "justify-center", "space-x-0.5");
    
    // Check if exactly three dot elements are rendered
    const dotElements = container.querySelectorAll(".h-1.5.w-1.5");
    expect(dotElements.length).toBe(3);
    
    // Check if all dots have the correct styling and animation
    dotElements.forEach((dot, index) => {
      expect(dot).toHaveClass("rounded-full", "bg-neutral-60");
      
      // Check for the animation class with different delays
      const animationDelay = (index + 1) * 100;
      expect(dot).toHaveClass(`animate-[pulse_1s_infinite_${animationDelay}ms]`);
    });
  });
});
