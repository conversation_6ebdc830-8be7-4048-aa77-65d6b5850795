import React from "react";
import PropTypes from "prop-types";
import { useAuth } from "react-oidc-context";
import { Spinner } from "../../partials/atoms/loader";

const AuthContextProvider = ({ children }) => {
  const auth = useAuth();

  if (auth?.isLoading && auth?.activeNavigator !== "signinSilent") {
    return <Spinner />;
  }

  if (auth?.error) {
    if (auth?.error.message) auth?.signinRedirect();
  }

  if (!auth?.isAuthenticated) auth.signinRedirect();

  if (auth.isAuthenticated) return { ...children };
};

AuthContextProvider.propTypes = {
  children: PropTypes.element
};

export default AuthContextProvider;
