/* index.css */
body {
  margin: 0;
}

.container {
  font-size: 3rem;
  margin: auto;
  max-width: 50rem;
  margin-top: 1.25rem;
}

.logo-name {
  position: relative;
  left: -3.25rem;
  top: 0.375rem;
}

.MuiTabs-indicator {
  margin-bottom: "0rem";
  bottom: 0.625rem;
}

.nep-checkinput-switch {
  height: 2.5rem;
  width: 1.25rem;
  background: var(--neutral-gray-00) 0% 0% no-repeat padding-box !important;
  border: 0.0625rem solid var(--primary-blue-78);
  border-radius: 1rem;
}

.nep-checkinput-switch-indicator {
  height: 0.875rem !important;
  width: 0.875rem !important;
  top: 0px !important;
}

.nep-checkinput-checked.nep-checkinput-switch {
  background: var(--primary-blue-78) 0% 0% no-repeat padding-box !important;
  border-radius: 1rem !important;
}

.nep-checkinput-switch .nep-checkinput-switch-indicator {
  background-color: var(--primary-blue-78);
}

.nep-checkinput-checked .nep-checkinput-switch-indicator {
  background-color: white !important;
}

.custom-icon {
  position: relative;
  top: 0.5rem;
  right: 1.125rem;
}

.mb-active-card {
  color: var(--primary-blue-78) !important;
  font-weight: bold;
  background-color: #e9f3ff !important;
}

.mb-accordian {
  font-weight: bold;
}
.noEntries {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 9.625rem);
  color: var(--neutral-gray-60);
}

.editInput {
  width: calc(100% - 5.25rem) !important;
}

.mb-app-container {
  height: calc(100vh - 7rem);
}

.header-requests {
  height: 3rem;
  border-bottom: 0.0625rem solid var(--neutral-gray-10);
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  color: var(--primary-blue-78);
  font-family: var(--brand-font-family);
  line-height: var(--brand-line-spacing-20);
  letter-spacing: var(--brand-character-spacing-0);
  box-shadow: 0px 4px 8px 0px rgba(0 0 0 0.08%);
  background: var(--neutral-gray-02);
  padding-left: 6px;
}

.breadcrumb-title-last {
  color: var(--primary-blue-78) !important;
}

.breadcrumb-title .nep-breadcrumb-separator {
  vertical-align: sub;
}

.config-no-entries {
  min-height: 28.375rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--neutral-gray-60);
}

.mb-datasourcing-back-btn {
  color: var(--primary-blue-78) !important;
  display: inline-flex !important;
  align-items: center !important;
  padding-left: 0px !important;
  padding-right: 1rem !important;
}

.mb-datasourcing-back-btn-icon {
  height: 1rem;
  width: 1rem;
  margin-right: 0.25rem;
  margin-top: 1px;
}

.noentries-search {
  display: flex;
  align-items: center;
  justify-content: center;
  height: calc(100vh - 6.5rem);
  width: 99%;
}
