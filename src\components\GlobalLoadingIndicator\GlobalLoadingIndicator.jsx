import React, { useState, useEffect } from "react";
import { SpinnerCircle } from "../../partials/atoms/loader";
import { loadingEvents } from "../../infra/events/loading-events";

const GlobalLoadingIndicator = () => {
  const [isLoading, setIsLoading] = useState(true); // Start with true to show loader on initial load
  const [initialLoading, setInitialLoading] = useState(true); // Track initial page load separately

  useEffect(() => {
    // Handle initial page load
    const handleInitialLoad = () => {
      // Use a slight delay to avoid flickering on fast loads
      setTimeout(() => {
        setInitialLoading(false);
      }, 800);
    };

    // Wait for page to fully load
    if (document.readyState === 'complete') {
      handleInitialLoad();
    } else {
      window.addEventListener('load', handleInitialLoad);
    }

    // Subscribe to loading events for subsequent API calls, etc.
    let unsubscribeFunc = () => {}; // Default no-op function
    try {
      unsubscribeFunc = loadingEvents.subscribe((loading) => {
        setIsLoading(loading);
      }) || (() => {}); // Ensure we have a function even if subscribe returns undefined
    } catch (error) {
      console.error("Error subscribing to loading events:", error);
    }
    
    // Cleanup
    return () => {
      window.removeEventListener('load', handleInitialLoad);
      try {
        unsubscribeFunc();
      } catch (error) {
        console.error("Error unsubscribing from loading events:", error);
      }
    };
  }, []);

  // Don't render anything if not loading
  if (!isLoading && !initialLoading) return null;

  return  <SpinnerCircle />;
};

export default GlobalLoadingIndicator;
