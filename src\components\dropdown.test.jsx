import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import Dropdown from './dropdown';

describe('Dropdown Component', () => {
  const mockItems = ['Option 1', 'Option 2', 'Option 3'];
  const mockSetSelectedValue = jest.fn();
  
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  test('renders with placeholder', () => {
    render(
      <Dropdown 
        items={mockItems}
        placeholder="Select an option"
        selectedValue="Select an option"
        setSelectedValue={mockSetSelectedValue}
      />
    );
    
    const dropdownButton = screen.getByRole('button');
    expect(dropdownButton).toHaveTextContent('Select an option');
    
    // Check that the placeholder text has the expected styling
    expect(dropdownButton).toHaveClass('text-neutral-40');
  });
  
  test('renders with selected value', () => {
    render(
      <Dropdown 
        items={mockItems}
        placeholder="Select an option"
        selectedValue="Option 1"
        setSelectedValue={mockSetSelectedValue}
      />
    );
    
    const dropdownButton = screen.getByRole('button');
    expect(dropdownButton).toHaveTextContent('Option 1');
    
    // Check that the selected value has the expected styling
    expect(dropdownButton).toHaveClass('text-neutral-80');
  });
  
  test('renders in disabled state', () => {
    render(
      <Dropdown 
        items={mockItems}
        placeholder="Select an option"
        selectedValue="Select an option"
        setSelectedValue={mockSetSelectedValue}
        disabled={true}
      />
    );
    
    const dropdownButton = screen.getByRole('button');
    expect(dropdownButton).toBeDisabled();
    expect(dropdownButton).toHaveClass('bg-neutral-5');
    expect(dropdownButton).toHaveClass('cursor-not-allowed');
  });
  
  test('opens menu when clicked', async () => {
    render(
      <Dropdown 
        items={mockItems}
        placeholder="Select an option"
        selectedValue="Select an option"
        setSelectedValue={mockSetSelectedValue}
      />
    );
    
    const dropdownButton = screen.getByRole('button');
    fireEvent.click(dropdownButton);
    
    // Check that menu items are rendered
    await waitFor(() => {
      mockItems.forEach(item => {
        expect(screen.getByText(item)).toBeInTheDocument();
      });
    });
  });
  
  test('selects an item when clicked', async () => {
    render(
      <Dropdown 
        items={mockItems}
        placeholder="Select an option"
        selectedValue="Select an option"
        setSelectedValue={mockSetSelectedValue}
      />
    );
    
    const dropdownButton = screen.getByRole('button');
    fireEvent.click(dropdownButton);
    
    // Wait for menu to open
    await waitFor(() => {
      expect(screen.getByText('Option 2')).toBeInTheDocument();
    });
    
    // Click on an option
    fireEvent.click(screen.getByText('Option 2'));
    
    // Check that setSelectedValue was called with the correct value
    expect(mockSetSelectedValue).toHaveBeenCalledWith('Option 2');
  });
  
  test('displays chevron icon', () => {
    render(
      <Dropdown 
        items={mockItems}
        placeholder="Select an option"
        selectedValue="Select an option"
        setSelectedValue={mockSetSelectedValue}
      />
    );
    
    // Check that the chevron icon is present
    const chevronIcon = document.querySelector('svg');
    expect(chevronIcon).toBeInTheDocument();
    expect(chevronIcon).toHaveClass('text-gray-400');
  });
}); 