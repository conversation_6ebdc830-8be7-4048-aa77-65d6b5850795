import React from "react";
import { render } from "@testing-library/react";
import LoadingIndicator from "./LoadingIndicator";

describe("LoadingIndicator", () => {
  it("renders loading spinner when isLoading is true", () => {
    const { getByRole } = render(<LoadingIndicator isLoading />);
    expect(getByRole).toBeDefined();
  });

  it("does not render loading spinner when isLoading is false", () => {
    const { queryByRole } = render(<LoadingIndicator isLoading={false} />);
    queryByRole("progressbar");
  });

  it("does not render loading message when loadingMessage prop is not provided", () => {
    const { queryByText } = render(<LoadingIndicator isLoading />);
    queryByText(/.+/);
  });

  it("applies inline class when inline prop is true", () => {
    render(<LoadingIndicator isLoading inline />);
  });

  it("applies form-status class when inline prop is false", () => {
    render(<LoadingIndicator isLoading />);
  });
});
