import React from "react";
import PropTypes from "prop-types";

const TabIconText = ({
  onClick,
  icon = "",
  label = "",
  testId = "",
  active = false
}) => {
  return (
    <button
      id={testId}
      data-testid={testId}
      onClick={onClick}
      className={`h-full flex  items-center gap-2  ${active ? "border-b-2 border-primary-78 text-primary-78" : ""} body-r  px-3  text-neutral-60 hover:text-primary-78 disabled:text-neutral-20 `}
    >
      {icon}
      <span className="body-r flex items-center">{label}</span>
    </button>
  );
};

TabIconText.propTypes = {
  onClick: PropTypes.func.isRequired,
  label: PropTypes.string,
  icon: PropTypes.element,
  testId: PropTypes.string,
  active: PropTypes.bool
};

export default TabIconText;
