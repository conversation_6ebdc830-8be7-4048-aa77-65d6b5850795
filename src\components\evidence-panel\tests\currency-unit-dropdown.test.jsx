/**
 * @jest-environment jsdom
 */
import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import CurrencyUnitDropdown from '../currency-unit-dropdown';
import { FINANCIAL_VALUE_UNIT_ENUM } from '../utilities/financial-value-unit-enum';

// Mock Headless UI components
jest.mock('@headlessui/react', () => ({
  Menu: ({ as: Component = 'div', children, className }) => (
    <Component className={className} data-testid="menu">
      {typeof children === 'function' ? children({ open: true }) : children}
    </Component>
  ),
  MenuButton: ({ className, children, ...props }) => (
    <button className={className} data-testid="menu-button" {...props}>
      {children}
    </button>
  ),
  MenuItem: ({ children, ...props }) => {
    return (
      <div data-testid="menu-item" {...props}>
        {typeof children === 'function' ? children({ active: false, focus: false }) : children}
      </div>
    );
  },
  MenuItems: ({ className, children }) => (
    <div className={className} data-testid="menu-items">
      {children}
    </div>
  ),
}));

describe('CurrencyUnitDropdown Component', () => {
  // Default props
  const defaultProps = {
    currencyUnit: 'Millions',
    onCurrencyUnitChange: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  // Basic rendering test
  test('renders with default currency unit selected', () => {
    render(<CurrencyUnitDropdown {...defaultProps} />);
    
    // Dropdown button should show the selected currency unit
    const menuButton = screen.getByTestId('menu-button');
    expect(menuButton).toHaveTextContent('Millions');
  });

  // Test with empty currency unit
  test('renders with "No Unit" when currency unit is empty', () => {
    render(<CurrencyUnitDropdown {...defaultProps} currencyUnit="" />);
    
    // Dropdown button should show "No Unit"
    const menuButton = screen.getByTestId('menu-button');
    expect(menuButton).toHaveTextContent('No Unit');
  });

  // Test with null currency unit
  test('renders with "No Unit" when currency unit is null', () => {
    render(<CurrencyUnitDropdown {...defaultProps} currencyUnit={null} />);
    
    // Dropdown button should show "No Unit"
    const menuButton = screen.getByTestId('menu-button');
    expect(menuButton).toHaveTextContent('No Unit');
  });

  // Test currency unit selection
  test('calls onCurrencyUnitChange when a unit is selected', () => {
    render(<CurrencyUnitDropdown {...defaultProps} />);
    
    // Get all menu items
    const menuItems = screen.getAllByTestId('menu-item');
    
    // Click on the Billions option
    const billionsButton = menuItems[3].querySelector('button');
    fireEvent.click(billionsButton);
    
    // onCurrencyUnitChange should be called with the Billions object
    expect(defaultProps.onCurrencyUnitChange).toHaveBeenCalledWith(FINANCIAL_VALUE_UNIT_ENUM[3]);
    
    // Button text should be updated
    const menuButton = screen.getByTestId('menu-button');
    expect(menuButton).toHaveTextContent('Billions');
  });

  // Test case insensitive matching
  test('handles case insensitive matching for currency unit', () => {
    render(<CurrencyUnitDropdown {...defaultProps} currencyUnit="millions" />);
    
    // Dropdown button should show "Millions" even though the prop is lowercase
    const menuButton = screen.getByTestId('menu-button');
    expect(menuButton).toHaveTextContent('Millions');
  });

  // Test with non-existing currency unit
  test('defaults to "No Unit" when currency unit is not in the list', () => {
    render(<CurrencyUnitDropdown {...defaultProps} currencyUnit="Quadrillions" />);
    
    // Should default to "No Unit"
    const menuButton = screen.getByTestId('menu-button');
    expect(menuButton).toHaveTextContent('No Unit');
  });

  // Test component updates when props change
  test('updates when currencyUnit prop changes', () => {
    const { rerender } = render(<CurrencyUnitDropdown {...defaultProps} />);
    
    // Initially Millions
    let menuButton = screen.getByTestId('menu-button');
    expect(menuButton).toHaveTextContent('Millions');
    
    // Change to Billions
    rerender(<CurrencyUnitDropdown {...defaultProps} currencyUnit="Billions" />);
    
    // Should now show Billions
    menuButton = screen.getByTestId('menu-button');
    expect(menuButton).toHaveTextContent('Billions');
  });

  // Test without onCurrencyUnitChange callback
  test('works without onCurrencyUnitChange callback', () => {
    const props = {
      ...defaultProps,
      onCurrencyUnitChange: undefined,
    };
    
    render(<CurrencyUnitDropdown {...props} />);
    
    // Get all menu items
    const menuItems = screen.getAllByTestId('menu-item');
    
    // Click on a unit - this should not throw an error
    const thousandsButton = menuItems[1].querySelector('button');
    expect(() => fireEvent.click(thousandsButton)).not.toThrow();
    
    // Button text should still update
    const menuButton = screen.getByTestId('menu-button');
    expect(menuButton).toHaveTextContent('Thousands');
  });

  // Test styling based on selection
  test('applies correct styling when No Unit is selected', () => {
    render(<CurrencyUnitDropdown {...defaultProps} currencyUnit="" />);
    
    const menuButton = screen.getByTestId('menu-button');
    expect(menuButton).toHaveClass('text-neutral-40');
    expect(menuButton).not.toHaveClass('text-neutral-80');
  });

  test('applies correct styling when a unit is selected', () => {
    render(<CurrencyUnitDropdown {...defaultProps} />);
    
    const menuButton = screen.getByTestId('menu-button');
    expect(menuButton).toHaveClass('text-neutral-80');
    expect(menuButton).not.toHaveClass('text-neutral-40');
  });

  // Verify all enum options are rendered
  test('renders all currency unit options', () => {
    render(<CurrencyUnitDropdown {...defaultProps} />);
    
    // Get all menu items
    const menuItems = screen.getAllByTestId('menu-item');
    
    // Should have same number of items as in the enum
    expect(menuItems.length).toBe(FINANCIAL_VALUE_UNIT_ENUM.length);
    
    // Check each item text
    FINANCIAL_VALUE_UNIT_ENUM.forEach((unit, index) => {
      const button = menuItems[index].querySelector('button');
      expect(button).toHaveTextContent(unit.label);
    });
  });
}); 