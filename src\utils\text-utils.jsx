import React from 'react';
import {CommaFormatterRegex,QueryWithoutCommaRegex,TextWithoutCommaRegex} from '../constants';

export const highlightText = (text, query) => {
  if (!query) return text;
  if (text === undefined || text === null) return text;
  const textStr = String(text);
  const textWithoutCommas = textStr.replace(TextWithoutCommaRegex, '');
  const queryWithoutCommas = query.replace(TextWithoutCommaRegex, '');
  const displayText = !isNaN(Number(textWithoutCommas)) ? formatWithCommas(textWithoutCommas) : textStr;
  if (textWithoutCommas.toLowerCase().includes(queryWithoutCommas.toLowerCase())) {
    const escapedQuery = queryWithoutCommas
      .replace(QueryWithoutCommaRegex, '\\$&')
      .split('')
      .join('[,]?');
    const regex = new RegExp(`(${escapedQuery})`, 'gi');
    const parts = displayText.split(regex);
    return parts.map((part, index) => {
      const partWithoutCommas = part.replace(TextWithoutCommaRegex, '');
      if (partWithoutCommas.toLowerCase() === queryWithoutCommas.toLowerCase()) {
        return (
          <span key={`${part}-${index}`} className="bg-[#F8FF36]">
            {part}
          </span>
        );
      }
      return part;
    });
  }
  return displayText;
};
export const formatWithCommas = (value) => {
  if (value === null || value === undefined || value === "") return value;
  return value.toString().replace(CommaFormatterRegex, ",");
};