import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import DocumentViewerError from "./document-viewer-error";
import useDocumentViewerStore from "./document-viewer.store";
import { notify } from "../../../partials/molecules/toaster";

jest.mock("./document-viewer.store", () => ({
  __esModule: true,
  default: jest.fn(() => ({
    fileUrl: "http://example.com",
    fileExtension: "pdf",
    NEW_TAB_FILE_TYPES: ["pdf", "docx"],
    setPreviewClose: jest.fn()
  }))
}));

jest.mock("../../../partials/molecules/toaster", () => ({
  notify: {
    error: jest.fn()
  }
}));

describe("DocumentViewerError", () => {
  it("renders the error message and open link button", () => {
    render(<DocumentViewerError readOnly={false} />);

    expect(
      screen.getByText(
        "We are unable to process this file type, please try accessing the link."
      )
    ).toBeInTheDocument();
    expect(
      screen.getByTestId("document-viewer-error-preview-open-link")
    ).toBeInTheDocument();
  });

  it("does not render close button when readOnly is true", () => {
    render(<DocumentViewerError readOnly={true} />);

    const closeButton = screen.queryByTestId(
      "document-viewer-error-close-button"
    );
    expect(closeButton).toBeNull();
  });

  it("handles new tab link click for supported file types", () => {
    render(<DocumentViewerError readOnly={false} />);

    const openLink = screen.getByTestId(
      "document-viewer-error-preview-open-link"
    );
    fireEvent.click(openLink);

    expect(notify.error).not.toHaveBeenCalled();
  });

  it("handles new tab link click for unsupported file types", () => {
    useDocumentViewerStore.mockReturnValueOnce({
      fileUrl: "http://example.com",
      fileExtension: "exe",
      NEW_TAB_FILE_TYPES: ["pdf", "docx"],
      setPreviewClose: jest.fn()
    });

    render(<DocumentViewerError readOnly={false} />);

    const openLink = screen.getByTestId(
      "document-viewer-error-preview-open-link"
    );
    fireEvent.click(openLink);

    expect(notify.error).toHaveBeenCalledWith("File format not supported");
  });
});
