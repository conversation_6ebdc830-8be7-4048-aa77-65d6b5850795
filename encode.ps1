
# Encode
 

$File1 = ".\configurations.json"
$File2 = ".\bundle.js"
 
$Content1 = get-content $File1
# $Content1 = $Content1.replace('var configs =', '')
# $Content1 = $Content1.replace(';', '')
# # $Content1 = $Content1.replace('},','}')
# $Content1 = $Content1.replace('  ', '  "')
# $Content1 = $Content1.replace(':', '":')
# $Content1 = $Content1.replace('  ""', '  "')
# $Content1 = $Content1.replace('https":', 'https:')
# $Content1 = $Content1.replace('http":', 'http:')
# $Content1 = $Content1.replace('localhost":', 'localhost:')
# $Content1 = $Content1.replace('  "  "', '  "')
# $Content1 = $Content1.replace('true', '"true"')
# $Content1 = $Content1.replace('""true""', '"true"')
# $Content1 = $Content1.replace('false', '"false"')
# $Content1 = $Content1.replace('""false""', '"false"')
# $Content1 = $Content1.replace('"  "', '"')
# $Content1 = $Content1.replace('"}', '}')
# $Content1 = $Content1.replace('"loadUserInfo": "true",','"loadUserInfo": "true"')

$Bytes = [System.Text.Encoding]::UTF8.GetBytes($Content1)
$Encoded = [System.Convert]::ToBase64String($Bytes)
$Encoded = "QsEC" + $Encoded + "CEsQC"
$Encoded | set-content ($File1 + ".b64")

$Content1 = get-content ($File1 + ".b64")
$Bytes = [System.Text.Encoding]::UTF8.GetBytes($Content1)
$Encoded = 'var bundle = { value : "QsEC' 
$Encoded = $Encoded + [System.Convert]::ToBase64String($Bytes)
$Encoded = $Encoded + 'CEsQc"};'
$Encoded | set-content ($File2)

Remove-Item ($File1 + ".b64")

Write-Host "ENCODED: " $Encoded