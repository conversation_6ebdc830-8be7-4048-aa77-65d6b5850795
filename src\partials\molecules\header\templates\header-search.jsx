import { React, useEffect, useRef, useState } from "react";
import { Combobox, ComboboxInput, ComboboxButton } from "@headlessui/react";
import Transition from "../../../atoms/transition/transition";
import { FaSearch, FaTimes } from "react-icons/fa";
import { ENTITY_API } from "../../../../infra/api/data-lake-service";
import { notify } from "../../../../partials/molecules/toaster";
import {
  HeaderSearchDropDownResults,
  HeaderSearchDropdownLoader,
  HeaderSearchDropdownNoResults
} from "./header-search-dropdown";
import useWatchListStore from "../../../../pages/watch-list/watch-list.store";

const DROP_DOWN_STATUS = {
  LOADING: "LOADING",
  NO_RESULTS: "NO_RESULTS",
  RESULTS: "RESULTS",
  NO_ACTION: "NO_ACTION"
};

const HeaderSearch = () => {
  const phrases = [
    "Search by Ticker / Company",
    'Use "quotes" for keyword based search'
  ];
  const { query, setQuery } = useWatchListStore();
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [results, setResults] = useState([]);
  const [dropdownStatus, setDropdownStatus] = useState(
    DROP_DOWN_STATUS.NO_ACTION
  );
  const [isKeywordSearch, setIsKeywordSearch] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchIn, setSearchIn] = useState([]);
  const [currentPhraseIndex, setCurrentPhraseIndex] = useState(0);
  const [displayText, setDisplayText] = useState("");

  const timerRef = useRef(null);
  const trigger = useRef(null);

  const entitySearch = ENTITY_API(searchQuery, searchIn);

  useEffect(() => {
    if (query.length > 0) {
      setDropdownOpen(true);
      setDropdownStatus(DROP_DOWN_STATUS.LOADING);
      setResults([]);
    } else {
      setDropdownOpen(false);
      setDropdownStatus(DROP_DOWN_STATUS.NO_ACTION);
      setResults([]);
    }
  }, [searchQuery]);

  const removeQuotes = (str) => {
    if (str.startsWith('"') && str.endsWith('"')) {
      return str.substring(1, str.length - 1);
    }
    return str;
  };

  useEffect(() => {
    if (query.length === 40) {
      notify.error("Search query should be less than 40 characters");
    }
    const isQueryInQuotes = /^"[^"]+"$/.test(query);
    setSearchQuery(removeQuotes(query));
    if (isQueryInQuotes) setSearchIn(["company_desc"]);
    else setSearchIn(["name", "security_ticker"]);
    setIsKeywordSearch(isQueryInQuotes);
  }, [query]);

  useEffect(() => {
    if (!entitySearch?.isPending) {
      if (entitySearch?.data?.data?.length === 0) {
        setDropdownStatus(DROP_DOWN_STATUS.NO_RESULTS);
      } else if (entitySearch?.data?.data?.length > 0) {
        setResults(entitySearch.data.data);
        setDropdownStatus(DROP_DOWN_STATUS.RESULTS);
      } else {
        setDropdownStatus(DROP_DOWN_STATUS.NO_RESULTS);
      }
    }
  }, [entitySearch?.data, entitySearch?.isPending]);

  const dropdown = useRef(null);

  useEffect(() => {
    let currentPhrase = phrases[currentPhraseIndex];
    let currentIndex = 0;

    const displayText = () => {
      setCurrentPhraseIndex((prevIndex) => (prevIndex + 1) % phrases.length);
      setDisplayText("");
    };
    timerRef.current = setInterval(() => {
      setDisplayText(currentPhrase.substring(0, currentIndex + 1));
      currentIndex++;

      if (currentIndex >= currentPhrase.length) {
        clearInterval(timerRef.current);
        setTimeout(() => {
          displayText();
        }, 3000);
      }
    }, 100);

    return () => clearInterval(timerRef.current);
  }, [currentPhraseIndex]);

  // close on click outside
  useEffect(() => {
    const clickHandler = ({ target }) => {
      if (!dropdown.current) return;
      if (
        !dropdownOpen ||
        dropdown.current.contains(target) ||
        trigger.current.contains(target)
      )
        return;

      setDropdownOpen(false);
      setQuery("");
    };
    document.addEventListener("click", clickHandler);
    return () => document.removeEventListener("click", clickHandler);
  });

  // close if the esc key is pressed
  useEffect(() => {
    const keyHandler = ({ keyCode }) => {
      if (!dropdownOpen || keyCode !== 27) return;

      setDropdownOpen(false);
      setQuery("");
    };
    document.addEventListener("keydown", keyHandler);
    return () => document.removeEventListener("keydown", keyHandler);
  });

  return (
    <>
      <Combobox value={query}>
        <ComboboxInput
          id="header-search-input"
          data-testid="header-search-input"
          value={query}
          maxLength={40}
          className="body-r relative h-8 w-96 rounded border border-neutral-10 px-9 py-1.5 text-neutral-80 placeholder-neutral-30 hover:border-primary-78 focus:border-primary-90 focus:outline-none focus:ring-0"
          placeholder={displayText}
          autoComplete="off"
          ref={trigger}
          onChange={(e) => {
            setQuery(e.target.value);
          }}
        ></ComboboxInput>
        <ComboboxButton className="absolute left-4 top-1 z-30 size-3 pr-2">
          <FaSearch
            data-testid={"header-search-icon"}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
            }}
            className="absolute h-3 w-3 cursor-pointer text-primary-78 focus:text-neutral-60"
            strokeWidth={1}
          />
        </ComboboxButton>
        <ComboboxButton
          data-testid="header-search-cross"
          className="absolute right-4 top-1 z-30 h-3 w-3"
        >
          {query.length > 0 && (
            <FaTimes
              data-testid="header-search-cross"
              onClick={(e) => {
                setQuery("");
                setDropdownStatus(false);
                e.preventDefault();
              }}
              className="absolute h-3 w-3 cursor-pointer text-neutral-60"
              strokeWidth={1}
            />
          )}
        </ComboboxButton>
      </Combobox>
      <Transition
        show={dropdownOpen}
        className={`body-r absolute top-9 z-50 overflow-hidden rounded border border-neutral-10 bg-white shadow-xr`}
        enter="transition ease-out duration-300 transform"
        enterStart="opacity-0 -translate-y-2"
        enterEnd="opacity-100 translate-y-0"
        leave="transition ease-out duration-300"
        leaveStart="opacity-100"
        leaveEnd="opacity-0"
      >
        <button
          data-testid={"header-search-search-result-dropdown"}
          ref={dropdown}
          onFocus={() => setDropdownOpen(true)}
          onBlur={(e) => {
            e.target.type === "text"
              ? setDropdownOpen(true)
              : setDropdownOpen(false);
          }}
          className="items-center pt-3"
        >
          <div
            data-testid="header-search-dropdown-loader"
            className="body-r flex w-96 flex-col gap-3"
          >
            {dropdownStatus === DROP_DOWN_STATUS.LOADING && (
              <HeaderSearchDropdownLoader />
            )}
            {dropdownStatus === DROP_DOWN_STATUS.RESULTS && (
              <HeaderSearchDropDownResults
                results={results}
                query={query}
                isKeywordSearch={isKeywordSearch}
                setDropdownOpen={setDropdownOpen}
                setQuery={setQuery}
              />
            )}
            {dropdownStatus === DROP_DOWN_STATUS.NO_RESULTS && (
              <HeaderSearchDropdownNoResults data-testid="header-search-dropdown-no-results" />
            )}
          </div>
        </button>
      </Transition>
    </>
  );
};

export default HeaderSearch;
