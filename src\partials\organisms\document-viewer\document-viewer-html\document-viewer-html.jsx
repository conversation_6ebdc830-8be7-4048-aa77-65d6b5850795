import React from "react";
import { GET_HTML_FROM_URL_API } from "../../../../infra/api/miscellaneous-service";

import DocumentViewerLoading from "../document-viewer-loading";
import PropTypes from "prop-types";
import DocumentViewerHTMLParser from "./document-viewer-html-parser";

const DocumentViewerHTML = ({ metaData, readOnly, fileUrl, fileExtension }) => {
  let filingsUrl = fileUrl.replace("ix?doc=/", "");
  let getHtmlFromUrlApiQuery = GET_HTML_FROM_URL_API(filingsUrl);

  if (getHtmlFromUrlApiQuery?.isLoading) {
    return <DocumentViewerLoading />;
  }

  return (
    <DocumentViewerHTMLParser
      metaData={metaData}
      readOnly={readOnly}
      fileData={getHtmlFromUrlApiQuery?.data?.data}
      fileExtension={fileExtension}
      filingsUrl={fileUrl}
    />
  );
};

export default DocumentViewerHTML;

DocumentViewerHTML.propTypes = {
  metaData: PropTypes.object,
  readOnly: PropTypes.bool,
  fileUrl: PropTypes.string,
  fileExtension: PropTypes.string
};
