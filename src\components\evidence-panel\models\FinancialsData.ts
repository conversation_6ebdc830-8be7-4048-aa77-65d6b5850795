import { Extraction } from "./enums";
import { A1TableGroup } from "./A1TableGroup";
import { A1File } from "./A1File";

export interface FinancialsData {
  id: string;
  companyId: string;
  companyName: string;
  typeofExtraction: Extraction;
  tableGroups: A1TableGroup[];
  files: A1File[];
  processId: string;
  jobId: string;
  ticker: string;
  templateId: string;
  currencyUnit: string;
  isPublished: boolean;
}
