import { render, screen } from "@testing-library/react";
import NavLinkComponent from "./nav-link-component";
import TestAppRenderer from "../../../infra/test-utils/test-app-renderer";

jest.mock("react-router", () => ({
  ...jest.requireActual("react-router"),
  useLocation: jest.fn().mockImplementation(() => {
    return { pathname: "/testroute" };
  })
}));

describe("Component NavLinkComponent render", () => {
  it("should render NavLinkComponent without value", () => {
    render(
      <TestAppRenderer>
        <NavLinkComponent text={"Test"} to={"Test"} />
      </TestAppRenderer>
    );
    expect(screen.getByTestId("nav-link")).toBeInTheDocument();
  });
});
