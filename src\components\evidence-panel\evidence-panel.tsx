import { useState, useEffect, useRef, SetStateAction } from "react";
import PropTypes from "prop-types";
import TabIconText from "../../partials/atoms/tab/tab-icon-text";
import KendoGrid from "../../partials/atoms/grid/kendo-grid";
import Header from "./header";
import { PdfHolder, PdfHighlighter } from "../pdf-highlighter";
import PdfIcon from "../../resources/images/pdf-icon.svg";
import "../pdf-highlighter/style/index.css";
import { Checkbox } from "@progress/kendo-react-inputs";
import { AreaHighlight } from "../pdf-highlighter/components/AreaHighlight";
import { Highlight } from "../pdf-highlighter/components/TextSelectionHighlight";
import { formatPeriodTypeWithDataType } from "../../utils/formatPeriodType.tsx";
import type { IHighlight } from "../pdf-highlighter/types";
import { Popup } from "../pdf-highlighter/components/Popup";
import PopupcolumnComponent from "../popup/column-edit-popup";
import { GET_FINANCIAL_DATA } from "../../infra/api/financial/get-financials-service";
import { v4 as uuidv4 } from "uuid";
import PopupComponent from "../popup/add-row-popup";
import { ChatOfAccounts } from "../../infra/api/alexanderia/chartofaccounts-service";
import { DOWNLOAD_FILE_SERVICE } from "../../infra/api/company/download-file-service";
import NotesTab from './notes-tab';
import { mergeRowUp } from "./row-operations-merge-up";
import { mergeRowBelow } from "./row-operations-merge-down";
import type { PDFDocumentProxy } from "pdfjs-dist";
import { CircularStatusButtons } from "./components/status-button";
import { FaRegFlag } from "react-icons/fa";
import {
  DefaultValues,
  FilingPeriods,
  PdfPageHeight,
  PdfPageWidth,
  CommaFormatterRegex,
  RowsSuccessMessages,
  FinancialStatements,
  BalanceSheet,
  CashFlow,
  FinancialStatementsWithName,
  ColumnSuccessMessages
} from "../../constants";
import { notify } from "../../partials/molecules/toaster";
import { LuArrowLeftToLine, LuFlag } from "react-icons/lu";
import ButtonIcon from "../../partials/atoms/button/button-icon";
import { SpinnerCircle } from "../../partials/atoms/loader";
import { Tooltip } from "react-tooltip";
import StatementDropdown from "./StatementDropdown";
import { makeHeaderRow,formatDateWithHyphens } from "./utils";
import { highlightText } from "../../utils/text-utils";
import {
  TAB_LABELS,
  LEGEND_COLORS,
  COLUMN_WIDTHS,
  SUCCESS_MESSAGES,
  NOTES_PREFIX,
  CELL_STYLES,
  Standard_KPI
} from "./evidence-panel-constants";
import { insertNewColumn } from "./column-operations/add-column";
import { deleteColumns } from "./column-operations/delete-column";
import { updateFinancialDataAfterRowAddition,  updateFinancialDataAfterRowDeletion,updateCircularStatusButtonDataMapping, convertPositionToPercentage } from "../../utils/financial-data-operations";
import { KpiModule } from "../../constants/kpi-module";
import { GET_KPI_CONFIG_SERVICE , GET_KPI_MAPPING_SERVICE} from "../../infra/api/company/kpi-service";
import { useSearchParams } from "react-router-dom";
import { GET_COMPANY_DETAILS } from "../../infra/api/company/get-company-details-service";
import { CompanyModel, CurrencyModel } from "./models/ingestion.model";
import { validatePeriod } from "../../constants/commonFunctions.js";
// Global variable to store company details

// Define global variables
let plData = [];
let bsData = [];
let cfData = [];

export const EvidencePanel = ({
  handleBackfunc,
  processId: propProcessId,
  companyName: propCompanyName,
  companyTicker: propCompanyTicker,
  acuityid: propAcuityId,
  extractionType
}) => {
  // Use URL query parameters or fallback to props
  const [searchParams] = useSearchParams();
  
  // Extract parameters from the URL query string
  const queryProcessId = searchParams.get('processId');
  const queryCompanyId = searchParams.get('companyId');
  const queryClientEnv = searchParams.get('environment');
  
  // Use query parameters if available, otherwise use props
  const processId = queryProcessId || propProcessId;
  const acuityid = queryCompanyId || propAcuityId;
    // For these props, we'll keep using the props until we can get them from the API
  const [companyName, setCompanyName] = useState(propCompanyName);
  const companyTicker = propCompanyTicker;
  interface EditingCell {
    rowId: string | null;
    field: string | null;
  }
  const[companyDetails,setCompanyDetails] = useState<CompanyModel | null>(null);
  const[currencyDetails,setCurrencyDetails] = useState<Array<CurrencyModel> | []>([]);
  const [pdfDocument, setPdfDocument] = useState<PDFDocumentProxy | null>(null);
  // Add these state declarations at the top of the EvidencePanel component, near other state declarations
  
  // First method to execute after retrieving company ID from query string
  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true);
        const data = await GET_COMPANY_DETAILS(queryCompanyId);
        if(data != null) {
          setCompanyDetails(data.companyListModel);
          setCurrencyDetails(data.currencies);
          setCompanyName(data.companyListModel?.companyName || '');
          let result  = await  GET_KPI_MAPPING_SERVICE(data.companyListModel.portfolioCompanyID)
          plData = result?.find(x => x.moduleId == KpiModule.ProfitAndLoss)?.kpis || [];
          bsData = result?.find(x => x.moduleId == KpiModule.BalanceSheet)?.kpis || [];
          cfData = result?.find(x => x.moduleId == KpiModule.CashFlow)?.kpis || [];
          await loadData();
        }       
      } catch (err) {
        console.error("Error fetching company details:", err);
        setError(err);
      } finally {
        setIsLoading(false);
      }
    };
    
    // Execute the fetch function immediately when the component mounts
    fetchData();
  }, [queryCompanyId,processId]); // Use queryCompanyId in dependency array

  const [originalCellValue, setOriginalCellValue] = useState(null);
  const [editingCell, setEditingCell] = useState<EditingCell>({
    rowId: null,
    field: null
  });
  const [selectedTab, setSelectedTab] = useState(0);
  const [showPdfOverlay, setShowPdfOverlay] = useState(false);
  const [currentSelectedState, setCurrentSelectedState] = useState({});
  const [selectedCell, setSelectedCell] = useState<SelectedCellInfo | null>(
    null
  );
  const [searchQuery, setSearchQuery] = useState("");
  interface TableRow {
    id: string;
    [key: string]: any;
  }
  const [pdfHighlighted, setPdfHighlighted] = useState(undefined);
  const [currentTableData, setCurrentTableData] = useState<TableRow[]>([]);  const [hasChanges, setHasChanges] = useState(false);
  const [hasPublishChanges, setHasPublishChanges] = useState(false);
  // Using a number instead of boolean to ensure each change creates a unique reference
  const [hasMappedChanges, setHasMappedChanges] = useState(0);
  const [checkedItems, setCheckedItems] = useState({});
  const [dropdownOptions, setDropdownOptions] = useState([]);
  const [highlights, setHighlights] = useState<Array<IHighlight>>([]);
  const [notesData, setNotesData] = useState([]);
  const [
    standardLineItemsForIncomeStatement,
    setStandardLineItemsForIncomeStatement
  ] = useState([]);
  const [
    standardLineItemsForBalanceSheet,
    setStandardLineItemsForBalanceSheet
  ] = useState([]);
  const [standardLineItemsForCashFlow, setStandardLineItemsForCashFlow] =
    useState([]);
  const [checkedColumnHeaders, setCheckedColumnHeaders] = useState({});
  const [isCheckboxHeader, setisCheckboxHeader] = useState(false);
  const [editingColumnIds, setEditingColumnIds] = useState<string[]>([]);
  const [columnDetails, setColumnDetails] = useState({
    Filing_Type: "",
    Filing_Version: "",
    Filing_Date: null
  });
  const tableRef = useRef(null);
  const [showDropdown, setShowDropdown] = useState(false);
  // Add this new state to store data for each tab
  const [tableDataByTab, setTableDataByTab] = useState({
    0: [],
    1: [],
    2: [],
    3: []
  });   
  //#region Removed duplicate calls to SameMethods when the page loads
  // useEffect(() => {
  //   const fetchData = async () => {
  //     try {
  //       setIsLoading(true);
  //       const data = await GET_COMPANY_DETAILS(queryCompanyId);
  //       if(data!=null)
  //       {
  //         setCompanyDetails(data.companyListModel);
  //         setCurrencyDetails(data.currencies);
  //       // Set KPI mapping data for each statement
  //       }
  //     } catch (err) {
  //       setError(err);
  //     } finally {
  //       setIsLoading(false);
  //     }
  //   };
    
  //   // Execute the fetch function
  //   fetchData();
  // }, [queryCompanyId]); // Use queryCompanyId in dependency array instead of processId
  //#endregion
  const resetRows = () => {
    if (financialsData?.tableGroups) {
      // Create a new object with data from original financialsData
      const originalData = Object.fromEntries(
        financialsData.tableGroups.map((group, index) => {
          const sections = [
            {
              rows: group?.tables[0]?.rows || [],
              columns: group?.tables[0]?.columns || []
            }
          ];
          // Use group.label as key instead of numeric index
          return [index, processTableData(sections)];
        })
      );
  
      // Reset table data to original state
      setTableDataByTab(originalData);
  
      // Reset current table data to match selected tab
      setCurrentTableData(originalData[selectedTab] || []);
    } else {
      // Fallback: If no original data, just clear everything with string keys
      setTableDataByTab({
        0: [],
        1: [],
        2: [],
        3: []
      });
      setCurrentTableData([]);
    }
  };
  const [dropdownOptionsByTab, setDropdownOptionsByTab] = useState({
    0: [],
    1: [],
    2: [],
    3: []
  });
  const [highlightContent, setHighlightContent] = useState("");
  const [highlightPosition, setHighlightPosition] = useState("");
  const [isMapped, setIsMapped] = useState(false);  
  const [currency, setCurrency] = useState("");
  const [selectedCurrency, setSelectedCurrency] = useState({});
  const [selectedCurrencyUnit, setSelectedCurrencyUnit] = useState({});
  const [showPopup, setShowPopup] = useState(false);
  // New state to track if all dropdowns have values selected
  const [areAllDropdownsSelected, setAreAllDropdownsSelected] = useState(false);
    // Handle currency change from dropdown
  const handleCurrencyChange = (newCurrency) => {
    // Update local currency state
    setCurrency(newCurrency?.currencyCode);
    setSelectedCurrency(newCurrency);
  };

  const handleCurrencyUnitChange = (newCurrencyUnit) => {
      setSelectedCurrencyUnit(newCurrencyUnit);
  };

  const pdfHighlighterRef = useRef(null);
  const [ISChartofAccounts, setISCOS] = useState([]);
  const [BSChartofAccounts, setBSCOS] = useState([]);
  const [CFChartofAccounts, setCFCOS] = useState([]);
  const [IsFileNameLoading, setIsFileNameLoading] = useState(false);
  interface LocalFileObj {
    fileName: string;
    file: string;
    fileKey: string;
  }
  const [AllDownloadedPdf, setAllDownloadedPdf] = useState<LocalFileObj[] | []>(
    []
  );

  const [localPdfUrl, setLocalPdfUrl] = useState<LocalFileObj | null>(null);
  // Replace line 145 and surrounding code with:
  interface FinancialFile {
    url: string;
    fileName: string; // Added fileName property
  }

  interface FinancialsData {
    files?: FinancialFile[];
    tableGroups?: any[];
    jobID?: string;
    country?: string;
    companyId?: string;
    industry?: string;
    currencyUnit?: string;
    currencyCode?: string;
    currencyId?: number;
  }

  const [financialsData, setFinancialsData] = useState<FinancialsData | null>(null);
  const [financialData, setStatusFinancialData] = useState<FinancialsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [columnIdMapping, setColumnIdMapping] = useState({});
  const [columnHeaderInfo, setColumnHeaderInfo] = useState({});
  const sortTableGroups = (tableGroups: any) => {
    if (!tableGroups) return [];
    
    const orderMap = {
      'income statement': 0,
      'balance sheet': 1,
      'cashflow': 2
    };
  
    return [...tableGroups].sort((a, b) => {
      const labelA = a.label?.toLowerCase() || '';
      const labelB = b.label?.toLowerCase() || '';
  
      // Get the order values, default to a high number for unknown labels
      const orderA = orderMap[labelA] ?? 999;
      const orderB = orderMap[labelB] ?? 999;
  
      return orderA - orderB;
    });
  };
  //#region  Removed Method Calling when the page loads synchronously
  // useEffect(() => {
  //   const fetchData = async () => {
  //     try {
  //       loadData();
  //   } catch (err) {
  //       setError(err);
  //     } finally {
  //       setIsLoading(false);
  //     }
  //   };

  //   fetchData();
  // }, [processId]);
//#endregion
  const loadData = async () => {
    setIsLoading(true);
    const data = await GET_FINANCIAL_DATA(processId);
    const ensureTableGroupExists = (label: string) => {
      let tableGroup = data?.tableGroups?.find((tg:any) => tg.label?.toLowerCase() === label.toLowerCase());
      if (!tableGroup) {
      tableGroup = {
        label,
        tables: []
      };
      }
      return tableGroup;
    };

    const incomeStatement = ensureTableGroupExists("income statement");
    const balanceSheet = ensureTableGroupExists("balance sheet");
    const cashflow = ensureTableGroupExists("cash flow");

    if (data.tableGroups.length > 0) {
      data.tableGroups[0] = incomeStatement;
      data.tableGroups[1] = balanceSheet;
      data.tableGroups[2] = cashflow;
    }
    data.tableGroups = sortTableGroups(data.tableGroups);
    setFinancialsData(data);
    setStatusFinancialData(data);
  }
  const isTemplate = financialsData?.templateId !== null;

  const selectedCellRef = useRef<SelectedCellInfo | null>(null);

  const handleBackButtonClick = () => {
    // Clean up any editing state first
    setEditingCell({
      rowId: null,
      field: null
    });

    // Reset the highlights and clear selection
    setHighlights([]);
    setSelectedCell(null);
    setShowPdfOverlay(false);
    resetRows();

    // Now call the parent's back function
    setLocalPdfUrl(null); // Now call the parent's back function

    handleBackfunc();
  };
  useEffect(() => {
    setSearchQuery("");
    setSelectedTab(0);
    // We'll set the default tab when financialsData loads
  }, []);

  useEffect(() => {
    setSearchQuery("");
    // We'll set the default tab when financialsData loads
  }, [handleBackfunc]);

  useEffect(() => {
    if (financialsData?.tableGroups && financialsData.tableGroups.length > 0) {
      // Find the first non-Note tab
      const firstMainTab = financialsData.tableGroups.find(
        (tg) => !tg.label.startsWith(NOTES_PREFIX)
      );
      if (firstMainTab && selectedTab === "") {
        setSelectedTab(firstMainTab.label);
      }
    }
  }, [financialsData]);

  const [activeCheckboxType, setActiveCheckboxType] = useState<
    "none" | "row" | "column" | "note"
  >("none");
  const [checkedColumns, setCheckedColumns] = useState<{
    [key: string]: boolean;
  }>({});
  const [fileName, setFileName] = useState("");

  const HighlightPopup = ({
    comment
  }: {
    comment: { text: string; emoji: string };
  }) =>
    comment.text ? (
      <div className="Highlight__popup">
        {comment.emoji} {comment.text}
      </div>
    ) : null;

  const addHighlight = (highlight: NewHighlight) => {
    setHighlightContent(highlight.content.text || "");
    setHighlightPosition(JSON.stringify(highlight.position, null, 2));
    setHighlights((prevHighlights) => [
      { ...highlight, id: "test" },
      ...prevHighlights
    ]);
  };

  useEffect(() => {
    if (pdfDocument && pdfHighlighted) {
      setTimeout(() => {
        handleSubmit(pdfHighlighted);
      }, 300);
    }
  }, [pdfDocument]);

  const handleSubmit = async (pdfHighlight) => {
    try {
      setPdfHighlighted(pdfHighlight);
      let pageWidth = PdfPageWidth; // Default fallback
      let pageHeight = PdfPageHeight; // Default fallback

      // Get actual page dimensions if available
      if (pdfDocument) {
        // Get the page based on the highlight's page number
        const pageNumber = pdfHighlight.pageNumber || 1;

        // Use a Promise to get page dimensions
        await pdfDocument
          .getPage(pageNumber)
          .then((page) => {
            const viewport = page.getViewport({ scale: 1.0 });
            pageWidth = viewport.width;
            pageHeight = viewport.height;
          })
          .catch((error) => {
            console.error("Error getting PDF page dimensions:", error);
          });
      }

      const boundingRect = {
        x1:
          pdfHighlight.bounds[0] < 1
            ? pdfHighlight.bounds[0] * pageWidth
            : pdfHighlight.bounds[0],
        y1:
          pdfHighlight.bounds[1] < 1
            ? pdfHighlight.bounds[1] * pageHeight
            : pdfHighlight.bounds[1],
        x2:
          pdfHighlight.bounds[2] < 1
            ? (pdfHighlight.bounds[0] + pdfHighlight.bounds[2]) * pageWidth
            : pdfHighlight.bounds[2],
        y2:
          pdfHighlight.bounds[3] < 1
            ? (pdfHighlight.bounds[1] + pdfHighlight.bounds[3]) * pageHeight
            : pdfHighlight.bounds[3],
        width: pageWidth, // Use dynamically retrieved width or fallback
        height: pageHeight, // Use dynamically retrieved height or fallback
        pageNumber: pdfHighlight.pageNumber || 1
      };

      // Create position with proper structure
      const position = {
        boundingRect,
        rects: [boundingRect],
        pageNumber: pdfHighlight.pageNumber || 1
      };

      // Create highlight object with unique ID
      const highlight = {
        id: uuidv4(), // Make sure highlight has unique ID
        content: {
          text: pdfHighlight.text
        },
        position,
        comment: {
          text: `${pdfHighlight.label}: ${pdfHighlight.text}`,
          emoji: "💬"
        }
      };

      // Scroll to the new highlight after a short delay to ensure rendering
      setTimeout(() => {
        if (pdfHighlighterRef.current?.scrollTo) {
          pdfHighlighterRef.current.scrollTo(highlight);
        }
      }, 50);

      setTimeout(() => {
        // Add highlight to state
        addHighlight(highlight);
      }, 100);
    } catch (error) {
      console.error("Failed to create highlight:", error);
    }
  };
  const determineRowLabelColor = (label:any) => {
    if (!label) return LEGEND_COLORS.UNMAPPED;
    
    if (label.mappingId !== undefined) {
      if (label.mappingId && label.mappingScore === 1) {
        return LEGEND_COLORS.MAPPED; // Has a mappingId with perfect score
      } else if (label.mappingScore && (label.mappingScore >= 0.7 && label.mappingScore <= 1) && label.mappingId) {
        return LEGEND_COLORS.MATCH_FOUND; // Has a mapping score between 0.7-1 with mappingId
      } else {
        return LEGEND_COLORS.UNMAPPED; // Has mappingId but score is < 0.7 or missing
      }
    } else if (label.text && !label.isEmptyRow) {
      return LEGEND_COLORS.NO_MATCH_FOUND; // Has text but no mapping information
    }
    
    return LEGEND_COLORS.UNMAPPED;
  }
  const processTableData = (sections:any,tabIndex:number=0) => {
     usedMappingIds.clear(); // Clear the set for each call
    console.log(`[${new Date().toISOString()}] processTableData called with tabIndex: ${tabIndex}, sections:`, sections);
    const gridData = [];
    if (!sections || !sections[0]?.rows) return gridData;

    const columns = sections[0]?.columns || [];
    // Get all possible column keys
    const allColumnKeys = columns.map((col) => col.columnKey);

    const columnKeyToPeriodDate = {};
    columns.forEach((col) => {
      // Combine the periodDate with FilingPeriods value separated by a pipe character
      const filingPeriodText = FilingPeriods[col.reportingPeriod] || "";
      columnKeyToPeriodDate[col.columnKey] = col.periodDate
        // ? `${col.periodDate}|Original|${filingPeriodText}`
        ? `${col.periodDate}`
        : col.periodDate; // Keep original if periodDate is missing
    });
    let currentModuleId = 0
    let tabData = FinancialStatementsWithName.find(x => x.value.toLowerCase() == financialsData?.tableGroups?.[tabIndex]?.label?.toLowerCase())
    if(tabData != undefined){
      currentModuleId = getModuleIdByTabNo(tabData.tabNo);
    }
    let mappingData = getMappingDataForModule(currentModuleId); 
    sections[0].rows.forEach((row) => {
      if (!row?.label) return;
      applyKpiMapping(row,mappingData);
      const color= determineRowLabelColor(row.label);  
      const rowData = {
        id: row.label.id,
        label: row.label.text,
        definedName: row.label.id,
        style: row.label.style || "lineitem",
        status: row.label.mapping || "",
        cellIds: {}, // Store cell IDs here
        pdfHighlights: {}, // Add new object to store PDF highlights
        periodDates: columnKeyToPeriodDate, // Add mapping for reference
        columnIds: {}, // Store column IDs here
        mappingId:row.label.mappingId,
        mapping:row.label.text,
        mappingScore:row.label.mappingScore,
        color:color,
      };

      // Generate IDs for all possible columns, regardless of value
      allColumnKeys.forEach((columnKey) => {
        const cell = row.cells?.find((c) => c.columnKey === columnKey);
        rowData[columnKey] = cell?.value ?? null; // Set null for missing values
        rowData.cellIds[columnKey] = uuidv4(); // Generate ID for every column cell
        
        // Store the column ID if we have one
        if (columnIdMapping[columnKey]) {
          rowData.columnIds[columnKey] = columnIdMapping[columnKey];
        }
        
        // Store PDF highlight data if it exists
        if (cell?.pdfHighlight) {
          rowData.pdfHighlights[columnKey] = {
            ...cell.pdfHighlight,
            fileKey: cell.source || financialsData?.files?.[0]?.url
          };
        }
      });

      gridData.push(rowData);
    });

    return gridData;  };  // A set to track already used KPI IDs across all calls to applyKpiMapping
  const usedMappingIds = new Set<number>();
  
  // Apply KPI mapping
  // First add the new method before the processTableData function:
  const applyKpiMapping = (row: any, mappingData: any[]) => {
    // Fetch KPI mapping data    
    if (row.label.mappingId && row.label.mappingScore < 0.7) {
      row.label.mapping = "";
      row.label.mappingId = 0;
    }
    
    // Check if we have KPI mapping data and the row needs mapping
    if (mappingData?.length > 0 && !row.label.mappingId && (row.label.mappingScore === null || row.label.mappingScore === undefined || row.label.mappingScore < 0.7)) {
      // Find matching KPI mapping by comparing names case-insensitively and ensuring it hasn't been used before
      const matchingKpi = mappingData.find(
        item => item.name.toLowerCase().trim() === row.label.text.toLowerCase().trim() && 
                !usedMappingIds.has(item.mappingId)
      );
      
      // Apply mapping if found
      if (matchingKpi) {
        row.label.mappingId = matchingKpi.mappingId;
        row.label.mapping = matchingKpi.name;
        row.label.mappingScore = 1;
        
        // Add this kpiId to the set of used IDs
        usedMappingIds.add(matchingKpi.mappingId);
      } else {
        row.label.mapping = "";
      }    
    } else if(row.label.mappingId && row.label.mappingId > 0){
      const matchingKpi = mappingData.find(item => item.mappingId === row.label.mappingId);
      if (matchingKpi && !usedMappingIds.has(row.label.mappingId)) {
        row.label.mapping = matchingKpi.name;
        usedMappingIds.add(matchingKpi.mappingId);
      } else {
        row.label.mapping = "";
      }
    }   
  };
  const handleMergeRowUp = () => {
    const selectedRowIds = Object.keys(checkedItems).filter(
      (id) => checkedItems[id]
    );
    mergeRowUp(
      selectedRowIds,
      checkedItems,
      currentTableData,
      setCurrentTableData,
      setTableDataByTab,
      selectedTab,
      setCheckedItems,
      setShowPopup,
      setActiveCheckboxType
    );
  };

  const handleMergeRowBelow = () => {
    const selectedRowIds = Object.keys(checkedItems).filter(
      (id) => checkedItems[id]
    );
    mergeRowBelow(
      selectedRowIds,
      checkedItems,
      currentTableData,
      setCurrentTableData,
      setTableDataByTab,
      selectedTab,
      setCheckedItems,
      setShowPopup,
      setActiveCheckboxType
    );
  };
   const assignDefaultColor=(newRow:any)=>{
    if (!newRow.color)
      newRow.color = LEGEND_COLORS.UNMAPPED;
   }; 
  /**
   * Adds a new row above the currently checked row (if exactly one row is checked).
   */
  const addRow = () => {

    const selectedRowIds = Object.keys(checkedItems).filter(
      (id) => checkedItems[id]
    );
    if (selectedRowIds.length !== 1) return; // Must select exactly one row

    const selectedRowId = selectedRowIds[0];
    const selectedIndex = currentTableData.findIndex(
      (row) => row.id === selectedRowId
    );
    if (selectedIndex === -1) return;

    const newRow = createNewRow();
    if (!newRow) return;
    
    assignDefaultColor(newRow);
    const newData = [...currentTableData];
    newData.splice(selectedIndex, 0, newRow); // Insert above the selected row

    // Update both current and stored data
    setCurrentTableData(newData);
    setTableDataByTab((prev) => ({
      ...prev,
      [selectedTab]: newData
    }));
    updateFinancialDataStorage(selectedIndex, newRow);
    setCheckedItems({});
    setShowPopup(false);
    setisCheckboxHeader(false);

    notify.success(RowsSuccessMessages.ROW_ADDED);
    setHasMappedChanges(prev => prev + 1);
    setHasChanges(true);
  };

  /**
   * Helper to create a new row, reusing any "value_" fields from the first row or a similar reference row.
   */
  const createNewRow = () => {
    if (currentTableData.length === 0) return null;
    const referenceRow =
      currentTableData.find((row) =>
        Object.keys(row).some((key) => key.startsWith("value_"))
      ) || currentTableData[0];

    // Extract all column keys that need to be preserved
    const allKeys = Object.keys(referenceRow).filter(
      (key) =>
        ![
          "id",
          "label",
          "definedName",
          "style",
          "status",
          "cellIds",
          "pdfHighlights"
        ].includes(key)
    );

    // Build the new row with empty values
    return {
      id: `new_${Date.now()}`,
      label: "",
      definedName: `new_${Date.now()}`,
      style: "lineitem",
      status: "",
      color: LEGEND_COLORS.UNMAPPED, 
      // Add empty values for all relevant columns
      ...Object.fromEntries(allKeys.map((key) => [key, ""])),

      // Generate unique cell IDs for all value columns and status
      cellIds: {
        status: uuidv4(),
        ...Object.fromEntries(
          allKeys
            .filter((key) => key.startsWith("value_"))
            .map((key) => [key, uuidv4()])
        )
      }
    };
  };
  /**
   * Adds a new row below the currently checked row (if exactly one row is checked).
   */
  const addRowToBottom = () => {
    const selectedRowIds = Object.keys(checkedItems).filter(
      (id) => checkedItems[id]
    );
    if (selectedRowIds.length !== 1) return;

    const selectedRowId = selectedRowIds[0];
    const selectedIndex = currentTableData.findIndex(
      (row) => row.id === selectedRowId
    );
    if (selectedIndex === -1) return;

    const newRow = createNewRow();
    if (!newRow) return;
    
    assignDefaultColor(newRow);
    const newData = [...currentTableData];
    newData.splice(selectedIndex + 1, 0, newRow); // Insert below the selected row

    // Update both current and stored data
    setCurrentTableData(newData);
    setTableDataByTab((prev) => ({
      ...prev,
      [selectedTab]: newData
    }));
    updateFinancialDataStorage(selectedIndex+1, newRow);
    setCheckedItems({});
    setShowPopup(false);
    setisCheckboxHeader(false);
    notify.success(RowsSuccessMessages.ROW_ADDED);
    setHasMappedChanges(prev => prev + 1);
    setHasChanges(true);
  };

  const deleteRow = () => {
    const selectedRowIds = Object.keys(checkedItems).filter(
      (key) => checkedItems[key]
    );
    if (selectedRowIds.length === 0) return;

    const newData = currentTableData.filter(
      (row) => !selectedRowIds.includes(row.id)
    );

    // Update both current and stored data
    setCurrentTableData(newData);
    setTableDataByTab((prev) => ({
      ...prev,
      [selectedTab]: newData
    }));
    deleteFinancialDataRows(selectedRowIds);
    // Update the checkedItems state to remove the deleted row IDs
    const newCheckedItems = { ...checkedItems };
    selectedRowIds.forEach((id) => {
      delete newCheckedItems[id];
    });

    // Update both current state and saved state
    setCheckedItems(newCheckedItems);

    setShowPopup(false);
    setisCheckboxHeader(false);
    const message =
      selectedRowIds.length === 1
        ? RowsSuccessMessages.SINGLE_ROW_DELETED
        : RowsSuccessMessages.MULTIPLE_ROWS_DELETED;    notify.info(message);
    setHasMappedChanges(prev => prev + 1);
    setHasChanges(true);
  };
// Helper function to update financial data after row addition
const updateFinancialDataStorage = (selectedIndex: number, newRow: any) => {
  if (financialData) {
    updateFinancialDataAfterRowAddition(
      financialData, 
      selectedTab, 
      selectedIndex,
      newRow,
      setStatusFinancialData
    );
  }
};

// Helper function to update financial data after row deletion
const deleteFinancialDataRows = (rowIds: string[]) => {
  if (financialData) {
    updateFinancialDataAfterRowDeletion(
      financialData,
      selectedTab,
      rowIds,
      setStatusFinancialData
    );
  }
};
  const handleCellClick = (e) => {
    if (e.field === "status") {
      setSelectedCell(null);
      return;
    }

    let cellId = e.dataItem.cellIds?.[e.field];
    if (!cellId) {
      // Generate a new cellId if it doesn't exist
      cellId = uuidv4();

      // Update the dataItem with the new cellId
      e.dataItem.cellIds = {
        ...e.dataItem.cellIds,
        [e.field]: cellId
      };
      // Update the currentTableData with the new cellId
      const updatedData = currentTableData.map((item) => {
        if (item.id === e.dataItem.id) {
          return {
            ...item,
            cellIds: {
              ...item.cellIds,
              [e.field]: cellId
            }
          };
        }
        return item;
      });

      setCurrentTableData(updatedData);
      setTableDataByTab((prev) => ({
        ...prev,
        [selectedTab]: updatedData
      }));
    }

    const isSameCell = selectedCell?.cellId === cellId;
    if (isSameCell) {
      setSelectedCell(null);
      return;
    }

    const newSelection = {
      cellId,
      rowId: e.dataItem.id,
      field: e.field,
      value: e.dataItem[e.field],
      tableIndex: e.tableIndex
    };
    setSelectedCell(newSelection);
    selectedCellRef.current = newSelection;

    // Get PDF highlight data from the stored data
    const pdfHighlight = e.dataItem.pdfHighlights?.[e.field];
    if (pdfHighlight && (selectedCellRef.current.value !== "")) {
      // Ensure we clear previous highlights before adding new one
      setHighlights([]);

      setShowPdfOverlay(true);

      // Get the specific PDF file for this highlight if one is specified
      const pdfFileKey =
        pdfHighlight?.fileKey || financialsData?.files?.[0]?.url;
      const pdfFileURL = getUrlFromFileKey(pdfFileKey);
      if (pdfFileKey && !localPdfUrl?.fileName.includes(pdfFileKey)) {
        // Only download if it's a different file than what we already have
        setIsFileNameLoading(true);
        const foundPdf = AllDownloadedPdf.find((pdf) =>
          pdf?.fileKey.includes(pdfFileKey)
        );
        setLocalPdfUrl(foundPdf || null);
        // setLocalPdfUrl(AllDownloadedPdf.find((pdf) => pdf.url === pdfFileURL)?.file || null);
        setFileName(foundPdf?.fileName);
        setIsFileNameLoading(false);
        handleSubmit({
          bounds: pdfHighlight?.bounds,
          pageNumber: pdfHighlight?.pageNumber || 1,
          text: String(e.dataItem[e.field]) || "N/A",
          label: e.dataItem?.label
        });
      } else {
        // If we already have the correct PDF loaded, just apply the highlight
        handleSubmit({
          bounds: pdfHighlight?.bounds,
          pageNumber: pdfHighlight?.pageNumber || 1,
          text: String(e.dataItem[e.field]) || "N/A",
          label: e.dataItem?.label
        });
      }
    }
  };
  const getUrlFromFileKey = (pdfFileKey: string) => {
    if (!pdfFileKey) return null;
    const pdfFile = financialsData?.files?.find((file) => file.fileName.includes(pdfFileKey));
    return pdfFile?.url;

  };
  const handleClosePdf = () => {
    setShowPdfOverlay(false);
    setSelectedCell(null);
  };

  const handleTabChange = (index: number,moduleName: string) => {
    let tabData = FinancialStatementsWithName.find(x => x.value.toLowerCase() == moduleName.toLowerCase())
    if(tabData != undefined){
      index = tabData.tabNo
    }
    // Set the new selected tab
    if (selectedTab === index) return; // Don't reset if clicking the same tab
    setSelectedTab(index);
    

    // Set current table data for the new tab
    const newTabData = tableDataByTab[index] || [];
    setCurrentTableData(newTabData);

    // Clear all checkboxes when switching tabs
    setShowPdfOverlay(true);
    // Close the popup when switching tabs
    setShowPopup(false);
    // Store current table data in the tab-specific storage before switching
    setTableDataByTab((prev) => ({
      ...prev,
      [selectedTab]: currentTableData // Save current data to the current tab's storage
    }));

    // Clear all checkboxes for the new tab too
    setCheckedItems({});

    // Reset selected cell and highlights when changing tabs
    setSelectedCell(null);
    clearAllCheckedItems();
    setHighlights([]);
    // Reset other states that shouldn't persist
    clearAllCheckedItems();
    setDropdownOptions([]);
    setSearchQuery("");
    setSelectedCell(null);
    setHighlights([]);

    if (dropdownOptionsByTab[index] && dropdownOptionsByTab[index].length > 0) {
      setDropdownOptions(dropdownOptionsByTab[index]);
    } else {
      setDropdownOptions([]);
    }

    // // If we have stored data for the new tab, use it instead of fetching new data
    if (tableDataByTab[index] && tableDataByTab[index].length > 0) {
      //   setCurrentTableData(tableDataByTab[index]);

      // Call handleCellClick for the first cell in the first row after a short delay to ensure the data is loaded
      setTimeout(() => {
        if (tableDataByTab[index].length > 0) {
          // Get the first row's data
          const firstRow = tableDataByTab[index][0];
          if (firstRow) {
            // Call handleCellClick with the necessary parameters
            const pdfHighlight = firstRow.pdfHighlights;
            handleSubmit({
              bounds: pdfHighlight?.bounds,
              pageNumber: pdfHighlight?.pageNumber || 1,
              text:
                Object.keys(pdfHighlight).find((key) => key.indexOf("|"))
                  .text || "N/A",
              label: pdfHighlight?.status
            });
          }
        }
      }, 100); 
    }
    setShowPdfOverlay(false);
    handleExportClick(index,newTabData);
  };

  // Update useEffect to cleanly handle data updates
  useEffect(() => {
    if (financialsData?.tableGroups) {
      // Use stored data for the selected tab
      setCurrentTableData(tableDataByTab[selectedTab] || []);
      setHasMappedChanges(prev => prev + 1);
    }
  }, [selectedTab, tableDataByTab, financialsData]);

  const getItemsWithMapping = (tabLabel) => {
    const currentGroup = financialsData?.tableGroups?.find(group => group.label === tabLabel);
    if (!currentGroup?.tables?.[0]?.rows) return [];
    return currentGroup.tables[0].rows.map((row) => ({
      label: row.label.text,
      mapping: row.label.mapping
    }));
  };

  const getDocumentLineItems = (tabLabel, fetchLabelOnly = false) => {
    const itemsWithMappings = getItemsWithMapping(tabLabel);
    const dropdownItems = (itemsWithMappings || []).map((item) =>
      fetchLabelOnly ? item.label : item.mapping || item.label
    );

    return [...new Set(dropdownItems)];
  };

  const performExportAction = () => {
    const dropdownItems = getDocumentLineItems(selectedTab);

    setDropdownOptions(dropdownItems);
    const itemsWithMappings = getItemsWithMapping(selectedTab);
    // Update each row's dropdown considering mapping
    const newData = currentTableData.map((item) => {
      const rowData = itemsWithMappings.find((row) => row.label === item.label);
      return {
        ...item,
        status: rowData?.mapping || item.label // Use mapping if exists, otherwise use label
      };
    });

    setCurrentTableData(newData);
  };

  useEffect(() => {
    const handleClickOutside = (event) => {
      const pdfOverlay = document.querySelector(".pdf-overlay");
      const isPdfClick = pdfOverlay?.contains(event.target);

      if (
        tableRef.current &&
        !tableRef.current.contains(event.target) &&
        !isPdfClick
      ) {
        setSelectedCell(null);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [tableRef]);

  useEffect(() => {
    if (financialsData?.tableGroups?.[selectedTab]?.tables?.[0]?.rows) {
      const rows = financialsData.tableGroups[selectedTab].tables[0].rows;

      // Find first cell with type or format
      for (const row of rows) {
        const firstCell = row.cells?.find((cell) => cell.type || cell.format);
        if (firstCell) {
          const capitalizedType = firstCell.type
            ? `${firstCell.type.charAt(0).toUpperCase()}${firstCell.type.slice(
                1
              )}`
            : "";
          const capitalizedFormat = firstCell.format
            ? `${firstCell.format
                .charAt(0)
                .toUpperCase()}${firstCell.format.slice(1)}`
            : "";

          setCurrency(`${capitalizedFormat} ${capitalizedType}`.trim());
          break;
        }
      }
    }
  }, [financialsData, selectedTab]);

  const updateCellValue = (
    rowId: string,
    cellId: string,
    field: string,
    newValue: string
  ) => {
    if (selectedTab === 3) {
      // Create a deep copy of notesTables
      const updatedNotesTables = notesTables.map((tableGroup:any) => ({
        ...tableGroup,
        tables: tableGroup.tables.map((table:any) => ({
          ...table,
          rows: table.rows.map((row:any) => {
            if (row.label?.id === rowId) {
              return {
                ...row,
                cells: row.cells.map((cell:any) =>
                  cell.columnKey === field ? { ...cell, value: newValue } : cell
                )
              };
            }
            return row;
          })
        }))
      }));

      // Update both state variables
      setNotesTables(updatedNotesTables);
      //setNotesTables([]);

      // Update tableDataByTab with the updated notesTables
      setTableDataByTab((prev:any) => ({
        ...prev,
        [3]: updatedNotesTables
      }));
    } else {
      // Existing logic for main tabs
      const updatedData = currentTableData.map((row) => {
        if (row.cellIds?.[field] === cellId) {
          return {
            ...row,
            [field]: newValue,
            lastModified: new Date().toISOString()
          };
        }
        return row;
      });
      // Existing logic for main tabs
      setCurrentTableData(updatedData);
      setTableDataByTab((prev) => ({
        ...prev,
        [selectedTab]: updatedData
      }));
    }
  };

  const updateGridDataWithHighlight = (content:any,position:any) => {
   if (!selectedCellRef.current || !content?.text) return;

    setHighlights([]);

    const { rowId, cellId, field } = selectedCellRef.current;
    if (!cellId) return;

    // First update the cell value
    updateCellValue(rowId, cellId, field, content.text);

    // Convert position coordinates to percentage values
    const convertedPosition = convertPositionToPercentage(position);

    if (selectedTab === 3) {
      // Special handling for Notes tab
      const updatedNotesTables = notesTables.map((tableGroup:any) => ({
        ...tableGroup,
        tables: tableGroup.tables.map((table:any) => ({
          ...table,
          rows: table.rows.map((row:any) => {
            if (row.label?.id === rowId) {
              return {
                ...row,
                cells: row.cells.map((cell:any) => {
                  if (cell.columnKey === field) {
                    return {
                      ...cell,
                      value: content.text,
                      pdfHighlight: {
                        text: content.text,
                        bounds: convertedPosition.bounds,
                        pageNumber: convertedPosition.pageNumber ?? 1,
                        fileKey:
                          localPdfUrl?.fileKey ??
                          financialsData?.files?.[0]?.fileName
                      }
                    };
                  }
                  return cell;
                })
              };
            }
            return row;
          })
        }))
      }));

      setNotesTables(updatedNotesTables);

      // Also update tableDataByTab for notes
      setTableDataByTab((prev:any) => ({
        ...prev,
        [3]: updatedNotesTables
      }));
    } else {
      // Existing logic for main tabs
      const updatedData = currentTableData.map((row) => {
        if (row.cellIds?.[field] === cellId) {
          return {
            ...row,
            [field]: content.text,
            lastModified: new Date().toISOString(),
            pdfHighlights: {
              ...row.pdfHighlights,
              [field]: {
                ...(row.pdfHighlights?.[field] || {}),
                text: content.text,
                bounds: convertedPosition.bounds,
                pageNumber: convertedPosition.pageNumber || 1,
                fileKey: localPdfUrl?.fileKey ?? financialsData?.files?.[0]?.url
              }
            }
          };
        }
        return row;
      });

      setCurrentTableData(updatedData);
      setTableDataByTab((prev) => ({
        ...prev,
        [selectedTab]: updatedData
      }));
    }

    // Update selected cell state to reflect new value
    setSelectedCell((current:any) => {
      if (current?.cellId === cellId) {
        return {
          ...current,
          value: content.text
        };
      }
      return current;
    });
    setHasChanges(true);
  };

  const formatWithCommas = (value) => {
    if (value === null || value === undefined || value === "") return value;
    return value.toString().replace(CommaFormatterRegex, ",");
  };

    // Add new state for KPI mapping data
    const [kpiMappingData, setKpiMappingData] = useState<{
      kpiId: number;
      name: string;
      parentId: number | null;
      kpiInfo: string;
      displayOrder: number;
      mappingId: number;
      isHeader: boolean;
      isBoldKPI: boolean;
      methodologyID: number;
    }[]>([]);  // Change to array type and initialize with empty array

const getModuleIdByTabNo = (index: number) => {
  let currentModuleId;
  switch(index) {
    case 0: // Income Statement
      currentModuleId = KpiModule.ProfitAndLoss;
      break;
    case 1: // Balance Sheet
      currentModuleId = KpiModule.BalanceSheet;
      break;
    case 2: // Cash Flow
      currentModuleId = KpiModule.CashFlow;
      break;
    default:
      currentModuleId = KpiModule.ProfitAndLoss;
  }
  return currentModuleId;
}

// Helper function to get mapping data for the current module
const getMappingDataForModule = (currentModuleId: number): KpiMappingData[] => {
  switch(currentModuleId) {
    case KpiModule.ProfitAndLoss:
      return plData;
    case KpiModule.BalanceSheet:
      return bsData;
    case KpiModule.CashFlow:
      return cfData;
    default:
      return [];
  }
};

  const handleExportClick = async (index: number, newTabData:TableRow[]) => {
    try {
      let currentModuleId = getModuleIdByTabNo(index);
      // Fetch KPI mapping data
      let mappingData = getMappingDataForModule(currentModuleId);
      if(mappingData == undefined || mappingData?.length == 0){
        let result  = await GET_KPI_MAPPING_SERVICE(acuityid);
        mappingData = result?.find(x => x.moduleId == currentModuleId)?.kpis || [];
        if (!mappingData || mappingData === undefined || mappingData.length === 0) {
          setKpiMappingData([]); // Set to empty array to maintain consistent state
          setDropdownOptions([]); // Also clear dropdown options
          return; // Exit early if no mapping data
        }
        // Update state with mapping data
        setKpiMappingData(mappingData);
      } 

      // Only proceed with export action if we have mapping data
      performExportAction();
      
      const currentEdited = newTabData.map(item => ({
        label: item.label,
        mapping: item.status || "",
        style: item.style
      }));

      // Create dropdown options with mappings
      const dropdownItems = mappingData.map((item) => {
        return {
          parent_text: Standard_KPI,
          stext: item.name,
          mapping_id: item.mappingId,
          kpiId: item.kpiId,
          mappingId: item.mappingId,
          kpiInfo: item.kpiInfo,
          isHeader : item.isHeader,
          isBold : item.isBoldKPI,
          parentId: item.parentId,
          mapping: item.name,
          methodologyID : item.methodologyID
        };
      });

      setDropdownOptions([...new Set(dropdownItems)]);
      setDropdownOptionsByTab((prev) => ({
        ...prev,
        [selectedTab]: [...new Set(dropdownItems)]
      }));      // Set to track already used KPI IDs for this export operation
      const usedMappingIds = new Set<number>();
      
      // Update the data with mappings - process rows that already have mappings first
      // This preserves existing mappings before assigning new ones
      const newData = newTabData.map(item => {
        // First pass - only keep existing mappings to preserve them
        if (item.mappingId && item.kpiId) {
          // Mark this KPI as used
          usedMappingIds.add(item.mappingId);
          return item;
        }
        return item;
      }).map(item => {
        // Second pass - process all rows
        if (item.style === CELL_STYLES.HEADER) {
          return {
            ...item,
            status: ""
          };
        }
        
        // Skip items that already have mappings (processed in first pass)
        if(item.mappingId != undefined && item.mappingId != null && item.mappingId != 0 && !usedMappingIds.has(item.mappingId)){
          usedMappingIds.add(item.mappingId);
          return item;
        }
        
        // Find a matching KPI that hasn't been used yet
        const rowData = mappingData.find(row => 
          row.name.toLowerCase() === item.label.toLowerCase() && 
          !usedMappingIds.has(row.mappingId)
        );
        
        if(rowData != undefined){
          // Mark this KPI as used
          usedMappingIds.add(rowData.mappingId);
          
          return {
            ...item,
            status: rowData.name,
            mapping_id: rowData.mappingId,
            kpiId: rowData.kpiId,
            mappingId: rowData.mappingId,
            kpiInfo: rowData.kpiInfo,
            isHeader : rowData.isHeader,
            isBold : rowData.isBoldKPI,
            parentId: rowData.parentId,
            mapping: rowData.name,
            methodologyID : rowData.methodologyID
          };
        } else {
          return {
            ...item,
            mappingId: undefined,
            mapping:"",
            status: "",
          };
        }
      });
      if (index === 0) {
        setStandardLineItemsForIncomeStatement(prepareMappedData_feedback(newData));
      } else if (index === 1) {
        setStandardLineItemsForBalanceSheet(prepareMappedData_feedback(newData));
      } else if (index === 2) {
        setStandardLineItemsForCashFlow(prepareMappedData_feedback(newData));
      }

      setTableDataByTab((prev) => ({
        ...prev,
        [index]: newData
      }));

      setCurrentTableData(newData);
    } catch (error) {
      console.error('Error in handleExportClick:', error);
      setKpiMappingData([]); // Reset to empty array on error
    }
  };

  const getLegendColor = (status) => {
    return status ? LEGEND_COLORS.MAPPED : LEGEND_COLORS.UNMAPPED;
  };

  const handleCheckboxClick = (id, isChecked) => {
    setHasChanges(true);
    // Save the current scroll position
    const scrollPosition = document.querySelector(".k-grid-content")?.scrollTop;

    // If column checkboxes are active, prevent row checkbox selection
    if (isChecked && activeCheckboxType === "column") {
      return;
    }

    // Update checked items using the functional update pattern
    setCheckedItems((prevState) => {
      // Create a new state object to avoid mutating the previous state
      const newCheckedItems = { ...prevState };

      if (isChecked) {
        // Add this item to selections and set active type to 'row'
        newCheckedItems[id] = true;
        setActiveCheckboxType("row");
      } else {
        // Remove this item from selections
        delete newCheckedItems[id];

        // If no items are checked, reset the active type
        if (Object.values(newCheckedItems).filter(Boolean).length === 0) {
          setActiveCheckboxType("none");
        }
      }

      // Show popup if at least one checkbox is selected
      const hasCheckedItems = Object.values(newCheckedItems).some(Boolean);
      setShowPopup(hasCheckedItems);

      return newCheckedItems;
    });

    // Restore scroll position
    setTimeout(() => {
      if (scrollPosition !== undefined) {
        const gridContent = document.querySelector(".k-grid-content");
        if (gridContent) gridContent.scrollTop = scrollPosition;
      }
    }, 0);
  };

  // Add handler for column checkboxes
  const handleColumnCheckboxClick = (columnUUID, isChecked) => {
    // If row checkboxes are active, prevent column checkbox selection
    if (isChecked && activeCheckboxType === "row") {
      return;
    }

    setCheckedColumns((prevState) => {
      const newCheckedColumns = { ...prevState };

      if (isChecked) {
        // Add this column to selections and set active type to 'column'
        newCheckedColumns[columnUUID] = true;
        setActiveCheckboxType("column");
      } else {
        // Remove this column from selections
        delete newCheckedColumns[columnUUID];

        // If no columns are checked, reset the active type
        if (Object.values(newCheckedColumns).filter(Boolean).length === 0) {
          setActiveCheckboxType("none");
        }
      }

      return newCheckedColumns;
    });
  };

  const clearAllCheckedItems = () => {
    setCheckedItems({});
    setCheckedColumns({});
    setCheckedColumnHeaders({});
    setEditingColumnIds([]);
    setShowPopup(false);
    setisCheckboxHeader(false);
    setActiveCheckboxType("none");
    setShowPopup(false);
  };
  const handleResetPublish = () => {
    clearAllCheckedItems();
    setSearchQuery("");
  };
  const [selectedMappings, setSelectedMappings] = useState<{[key: string]: string}>({});

  // Initialize selectedMappings when currentTableData changes
  useEffect(() => {
    const initialMappings = {};
    currentTableData.forEach(item => {
      if (item.status) {
        initialMappings[item.id] = item.status;
      }
    });
    setSelectedMappings(initialMappings);
  }, [currentTableData]);

const handleStatementChange = (e, props, selectedTab) => {
    // Save the current scroll position before making any changes
    const gridElement = document.querySelector(".k-grid-content");
    const scrollPosition = gridElement?.scrollTop || 0;
    // Set hasChanges to true whenever a statement changes
    setHasChanges(true);
    // Increment hasMappedChanges to ensure it creates a new reference each time
    setHasMappedChanges(prev => prev + 1);
    const newData = currentTableData.map((item) => {
      if (item.id === props.dataItem.id) {
        // Remove previous mapping if it exists
        if (selectedMappings[props.dataItem.id]) {
          const newSelectedMappings = {...selectedMappings};
          delete newSelectedMappings[props.dataItem.id];
          setSelectedMappings(newSelectedMappings);
        }
        item.color=LEGEND_COLORS.MAPPED;
        item.mappingScore= 1;
        // Add new mapping
        setSelectedMappings(prev => ({
          ...prev,
          [props.dataItem.id]: e.value.stext
        }));
        return { ...item, status: e.value.stext, mappingId: e.value.mappingId || e.value.mapping_id, mapping_id: e.value.mapping_id || e.value.mappingId,
          kpiId: e.value.kpiId, kpiInfo: e.value.kpiInfo, isHeader: e.value.isHeader , isBold: e.value.isBold, parentId: e.value.parentId, mapping: e.value.stext, methodologyID : e.value.methodologyID
         };
      }
      return item;
    });
    // Update the current table data
    setCurrentTableData(newData);
    // Also update the stored data for the current tab
    setTableDataByTab((prev) => ({
      ...prev,
      [selectedTab]: newData
    }));
    const updatedMappedData = prepareMappedData_feedback(newData);
    // Update all relevant mapped data states
    if (selectedTab === 0) {
      setStandardLineItemsForIncomeStatement(updatedMappedData);
    } else if (selectedTab === 1) {
      setStandardLineItemsForBalanceSheet(updatedMappedData);
    } else if (selectedTab === 2) {
      setStandardLineItemsForCashFlow(updatedMappedData);
    }
    // Update the source data in financialsData if available
    if (financialsData.tableGroups[selectedTab]) {
      financialsData.tableGroups[selectedTab].tables[0].rows =
        financialsData.tableGroups[selectedTab].tables[0].rows.map((item) => {
          if (item.label.id === props.dataItem.id) {
            item.label.mapping = e.value.stext;
            item.label.mappingId = e.value.mappingId || e.value.mapping_id;
            item.label.mappingScore= 1;// Set mapping score to 1 when a mapping is applied
          }
          return item;
        });    }
        updateCircularStatusButtonDataMapping(financialData,props.dataItem,e.value,selectedTab);
      // Use requestAnimationFrame for smoother scroll restoration
    // This ensures the scroll position is preserved after React updates the DOM
    requestAnimationFrame(() => {
      if (gridElement) {
        gridElement.scrollTop = scrollPosition;
        
        // Add a second attempt with another RAF
        // This ensures more reliable restoration in case the first attempt happens before DOM updates
        requestAnimationFrame(() => {
          if (gridElement) {
            gridElement.scrollTop = scrollPosition;
          }
        });
      }
    });
  };

  const generateColumnId = (): string => {
    // Generate a completely unique ID using UUID v4
    return uuidv4();
  };

   // Modify the dropdown onChange handler inside generateColumns function
   const generateColumns = (currentGroup, selectedTab, isNotesTable = false) => {
    if (!currentGroup) return [];
    
    // Add this near your other state declarations
    const currentTable = currentGroup?.tables?.[0];
    const tableColumns = currentTable?.columns || [];

    const dropdownColumn = {
      field: "status",
      title: "Standard Line Item",
      width: COLUMN_WIDTHS.STANDARD_LINE_ITEM,
      locked: true, // Ensure this is explicitly set to true
      headerCell: (props) => (
        <th
          {...props.tdProps}
          className="!p-0 absolute left-[1px] top-0 filter  shadow-[0px_0px_8px_0px_rgba(0,0,0,0.12)]flex-none order-16 z-16"
        >
          <div className="h-[52px] body-r bg-[#fafafa] flex items-center justify-center px-4">
            <div className="text-neutral-60 text-body-m font-body-m flex-1">
              Standard KPI
            </div>
          </div>
        </th>
      ),
      className: "body-r text-neutral-80 bg-[#e6e6e6]",
      headerClassName: "body-m text-neutral-60 sticky left-0 z-20 ",
      cell: (props) => {
        const dropdownId = props.dataItem.cellIds?.status || props.dataItem.id;
        // Get the appropriate chart of accounts based on the selected tab
        let chartOfAccounts = [];
        if (isTemplate) {
          switch (selectedTab) {
            case 0:
              chartOfAccounts = ISChartofAccounts;
              break;
            case 1:
              chartOfAccounts = BSChartofAccounts;
              break;
            case 2:
              chartOfAccounts = CFChartofAccounts;
              break;
            default:
              chartOfAccounts = dropdownOptions;
          }
        }

        // Create dropdown data based on template mode and selected tab
        let dropdownData = isTemplate
          ? [...new Set(chartOfAccounts)]
          : [...new Set(dropdownOptions)];

        // If there's a mapping and we're in template mode, add it to the beginning if not already present
        if (isTemplate && props.dataItem.status) {
          if (!chartOfAccounts.includes(props.dataItem.status)) {
            dropdownData = [...new Set(chartOfAccounts)].sort((a, b) => {
              // Sort by parent_text alphabetically
              const parentA = a.parent_text || "Uncategorized";
              const parentB = b.parent_text || "Uncategorized";
              return parentA.localeCompare(parentB);
            });
          }
        }

        // Check if checkbox should be disabled based on active checkbox type
        const isCheckboxDisabled = isMapped || activeCheckboxType === "column";

        return (
          <td
            className={`${
              isMapped || isCheckboxDisabled
              ? "cursor-not-allowed text-neutral-60 bg-zinc-50"
              : "cursor-pointer"
              } !p-0 absolute left-[1px] top-0 filter  flex-none order-16 z-16 bg-[#e6e6e6] sticky left-0 z-10`}
            title={props.dataItem.status || ""}
            style={{
              position: "sticky",
              left: 0,

              zIndex: 20
            }}
          >
            <div className="relative flex items-center gap-2 px-3 py-2">
              <div
                className="absolute left-1 w-1 h-4 rounded-3xl z-10"
                style={{
                  backgroundColor: props.dataItem.color
                }}
              />
              <div className="ml-2">
                <Checkbox
                  disabled={isCheckboxDisabled}
                  className={
                    isCheckboxDisabled
                      ? "cursor-not-allowed text-neutral-60 bg-zinc-50"
                      : "cursor-pointer"
                  }
                  checked={checkedItems[props.dataItem.id] || false}
                  onChange={(e) =>
                    handleCheckboxClick(props.dataItem.id, e.value)
                  }
                />
              </div>
              <div
                className={`${
                  isMapped
                    ? "cursor-not-allowed text-neutral-60 bg-zinc-50"
                    : "cursor-pointer"
                } flex-1 relative text-body-r font-body-r text-neutral-80 truncate w-full`}
              >
                <span className="statement-dropdown">
                  <StatementDropdown
                    isMapped={isMapped}
                    comboProps={props}
                    highlightText={highlightText}
                    dropdownData={dropdownData}
                    handleStatementChange={handleStatementChange}
                    searchQuery={searchQuery}
                    selectedTab={selectedTab}
                  />
                </span>
              </div>
            </div>
          </td>
        );
      }
    };

    const labelColumn = {
      field: "label",
      title: isNotesTable ? "Notes Line Item" : "Document KPI",
      width: COLUMN_WIDTHS.DOCUMENT_LINE_ITEM,
      locked: isNotesTable, // Ensure this is explicitly set to true
      headerCell: (props) => (
        <th {...props.tdProps} className="!p-0">
          <div className="h-[52px] body-r bg-[#fafafa] flex items-center justify-center px-3 gap-2">
            {!isNotesTable && !isTemplate && (
              <>
                <Tooltip id="pushdocumenttooltip" place="left" />
              </>
            )}
            <div
              className={`${isNotesTable
                ? "text-neutral-60 header flex-1"
                : "text-neutral-60 text-body-r font-body-r flex-1"
                }`}
            >
              {isNotesTable ? "Notes Line Item" : "Document KPI"}
            </div>
          </div>
        </th>
      ),
      className:
        "text-body-r font-body-r text-neutral-80 cursor-pointer bg-white",
      headerClassName: "body-m text-neutral-60 sticky",
      cell: (props) => {
        const isThisCellEditing =
          editingCell.rowId === props.dataItem.id &&
          editingCell.field === props.field;
        const [inputValue, setInputValue] = useState(props.dataItem.label);

        // Get row style to determine if this is a header row
        const isHeaderRow = props.dataItem.style === "header";

        const isNotesSelected = isMapped || (isNotesTable ? activeCheckboxType === "note" : false);
        const handleClick = (e) => {
          if (e && e.target && e.target.classList && e.target.classList.contains('k-checkbox-input')) {
            // If clicking on checkbox, don't start cell editing
            return;
          }

          setOriginalCellValue(props.dataItem.label);
          setEditingCell({
            rowId: props.dataItem.id,
            field: props.field
          });
        };

        const handleChange = (e) => {
          const newValue = e.target.value;
          setInputValue(newValue);

          // Directly update the data as you type
          const updatedData = currentTableData.map((item) => {
            if (item.id === props.dataItem.id) {
              return { ...item, label: newValue };
            }
            return item;
          });
          setCurrentTableData(updatedData);
          setTableDataByTab((prev) => ({
            ...prev,
            [selectedTab]: updatedData
          }));
        };
        const handleKeyDown = (e) => {
          if (e.key === DefaultValues.EscapeKey) {
            setInputValue(originalCellValue);
            const updatedData = currentTableData.map((item) => {
              if (item.id === props.dataItem.id) {
                return { ...item, label: originalCellValue };
              }
              return item;
            });
            setCurrentTableData(updatedData);
            setTableDataByTab((prev) => ({
              ...prev,
              [selectedTab]: updatedData
            }));
            setEditingCell({ rowId: null, field: null });
          } else if (e.key === "Enter") {
            // Finish editing on Enter
            setEditingCell({ rowId: null, field: null });
          }
        };
        return (
          <td
            className={`${isHeaderRow
              ? "border-r-0 font-body-m text-body-b font-bold"
              : "font-body-r text-body-r"
              }
              ${isNotesSelected
                ? "cursor-not-allowed text-neutral-60 bg-zinc-50"
                : "cursor-pointer"
              }
              ${isMapped
                ? "cursor-not-allowed text-neutral-60 bg-zinc-50"
                : "cursor-pointer"
              }
              ${isNotesSelected
                ? "hover:bg-zinc-50"
                : "hover:bg-neutral-10 "
              }
              hover:bg-neutral-10 
              sticky left-0 z-10 
              ${isThisCellEditing ? "editing-cell" : ""} 
              overflow-x-hidden`}
            onClick={(e) => !isThisCellEditing && !isMapped && !isNotesSelected && handleClick(e)}
            style={{
              border: !isNotesSelected && isThisCellEditing ? "3px solid #EDDA70" : "none",
              position: "sticky",
              left: 0,
              cursor: isNotesSelected ? "auto" : "pointer",
              whiteSpace: "nowrap",
              textOverflow: "ellipsis",
              height: "30px",
              maxWidth: "250px", // Match the width specified in column definition
            }}
            data-row-id={props.dataItem.id}
            data-field={props.field}
            title={inputValue}
          >
            {!isNotesSelected && isThisCellEditing ? (
              <input
                type="text"
                value={inputValue}
                onChange={handleChange}
                onKeyDown={handleKeyDown}
                disabled={isMapped}
                className={`${isHeaderRow ? "font-bold" : "font-normal"} ${isMapped
                  ? "cursor-not-allowed text-neutral-60"
                  : "cursor-pointer"
                  } w-full h-full border-none focus:outline-none focus:ring-0 p-0 m-0 text-body-r`}
                style={{
                  boxShadow: "none",
                  background: "transparent"
                }}
                autoFocus
                data-row-id={props.dataItem.id}
                data-field={props.field}
              />
            ) : searchQuery ? (
              <span
                className={`${isHeaderRow
                  ? "text-body-m font-body-m"
                  : "text-body-r font-body-r"
                  } ${isMapped
                    ? "cursor-not-allowed text-neutral-60"
                    : "cursor-pointer"
                  }`}
              >
                {highlightText(inputValue, searchQuery)}{" "}
              </span>
            ) : (
              <span
                className={`${isHeaderRow
                  ? "text-body-m font-body-m"
                  : "text-body-r font-body-r"
                  } ${isMapped
                    ? "cursor-not-allowed text-neutral-60"
                    : "cursor-pointer"
                  }`}
              >
                {inputValue}
              </span>
            )}
          </td>
        );
      }
    };

    const periodColumns = tableColumns.map((column) => {
      // Check if we already have a UUID for this column key
      let columnUUID = columnIdMapping[column.columnKey];
      
      // If not, generate a new one and store it
      if (!columnUUID) {
        columnUUID = generateColumnId();
        setColumnIdMapping((prevMapping) => ({
          ...prevMapping,
          [column.columnKey]: columnUUID
        }));
      }

      // Get updated column information if available
      const updatedInfo = columnHeaderInfo[columnUUID];

      return {
        field: column.columnKey,
        locked: false, // Explicitly set other columns to false
        columnId: columnUUID, // Store the UUID with the column
        headerCell: (props) => {          // Check if checkbox should be disabled based on active checkbox type
          const isColumnCheckboxDisabled =
            isMapped || activeCheckboxType === "row" || activeCheckboxType === "note";

          // Use updated column information if available, otherwise use original values
          // const displayDate = updatedInfo?.filingDate
          //   ? formatDateWithHyphens(updatedInfo.filingDate)
          //   : column.periodDate;
            
          // const displayDocStatus = updatedInfo?.documentStatus || "Original";
          
          // const displayFilingType = updatedInfo?.filingType 
          //   ? updatedInfo.filingType 
          //   : FilingPeriods[column.reportingPeriod];
        const displayDate = updatedInfo?.periodValue ? updatedInfo?.periodValue : column.periodDate; 
        const displayDocStatus =  "";
        const displayFilingType = "";
        const [isColumnHovered, setIsColumnHovered] = useState(false);

          const isNewNoteColumn = column?.isNewNoteColumn;
          return (
            <th onMouseEnter={() => setIsColumnHovered(true)}
            onMouseLeave={() => setIsColumnHovered(false)}
              {...props.tdProps}
              className="!p-0 absolute left-[1px] top-0 filter  flex-none order-16 z-16 w-[200px]"
            >              
            <div className="flex flex-row w-full h-[52px] bg-neutral-2">              
            <div className="flex items-center justify-center px-3">
                 {/* Only display flag when period value is undefined or null */}
                 { (validatePeriod(displayDate)) && <div className="flex items-center mr-2">
                    <div className="w-8 h-8 bg-noticeable-50  rounded-full flex items-center justify-center">
                      <FaRegFlag className="w-6 text-negative-80"/> 
                    </div>
                  </div>}                
                  {(isColumnHovered || checkedColumnHeaders[columnUUID]) && ( 
                    <Checkbox
                    id={columnUUID} // Use the consistent UUID for the checkbox
                    disabled={isColumnCheckboxDisabled}
                    className={
                      isColumnCheckboxDisabled
                        ? "cursor-not-allowed text-neutral-60 bg-zinc-50"
                        : "cursor-pointer"
                    }
                    checked={checkedColumnHeaders[columnUUID] || false}
                    onChange={(e) => handleCheckboxColumn(e.value, columnUUID, isNewNoteColumn)}
                  />)}
                </div>
                {isNewNoteColumn ? (
                  <div className="flex flex-col items-end text-caption-m font-caption-m justify-center w-[70%] py-3 px-2 text-neutral-30">
                    Add Details
                  </div>
                ) :
                  (                  
                  <div title={updatedInfo?.periodValue} className="flex flex-col items-center justify-center w-[90%] pr-3">
                    {updatedInfo?.periodValue !== undefined ? (
                      <div title={updatedInfo?.periodValue} className="text-caption-m font-caption-m text-neutral-90 w-full text-right overflow-hidden">
                        <div className="inline-block max-w-full whitespace-nowrap overflow-hidden text-ellipsis">
                          {updatedInfo.periodValue}
                        </div>
                      </div>
                    ) : (
                      <>
                        <div className="text-caption-m font-caption-m text-neutral-90 w-full text-right truncate">
                          {displayDate}
                        </div>
                        <div className="flex flex-row items-center justify-end gap-2 w-full">
                          <span className="px-1 py-0 bg-primary-43 rounded text-caption-r font-caption-r text-primary-90 truncate">
                            {displayDocStatus}
                          </span>
                          <span className="px-1 py-0 bg-primary-43 rounded text-caption-r font-caption-r text-primary-90 truncate">
                            {displayFilingType}
                          </span>
                        </div>
                      </>
                    )}
                    </div>
                  )
                }
              </div>
            </th>
          );
        },
        width: "200px",
        className: "body-r text-neutral-80 text-right cursor-pointer",
        headerClassName: "!p-0",
        cell: (props) => {
          // Check if this specific cell is being edited
          const isThisCellEditing =
            editingCell.rowId === props.dataItem.id &&
            editingCell.field === props.field;

          const [inputValue, setInputValue] = useState(
            props.dataItem[props.field] !== null &&
              props.dataItem[props.field] !== undefined
              ? String(props.dataItem[props.field])
              : ""
          );

          const handleClick = () => {
            setOriginalCellValue(props.dataItem[props.field]);
            setEditingCell({
              rowId: props.dataItem.id,
              field: props.field
            });
          };

          const handleChange = (e) => {
            setHasChanges(true);
            let newValue = e.target.value;

            // If the value is a whole number (no decimal point) and not empty
            if (
              newValue !== "" &&
              !newValue.includes(".") &&
              !isNaN(Number(newValue))
            ) {
              // Format with trailing decimal point and zero when editing is complete
              if (e.type === "blur" || e.key === "Enter") {
                newValue = `${newValue}.0`;
              }
            }
            setInputValue(newValue);
            // Update the data with the new value, keeping it as a string during editing
            // to preserve decimal places and formatting
            const updatedData = currentTableData.map((item) => {
              if (item.id === props.dataItem.id) {
                // Store the value as is (string) to preserve decimals during editing
                return { ...item, [props.field]: newValue };
              }
              return item;
            });

            setTableDataByTab((prev) => ({
              ...prev,
              [selectedTab]: updatedData
            }));
            setCurrentTableData(updatedData);
          };
          const handleKeyDown = (e) => {
            setHasChanges(true);
            if (e.key === "Enter") {
              // If value is a whole number, add .0
              if (
                inputValue !== "" &&
                !inputValue.includes(".") &&
                !isNaN(Number(inputValue))
              ) {
                const formattedValue = `${inputValue}.0`;
                setInputValue(formattedValue);

                // Update the data with formatted value
                const updatedData = currentTableData.map((item) => {
                  if (item.id === props.dataItem.id) {
                    return { ...item, [props.field]: formattedValue };
                  }
                  return item;
                });

                setTableDataByTab((prev) => ({
                  ...prev,
                  [selectedTab]: updatedData
                }));
                setCurrentTableData(updatedData);
              }

              // Finish editing when Enter key is pressed
              setEditingCell({ rowId: null, field: null });
            } else if (e.key === DefaultValues.EscapeKey) {
              const originalValue =
                originalCellValue !== null && originalCellValue !== undefined
                  ? String(originalCellValue)
                  : "";
              setInputValue(originalValue);
              const updatedData = currentTableData.map((item) => {
                if (item.id === props.dataItem.id) {
                  return { ...item, [props.field]: originalCellValue };
                }
                return item;
              });
              setCurrentTableData(updatedData);
              setTableDataByTab((prev) => ({
                ...prev,
                [selectedTab]: updatedData
              }));
              setEditingCell({ rowId: null, field: null });
            }
          };
          // Modified condition to distinguish between null/undefined and empty string
          const isNoValue =
            props.dataItem[column.columnKey] === null || props.dataItem[column.columnKey] === '' ||
            props.dataItem[column.columnKey] === undefined;

          props.dataItem.value = props.dataItem[column.columnKey];

          const isHeaderRow = props.dataItem.style === "header";
          return (
            <td
              style={{
                textAlign: "right",
                backgroundColor: isHeaderRow
                  ? "white hover:bg-white"
                  : isNoValue
                    ? "bg-noticeable-50"
                    : "inherit hover:bg-neutral-10",
                color: isHeaderRow ? "#000" : "inherit",
                border: isThisCellEditing
                  ? "3px solid #EDDA70"
                  : isHeaderRow
                    ? "none"
                    : "1px solid #E6E6E6",
                fontWeight: isHeaderRow ? "600" : "normal"
              }}
              onClick={(e) => {
                if (!isHeaderRow) {
                  handleClick();
                  handleCellClick({
                    dataItem: props.dataItem,
                    field: props.field
                  });
                }
              }}
              className={`${isMapped
                ? "cursor-not-allowed text-neutral-60 bg-zinc-50"
                : "cursor-pointer"
                } text-body-r font-body-r
                ${isHeaderRow ? "hover:bg-white" : "hover:bg-neutral-10"} 
                text-right
                ${isNoValue && !isHeaderRow
                  ? "hover:bg-blue bg-noticeable-50"
                  : "bg-inherit text-inherit"
                }
                  ${isThisCellEditing
                  ? "border-[2px] border-[#EDDA70]"
                  : isHeaderRow
                    ? "border-0"
                    : " border-neutral-10"
                }`}
              data-row-id={props.dataItem.id}
              data-field={props.field}
            >
              {/* If it's a header row, don't show 'no value found' text */}
              {!isThisCellEditing && isNoValue && !isHeaderRow && (
                <span
                  className={`${
                    isMapped
                      ? "cursor-not-allowed"
                      : "cursor-pointer"
                  }`}
                >
                  no value found
                </span>
              )}
              {isThisCellEditing && (
                <input
                  type="text"
                  disabled={isMapped}
                  value={inputValue}
                  onChange={handleChange}
                  onKeyDown={handleKeyDown}
                  className={`${isMapped
                    ? "cursor-not-allowed text-neutral-60"
                    : "cursor-pointer"
                    } w-full h-full border-none  focus:outline-none focus:ring-0 p-0 m-0 text-body-r text-right`}
                  style={{
                    boxShadow: "none",
                    background: "transparent",
                    border: "none"
                  }}
                  autoFocus
                  data-row-id={props.dataItem.id}
                  data-field={props.field}
                />
              )}
              {!isThisCellEditing &&
                !isNoValue &&
                props.dataItem[props.field] === "" &&
                ""}
              {!isThisCellEditing &&
                !isNoValue &&
                props.dataItem[props.field] === "" &&
                ""}
              {/* Show empty string when value is explicitly empty */}
              {!isThisCellEditing &&
                !isNoValue &&
                props.dataItem[props.field] !== "" &&
                searchQuery &&
                typeof props.dataItem[props.field] === "string" && (
                  <span
                    className={`${isMapped ? "cursor-not-allowed" : "cursor-pointer"
                      }`}
                  >
                    {" "}
                    {highlightText(
                      formatWithCommas(props.dataItem[props.field]),
                      searchQuery
                    )}
                  </span>
                )}
              {!isThisCellEditing &&
                !isNoValue &&
                props.dataItem[props.field] !== "" &&
                !searchQuery && (
                  <span
                    className={`${isMapped
                      ? "cursor-not-allowed text-neutral-60"
                      : "cursor-pointer"
                      }`}
                  >
                    {formatWithCommas(props.dataItem[props.field])}
                  </span>
                )}
            </td>
          );
        }
      };
    });

    // Return columns based on whether it's a notes table
    return isNotesTable
      ? [labelColumn, ...periodColumns]
      : [dropdownColumn, labelColumn, ...periodColumns];
  };

  // Add new state for notes tables
  const [notesTables, setNotesTables] = useState([]);
  const [notesExpandedState, setNotesExpandedState] = useState({});

  // Modify useEffect to separate first three tables and remaining tables
  useEffect(() => {
    if (financialsData?.tableGroups) {
      // Split tables into main tabs and notes tables
      const mainTables = financialsData.tableGroups.filter(
        (tg) => FinancialStatements.findIndex(label => label.toLowerCase() === tg.label.toLowerCase()) >= 0
      );
      const remainingTables = financialsData.tableGroups.filter(
        (tg) => FinancialStatements.findIndex(label => label.toLowerCase() === tg.label.toLowerCase()) === -1
      );
      if (notesTables.length === 0) {
        setNotesTables(remainingTables);
      }

      // Check if we have stored data for this tab
      if (tableDataByTab[selectedTab]?.length > 0) {
        // Use previously saved data if available
        setCurrentTableData(tableDataByTab[selectedTab]);
      }
      // Otherwise process new data from API
      else if (selectedTab < 3) {
        const currentGroup = mainTables[selectedTab];
        if (currentGroup?.tables?.[0]) {
          const sections = [
            {
              rows: currentGroup.tables[0].rows || [],
              columns: currentGroup.tables[0].columns || []
            }
          ];
          const newData = processTableData(sections);
          setCurrentTableData(newData);
          if (financialsData?.tableGroups && newData?.length > 0) {
            let tabData = FinancialStatementsWithName.find(x => x.value.toLowerCase() == financialsData?.tableGroups?.[0]?.label?.toLowerCase())
            if(tabData != undefined){
              handleExportClick(tabData.tabNo,newData);
            } 
          }
          // Also store this initial data
          setTableDataByTab((prev) => ({
            ...prev,
            [selectedTab]: newData
          }));
          setHasMappedChanges(prev => prev + 1);
          //setHasChanges(true);
        }
      }
    }
  }, [selectedTab, financialsData]); // Only depend on tab change and API data

  const renderContent = () => {
    const dataResult = selectedTab === 3 ? [] : currentTableData;
    if (selectedTab === 3) {
      const updateNotesTables = (updatedNotesTables) => {
        // Update the notes tables state in the parent component
        setNotesTables(updatedNotesTables);
        // Any additional logic like saving to backend, etc.
      };

      return (
        <NotesTab
          notesTables={notesTables}
          updateNotesTables={updateNotesTables}
          isLoading={isLoading}
          searchQuery={searchQuery}
          getDocumentLineItems={getDocumentLineItems}
          generateColumns={generateColumns}
          isMapped={isMapped}
          showDropdown={showDropdown}
          setShowDropdown={setShowDropdown}
          setCurrentSelectedState={setCurrentSelectedState}
          handleCellClick={handleCellClick}
          updateNoteLabel={updateNoteLabel}
          // Pass the row operation functions
          addRow={addRow}
          addRowToBottom={addRowToBottom}
          deleteRow={deleteRow}
          makeHeader={makeHeader}
          mergeRowUp={handleMergeRowUp}
          mergeRowBelow={handleMergeRowBelow}
          // Pass the checkbox mutual exclusivity props
          activeCheckboxType={activeCheckboxType}
          setActiveCheckboxType={setActiveCheckboxType}
          checkedColumns={checkedColumns}
          handleColumnCheckboxClick={handleColumnCheckboxClick}
          initialExpandedNotes={notesExpandedState}
          onExpandedStateChange={setNotesExpandedState}
          addNewNote={handleAddNewNote}
          deleteNotes={handleDeleteNotes}
        />
      );
    }

    return (
      <KendoGrid
        className="w-full h-full"
        dataId="id"
        dataResult={dataResult}
        resultState={{ total: currentTableData.length }}
        currentSelectedState={currentSelectedState}
        setCurrentSelectedState={setCurrentSelectedState}
        setDataResult={() => {}}
        columns={generateColumns(
          financialsData?.tableGroups[selectedTab],
          selectedTab,
          false
        )}
        pageable={false}
        isLoading={false}
        isNotes={selectedTab === 3}
        enableColumnLocking={true} // Add this prop to enable column locking
      />
    );
  };


  const [kpiConfig, setKpiConfig] = useState<{
    moduleId: number;
    pageConfigAliasName: string;
    moduleName: string;
    subSectionFields: {
    valueTypId: number;
    name: string;
    aliasName: string;
    chartValue: string;
    }[];
  }[] | null>(null);
    
  /**
  * Fetches the KPI configuration from the server.
  * The configuration includes module details and sub-section fields.
  * Updates the `kpiConfig` state with the fetched data.
  * Logs an error if the fetch operation fails.
  */
  useEffect(() => {
    const fetchKpiConfig = async () => {
      try {
        const data = await GET_KPI_CONFIG_SERVICE();
        console.log("KPI Config Data:", data);
        setKpiConfig(data);
      } catch (error) {
        console.error("Error fetching KPI config:", error);
      }
    };

    fetchKpiConfig();
  }, []);
  // Add this component where you want the buttons to appear in evidence-panel.tsx
  // Modify the tab rendering section in the return statement
  const renderTabs = () => {
    
    if (!financialsData?.tableGroups) return null;
    // Group tables by type (main financial statements vs notes)
    // const mainTabs = financialsData.tableGroups.filter(
    //   (tg) => FinancialStatements.findIndex(label => label.toLowerCase() === tg.label.toLowerCase()) >= 0
    // );
    const mainTabs = financialsData.tableGroups.filter((tg) => {
      let data = FinancialStatementsWithName.find(statement => tg.label.toLowerCase()?.includes(statement.value.toLowerCase() ));
      if (data && kpiConfig) {
        const kpiItem = kpiConfig?.find((item) => item.moduleName === data.key);
        if (kpiItem) {
          tg.alias = kpiItem.pageConfigAliasName;
        }else{
          tg.alias = tg.label;
        }
      } 
      return tg.label != undefined && tg.label != null;
    });

    // // Check if we have any notes tables
    // const hasNotesTabs = financialsData.tableGroups.some((tg) =>
    //   tg.label.startsWith(NOTES_PREFIX)
    // );
    return (
      <div className="mt-3 flex border-t border-t-[#EDEDF2] border-l border-l-[#EDEDF2] border-r border-r-[#EDEDF2]">
        {mainTabs.map((group,index) => (
          <TabIconText
            key={index} 
            label={group.alias}
            active={selectedTab === index}
            onClick={() => handleTabChange(index, group.label)}
          />
        ))}

        <div className="flex items-center ml-auto pr-4 p-2 gap-2">
          <CircularStatusButtons financialData={financialData} selectedTab={selectedTab} hasMappedChanges = {hasMappedChanges}  />
        </div>
      </div>
    );
  };

  const getFinancialTypeByTab = (tabIndex) => {
    switch (tabIndex) {
      case 0:
        return "IS";
      case 1:
        return "CF";
      case 2:
        return "BS";
      default:
        return "IS";
    }
  };  // Update the prepareMappedData_feedback function to ensure rtext is correctly set with the current label value
  const prepareMappedData_feedback = (newData) => {
    return newData.map((row, index) => {
      
      // Try to get mapping ID from multiple possible sources with strict comparison
      const mappingId = 
        row.mappingId !== undefined && row.mappingId !== null ? row.mappingId : 
        row.mapping_id !== undefined && row.mapping_id !== null ? row.mapping_id : 0;
        
      return {
        rtext: row.label,
        stext: row.status || "",
        mapping_id: mappingId, // Use the retrieved mapping ID
        order: index,
        style: row.style === "header" ? "header" : "lineitem"
      };
    });
  };
  // Update headerProps to ensure mapped data gets the latest values
  const headerProps = {
    currentTableData,
    incomeStatementData: tableDataByTab[0] || [],
    balanceSheetData: tableDataByTab[1] || [],
    cashFlowData: tableDataByTab[2] || [],
    job_id: financialsData?.jobID || "",
    country: financialsData?.country || "",
    client_id: processId || "",
    company_id: financialsData?.companyId || "",
    fs_type: getFinancialTypeByTab(selectedTab),
    industry: financialsData?.industry || "",
    currency: currency || "",
    companyName,
    companyTicker,
    acuityid,
    currencyUnit: financialsData?.currencyUnit,
    mappedData: prepareMappedData_feedback(currentTableData),
    generateColumns,
    deserializeData: { ...financialsData, columnHeaderInfo },
    incomeStatementMappedData: prepareMappedData_feedback(
      tableDataByTab[0] || []
    ),
    extractionType: extractionType,
    balanceSheetMappedData: prepareMappedData_feedback(tableDataByTab[1] || []),
    cashFlowMappedData: prepareMappedData_feedback(tableDataByTab[2] || []),
    setSearchQuery,
    columnHeaderInfo,
    kpiConfig, // Add kpiConfig prop
  };

  

  useEffect(() => {
    if (financialsData?.tableGroups) {
      const sectionForIncomeStatement = [
        {
          rows: financialsData.tableGroups[0]?.tables[0]?.rows || [],
          columns: financialsData.tableGroups[0]?.tables[0]?.columns || []
        }
      ];
      const sectionsForBalanceSheet = [
        {
          rows: financialsData.tableGroups[1]?.tables[0]?.rows || [],
          columns: financialsData.tableGroups[1]?.tables[0]?.columns || []
        }
      ];
      const sectionsForCashFlow = [
        {
          rows: financialsData.tableGroups[2]?.tables[0]?.rows || [],
          columns: financialsData.tableGroups[2]?.tables[0]?.columns || []
        }
      ];
      const notesExportData = financialsData.tableGroups
        .slice(3)
        .map((group) => {
          const tableData = processTableData([
            {
              rows: group.tables[0]?.rows || [],
              columns: group.tables[0]?.columns || []
            }
          ]);

          // Add the note label as a property to the processed data
          // JavaScript allows adding properties to arrays
          tableData.noteLabel = group.label;

          return tableData;
        });
      setNotesData(notesExportData);
      const processedIncomeStatementData = processTableData(
        sectionForIncomeStatement
      );
      const processedBalanceSheetData = processTableData(
        sectionsForBalanceSheet,1
      );
      const processedCashFlowData = processTableData(sectionsForCashFlow,2);

      // Initialize tableDataByTab with processed data
      setTableDataByTab({
        0: processedIncomeStatementData,
        1: processedBalanceSheetData,
        2: processedCashFlowData,
        3: notesExportData
      });

      // Set current table data based on the selected tab
      setCurrentTableData(
        selectedTab === 0
          ? processedIncomeStatementData
          : selectedTab === 1
          ? processedBalanceSheetData
          : selectedTab === 2
          ? processedCashFlowData
          : []
      );
    }
    // setHasMappedChanges(prev => prev + 1);
    // setHasChanges(true);
  }, [financialsData]); // Only depend on financialsData changes

  const downloadSingleFile = async (fileKey: string) => {
    try {
      const response = await DOWNLOAD_FILE_SERVICE({ fileKey });

      return response;
    } catch (error) {
    }
  };

  const handleFileDownloadAndUpdate = (file: FinancialFile) => {
    downloadSingleFile(file.url).then((response) => {
      setAllDownloadedPdf((prevFiles) => [...prevFiles, response]);
    });
  };

  useEffect(() => {
    if (pdfDocument) return;
    if (financialsData?.files != null && financialsData?.files?.length > 0) {
      // Use the previously defined filterUniqueFiles helper
      const uniqueFiles = filterUniqueFiles(financialsData.files);

      
      // Download files sequentially to avoid overwhelming the server
      uniqueFiles.forEach((file) => {
        handleFileDownloadAndUpdate(file);
      });

    }

    // Cleanup function to revoke object URL
    return () => {
      if (localPdfUrl) {
        URL.revokeObjectURL(localPdfUrl);
      }
    };
  }, [financialsData, pdfDocument]);

  // Add useEffect to call the service
  const fetchChartOfAccounts = async (fsType) => {
    try {
      const response = await ChatOfAccounts({
        fs_type: fsType,
        company_id: financialsData?.companyId
      });
      let coa = response?.data;

      return coa?.map((item) => {
        item.parent_text =
          item.parent_text && item.parent_text?.trim() !== ""
            ? item.parent_text
            : "Uncategorized";
        return item;
      });

      // Handle the response data as needed
    } catch (error) {
      console.error("Error fetching chart of accounts:", error);
    }
  };

  useEffect(() => {
    const fetchAllChartOfAccounts = async () => {
      if (financialsData?.companyId) {
        const isCoa = await fetchChartOfAccounts("IS");
        setISCOS(isCoa || []);
        const bsCoa = await fetchChartOfAccounts("BS");
        setBSCOS(bsCoa || []);
        const cfCoa = await fetchChartOfAccounts("CF");
        setCFCOS(cfCoa || []);
      }
    };
    fetchAllChartOfAccounts();
  }, [financialsData?.companyId]);

  const makeHeader = () => {
    const selectedRowIds = Object.keys(checkedItems).filter(
      (id) => checkedItems[id]
    );

    if (selectedRowIds.length !== 1) return; // Must select exactly one row

    const selectedRowId = selectedRowIds[0];

    // Use the utility function to handle the conversion logic
    const result = makeHeaderRow(currentTableData, selectedRowId);

    if (result.success) {
      // Update both current and stored data
      setCurrentTableData(result.updatedData);
      setTableDataByTab((prev) => ({
        ...prev,
        [selectedTab]: result.updatedData
      }));

      notify.success(result.message);
    } else {
      notify.error(result.message);
    }

    // Close the popup regardless of success
    setShowPopup(false);
    setCheckedItems({});
  };

  // Define saveCurrentEdit function to save any current edits
  const saveCurrentEdit = (exitEditMode = false) => {
    if (editingCell.rowId && editingCell.field) {
      // Find the edited row in the current table data
      const editedRow = currentTableData.find(
        (row) => row.id === editingCell.rowId
      );

      if (editedRow) {
        // Try to find the input element using data attributes
        const inputElement = document.querySelector(
          `input[data-row-id="${editingCell.rowId}"][data-field="${editingCell.field}"]`
        );

        if (inputElement) {
          const newValue = (inputElement as HTMLInputElement).value;

          // Apply different logic based on which field is being edited
          if (editingCell.field === "label") {
            // For label fields, just update the label property
            const updatedData = currentTableData.map((item) => {
              if (item.id === editingCell.rowId) {
                return { ...item, label: newValue };
              }
              return item;
            });

            setCurrentTableData(updatedData);
            setTableDataByTab((prev) => ({
              ...prev,
              [selectedTab]: updatedData
            }));
          } else if (editingCell.field.startsWith("value_")) {
            // For numeric value fields
            let formattedValue = newValue;

            // Format numbers: add .0 to whole numbers
            if (
              newValue !== "" &&
              !newValue.includes(".") &&
              !isNaN(Number(newValue))
            ) {
              formattedValue = `${newValue}.0`;
            }

            const updatedData = currentTableData.map((item) => {
              if (item.id === editingCell.rowId) {
                return { ...item, [editingCell.field]: formattedValue };
              }
              return item;
            });

            setCurrentTableData(updatedData);
            setTableDataByTab((prev) => ({
              ...prev,
              [selectedTab]: updatedData
            }));
          }
        }
      }

      // Only clear editing state if exitEditMode is true
      if (exitEditMode) {
        setEditingCell({ rowId: null, field: null });
      }
    }
  };
  
  // Download all files once when component loads
  // Helper function to filter unique files - extracted to top level
  const filterUniqueFiles = (files: FinancialFile[]): FinancialFile[] => {
    const uniqueFiles: FinancialFile[] = [];
    files.forEach((file) => {
      const fileExists = uniqueFiles.some(
        (existingFile) => existingFile.fileName === file.fileName
      );
      if (!fileExists) {
        uniqueFiles.push(file);
      }
    });
    return uniqueFiles;
  };

   
  // Helper function to download all files - extracted to top level
  const downloadAllFiles = async (files: FinancialFile[]) => {
    if (!files || files.length === 0) return [];
    
    try {
      // Create an array of promises for all downloads
      const downloadPromises = files.map((file) =>
        downloadSingleFile(file.url)
      );
      
      // Wait for all downloads to complete
      return await Promise.all(downloadPromises);
    } catch (error) {
      console.error("Error downloading files:", error);
      return [];
    }
  };

  // Example 2: Download all files once when component loads
  useEffect(() => {
    setIsLoading(true)

    const handleFileDownloads = async () => {
      if (!financialsData?.files || financialsData.files.length === 0) return;
      console.info("Starting file downloads - will only run once");
      
      // Use the extracted helper functions
      const uniqueFiles = filterUniqueFiles(financialsData.files);
      const responses = await downloadAllFiles(uniqueFiles);
      
      // Update state with all downloaded files
      setAllDownloadedPdf(responses);
      setIsLoading(false)
      console.info("All files downloaded successfully:", responses);
    };
    
    // Only trigger the downloads once when financialsData becomes available
    if (financialsData && !pdfDocument) {
      handleFileDownloads();
    }
    
  }, [financialsData]); // This will only run when financialsData changes from null to having a value

  // Add the updateNoteLabel function near the other functions in the component
const updateNoteLabel = (noteIndex, newLabel) => {
  if (!notesTables || notesTables.length <= noteIndex) return;
  
  // Create a deep copy of the notesTables array
  const updatedNotesTables = notesTables.map((tableGroup, index) => {
    if (index === noteIndex) {
      // Return a new object with the updated label
      return {
        ...tableGroup,
        label: newLabel
      };
    }
    return tableGroup;
  });
  
  // Update the notesTables state
  setNotesTables(updatedNotesTables);
  
  // If this is affecting the current tab data, update it as well
  if (selectedTab === TAB_LABELS.NOTES) {
    setTableDataByTab(prev => ({
      ...prev,
      [TAB_LABELS.NOTES]: updatedNotesTables
    }));
  }
  
  // Show success notification
  notify.success("Note heading renamed successfully.");
};

  const handleCheckboxColumn = (isChecked, columnUUID, isNewNoteColumn) => {
    // Save the current scroll position
    const scrollPosition = document.querySelector(".k-grid-content")?.scrollTop;

    // Clear row selections if switching to column mode
    if (activeCheckboxType === "row" && isChecked) {
      setCheckedItems({});
    }

    setCheckedColumnHeaders((prevState) => {
      const newCheckedColumnHeaders = { ...prevState };

      if (isChecked) {
        // Add this column header to selections and set active type to 'column'
        newCheckedColumnHeaders[columnUUID] = true;
        setActiveCheckboxType("column");

        // Clear any row selections when selecting a column
        if (Object.keys(checkedItems).length > 0) {
          setCheckedItems({});
        }

        // Add the column UUID to the editingColumnIds array
        setEditingColumnIds((prev) => [...prev, columnUUID]);

        // Set isCheckboxHeader to true to show column edit popup
        setisCheckboxHeader(true);

        // Get column details for the selected column
        const columnKey = Object.keys(columnIdMapping).find(
          (key) => columnIdMapping[key] === columnUUID
        );        // First initialize empty column details before trying to populate it
        const defaultColumnDetails = {
          Filing_Type: "",
          Filing_Version: "Original",
          Filing_Date: null
        };

        if (isNewNoteColumn) {
          // For new note columns, use empty values
          const emptyDetails = {
            Filing_Type: "",
            Filing_Version: "",
            Filing_Date: null
          };
          setColumnDetails(emptyDetails);
          console.log('Setting empty column details for new note:', emptyDetails);
        } 
        else if (
          columnKey &&
          financialsData?.tableGroups?.[selectedTab]?.tables?.[0]
        ) {
          const column = financialsData.tableGroups[
            selectedTab
          ]?.tables[0]?.columns.find((col) => col.columnKey === columnKey);

          if (column) {
            // Get updated column information if available
            const updatedInfo = columnHeaderInfo[columnUUID];
            
            // Handle potentially undefined properties safely
            const reportingPeriod = column.reportingPeriod !== undefined ? column.reportingPeriod : -1;
            const originalRestated = column.originalRestated || "Original";
            const periodDate = column.periodDate || null;
            
            // Ensure reportingPeriod is a valid index for FilingPeriods array
            const filingType = updatedInfo?.filingType || 
              (reportingPeriod >= 0 && reportingPeriod < FilingPeriods.length ? 
              FilingPeriods[reportingPeriod] : "") || "";
              
            const newColumnDetails = {
              Filing_Type: filingType,
              Filing_Version: updatedInfo?.documentStatus || originalRestated,
              Filing_Date: updatedInfo?.filingDate !== undefined ? updatedInfo.filingDate : periodDate
            };
            
            setColumnDetails(newColumnDetails);
          } else {
            // Column not found in the table
            setColumnDetails(defaultColumnDetails);
          }
        } else {
          // Either columnKey not found or no table data
          setColumnDetails(defaultColumnDetails);
        }
      } else {
        // Remove this column from selections
        delete newCheckedColumnHeaders[columnUUID];

        // Remove from editingColumnIds
        setEditingColumnIds((prev) => prev.filter((id) => id !== columnUUID));

        // If no columns are checked, reset the active type and hide popup
        if (
          Object.values(newCheckedColumnHeaders).filter(Boolean).length === 0
        ) {
          setActiveCheckboxType("none");
          setisCheckboxHeader(false);
        }
      }

      return newCheckedColumnHeaders;
    });
    // Restore scroll position
    setTimeout(() => {
      if (scrollPosition !== undefined) {
        const gridContent = document.querySelector(".k-grid-content");
        if (gridContent) gridContent.scrollTop = scrollPosition;
      }
    }, 0);
  };

  // Add a handler for the column popup close action
  const handleColumnPopupClose = (values) => {
    setHasChanges(true);
    if (!values) {
      // If no values provided, just close the popup
      setisCheckboxHeader(false);
      setEditingColumnIds([]);
      setCheckedColumnHeaders({});
      setActiveCheckboxType("none");
      return;
    }

    // Extract all field values from the edit popup
    // const { Filing_Type, Filing_Version, Filing_Date, columnKey, actionType } =
    //   values;
    const { Data_Type, Period_Month, Period_Quarter, Period_Type,Period_Year,columnKey, actionType } = values;

    // Check what action we need to perform
    if (actionType === "insertLeft") {
      // Insert new column to the left of selected column
      insertNewColumn(
        "insertLeft",
        columnKey,
        values,
        financialsData,
        selectedTab,
        columnIdMapping,
        currentTableData,
        {
          setColumnIdMapping,
          setColumnHeaderInfo,
          setCurrentTableData,
          setTableDataByTab,
          clearAllCheckedItems,
          generateColumnId
        }
      );
      notify.success(ColumnSuccessMessages.COLUMN_ADDED);
    } else if (actionType === "insertRight") {
      // Insert new column to the right of selected column
      insertNewColumn(
        "insertRight",
        columnKey,
        values,
        financialsData,
        selectedTab,
        columnIdMapping,
        currentTableData,
        {
          setColumnIdMapping,
          setColumnHeaderInfo,
          setCurrentTableData,
          setTableDataByTab,
          clearAllCheckedItems,
          generateColumnId // Add this line
        }
      );
      notify.success(ColumnSuccessMessages.COLUMN_ADDED);
    } else if (actionType === "delete") {
      // Handle column deletion
      const columnKeysToDelete =
        Array.isArray(values.columnIds) && values.columnIds.length > 0
          ? values.columnIds
          : values.columnKey
            ? [values.columnKey]
            : [];

      if (columnKeysToDelete.length > 0) {
        if (selectedTab === 3) {
          // Special handling for Notes tab
          setNotesTables((prevNotesTables) =>
            prevNotesTables.map((noteTable) => {
              if (!noteTable.tables || !noteTable.tables[0]?.columns) {
                return noteTable; // Return the original noteTable if tables or columns are missing
              }

              // Filter out the columns to delete
              const updatedColumns = noteTable.tables[0].columns.filter(
                (col) => {
                  const colKey = columnIdMapping[col.columnKey];
                  return !columnKeysToDelete.includes(colKey);
                }
              );

              return {
                ...noteTable,
                tables: [
                  {
                    ...noteTable.tables[0],
                    columns: updatedColumns
                  }
                ]
              };
            })
          );

          // Update table data for the current tab
          setTableDataByTab((prev) => ({
            ...prev,
            [selectedTab]: notesTables
          }));

          // Reset UI state after deletion
          setisCheckboxHeader(false);
          setEditingColumnIds([]);
          setCheckedColumnHeaders({});
          setActiveCheckboxType("none");
        } else {
          // Handle regular tables (tabs 0-2)
          deleteColumns(
            columnKeysToDelete,
            financialsData,
            selectedTab,
            columnIdMapping,
            currentTableData,
            {
              setCurrentTableData,
              setTableDataByTab,
              setisCheckboxHeader,
              setEditingColumnIds,
              setCheckedColumnHeaders,
              setActiveCheckboxType
            }
          );
        }

        notify.success(
          columnKeysToDelete.length === 1
            ? ColumnSuccessMessages.SINGLE_COLUMN_DELETED
            : ColumnSuccessMessages.MULTIPLE_COLUMNS_DELETED
        );
      }
    } else if (columnKey) {
      // Find the column key for this UUID
      const columnKeyToUpdate = Object.keys(columnIdMapping).find((key) => columnIdMapping[key] === columnKey);
      if (columnKeyToUpdate && financialsData?.tableGroups?.[selectedTab]?.tables?.[0]) {
        // Update the column information in the financialsData
        financialsData.tableGroups[selectedTab].tables[0].columns =
          financialsData.tableGroups[selectedTab].tables[0].columns.map(
            (col) => {
              if (col.columnKey === columnKeyToUpdate) {
                const updatedColumn = { ...col };
                if (updatedColumn.isNewNoteColumn) {
                  updatedColumn.isNewNoteColumn = false;
                  delete updatedColumn.isNewNoteColumn; // Remove the property
                }           
                // Store the reporting period (actual, budget, etc.)
                updatedColumn.reportingPeriod = 0;
                updatedColumn.periodDate = formatPeriodTypeWithDataType(Period_Type, Period_Month, Period_Quarter, Period_Year, Data_Type);
                updatedColumn.periodValue = formatPeriodTypeWithDataType(Period_Type, Period_Month, Period_Quarter, Period_Year, Data_Type);
                return updatedColumn;
              }
              return col;
            }
          );        // Store column information for display
        // setColumnHeaderInfo((prev) => ({
        //   ...prev,
        //   [columnKey]: {
        //     filingType: "",
        //     documentStatus: "",
        //     // Format the header to display as "actual jan 2020" or "actual Q1 2020"
        //     filingDate: Data_Type ? 
        //     formatPeriodTypeWithDataType(Period_Type, Period_Month, Period_Quarter, Period_Year, Data_Type)
        //       : '',
        //       periodValue :Data_Type ? 
        //       formatPeriodTypeWithDataType(Period_Type, Period_Month, Period_Quarter, Period_Year, Data_Type)
        //         : '',
        //   }
        // }));

        if (selectedTab === 3) {
          setNotesTables((prevNotesTables) =>
            prevNotesTables.map((noteTable) => {
              if (!noteTable.tables || !noteTable.tables[0]?.columns) {
                return noteTable; // Return the original noteTable if tables or columns are missing
              }

              const updatedColumns = noteTable.tables[0].columns.map((col) => {
                if (col.columnKey === columnKeyToUpdate) {
                  const updatedColumn = { ...col };
                  if (updatedColumn.isNewNoteColumn) {
                    updatedColumn.isNewNoteColumn = false;
                    delete updatedColumn.isNewNoteColumn; // Remove the property
                  }
                  return updatedColumn;
                }
                return col;
              });

              return {
                ...noteTable,
                tables: [
                  {
                    ...noteTable.tables[0],
                    columns: updatedColumns
                  }
                ]
              };
            })
          );
        }

        notify.success(ColumnSuccessMessages.COLUMN_UPDATED);
        setHasChanges(true);
      }
    }
    
    // Reset states to close the popup
    setisCheckboxHeader(false);
    setEditingColumnIds([]);
    setCheckedColumnHeaders({});
    setActiveCheckboxType("none");
  };

  // Add function to validate if all dropdown values are selected
  const validateAllDropdownsSelected = () => {
    // Skip validation if mapped is true
    if (isMapped) return true;

    // Check all rows except headers
    const nonHeaderRows = currentTableData.filter(row => row.style !== "header");
    
    // Check if all non-header rows have a status value
    const allSelected = nonHeaderRows.every(row => row.status && row.status.trim() !== "");
    
    // Update state based on validation result
    setAreAllDropdownsSelected(allSelected);
    
    return allSelected;
  };

  // Add useEffect to validate dropdowns on data changes
  useEffect(() => {
    if (!isMapped) { // Only validate on main tabs, not notes tab
      validateAllDropdownsSelected();
    }
  }, [currentTableData, isMapped]);


  // Update the main return statement
  return (
    <div className="flex flex-col h-screen mr-0">
      <Header
        {...headerProps}
        className="flex flex-row w-full h-[60px]"
        handleBackButton={handleBackButtonClick}
        companyName={companyName}
        companyTicker={companyTicker}
        acuityid={acuityid || financialsData?.companyId}
        currencyUnit={financialsData?.currencyUnit}
        currency={financialsData?.currencyCode}
        currentTableData={currentTableData}
        notesData={notesData}
        generateColumns={generateColumns}
        tableGroups={financialsData?.tableGroups}
        financialsData={financialsData}
        isMapped={isMapped}
        setIsMapped={setIsMapped}
        standardLineItemsForIncomeStatement={
          standardLineItemsForIncomeStatement
        }
        standardLineItemsForBalanceSheet={standardLineItemsForBalanceSheet}
        standardLineItemsForCashFlow={standardLineItemsForCashFlow}
        selectedTab={selectedTab}
        handleResetPublish={handleResetPublish}
        currencyDetails = {currencyDetails}
        onCurrencyChange = {handleCurrencyChange}
        onCurrencyUnitChange = {handleCurrencyUnitChange}
        kpiConfig={kpiConfig} // Add kpiConfig prop
        areAllDropdownsSelected={areAllDropdownsSelected}
        disableButton={false}
        disableSaveButton={true}
        hasChanges={hasChanges}
        client_env={queryClientEnv}
        companyDetails={companyDetails}
      />
      {renderTabs()}
      {/* Main Content Area */}
      <div className="flex-1 flex overflow-hidden">
        {/* Grid Section - Adjusts width based on overlay */}
        <div
          ref={tableRef}
          className={`${showPdfOverlay ? "w-1/2" : "w-full"
            }  transition-width duration-300 border-l border-l-[#EDEDF2] border-b border-b-[#EDEDF2]`}
        >
          {renderContent()}
        </div>

        {/* PDF Overlay - Fixed position relative to content area */}

        <div
          className={` ${showPdfOverlay ? "block" : "hidden"
            } w-1/2 border-l border-neutral-20 flex flex-col bg-white pdf-overlay h-[100%]`}
        >
          {/* PDF Document Header */}
          <div className="flex items-center px-4 py-2 bg-[#fafafa] border-x border-b border-neutral-20">
            <div className="flex items-center gap-1 flex-grow">
              {IsFileNameLoading ? (
                <></>
              ) : fileName ? (
                <>
                  <img src={PdfIcon} alt="PDF" className="w-[18px] h-[18px]" />
                  <span className="body-b text-neutral-90 flex-grow">
                    {fileName}
                  </span>
                </>
              ) : (
                <>
                  <img src={PdfIcon} alt="PDF" className="w-[18px] h-[18px]" />
                  <span className="body-b text-neutral-90 flex-grow">
                    Document
                  </span>
                </>
              )}
            </div>
            <button
              onClick={handleClosePdf}
              className="p-1.5 rounded hover:bg-neutral-10"
            >
              <svg
                width="12"
                height="12"
                viewBox="0 0 12 12"
                className="text-neutral-80"
              >
                <path
                  d="M2.5 2.5L9.5 9.5M2.5 9.5L9.5 2.5"
                  stroke="currentColor"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                />
              </svg>
            </button>
          </div>
          <div className="flex-1 relative border-r border-r-[#EDEDF2] border-b border-b-[#EDEDF2]">
            {!localPdfUrl ? (
              <div className="flex flex-col items-center justify-center h-full">
                <SpinnerCircle />
                <span className="body-r pt-4 text-neutral-60">
                  Please wait, we are getting the file ready!
                </span>
              </div>
            ) : (
              <PdfHolder
                beforeLoad={
                  <div className="flex flex-col items-center justify-center h-full">
                    <SpinnerCircle />
                    <span className="body-r pt-4 text-neutral-60">
                      Processing PDF document...
                    </span>
                  </div>
                }
                base64data={localPdfUrl.file}
                url={localPdfUrl.file}
                pdfDocument={pdfDocument}
                setPdfDocument={setPdfDocument}
              >
                {(pdfDocument) => (
                  <PdfHighlighter
                    ref={pdfHighlighterRef}
                    pdfDocument={pdfDocument}
                    highlights={highlights}
                    onSelectionFinished={(position, content) => {
                      if (selectedCell && !isMapped) {
                        updateGridDataWithHighlight(content,position);
                      }
                    }}
                    pdfScaleValue="page-width"
                    enableAreaSelection={(event) => event.altKey}
                    scrollRef={(scrollTo) => {
                      if (scrollTo) {
                        pdfHighlighterRef.current = { scrollTo };
                      }
                    }}
                    onScrollChange={() => {}}
                    highlightTransform={(
                      highlight,
                      index,
                      setTip,
                      hideTip,
                      viewportToScaled,
                      screenshot,
                      isScrolledTo
                    ) => {
                      const isTextHighlight = !highlight.content?.image;

                      const component = isTextHighlight ? (
                        <Highlight
                          position={highlight.position}
                          comment={highlight.comment}
                          isScrolledTo={isScrolledTo}
                          onClick={() => {}}
                        />
                      ) : (
                        <AreaHighlight
                          isScrolledTo={isScrolledTo}
                          highlight={highlight}
                          onChange={(boundingRect) => {
                            updateHighlight(
                              highlight.id,
                              {
                                boundingRect: viewportToScaled(boundingRect)
                              },
                              { image: screenshot(boundingRect) }
                            );
                          }}
                        />
                      );

                      return (
                        <Popup
                          popupContent={<HighlightPopup {...highlight} />}
                          onMouseOver={(popupContent) =>
                            setTip(highlight, (highlight) => popupContent)
                          }
                          onMouseOut={hideTip}
                          key={index}
                        >
                          {component}
                        </Popup>
                      );
                    }}
                  />
                )}
              </PdfHolder>
            )}
          </div>
        </div>
      </div>
      <PopupComponent
        showPopup={showPopup}
        setShowPopup={setShowPopup}
        addRow={addRow}
        addRowToBottom={addRowToBottom}
        deleteRow={deleteRow}
        makeHeader={makeHeader}
        mergeRowUp={handleMergeRowUp}
        mergeRowBelow={handleMergeRowBelow}
        selectedCount={Object.values(checkedItems).filter(Boolean).length}
        clearAllCheckedItems={clearAllCheckedItems}
        isAllRowsSelected={
          Object.values(checkedItems).filter(Boolean).length ===
            currentTableData.length && currentTableData.length > 0
        }
      />
      <PopupcolumnComponent
        isCheckboxHeader={isCheckboxHeader}
        editingColumnKey={
          editingColumnIds.length === 1 ? editingColumnIds[0] : undefined
        }
        editingColumnIds={editingColumnIds}
        onSave={handleColumnPopupClose}
        selectedTab={selectedTab}
        checkedColumnHeaders={checkedColumnHeaders}
        columnDetails={columnDetails} // Pass column details to popup
        kpiConfig={kpiConfig}
        totalColumns={
          selectedTab === 3 && editingColumnIds.length > 0
            ? // For Notes tab, calculate columns from notesTables
            notesTables.reduce((count, note) => {
              // Find the note that contains the column being edited
              const hasEditingColumn = note.tables?.[0]?.columns?.some(
                (col) =>
                  editingColumnIds.includes(columnIdMapping[col.columnKey])
              );
              // If this note contains the editing column, return its column count
              return hasEditingColumn
                ? note.tables?.[0]?.columns?.length || 0
                : count;
            }, 0)
            : // For other tabs, use the existing method
            financialsData?.tableGroups?.[selectedTab]?.tables?.[0]?.columns
              ?.length || 0
        }
        extractionType="AsIsExtraction"
      />
    </div>
  );
};

EvidencePanel.propTypes = {
  handleBackfunc: PropTypes.func.isRequired,
  processId: PropTypes.string.isRequired,
  companyName: PropTypes.string,
  companyTicker: PropTypes.string,
  acuityid: PropTypes.string
};

export default EvidencePanel;
