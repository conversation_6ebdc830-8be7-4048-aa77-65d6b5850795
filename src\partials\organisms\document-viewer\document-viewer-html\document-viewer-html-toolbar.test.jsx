import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import DocumentViewerHTMLToolBar from "./document-viewer-html-toolbar";

// Mock the necessary hooks and modules
jest.mock("../document-viewer.store", () => ({
  __esModule: true,
  default: jest.fn(() => ({
    setPreviewClose: jest.fn()
  }))
}));

describe("DocumentViewerHTMLToolBar", () => {
  it("renders the toolbar with a search query", () => {
    render(
      <DocumentViewerHTMLToolBar
        searchQuery="test"
        readOnly={false}
        setSearchQuery={jest.fn()}
      />
    );

    const searchInput = screen.getByTestId(
      "document-viewer-html-toolbar-search-input"
    );
    expect(searchInput).toHaveValue("test");
  });

  it("handles search input change", () => {
    const setSearchQueryMock = jest.fn();
    render(
      <DocumentViewerHTMLToolBar
        searchQuery=""
        readOnly={false}
        setSearchQuery={setSearchQueryMock}
      />
    );

    const searchInput = screen.getByTestId(
      "document-viewer-html-toolbar-search-input"
    );
    fireEvent.change(searchInput, { target: { value: "new query" } });

    expect(setSearchQueryMock).toHaveBeenCalledWith("new query");
  });

  it("clears the search query", () => {
    const setSearchQueryMock = jest.fn();
    render(
      <DocumentViewerHTMLToolBar
        searchQuery="test"
        readOnly={false}
        setSearchQuery={setSearchQueryMock}
      />
    );

    const clearButton = screen.getByTestId(
      "document-viewer-html-toolbar-search-input"
    ).nextSibling;
    fireEvent.click(clearButton);

    expect(setSearchQueryMock).toHaveBeenCalledWith("");
  });

  it("does not render close button when readOnly is true", () => {
    render(
      <DocumentViewerHTMLToolBar
        searchQuery=""
        readOnly={true}
        setSearchQuery={jest.fn()}
      />
    );

    const closeButton = screen.queryByTestId(
      "document-viewer-html-toolbar-close-button"
    );
    expect(closeButton).toBeNull();
  });
});
