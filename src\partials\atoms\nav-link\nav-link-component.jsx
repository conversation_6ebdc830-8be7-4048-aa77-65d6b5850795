import React from "react";
import PropTypes from "prop-types";
import { NavLink, useLocation } from "react-router-dom";

const NavLinkComponent = ({ text = "", to = "" }) => {
  const { pathname } = useLocation();
  const pathArray = pathname.split("/").filter((n) => n);
  return (
    <li
      id="tab-menu-dashboard"
      data-testid="tab-menu-dashboard"
      className={`px-3 hover:text-primary-78 ${
        pathArray.includes(to) && !pathArray.includes("myhighlights")
          ? "s-m border-b-4 border-primary-78 text-primary-78"
          : "text-neutral-60"
      }`}
    >
      <NavLink
        data-testid="nav-link"
        className={`body-r flex items-center justify-center whitespace-nowrap pb-10 hover:text-primary-78 ${
          pathArray.includes(to) &&
          pathArray.includes("myhighlights") &&
          "s-m flex items-center justify-center"
        }`}
        to={to}
      >
        {text}
      </NavLink>
    </li>
  );
};

NavLinkComponent.propTypes = {
  text: PropTypes.string,
  to: PropTypes.string
};

export default NavLinkComponent;
