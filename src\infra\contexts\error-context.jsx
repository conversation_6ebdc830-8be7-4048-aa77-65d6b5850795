/* eslint-disable */
import React from "react";
import { ErrorBoundary } from "react-error-boundary";
import PropTypes from "prop-types";

export const ErrorContextProvider = (props) => {
  return (
    <ErrorBoundary
      FallbackComponent={ErrorPage}
      onError={(e) => {
        console.log("ErrorBoundary onError ===> ", e);
      }}
      onReset={() => {
        // reloading the page to restore the initial state
        // of the current page
        window.location.reload();
      }}
    >
      {props.children}
    </ErrorBoundary>
  );
};

ErrorContextProvider.propTypes = {
  children: PropTypes.element
};

const ErrorPage = (props) => {
  return (
    <div
      data-testid="error-context-page"
      className="flex h-screen w-full items-center justify-center bg-white p-5"
    >
      <div className="space-y-8 text-center">
        <div className="inline-flex rounded-full bg-red-100 p-4">
          <div className="rounded-full bg-red-200 stroke-red-600 p-4">
            <svg
              className="h-16 w-16"
              viewBox="0 0 28 28"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M6 8H6.01M6 16H6.01M6 12H18C20.2091 12 22 10.2091 22 8C22 5.79086 20.2091 4 18 4H6C3.79086 4 2 5.79086 2 8C2 10.2091 3.79086 12 6 12ZM6 12C3.79086 12 2 13.7909 2 16C2 18.2091 3.79086 20 6 20H14"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              ></path>
              <path
                d="M17 16L22 21M22 16L17 21"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              ></path>
            </svg>
          </div>
        </div>
        <h1 className="text-4xl font-bold text-slate-800">
          500 - Server error
        </h1>
        <p className="lg:text-lg text-slate-600">
          Oops something went wrong. Try to refresh this page
        </p>
        {props.resetErrorBoundary && (
          <div className="flex justify-center">
            <button
              data-testid="error-context-reload"
              type="button"
              className="s-r flex h-8 items-center rounded-md bg-primary-78 px-4 text-white hover:bg-primary-90"
              onClick={props.resetErrorBoundary}
            >
              🔄 Try Again
            </button>
          </div>
        )}
      </div>
    </div>
  );
};
ErrorPage.propTypes = {
  resetErrorBoundary: PropTypes.bool
};
