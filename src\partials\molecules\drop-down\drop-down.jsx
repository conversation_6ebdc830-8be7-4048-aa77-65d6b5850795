import React, { useRef, useEffect } from "react";
import Transition from "../../atoms/transition/transition";
import PropTypes from "prop-types";

// eslint-disable-next-line react/prop-types
const Dropdown = ({
  children,
  align,
  dropdownOpen,
  setDropdownOpen,
  trigger
}) => {
  const dropdown = useRef(null);

  // close on click outside
  useEffect(() => {
    const clickHandler = ({ target }) => {
      if (!dropdown.current) return;
      if (
        !dropdownOpen ||
        dropdown.current.contains(target) ||
        trigger.current.contains(target)
      )
        return;
      setDropdownOpen(false);
    };
    document.addEventListener("click", clickHandler);
    return () => document.removeEventListener("click", clickHandler);
  });

  // close if the esc key is pressed
  useEffect(() => {
    const keyHandler = ({ keyCode }) => {
      if (!dropdownOpen || keyCode !== 27) return;
      setDropdownOpen(false);
    };
    document.addEventListener("keydown", keyHandler);
    return () => document.removeEventListener("keydown", keyHandler);
  });

  return (
    <Transition
      data-testid="drop-down"
      show={dropdownOpen}
      className={`body-r absolute z-50 overflow-hidden rounded border border-neutral-10 bg-white shadow-md ${align}`}
      enter="transition ease-out duration-300 transform"
      enterStart="opacity-0 -translate-y-2"
      enterEnd="opacity-100 translate-y-0"
      leave="transition ease-out duration-300"
      leaveStart="opacity-100"
      leaveEnd="opacity-0"
    >
      <ul data-testid="drop-down-list" ref={dropdown}>
        {children}
      </ul>
    </Transition>
  );
};

export default Dropdown;

Dropdown.propTypes = {
  children: PropTypes.node,
  align: PropTypes.string,
  dropdownOpen: PropTypes.bool,
  setDropdownOpen: PropTypes.func,
  trigger: PropTypes.object
};
